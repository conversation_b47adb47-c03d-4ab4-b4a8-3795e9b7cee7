<script lang="ts" setup name="GreyClothInventorySheetAdd">
import { ElMessage } from 'element-plus'
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
// import { useRouter } from 'vue-router'
import currency from 'currency.js'
import AddInventoryDialog from '../components/AddInventoryDialog.vue'
import AddInventoryXimaDialog from '../components/AddInventoryXimaDialog.vue'
import AddXimaDialog from '../components/AddXimaDialog.vue'
import { addGfmStockCheckOrder } from '@/api/greyClothInventorySheet'
import { BusinessUnitIdEnum, EmployeeType, GetGfmCheckOrderTypeEnum } from '@/common/enum'
import { formatDate, formatNumberPrefix, formatPriceDiv, formatPriceMul, formatRollDiv, formatWeightDiv, formatWeightMul, sumNum } from '@/common/format'
import { deepClone, getCurrentDate, getDefaultSaleSystem, getTableCellTextClass } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'
import SelectDialog from '@/components/SelectDialog/index.vue'
import SelectGreyFabricDialog from '@/components/SelectGreyFabricDialog/index.vue'
import type { TableColumn } from '@/components/Table/type'
import { GetGfmWarehouseSummaryDyeingListEnum } from '@/api/dyeingFactoryTable'
import { getGfmWarehouseSumList } from '@/api/greyFabricPurchaseReturn'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'

const routerList = useRouterList()

const state = reactive<any>({
  form: {
    marketingSystem: '', // 营销体系
    consignee: '', // 盘点单位
    destprovidername: '',
    date: '', // 退货日期
    remark: '',
    dye_unit_use_order_no: '', // 染厂用坯单号
    warehouse_keeper: '', // 仓管员
    store_keeper_name: '',
    order_type: 1 as GetGfmCheckOrderTypeEnum,
  },
  formRules: {
    marketingSystem: [{ required: true, message: '请选择营销体系', trigger: 'blur' }],
    consignee: [{ required: true, message: '请选择盘点单位', trigger: 'blur' }],
    // destprovidername: [{ required: true, message: '请选择供方名称', trigger: 'blur' }],
    date: [{ required: true, message: '请选择盘点日期', trigger: 'blur' }],
    order_type: [{ required: true, message: '请选择坯布状态', trigger: 'blur' }],
  },
  tableList: [],
  multipleSelection: [],
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  unit_name: '',
  raw_code: '',
  raw_name: '',
  color_code: '',
  color_name: '',
  grey_fabric_code: '',
  grey_fabric_name: '',
})

const tableConfig = ref({
  filterStatus: false,
  showSlotNums: true,
  // showOperate: true,
  // operateWidth: '80',
  footerMethod: (val: any) => FooterMethod(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  footerCellClassName: ({ column, $columnIndex, row }: any) => {
    if (['inventory_roll', 'inventory_weight'].includes(column.field)) {
      const footVal = row[$columnIndex]
      return getTableCellTextClass(footVal)
    }
  },
  showSpanHeader: true,
})

onMounted(() => {
  state.form.date = getCurrentDate()
  const resDes = getDefaultSaleSystem()
  state.form.marketingSystem = resDes?.default_sale_system_id
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['before_roll'].includes(column.property))
        return `${formatRollDiv(sumNum(data, column.property))}`

      if (['before_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, column.property))}`

      if (['roll', 'total_weight'].includes(column.property))
        return `${sumNum(data, column.property)}`

      if (['inventory_roll', 'inventory_weight'].includes(column.property)) {
        const total = data.reduce((acc: number, cur: any) => {
          const num = Number(computedTableKey(cur, column.property))
          if (!Number.isNaN(num))
            return currency(acc).add(num).value
          else
            return acc
        }, 0)
        return formatNumberPrefix(total)
      }

      if (['other_price'].includes(column.property))
        return `￥${sumNum(data, 'other_price')}`

      if (['total_price'].includes(column.property))
        return `￥${sumNum(data, 'total_price')}`

      return null
    }),
  ]
}

/**
 * 处理表格数据显示
 * @param row 表格行数据
 * @param key 表格列key
 */
function computedTableKey(row: any, key: string) {
  if (!row.surplus)
    return
  // 盘盈/亏 - 匹数
  if (key === 'inventory_roll') {
    const result_roll = currency(row.roll).subtract(formatPriceDiv(row.before_roll)).value // 盘盈亏=实盘-盘前
    return result_roll // 保留两位小数
  }
  // 盘盈/亏 - 数量
  if (key === 'inventory_weight') {
    const result_weight = currency(row.total_weight).subtract(formatWeightDiv(row.before_weight)).value // 盘盈亏=实盘-盘前
    return result_weight // 保留两位小数
  }
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

const columnList = computed<TableColumn[]>(() => [
  {
    title: '基础信息',
    childrenList: [
      {
        field: 'grey_fabric_code',
        title: '坯布编号',
        minWidth: 100,
        soltName: 'grey_fabric_code',
        required: true,
      },
      {
        field: 'grey_fabric_name',
        title: '坯布名称',
        minWidth: 150,
        soltName: 'grey_fabric_name',
        required: true,
      },
      {
        field: 'supplier_id',
        title: '供方名称',
        minWidth: 100,
        soltName: 'supplier_id',
        required: true,
      },
      {
        field: 'customer_id',
        title: '所属客户',
        soltName: 'customer_id',
        minWidth: 100,
        required: true,
      },
      {
        field: 'yarn_batch',
        title: '纱批',
        soltName: 'yarn_batch',
        minWidth: 100,
      },
      {
        field: 'machine_number',
        title: '机台号',
        soltName: 'machine_number',
        minWidth: 100,
      },
      {
        field: 'gray_fabric_color_id',
        title: '织坯颜色',
        minWidth: 100,
        soltName: 'gray_fabric_color_id',
      },
      {
        field: 'grey_fabric_level_id',
        title: '坯布等级',
        required: true,
        minWidth: 100,
        soltName: 'grey_fabric_level_id',
      },
      {
        field: 'unit_name',
        title: '坯布单位',
        minWidth: 90,
      },
      {
        field: 'raw_material_yarn_name',
        title: '原料纱名',
        minWidth: 100,
        soltName: 'raw_material_yarn_name',
      },
      {
        field: 'raw_material_batch_num',
        title: '原料批号',
        minWidth: 100,
        soltName: 'raw_material_batch_num',
      },
      {
        field: 'raw_material_batch_brand',
        title: '原料品牌',
        minWidth: 100,
        soltName: 'raw_material_batch_brand',
      },
      {
        field: 'grey_fabric_remark',
        title: '坯布备注',
        minWidth: 150,
        // soltName: 'gray_fabric_remark',
      },
    ],
  },
  {
    title: '盘前',
    field: 'before_data',
    childrenList: [
      {
        field: 'before_roll',
        title: '盘前匹数',
        minWidth: 80,
        isPrice: true,
      },
      {
        field: 'before_weight',
        title: '盘前数量',
        minWidth: 80,
        isWeight: true,
      },
    ],
  },
  {
    title: '实盘',
    field: 'actual_data',
    childrenList: [
      {
        field: 'roll',
        title: '实盘匹数',
        minWidth: 80,
        soltName: 'roll',
        required: true,
      },
      {
        field: 'total_weight',
        title: '实盘数量',
        minWidth: 80,
        required: state.form.order_type === GetGfmCheckOrderTypeEnum.GfmCheckOrderTypeDyeing,
        soltName: 'total_weight',
      },
    ],
  },
  {
    title: '盘盈/亏',
    childrenList: [
      {
        field: 'inventory_roll',
        title: '匹数',
        minWidth: 80,
        soltName: 'inventory_roll',
      },
      {
        field: 'inventory_weight',
        title: '数量',
        minWidth: 80,
        soltName: 'inventory_weight',
      },
    ],
  },
  {
    title: '其他信息',
    childrenList: [
      {
        title: '细码',
        minWidth: 100,
        show: state.form.order_type !== GetGfmCheckOrderTypeEnum.GfmCheckOrderTypeDyeing,
        soltName: 'xima',
        required: true,
      },
      {
        field: 'remark',
        title: '备注',
        minWidth: 150,
        soltName: 'remark',
      },
      {
        title: '操作',
        minWidth: 100,
        soltName: 'operation',
        fixed: 'right',
      },
    ],
  },
])

const AddInventoryDialogRef = ref() // 从库存中添加弹出框

function handAdd() {
  AddInventoryDialogRef.value.state.sale_system_id = state.form.marketingSystem // 绑定营销体系
  AddInventoryDialogRef.value.state.filterData.warehouse_id = state.form.consignee // 绑定盘点单位
  AddInventoryDialogRef.value.state.showModal = true // 打开从库存中添加框
}

// 点击《选择坯布库存》确认添加
function handleSureFabric(list: any) {
  //
  list.forEach((item: any) => {
    state.tableList.push({
      grey_fabric_code: item.grey_fabric_code,
      grey_fabric_name: item.grey_fabric_name,
      customer_id: item.customer_id,
      customer_name: item.customer_name,
      yarn_batch: item.yarn_batch,
      supplier_id: item.supplier_id,
      supplier_name: item.supplier_name,
      gray_fabric_color_id: item.gray_fabric_color_id,
      gray_fabric_color_name: item.gray_fabric_color_name,
      grey_fabric_level_id: item.grey_fabric_level_id,
      grey_fabric_level_name: item.grey_fabric_level_name,
      unit_name: item.unit_name,
      unit_id: item.unit_id,
      machine_number: item.machine_number,
      total_weight: null,
      before_roll: item.stock_roll,
      before_weight: item.stock_weight,
      roll: '',
      inventory_roll: 0,
      inventory_weight: 0,
      remark: '',
      item_fc_data: [],
      surplus: true,
      is_stock_source: true,
      grey_fabric_remark: item.source_remark,
      raw_material_batch_brand: item.raw_material_batch_brand,
      raw_material_batch_num: item.raw_material_batch_num,
      raw_material_yarn_name: item.raw_material_yarn_name,
      grey_fabric_id: item?.grey_fabric_id || 0,
      warehouse_sum_id: item?.id || 0,
    })
  })
  AddInventoryDialogRef.value.state.showModal = false
}

function handSurplusAdd() {
  state.tableList.push({
    grey_fabric_code: '',
    grey_fabric_name: '',
    customer_id: '',
    customer_name: '',
    yarn_batch: '',
    gray_fabric_color_id: '',
    gray_fabric_color_name: '',
    grey_fabric_level_id: '',
    grey_fabric_level_name: '',
    machine_number: '',
    total_weight: null,
    before_roll: 0,
    before_weight: 0,
    roll: '',
    inventory_roll: '',
    inventory_weight: '',
    remark: '',
    item_fc_data: [],
    supplier_id: '',
    supplier_name: '',
    raw_material_yarn_name: '',
    raw_material_batch_num: '',
    raw_material_batch_brand: '',
    grey_fabric_remark: '',
    warehouse_sum_id: 0,
    grey_fabric_id: 0,
    isProfit: true,
  })
}

const AddInventoryXimaDialogRef = ref()

const AddXimaDialogRef = ref()

function handWrite(row: any, rowIndex: number) {
  // 1，先判断触发条件是否成立
  if (row.roll === '')
    return ElMessage.error('请先输入实盘匹数')

  //
  if (row.surplus) {
    const filterData = {
      grey_fabric_id: row.grey_fabric_id,
      customer_id: row.customer_id,
      customer_name: row.customer_name,
      yarn_batch: row.yarn_batch,
      supplier_id: row.supplier_id,
      gray_fabric_color_id: row.gray_fabric_color_id,
      grey_fabric_level_id: row.grey_fabric_level_id,
      raw_material_batch_brand: row.raw_material_batch_brand,
      raw_material_batch_num: row.raw_material_batch_num,
      raw_material_yarn_name: row.raw_material_yarn_name,
      grey_fabric_remark: row.grey_fabric_remark,
      warehouse_id: state.form.consignee,
      warehouse_sum_id: row?.warehouse_sum_id || 0,
      page: 1, // 设定页数
      size: 50, // 每页多少条数据
    }
    const info = {
      supplier_name: row.supplier_name,
      grey_fabric_code: row.grey_fabric_code,
      grey_fabric_name: row.grey_fabric_name,
      gray_fabric_color_name: row.gray_fabric_color_name,
      grey_fabric_level_name: row.grey_fabric_level_name,
      yarn_batch: row.yarn_batch,
      machine_number: row.machine_number,
      customer_name: row.customer_name,
    }
    AddInventoryXimaDialogRef.value.state.filterData = filterData
    AddInventoryXimaDialogRef.value.state.info = info
    AddInventoryXimaDialogRef.value.state.canEnter = row.roll
    AddInventoryXimaDialogRef.value.state.rowIndex = rowIndex
    AddInventoryXimaDialogRef.value.state.ximaList = deepClone(row?.item_fc_data) || []
    AddInventoryXimaDialogRef.value.state.showModal = true // 最后让弹出框显示出来
  }
  else {
    AddXimaDialogRef.value.state.showModal = true
    const arr = [
      {
        roll: row.roll,
        position: '',
        volume_number: '',
        weight: '',
      },
    ]
    if (!row?.item_fc_data?.length)
      AddXimaDialogRef.value.state.tableData = arr
    else
      AddXimaDialogRef.value.state.tableData = deepClone(row?.item_fc_data) || []
    AddXimaDialogRef.value.state.isAdd = true

    AddXimaDialogRef.value.state.canEnter = row.roll
    AddXimaDialogRef.value.state.horsepower = row.roll
    AddXimaDialogRef.value.state.code = row.grey_fabric_code
    AddXimaDialogRef.value.state.name = row.grey_fabric_name
    AddXimaDialogRef.value.state.rowIndex = rowIndex
  }
}

const ruleFormRef = ref()

const { fetchData: addPost, data: addData, success: addSuccess, msg: addMsg } = addGfmStockCheckOrder()

// 提交数据
async function handleSure() {
  if (!state.tableList.length)
    return ElMessage.error('至少添加一条坯布信息')

  const list = deepClone(state.tableList)

  for (let i = 0; i < list.length; i++) {
    if (list[i].roll === '')
      return ElMessage.error('匹数不可为空')
    if (state.form.order_type !== GetGfmCheckOrderTypeEnum.GfmCheckOrderTypeDyeing) {
      if (Number(list[i].roll) - Number(sumNum(list[i]?.item_fc_data, 'roll')) !== 0 && Number(list[i].roll) !== 0 && !list[i].surplus)
        return ElMessage.error(`第${i + 1}行的细码必须录完`)

      if (Number(list[i].roll) - Number(sumNum(list[i]?.item_fc_data, 'actually_roll')) !== 0 && Number(list[i].roll) !== 0 && list[i].surplus)
        return ElMessage.error(`第${i + 1}行的细码必须录完`)
    }

    if (list[i].grey_fabric_code === '' && !list[i].surplus)
      return ElMessage.error('请选择坯布编号')

    if (list[i].grey_fabric_name === '' && !list[i].surplus)
      return ElMessage.error('请选择坯布名称')

    if (list[i].supplier_id === '' && !list[i].surplus)
      return ElMessage.error('请选择供方名称')

    if (list[i].customer_id === '' && !list[i].surplus)
      return ElMessage.error('请选择所属客户')

    list[i].actually_roll = Number(formatPriceMul(list[i]?.roll))
    list[i].actually_weight = Number(formatWeightMul(list[i]?.total_weight))
    // 将坯布信息的数据格式化
    for (let q = 0; q < list[i].item_fc_data?.length; q++) {
      if (list[i].surplus) {
        list[i].item_fc_data[q].actually_roll = Number(formatPriceMul(list[i].item_fc_data[q].actually_roll))
        list[i].item_fc_data[q].grey_fabric_stock_id = Number(list[i].item_fc_data[q].id)
        list[i].item_fc_data[q].actually_weight = Number(formatWeightMul(list[i].item_fc_data[q].actually_weight))
      }
      else {
        list[i].item_fc_data[q].actually_roll = Number(formatPriceMul(list[i].item_fc_data[q].roll))
        list[i].item_fc_data[q].actually_weight = Number(formatWeightMul(list[i].item_fc_data[q].weight))
      }
      list[i].gray_fabric_color_id ||= 0
      list[i].grey_fabric_level_id ||= 0
    }
  }
  const query = {
    item_data: list,
    remark: state.form.remark,
    order_type: state.form.order_type,
    dye_unit_use_order_no: state.form.dye_unit_use_order_no,
    check_unit_id: state.form.consignee,
    store_keeper_id: state.form.warehouse_keeper || 0,
    check_time: formatDate(state.form.date),
    sale_system_id: state.form.marketingSystem,
    store_keeper_name: state.form.store_keeper_name,
  }

  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(query)
      if (addSuccess.value) {
        ElMessage.success('成功')
        routerList.push({ name: 'GreyClothInventorySheetDetail', query: { id: addData.value.id } })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

function handDelete(index: number) {
  state.tableList.splice(index, 1)
}

const tableRef = ref()

watch(
  () => state.tableList,
  () => {
    if (state.tableList?.length > 0) {
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

function handleXima(val: any) {
  //
  state.tableList?.map((item: any, index: number) => {
    if (index === val.rowIndex) {
      item.item_fc_data = val.ximaList
      item.total_weight = sumNum(val.ximaList, 'actually_weight')
      item.roll = sumNum(val.ximaList, 'actually_roll')
      return item
    }
  })
  AddInventoryXimaDialogRef.value.state.showModal = false
}

const bulkShow = ref(false)

function bulkHand() {
  bulkShow.value = true
}

function handBulkClose() {
  bulkShow.value = false
}

const bulkSetting = ref<any>({})

const bulkList = reactive<any>([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    component: 'select',
    api: 'GetGreyFabricInfoListUseByOthers',
    labelField: 'code',
    valueField: 'code',
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    component: 'select',
    api: 'GetGreyFabricInfoListUseByOthers',
    labelField: 'name',
    valueField: 'name',
  },
  {
    field: 'supplier_id',
    title: '供方名称',
    component: 'select',
    api: 'BusinessUnitSupplierEnumlist',
    query: { sale_system_id: state.form.marketingSystem },
  },
  {
    field: 'customer_id',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
  },
  {
    field: 'raw_material_yarn_name',
    title: '原料纱名',
    component: 'input',
    type: 'text',
  },
  {
    field: 'raw_material_batch_num',
    title: '原料批号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'raw_material_batch_brand',
    title: '原料品牌',
    component: 'input',
    type: 'text',
  },
  {
    field: 'gray_fabric_color_id',
    title: '织坯颜色',
    component: 'select',
    api: 'getInfoProductGrayFabricColorList',
  },
  {
    field: 'grey_fabric_level_id',
    title: '坯布等级',
    component: 'select',
    api: 'getInfoBaseGreyFabricLevelList',
  },
  {
    field: 'machine_number',
    title: '机台号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'roll',
    title: '实盘匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'grey_fabric_remark',
    title: '坯布备注',
    component: 'input',
    type: 'text',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])

async function bulkSubmit({ row, value }: any, val: any, field: string) {
  state.tableList?.map((item: any) => {
    if (item?.selected && !item.surplus) {
      item[row.field] = value[row.field]
      return item
    }
  })

  if (field === 'grey_fabric_name' || field === 'grey_fabric_code') {
    state.tableList?.map((item: any) => {
      if (item?.selected && !item.surplus) {
        item.grey_fabric_id = val.id
        item.grey_fabric_code = val.code
        item.grey_fabric_name = val.name
        return item
      }
    })
  }

  bulkShow.value = false
  ElMessage.success('设置成功')
}

watch(
  () => state.form.marketingSystem,
  () => {
    state.tableList.map((item: any) => {
      item.supplier_id = ''
      return item
    })
    bulkList[2].query = { sale_system_id: state.form.marketingSystem }
  },
  {
    immediate: true,
  },
)

// 录入细码
function handleSureXima(val: any) {
  state.tableList?.map((item: any, index: number) => {
    if (index === val.rowIndex) {
      item.item_fc_data = val.tableData
      item.total_weight = sumNum(val.tableData, 'weight')
      // 新增该条roll的缘故：【*********】【erp】erp优化点三    《item.roll = sumNum(val.tableData, 'roll')》
      item.roll = sumNum(val.tableData, 'roll')
      return item
    }
  })
  AddXimaDialogRef.value.state.showModal = false
}

// 选择框设置需要的值
function changeValue(val: any, rowIndex: number) {
  state.tableList?.map((item: any, index: number) => {
    if (rowIndex === index) {
      item.grey_fabric_id = val.id
      item.grey_fabric_code = val.code
      item.grey_fabric_name = val.name
      return item
    }
  })
}
function handleChangeOrderType(value: GetGfmCheckOrderTypeEnum | null) {
  if (value) {
    if (value === GetGfmCheckOrderTypeEnum.GfmCheckOrderTypeDyeing)
      AddInventoryDialogRef.value.state.api = GetGfmWarehouseSummaryDyeingListEnum
    else
      AddInventoryDialogRef.value.state.api = getGfmWarehouseSumList
  }
  else {
    AddInventoryDialogRef.value.state.api = getGfmWarehouseSumList
  }
  state.tableList = state.tableList.filter(item => !item.is_stock_source)
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem required label="营销体系:">
          <template #content>
            <el-form-item prop="marketingSystem">
              <SelectComponents v-model="state.form.marketingSystem" api="AdminsaleSystemgetSaleSystemDropdownList" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="盘点单位:">
          <template #content>
            <el-form-item prop="consignee">
              <SelectDialog
                v-model="state.form.consignee"
                :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory, name: componentRemoteSearch.unit_name }"
                api="BusinessUnitSupplierEnumlist"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-input="val => (componentRemoteSearch.unit_name = val)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓管员:">
          <template #content>
            <el-form-item prop="warehouse_keeper">
              <SelectComponents
                v-model="state.form.warehouse_keeper"
                :query="{ duty: EmployeeType.warehouseManager }"
                api="Adminemployeelist"
                label-field="name"
                value-field="id"
                clearable
                @change-value="val => (state.form.store_keeper_name = val.name)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="盘点日期:">
          <template #content>
            <el-form-item prop="date">
              <el-date-picker v-model="state.form.date" type="date" placeholder="盘点日期" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="坯布状态:">
          <template #content>
            <el-form-item prop="order_type">
              <SelectComponents
                v-model="state.form.order_type"
                api="GetGfmCheckOrderTypeEnum"
                label-field="name"
                value-field="id"
                clearable
                @change="handleChangeOrderType"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据备注:" copies="2">
          <template #content>
            <el-form-item prop="remark">
              <el-input v-model="state.form.remark" placeholder="单据备注" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染厂用坯单号:">
          <template #content>
            <el-form-item prop="dye_unit_use_order_no">
              <el-input v-model="state.form.dye_unit_use_order_no" placeholder="染厂用坯单号" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="坯布信息" :tool-bar="false" class="mt-[5px]">
    <template #right-top>
      <el-button type="primary" :disabled="state.multipleSelection?.length <= 0" @click="bulkHand">
        批量操作
      </el-button>
      <el-button type="primary" :disabled="state.form.marketingSystem === '' || state.form.order_type === GetGfmCheckOrderTypeEnum.GfmCheckOrderTypeDyeing" @click="handSurplusAdd">
        盘盈增加
      </el-button>
      <el-button type="primary" :disabled="state.form.marketingSystem === '' || state.form.consignee === '' || !state.form.order_type" @click="handAdd">
        选择坯布库存
      </el-button>
    </template>
    <Table ref="tableRef" :config="tableConfig" :table-list="state.tableList" :column-list="columnList">
      <template #grey_fabric_code="{ row, rowIndex }">
        <div v-if="row.surplus">
          {{ row.grey_fabric_code }}
        </div>
        <SelectGreyFabricDialog
          v-else
          v-model="row.grey_fabric_id"
          field="code"
          :query="{
            code: componentRemoteSearch.grey_fabric_code,
          }"
          @change-value="val => changeValue(val, rowIndex)"
          @on-input="val => componentRemoteSearch.grey_fabric_code = val"
        />
        <!--        <SelectDialog -->
        <!--          v-else -->
        <!--          v-model="row.grey_fabric_id" -->
        <!--          :label-name="row.grey_fabric_code" -->
        <!--          :query="{ code: componentRemoteSearch.grey_fabric_code }" -->
        <!--          api="GetGreyFabricInfoListUseByOthers" -->
        <!--          :column-list="[ -->
        <!--            { -->
        <!--              field: 'name', -->
        <!--              title: '名称', -->
        <!--              minWidth: 100, -->
        <!--              isEdit: true, -->
        <!--              colGroupHeader: true, -->
        <!--              childrenList: [ -->
        <!--                { -->
        <!--                  field: 'name', -->
        <!--                  isEdit: true, -->
        <!--                  title: '名称', -->
        <!--                  minWidth: 100, -->
        <!--                }, -->
        <!--              ], -->
        <!--            }, -->
        <!--            { -->
        <!--              field: 'code', -->
        <!--              title: '编号', -->
        <!--              minWidth: 100, -->
        <!--              isEdit: true, -->
        <!--              colGroupHeader: true, -->
        <!--              childrenList: [ -->
        <!--                { -->
        <!--                  field: 'code', -->
        <!--                  isEdit: true, -->
        <!--                  title: '编号', -->
        <!--                  minWidth: 100, -->
        <!--                }, -->
        <!--              ], -->
        <!--            }, -->
        <!--          ]" -->
        <!--          :table-column="[ -->
        <!--            { -->
        <!--              field: 'code', -->
        <!--              title: '编号', -->
        <!--              minWidth: 100, -->
        <!--            }, -->
        <!--          ]" -->
        <!--          @change-value="val => changeValue(val, rowIndex)" -->
        <!--          @change-input="val => (componentRemoteSearch.grey_fabric_code = val)" -->
        <!--        /> -->
      </template>
      <template #grey_fabric_name="{ row, rowIndex }">
        <div v-if="row.surplus">
          {{ row.grey_fabric_name }}
        </div>
        <SelectGreyFabricDialog
          v-else
          v-model="row.grey_fabric_id"
          field="name"
          :query="{
            name: componentRemoteSearch.grey_fabric_name,
          }"
          @change-value="val => changeValue(val, rowIndex)"
          @on-input="val => componentRemoteSearch.grey_fabric_name = val"
        />
        <!--        <SelectDialog -->
        <!--          v-else -->
        <!--          v-model="row.grey_fabric_id" -->
        <!--          :label-name="row.grey_fabric_name" -->
        <!--          :query="{ name: componentRemoteSearch.grey_fabric_name }" -->
        <!--          api="GetGreyFabricInfoListUseByOthers" -->
        <!--          :column-list="[ -->
        <!--            { -->
        <!--              field: 'name', -->
        <!--              title: '名称', -->
        <!--              minWidth: 100, -->
        <!--              isEdit: true, -->
        <!--              colGroupHeader: true, -->
        <!--              childrenList: [ -->
        <!--                { -->
        <!--                  field: 'name', -->
        <!--                  isEdit: true, -->
        <!--                  title: '名称', -->
        <!--                  minWidth: 100, -->
        <!--                }, -->
        <!--              ], -->
        <!--            }, -->
        <!--            { -->
        <!--              field: 'code', -->
        <!--              title: '编号', -->
        <!--              minWidth: 100, -->
        <!--              isEdit: true, -->
        <!--              colGroupHeader: true, -->
        <!--              childrenList: [ -->
        <!--                { -->
        <!--                  field: 'code', -->
        <!--                  isEdit: true, -->
        <!--                  title: '编号', -->
        <!--                  minWidth: 100, -->
        <!--                }, -->
        <!--              ], -->
        <!--            }, -->
        <!--          ]" -->
        <!--          @change-value="val => changeValue(val, rowIndex)" -->
        <!--          @change-input="val => (componentRemoteSearch.grey_fabric_name = val)" -->
        <!--        /> -->
      </template>
      <template #supplier_id="{ row }">
        <div v-if="row.surplus">
          {{ row.supplier_name }}
        </div>

        <SelectDialog
          v-else
          v-model="row.supplier_id"
          :query="{ sale_system_id: state.form.marketingSystem, name: componentRemoteSearch.name }"
          api="BusinessUnitSupplierEnumlist"
          :column-list="[
            {
              field: 'name',
              title: '名称',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'name',
                  isEdit: true,
                  title: '名称',
                  minWidth: 100,
                },
              ],
            },
            {
              field: 'code',
              title: '供应商编号',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'code',
                  isEdit: true,
                  title: '供应商编号',
                  minWidth: 100,
                },
              ],
            },
          ]"
          @change-input="val => (componentRemoteSearch.name = val)"
        />
      </template>
      <template #customer_id="{ row }">
        <div v-if="row.surplus">
          {{ row.customer_name }}
        </div>
        <SelectCustomerDialog
          v-else
          v-model="row.customer_id"
          field="name"
          :query="{ sale_system_id: state.form.marketingSystem, name: componentRemoteSearch.customer_name }"
          @change-input="val => (componentRemoteSearch.customer_name = val)"
        />
        <!--        <SelectDialog -->
        <!--          v-else -->
        <!--          v-model="row.customer_id" -->
        <!--          api="GetCustomerEnumList" -->
        <!--          :query="{ sale_system_id: state.form.marketingSystem, name: componentRemoteSearch.customer_name }" -->
        <!--          :column-list="[ -->
        <!--            { -->
        <!--              title: '客户编号', -->
        <!--              minWidth: 100, -->
        <!--              required: true, -->
        <!--              colGroupHeader: true, -->
        <!--              childrenList: [ -->
        <!--                { -->
        <!--                  field: 'code', -->
        <!--                  isEdit: true, -->
        <!--                  title: '客户编号', -->
        <!--                  minWidth: 100, -->
        <!--                }, -->
        <!--              ], -->
        <!--            }, -->
        <!--            { -->
        <!--              title: '客户名称', -->
        <!--              minWidth: 100, -->
        <!--              colGroupHeader: true, -->
        <!--              required: true, -->
        <!--              childrenList: [ -->
        <!--                { -->
        <!--                  isEdit: true, -->
        <!--                  field: 'name', -->
        <!--                  title: '客户名称', -->
        <!--                  minWidth: 100, -->
        <!--                }, -->
        <!--              ], -->
        <!--            }, -->
        <!--            { -->
        <!--              title: '电话', -->
        <!--              colGroupHeader: true, -->
        <!--              minWidth: 100, -->
        <!--              childrenList: [ -->
        <!--                { -->
        <!--                  field: 'phone', -->
        <!--                  isEdit: true, -->
        <!--                  title: '电话', -->
        <!--                  minWidth: 100, -->
        <!--                }, -->
        <!--              ], -->
        <!--            }, -->
        <!--            { -->
        <!--              title: '销售员', -->
        <!--              minWidth: 100, -->
        <!--              colGroupHeader: true, -->
        <!--              childrenList: [ -->
        <!--                { -->
        <!--                  field: 'seller_name', -->
        <!--                  title: '销售员', -->
        <!--                  soltName: 'seller_name', -->
        <!--                  isEdit: true, -->
        <!--                  minWidth: 100, -->
        <!--                }, -->
        <!--              ], -->
        <!--            }, -->
        <!--          ]" -->
        <!--          @change-input="val => (componentRemoteSearch.customer_name = val)" -->
        <!--        /> -->
      </template>
      <template #yarn_batch="{ row }">
        <div v-if="row.surplus">
          {{ row.yarn_batch }}
        </div>
        <vxe-input v-else v-model="row.yarn_batch" />
      </template>
      <template #machine_number="{ row }">
        <div v-if="row.surplus">
          {{ row.machine_number }}
        </div>
        <vxe-input v-else v-model="row.machine_number" />
      </template>
      <template #gray_fabric_color_id="{ row }">
        <div v-if="row.surplus">
          {{ row.gray_fabric_color_name }}
        </div>
        <SelectComponents v-else v-model="row.gray_fabric_color_id" api="getInfoProductGrayFabricColorList" label-field="name" value-field="id" clearable />
      </template>
      <template #grey_fabric_level_id="{ row }">
        <div v-if="row.surplus">
          {{ row.grey_fabric_level_name }}
        </div>
        <SelectComponents v-else v-model="row.grey_fabric_level_id" api="getInfoBaseGreyFabricLevelList" label-field="name" value-field="id" clearable />
      </template>
      <template #roll="{ row }">
        <vxe-input v-model="row.roll" :min="0" type="float" :controls="false" />
      </template>
      <template #total_weight="{ row }">
        <vxe-input v-if="state.form.order_type === GetGfmCheckOrderTypeEnum.GfmCheckOrderTypeDyeing" v-model="row.total_weight" :min="0" type="float" :controls="false" />
        <span v-else>{{ row.total_weight }}</span>
      </template>
      <template #inventory_roll="{ row }">
        <div v-if="row.surplus">
          <span :class="getTableCellTextClass(computedTableKey(row, 'inventory_roll'))">
            {{ formatNumberPrefix(computedTableKey(row, 'inventory_roll')) }}
          </span>
        </div>
      </template>
      <template #inventory_weight="{ row }">
        <div v-if="row.surplus">
          <span :class="getTableCellTextClass(computedTableKey(row, 'inventory_weight'))">
            {{ formatNumberPrefix(computedTableKey(row, 'inventory_weight')) }}
          </span>
        </div>
      </template>
      <template #xima="{ row, rowIndex }">
        <div class="flex items-center">
          <el-button type="primary" text link @click="handWrite(row, rowIndex)">
            录入
          </el-button>
          <div v-if="!row.surplus">
            <div v-if="row?.item_fc_data.length || (Number(sumNum(row?.item_fc_data, 'roll')) >= row.roll && row.roll > 0 && row.roll !== '')" class="text-[#b5b39f] ml-[5px]">
              (已录完)
            </div>
            <!-- <div v-if="Number(sumNum(row?.item_fc_data, 'roll')) >= row.roll && row.roll > 0 && row.roll !== ''" class="text-[#b5b39f] ml-[5px]">(已录完)</div> -->
            <div v-else class="text-[#efa6ae] ml-[5px]">
              ({{ (row?.roll - Number(sumNum(row?.item_fc_data, 'roll'))).toFixed(2) }}匹未录)
            </div>
          </div>
          <div v-else>
            <div v-if="row?.item_fc_data.length || (Number(sumNum(row?.item_fc_data, 'actually_roll')) >= row.roll && row.roll > 0 && row.roll !== '')" class="text-[#b5b39f] ml-[5px]">
              (已录完)
            </div>
            <!-- <div v-if="Number(sumNum(row?.item_fc_data, 'actually_roll')) >= row.roll" class="text-[#b5b39f] ml-[5px]">(已录完)</div> -->
            <div v-else class="text-[#efa6ae] ml-[5px]">
              ({{ (row?.roll - Number(sumNum(row?.item_fc_data, 'actually_roll'))).toFixed(2) }}匹未录)
            </div>
          </div>
        </div>
      </template>
      <template #raw_material_yarn_name="{ row }">
        <vxe-input v-if="!row.surplus" v-model="row.raw_material_yarn_name" placeholder="请输入" />
        <span v-else>{{ row.raw_material_yarn_name }}</span>
      </template>
      <template #raw_material_batch_num="{ row }">
        <vxe-input v-if="!row.surplus" v-model="row.raw_material_batch_num" placeholder="请输入" />
        <span v-else>{{ row.raw_material_batch_num }}</span>
      </template>
      <template #raw_material_batch_brand="{ row }">
        <vxe-input v-if="!row.surplus" v-model="row.raw_material_batch_brand" placeholder="请输入" />
        <span v-else>{{ row.raw_material_batch_brand }}</span>
      </template>
      <template #gray_fabric_remark="{ row }">
        <vxe-input v-model="row.grey_fabric_remark" />
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" />
      </template>
      <template #operation="{ rowIndex }">
        <el-button text type="danger" @click="handDelete(rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <AddXimaDialog ref="AddXimaDialogRef" @handle-sure="handleSureXima" />
  <AddInventoryDialog ref="AddInventoryDialogRef" @handle-sure="handleSureFabric" />
  <AddInventoryXimaDialog ref="AddInventoryXimaDialogRef" @handle-sure="handleXima" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
</template>

<style></style>
