<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import { omit } from 'lodash-es'
import { GetStockProductDyelotNumberList } from '@/api/finishedGoodsInventory'
import { debounce, getFilterData } from '@/common/util'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import type { TableColumn } from '@/components/Table/type'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'

export interface Props {
  // eslint-disable-next-line vue/prop-name-casing
  warehouse_id: number | string
  modelValue: boolean
}

const props = withDefaults(defineProps<Props>(), {
  warehouse_id: 0,
  modelValue: false,
})
const emit = defineEmits(['update:modelValue', 'submit'])
const state = reactive({
  filterData: {
    product_code: '',
    product_name: '',
    product_id: '',
    customer_id: '',
    product_color_id: '',
    warehouse_id: '',
    product_level_id: '',
    dyelot_number: '',
  },
  tableList: [],
})

const componentRemoteSearch = ref({
  product_code: '',
  product_name: '',
  color_name: '',
  color_code: '',
})

function changeProductSelect(val: any) {
  state.filterData.product_id = val.id
  state.filterData.product_code = val.finish_product_code
  state.filterData.product_name = val.finish_product_name
  state.filterData.product_color_id = ''

  componentRemoteSearch.value.product_code = val.finish_product_code || ''
  componentRemoteSearch.value.product_name = val.finish_product_name || ''
}

const { fetchData: fetchDataList, data: dataList, total, page, size, loading, handleSizeChange, handleCurrentChange } = GetStockProductDyelotNumberList()
async function getData() {
  await fetchDataList(getFilterData({
    ...omit(state.filterData, ['product_code', 'product_name']),
    warehouse_id: Number.parseInt(props.warehouse_id as unknown as string),
  }))
}

const showModal = ref<boolean>(false)
watch(
  () => [props.modelValue, props.warehouse_id],
  () => {
    showModal.value = props.modelValue
    if (showModal.value && props.warehouse_id)
      getData()

    if (!showModal.value) {
      state.filterData = {
        product_code: '',
        product_name: '',
        product_id: '',
        customer_id: '',
        product_color_id: '',
        warehouse_id: '',
        product_level_id: '',
        dyelot_number: '',
      }
    }
  },
)

const columnList = ref<TableColumn[]>([
  {
    title: '成品编号',
    field: 'product_code',
    fixed: 'left',
    minWidth: 100,
  },
  {
    title: '成品名称',
    field: 'product_name',
    fixed: 'left',
    minWidth: 100,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    minWidth: 100,
  },
  {
    field: 'product_color_code',
    title: '色号',
    minWidth: 100,
  },
  {
    field: 'product_color_name',
    title: '颜色',
    minWidth: 100,
  },
  {
    field: 'warehouse_name',
    title: '所属仓库',
    minWidth: 100,
  },
  {
    field: 'dyelot_number',
    title: '染厂缸号',
    width: 100,
  },

  {
    field: 'finish_product_craft',
    title: '成品工艺',
    width: 100,
  },
  {
    field: 'product_level_name',
    title: '成品等级',
    width: 100,
  },
  {
    field: 'finish_product_ingredient',
    title: '成品成分',
    width: 100,
  },
  {
    field: 'product_remark',
    title: '成品备注',
    width: 100,
  },
  // {
  //   field: 'unit_price',
  //   title: '数量单价',
  //   width: 100,
  //   isUnitPrice: true,
  // },
  // {
  //   field: 'length_unit_price',
  //   title: '辅助数量单价',
  //   width: 100,
  //   isUnitPrice: true,
  // },
  {
    field: 'roll',
    title: '库存匹数',
    width: 100,
    isPrice: true,
  },
  {
    field: 'weight',
    title: '数量总计',
    width: 100,
    isWeight: true,
  },
  {
    field: 'measurement_unit_name',
    title: '单位',
    width: 70,
  },
  // {
  //   field: 'auxiliary_unit_name',
  //   title: '结算单位',
  //   width: 100,
  // },
  {
    field: 'length',
    title: '辅助数量总计',
    width: 100,
    isLength: true,
  },
  {
    field: 'available_roll',
    title: '可用库存',
    width: 100,
    isPrice: true,
  },
])

watch(
  () => state.filterData,
  debounce(() => {
    if (showModal.value)
      getData()
  }, 400),
  { deep: true },
)

const selectList = ref<any[]>()
function submit() {
  emit('submit', selectList.value)
}

function onClose() {
  emit('update:modelValue', false)
}

function handAllSelect({ records }: any) {
  selectList.value = records
}

function handleSelectionChange({ records }: any) {
  selectList.value = records
}

const tableConfig = ref({
  showCheckBox: true,
  handAllSelect,
  handleSelectionChange,
  showPagition: true,
  loading,
  size,
  height: '100%',
  page,
  total,
  checkRowKeys: [] as number[],
  handleSizeChange,
  handleCurrentChange,
  checkboxConfig: {
    checkMethod: ({ row }: any) => {
      return row.available_roll > 0 || row.weight > 0
    },
  },
})

const customerRef1 = ref()
const customerRef2 = ref()
function changeColor(stringRef: string, val: any) {
  if (stringRef === 'customerRef1')
    customerRef2.value.inputLabel = customerRef1.value.item?.product_color_name
  else
    customerRef1.value.inputLabel = customerRef2.value.item?.product_color_code

  componentRemoteSearch.value.color_code = val.product_color_code || ''
  componentRemoteSearch.value.color_name = val.product_color_name || ''
}
</script>

<template>
  <vxe-modal v-model="showModal" show-footer title="根据库存添加" width="80vw" height="80vh" :mask="false" :lock-view="false" :esc-closable="true" resize @close="onClose">
    <div class="flex flex-col overflow-hidden h-full">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="成品编号:">
          <template #content>
            <SelectProductDialog
              v-model="state.filterData.product_id"
              :label-name="state.filterData.product_code"
              field="finish_product_code"
              :query="{
                finish_product_code: componentRemoteSearch.product_code,
              }"
              @on-input="(inputValue) => (componentRemoteSearch.product_code = inputValue)"
              @change-value="changeProductSelect"
            />
            <!--            <SelectDialog -->
            <!--              v-model="state.filterData.product_id" -->
            <!--              label-field="finish_product_code" -->
            <!--              :label-name="state.filterData.product_code" -->
            <!--              :query="{ -->
            <!--                finish_product_code: componentRemoteSearch.product_code, -->
            <!--              }" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品名称', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      title: '成品名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品编号', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      title: '成品编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '成品编号', -->
            <!--                }, -->
            <!--              ]" -->
            <!--              @on-input="(inputValue) => (componentRemoteSearch.product_code = inputValue)" -->
            <!--              @change-value="changeProductSelect" -->
            <!--            /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称:">
          <template #content>
            <SelectProductDialog
              v-model="state.filterData.product_id"
              :label-name="state.filterData.product_name"
              field="finish_product_name"
              :query="{
                finish_product_name: componentRemoteSearch.product_name,
              }"
              @on-input="(inputValue) => (componentRemoteSearch.product_name = inputValue)"
              @change-value="changeProductSelect"
            />
            <!--            <SelectDialog -->
            <!--              v-model="state.filterData.product_id" -->
            <!--              label-field="finish_product_name" -->
            <!--              :label-name="state.filterData.product_name" -->
            <!--              :query="{ -->
            <!--                finish_product_name: componentRemoteSearch.product_name, -->
            <!--              }" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品名称', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      title: '成品名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品编号', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      title: '成品编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  title: '成品名称', -->
            <!--                }, -->
            <!--              ]" -->
            <!--              @on-input="(inputValue) => (componentRemoteSearch.product_name = inputValue)" -->
            <!--              @change-value="changeProductSelect" -->
            <!--            /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="所属客户:">
          <template #content>
            <SelectDialog v-model="state.filterData.customer_id" api="GetCustomerEnumList" />
            <!-- <SelectComponents api="GetCustomerEnumList" label-field="name" value-field="id" v-model="state.filterData.customer_id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="色号:">
          <template #content>
            <SelectDialog
              ref="customerRef1"
              key="color2"
              v-model="state.filterData.product_color_id"
              :disabled="!state.filterData.product_id"
              :query="{
                finish_product_id: state.filterData.product_id,
                product_color_code: componentRemoteSearch.color_code,
              }"
              :column-list="[
                {
                  field: 'product_color_code',
                  title: '色号',
                  minWidth: 100,
                },
                {
                  field: 'product_color_name',
                  title: '颜色',
                  minWidth: 100,
                },
              ]"
              :table-column="[
                {
                  field: 'product_color_code',
                  title: '色号',
                },
              ]"
              api="GetFinishProductColorDropdownList"
              label-field="product_color_code"
              @on-input="(inputValue) => (componentRemoteSearch.color_code = inputValue)"
              @change-value="(val) => changeColor('customerRef1', val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="颜色:">
          <template #content>
            <SelectDialog
              key="color1"
              ref="customerRef2"
              v-model="state.filterData.product_color_id"
              :disabled="!state.filterData.product_id"
              :query="{
                finish_product_id: state.filterData.product_id,
                product_color_name: componentRemoteSearch.color_name,
              }"
              :column-list="[
                {
                  field: 'product_color_code',
                  title: '色号',
                  minWidth: 100,
                },
                {
                  field: 'product_color_name',
                  title: '颜色',
                  minWidth: 100,
                },
              ]"
              :table-column="[
                {
                  field: 'product_color_name',
                  title: '颜色',
                },
              ]"
              api="GetFinishProductColorDropdownList"
              label-field="product_color_name"
              @on-input="(inputValue) => (componentRemoteSearch.color_name = inputValue)"
              @change-value="(val) => changeColor('customerRef2', val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="所属仓库:">
          <template #content>
            <SelectComponents v-model="state.filterData.warehouse_id" api="GetPhysicalWarehouseDropdownList" warehouse_type_id="finishProduction" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品等级:">
          <template #content>
            <SelectComponents v-model="state.filterData.product_level_id" api="GetInfoBaseFinishedProductLevelEnumList" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染厂缸号:">
          <template #content>
            <el-input v-model="state.filterData.dyelot_number" placeholder="请输入染厂缸号" />
          </template>
        </DescriptionsFormItem>
      </div>
      <Table :config="tableConfig" :table-list="dataList.list" :column-list="columnList">
        <template #creatTime />
      </Table>
    </div>

    <template #footer>
      <el-button type="primary" size="small" @click="submit">
        提交
      </el-button>
    </template>
  </vxe-modal>
</template>
