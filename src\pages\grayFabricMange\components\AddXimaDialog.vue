<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { computed, nextTick, reactive, ref, watch } from 'vue'
import currency from 'currency.js'
import { VxeInput } from 'vxe-pc-ui'
import { uniqueId } from 'xe-utils'
import Table from '@/components/Table.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { sumNum, sumTotal } from '@/common/format'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import useTableEnterAutoFocus from '@/use/useTableEnterAutoFocus'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { WarehouseTypeIdEnum } from '@/common/enum'
import { INIT_DATA } from '@/components/Table/constant'
import { SourceWarehouseTypeEnum } from '@/enum/grayFabricEnum'

const emits = defineEmits(['handleSure'])
const state = reactive<any>({
  showModal: false,
  modalName: '细码录入',
  code: '',
  name: '',
  type: 0 as SourceWarehouseTypeEnum,
  horsepower: 0,
  reference_weight: 0,
  tableData: [],
  canEnter: 0, // 可以录入的匹数
  total_roll: 0, // 录入的全部匹数
  id: -1,
  isDisabled: false,
  isAdd: false, // 是否新增细码录入
  rowIndex: -1,
  receivedRoll: 0, // 收货匹数
  totalWeight: 0, // 总数量
  item_source: 1, // 1-资料中添加；2-坯布采购单中添加
})
const addLoading = ref(false)
const tableConfig = computed(() => ({
  footerMethod: (val: any) => FooterMethod(val),
  showSlotNums: true,
  showOperate: !state.isDisabled,
  operateWidth: '100',
  scrollY: { enabled: false },
  height: '100%',
  isEdit: true,
  loading: addLoading.value,
}))
const bulkShow = ref(false)
const tableRef = ref()
// 根据分页信息和数据映射出需显示的数据
const displayedData = ref([])
const { addOrNextLineFocus } = useTableEnterAutoFocus(
  tableRef,
  displayedData,
  handAdd,
)

const bulkSetting = ref<any>({})
const bulkList: any = ref([
  {
    field: 'warehouse_bin_id',
    title: '仓位',
    component: 'select',
    api: 'GetDictionaryDetailEnumListApi',
    query: { dictionary_id: WarehouseTypeIdEnum.commonWarehouseBin },
    minWidth: 100,
  },
  {
    field: 'volume_number',
    title: '卷号',
    component: 'input',
    minWidth: 100,
  },
  //   {
  //     field: '',
  //     title: '条码',
  //     minWidth: 100,
  //     soltName: 'barCode',
  //   },
  {
    field: 'roll',
    title: '匹数',
    minWidth: 100,
    component: 'input',
    required: true,
    // isPrice: state.isDisabled,
  },
  {
    field: 'weight',
    title: '数量',
    minWidth: 100,
    component: 'input',
    required: true,
  },
])
const columnList = computed(() => [
  {
    field: 'position',
    title: '仓位',
    minWidth: 150,
    soltName: 'position',
  },
  {
    field: 'volume_number',
    title: '卷号',
    minWidth: 150,
    soltName: 'volume_number',
  },
  {
    field: 'before_check_roll',
    title: '盘前匹数',
    minWidth: 150,
    show: state.type === SourceWarehouseTypeEnum.Check,
  },
  {
    field: 'before_check_weight',
    title: '盘前数量',
    minWidth: 150,
    show: state.type === SourceWarehouseTypeEnum.Check,
  },
  {
    field: 'after_check_roll',
    title: '实盘匹数',
    minWidth: 150,
    show: state.type === SourceWarehouseTypeEnum.Check,
  },
  {
    field: 'after_check_weight',
    title: '实盘数量',
    minWidth: 150,
    show: state.type === SourceWarehouseTypeEnum.Check,
  },
  {
    field: 'roll',
    title: '匹数',
    minWidth: 150,
    soltName: 'roll',
  },
  {
    field: 'weight',
    title: '数量',
    minWidth: 180,
    soltName: 'weight',
  },
  {
    show: !state.isAdd,
    field: 'fabric_piece_code',
    title: '坯布条码',
    minWidth: 180,
  },
])

function handCancel() {
  state.showModal = false
}
function handBulkClose() {
  bulkShow.value = false
  bulkSetting.value.customer_id = ''
}
async function handleSure() {
  // 注释缘故:【*********】【erp】erp优化点三
  // if (state.total_roll > state.canEnter)
  //   return ElMessage.error('匹数不可大于原库存匹数')

  for (let i = 0; i < state.tableData.length; i++) {
    if (state.tableData[i].roll === '')
      return ElMessage.error('匹数和数量是必填内容')

    if (state.tableData[i].weight === '' || Number.parseFloat(state.tableData[i].weight) === 0)
      return ElMessage.error('数量不能为空和不能为0')

    state.tableData[i].roll = Number(state.tableData[i].roll)
    state.tableData[i].weight = Number(state.tableData[i].weight)
    state.tableData[i].volume_number = `${state.tableData[i].volume_number}`
  }
  emits('handleSure', state)
}

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property)) {
        state.total_roll = sumTotal(data, 'roll')
        // if (!state.isDisabled) {
        return `${sumNum(data, 'roll')}`
        // } else {
        //   return `${formatPriceDiv(sumNum(data, 'roll') as any)}`
        // }
      }
      if (['weight'].includes(column.property)) {
        if (!state.isDisabled)
          return `${sumNum(data, 'weight')}`
        else
          return `${(sumNum(data, 'weight') as any)}`
      }
      return null
    }),
  ]
}

watch(
  () => state.tableData,
  () => {
    // resetPager(state.tableData.length)
    // computeDisplayedData()
    if (state.tableData.length > 0) {
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

watch(
  () => state.showModal,
  (newVal) => {
    if (newVal) {
      resetPager(state.tableData.length)
      computeDisplayedData()
    }
  },
)

// 新增细码录入
function handAdd() {
  // 找出所有卷号中的最大值
  let maxVolumeNumber = 0
  state.tableData.forEach((item) => {
    const currentVolumeNumber = Number(item.volume_number) || 0
    if (currentVolumeNumber > maxVolumeNumber)
      maxVolumeNumber = currentVolumeNumber
  })
  // if (state.total_roll >= state.canEnter) {
  //   ElMessage.error('匹数不可大于原库存匹数')
  //   return false
  // }

  state.tableData.push({
    roll: 1,
    position: state.tableData[state.tableData.length - 1]?.position || '',
    warehouse_bin_id: state.tableData[state.tableData.length - 1]?.warehouse_bin_id || '',
    volume_number: maxVolumeNumber + 1, // 在最大卷号上加1
    weight: state?.weightOfFabric || 0,
    uniq_id: getUniqueId(),
  })
  state.tableData = [...state.tableData]
  nextTick(() => {
    resetPager(state.tableData.length)
    computeDisplayedData()
  })
  return true
}

const pager = ref({
  currentPage: 1,
  pageSize: INIT_DATA.defaultPageSize,
  total: 0,
})

function resetPager(total = 1, currentPage = 1) {
  pager.value.currentPage = currentPage
  pager.value.total = total
}

function computeDisplayedData() {
  const { currentPage, pageSize } = pager.value
  const start = (currentPage - 1) * pageSize
  const end = currentPage * pageSize

  displayedData.value = state.tableData.slice(start, end)
  tableRef.value?.tableRef?.loadData()
}

function handlePageChange(val: number) {
  pager.value.currentPage = val
  computeDisplayedData()
}

function handleSizeChange(pageSize: number) {
  pager.value.pageSize = pageSize
  computeDisplayedData()
}

function getUniqueId() {
  return uniqueId('uniq_id')
}
/**
 * 根据匹数和数量生成细码数据
 * 如果匹数为0则不生成
 * 如果匹数不为0，则按照匹数生成细码数据，每条都为1匹，如果已经自己加过几行细码，则按总匹数减已添加过的细码匹数生成，数量按照总数量除以要生成的行数生成
 *
 */
function generateFineCode() {
  // 如果输入的生成匹数为0，则不生成
  if (state.canEnter === 0)
    return ElMessage.error('请输入匹数')

  // 如果只有一条初始化的数据，则删除这条数据
  if (state.tableData.length === 1 && state.tableData[0].roll === 0 && Number(state.tableData[0].weight) === Number(state.totalWeight))
    state.tableData = []

  // 获取已有的细码数据的总匹数和总数量
  let totalRoll = 0
  let totalWeight = 0
  state.tableData.forEach((item: any) => {
    totalRoll += Number(item.roll)
    totalWeight += Number(item.weight)
  })
  // 如果可生成的总匹数小于等于0
  if (Number(totalRoll) === Number(state.canEnter))
    return ElMessage.error('无剩余可用匹数')

  // 计算可生成的匹数和每行的数量
  const canGenerateRoll = state.canEnter - totalRoll
  let averageWeight = currency(state.totalWeight).subtract(totalWeight).divide(canGenerateRoll).value
  // 如果每行数量等于0，则取定重(从资料中添加)
  if (state.item_source === 1 && averageWeight <= 0)
    averageWeight = state.weightOfFabric

  averageWeight = averageWeight < 0 ? 0 : averageWeight

  // 获取卷号的起始值，如果tableData最后一位的卷号有值则取这个值加1.如果没有值则取1,如果有仓位也取这个仓位
  let volumeNumber = 1
  let position = ''
  let warehouse_bin_id = 0
  if (state.tableData.length > 0) {
    volumeNumber = Number(state.tableData[state.tableData.length - 1].volume_number) + 1
    position = state.tableData[state.tableData.length - 1].position
    warehouse_bin_id = state.tableData[state.tableData.length - 1].warehouse_bin_id
  }

  // 增加细码数据
  addLoading.value = true
  setTimeout(() => {
    for (let i = 0; i < canGenerateRoll; i++) {
      state.tableData.push({
        position,
        warehouse_bin_id,
        volume_number: volumeNumber++,
        roll: 1,
        weight: averageWeight,
        uniq_id: getUniqueId(),
      })
    }
    nextTick(() => {
      resetPager(state.tableData.length)
      computeDisplayedData()
      addLoading.value = false
    })
  }, 0)
}

function handDelete(rowIndex: number) {
  const deleItem: any = displayedData.value[rowIndex]
  let deleteIndex = -1
  if (deleItem.id)
    deleteIndex = state.tableData.findIndex((item: any) => item.id === deleItem.id)

  else if (deleItem.uniq_id)
    deleteIndex = state.tableData.findIndex((item: any) => item.uniq_id === deleItem.uniq_id)

  if (deleteIndex === -1)
    return ElMessage.error('删除失败')

  state.tableData.splice(deleteIndex, 1)
  resetPager(state.tableData.length, pager.value.currentPage)
  computeDisplayedData()
}

/**
 * 批量编辑
 */
function bulkEdit() {
  // toggleBulkEditModal(true)
  bulkShow.value = true
}
async function bulkSubmit({ row, value, quickInputResult }: any) {
  if (state.multipleSelection?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  displayedData.value?.map((item: any, index: number) => {
    if (row.quickInput && quickInputResult?.[index]) {
      item[row.field] = quickInputResult[index]
      return item
    }
    item[row.field] = value[row.field]
    item[row.field_name] = value[row.field_name]
  })
  bulkShow.value = false
  ElMessage.success('设置成功')
}
defineExpose({
  state,
  resetPager,
  getUniqueId,
  computeDisplayedData,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" :esc-closable="true" :lock-view="false" :mask="false" :title="state.modalName" height="80vh" resize show-footer width="80vw">
    <div class="list-page">
      <div :style="{ '--minLabelWidth': '74px' }" class="descriptions_row">
        <DescriptionsFormItem label="坯布编号:">
          <template #content>
            {{ state.code }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布名称:">
          <template #content>
            {{ state.name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="匹数:">
          <template #content>
            {{ state?.horsepower }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="采购数量:">
          <template #content>
            {{ state?.reference_weight }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="定重:">
          <template #content>
            {{ state?.weightOfFabric }}
          </template>
        </DescriptionsFormItem>
      </div>
      <div v-if="!state.isDisabled" class="buttom-oper" style="margin-bottom: 20px">
        <el-button type="primary" @click="generateFineCode">
          生成细码
        </el-button>
        <el-button type="primary" @click="handAdd">
          新增
        </el-button>
        <el-button type="primary" @click="bulkEdit">
          批量编辑
        </el-button>
      </div>
      <div class="table-card-full">
        <Table ref="tableRef" :column-list="columnList" :config="tableConfig" :table-list="displayedData">
          <template #position="{ row }">
            <div v-if="state.isDisabled">
              {{ row?.position }}
            </div>
            <SelectComponents
              v-else
              v-model="row.warehouse_bin_id"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              :query="{ dictionary_id: WarehouseTypeIdEnum.commonWarehouseBin }"
              clearable
            />
          </template>
          <template #volume_number="{ row, rowIndex }">
            <VxeInput v-if="!state.isDisabled" :id="`volume_number${rowIndex}`" v-model="row.volume_number" @keydown="addOrNextLineFocus(rowIndex, $event, 'volume_number')" />
            <div v-else>
              {{ row?.volume_number }}
            </div>
          </template>
          <template #roll="{ row, rowIndex }">
            <VxeInput
              v-if="!state.isDisabled"
              :id="`roll${rowIndex}`"
              v-model="row.roll"
              placeholder="必填"
              type="float" @keydown="addOrNextLineFocus(rowIndex, $event, 'roll')"
            />
            <div v-else>
              {{ row?.roll }}
            </div>
          </template>
          <template #weight="{ row, rowIndex }">
            <VxeInput
              v-if="!state.isDisabled" :id="`weight${rowIndex}`" v-model="row.weight" :min="0" placeholder="必填"
              type="float" @keydown="addOrNextLineFocus(rowIndex, $event, 'weight')"
            >
              <template #suffix />
            </VxeInput>
            <div v-else>
              {{ row?.weight }}
            </div>
          </template>
          <template v-if="!state.isDisabled" #operate="{ rowIndex }">
            <el-button text type="danger" @click="handDelete(rowIndex)">
              删除
            </el-button>
          </template>
        </Table>
      </div>
    </div>

    <template #footer>
      <div>
        <div class="flex justify-end">
          <el-pagination
            v-model:current-page="pager.currentPage"
            v-model:page-size="pager.pageSize"
            :page-sizes="INIT_DATA.pageSizes"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pager.total"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
        <div v-if="!state.isDisabled" class="buttom-oper" style="margin-top: 20px">
          <el-button type="primary" @click="handCancel">
            取消
          </el-button>
          <el-button type="primary" @click="handleSure">
            确认
          </el-button>
        </div>
      </div>
    </template>
  </vxe-modal>

  <!-- 批量编辑 -->
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
</template>

<style></style>
