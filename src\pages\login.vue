<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { AdminLogin, Admininformation } from '@/api/login'
import { IMG_CND_Prefix } from '@/common/constant'
import { useUserStore } from '@/stores/user'
import { formatUrl, jointUrl } from '@/common/util'
import CustomRequest from '@/components/CustomRequest/index.vue'

const img = jointUrl(import.meta.env.VITE_APP_LOGIN_PNG1)
const title = import.meta.env.VITE_APP_LOGIN_TITLE

const subTitle = import.meta.env.VITE_APP_LOGIN_TITLE_ENG
const TOS = import.meta.env.VITE_APP_TOS
const PrivacyPolicy = import.meta.env.VITE_APP_PRIVACY_POLICY
const logoImage = jointUrl(import.meta.env.VITE_APP_LOGO_WITH_NAME)

function checkPhone(rule: any, value: any, callback: any) {
  const reg = /^1[3,4,5,6,7,8,9][0-9]{9}$/

  if (reg.test(state.form.phone))
    callback()
  else
    callback(new Error('请输入正确的手机号码'))
}
const centerDialogVisible = ref(false)

function handleRegister() {
  centerDialogVisible.value = true
}

function handleGoBack() {
  centerDialogVisible.value = false
}

const state = reactive({
  showScan: false,
  curren: 0,
  form: {
    phone: '',
    code: '',
    password: '',
  },
  rules: {
    phone: [
      { required: true, message: '请输入正确的手机号码', trigger: 'blur' },
      { require: true, validator: checkPhone, trigger: 'blur' },
    ],
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
    code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
  },
  respect: true,
})

onMounted(() => {})
function handChose(val: number) {
  if (val === 1)
    state.curren = 0
  else
    state.curren = 1
}

const formRef = ref()

const router = useRouter()

const dialogTableVisible = ref(false)

const { fetchData: loginFetch, data: loginData, success: loginSuccess, msg: loginMsg, loading } = AdminLogin()

const { fetchData: getFetch, data: getData } = Admininformation()

const { setUser, setToken } = useUserStore()

function handSubmit() {
  if (!state.respect)
    return ElMessage.error('请勾选阅读协议')

  if (!formRef.value)
    return
  formRef.value.validate(async (valid: any) => {
    if (valid) {
      await loginFetch({ phone: state.form.phone.toString(), password: state.form.password.toString() })

      if (loginSuccess.value) {
        setToken(loginData.value.token)
        await getFetch()
        ElMessage.success('登录成功')

        setUser({ ...loginData.value, ...getData.value })
        const { redirect } = router.currentRoute.value.query

        if (redirect)
          router.push({ path: redirect as string, replace: true }) //  跳转到原页面
        else
          router.push({ name: 'Home', replace: true }) // 跳转到控制台页面
      }
      else {
        ElMessage.error(loginMsg.value)
      }
    }
    else {
      return false
    }
    // router.push({ name: 'Home', replace: true })
  })
}
</script>

<template>
  <div class="main">
    <el-image class="absolute top-[23px] left-[32px] h-[60px] imgStyle" fit="fill" :src="logoImage" />
    <!--    <div class="absolute top-[19px] left-[80px] fontStyle">浩川ERP</div> -->
    <div class="continur">
      <!-- <el-image class="side" :src="`${IMG_CND_Prefix}/erp/side_bg(2).png`"></el-image> -->
      <div class="centet_box">
        <div class="left_side">
          <div class="side_font">
            {{ title }}
          </div>
          <div class="english_font">
            {{ subTitle }}
          </div>
          <el-image class="w-[262px] h-[235px]" :src="img" />
        </div>
        <el-image
          v-if="state.showScan"
          class="computer"
          :src="`${IMG_CND_Prefix}/erp/computer_bg(1).png`"
          @click="
            () => {
              state.showScan = false
            }
          "
        />
        <el-image
          v-else
          class="scan"
          :src="`${IMG_CND_Prefix}/erp/scan.png`"
          @click="
            () => {
              state.showScan = true
            }
          "
        />
        <div v-if="!state.showScan">
          <div class="flex_chose">
            <div :class="state.curren == 0 ? 'code_font_active' : 'code_font'" @click="handChose(1)">
              密码登录
            </div>
            <div class="shu" />
            <div :class="state.curren == 1 ? 'code_font_active' : 'code_font'" @click="handChose(2)">
              验证码登录
            </div>
          </div>
          <div class="ml-[54px] mt-[37px]">
            <el-form ref="formRef" label-position="top" :rules="state.rules" label-width="100px" :model="state.form" style="max-width: 460px">
              <el-form-item label="手机号码" prop="phone">
                <div class="phone_box">
                  <div class="phone_ast">
                    +86
                    <el-icon><ArrowDown /></el-icon>
                  </div>
                  <el-input v-model.number="state.form.phone" maxlength="11" placeholder="请输入手机号码" @keyup.enter="handSubmit()" />
                </div>
              </el-form-item>
              <el-form-item v-if="state.curren === 1" label="手机验证码" prop="code">
                <div class="phone_box">
                  <el-input v-model.number="state.form.code" maxlength="6" placeholder="请输入验证码" @keyup.enter="handSubmit()" />
                  <div class="code_font">
                    获取验证码
                  </div>
                </div>
              </el-form-item>
              <el-form-item v-else label="密码" prop="password">
                <div class="phone_box">
                  <el-input v-model="state.form.password" show-password placeholder="请输入密码" @keyup.enter="handSubmit()" />
                </div>
              </el-form-item>
            </el-form>
            <div class="flex justify-between items-center">
              <div class="respect_box">
                <el-checkbox v-model="state.respect" style="margin-right: 10px" size="large" />
                <div class="argree_font">
                  阅读并同意
                  <el-link type="primary" :underline="false" :href="TOS" target="_blank">
                    《服务条款》
                  </el-link>
                  和
                  <el-link type="primary" :underline="false" :href="PrivacyPolicy" target="_blank">
                    《隐私条款》
                  </el-link>
                </div>
              </div>
              <div>
                <el-link :underline="false" type="primary" @click="handleRegister">
                  注册
                </el-link>
              </div>
            </div>
            <div v-btnAntiShake="handSubmit" class="buttom">
              <el-icon v-show="loading" class="is-loading mr-2">
                <Loading />
              </el-icon>
              登录
            </div>
          </div>
        </div>
        <div v-else>
          <div class="scan_font">
            二维码登陆
          </div>
          <div class="wechact_box">
            <el-image class="scan_img" src="https://img0.baidu.com/it/u=304503446,1584310435&fm=253&fmt=auto&app=138&f=GIF?w=500&h=508" />
            <div class="wechat_tips_box">
              <el-image class="w-[50px] h-[50px]" :src="`${IMG_CND_Prefix}/erp/wechat(2).png`" />
              <div class="login_font">
                微信扫一扫登陆
              </div>
            </div>
          </div>
          <div class="argree_box">
            <el-checkbox v-model="state.respect" style="margin-right: 10px" size="large" />
            <div class="argree_font">
              阅读并同意
              <el-link type="primary" :underline="false" :href="TOS" target="_blank">
                《服务条款》
              </el-link>
              和
              <el-link type="primary" :underline="false" :href="TOS" target="_blank">
                《隐私条款》
              </el-link>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="centerDialogVisible"
      class="mask fixed bg-slate-600 w-full h-full z-50 transition-opacity ease-in-out delay-150 duration-[1000]" :class="[centerDialogVisible ? 'opacity-[0.7]' : ' opacity-[0]']"
      @click="handleGoBack"
    />
    <div
      v-if="centerDialogVisible"
      class="fixed bg-white transition-opacity ease-in-out delay-150 duration-[1000] overflow-hidden border border-gray-500 z-[51] rounded-xl border-solid w-[1000px] h-[541px] left-[50%] top-[50%] register" :class="[
        centerDialogVisible ? 'opacity-[1]' : ' opacity-[0]',
      ]"
    >
      <div class="flex flex-row">
        <div>
          <img :src="formatUrl('/product/d878e61b9571fb526f1139b9e9f9ee6920114929.png')">
        </div>
        <div class="flex-1 p-10 pt-6">
          <div class="flex justify-end cursor-pointer" @click="handleGoBack">
            {{ `< 返回` }}
          </div>
          <div class="font-bold text-2xl text-center text-[#333] mt-6">
            注册体验
          </div>
          <div class="p-8 mt-10">
            <div class="flex flex-row">
              <img class="w-[150px] h-[150px] mr-2 overflow-hidden rounded-t-2xl" :src="formatUrl('/product/a50a967c3491ebfdc35f2a0fe33df05620114929.png')">
              <img class="w-[150px] h-[150px]" :src="formatUrl('/product/640b7c36e38b124a817a74958d251d2420120518.jpg')">
            </div>
            <div class="text-center mt-6">
              微信扫一扫注册登录
            </div>
          </div>

          <div class="mt-10 text-center">
            或致电咨询：136 3017 0313
          </div>
        </div>
      </div>
    </div>
  </div>
  <CustomRequest />
</template>

<!-- /erp/computer_bg(1).png -->
<style lang="scss" scoped>
.main {
  height: 100vh;
  width: 100vw;
  position: relative;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  background: #ffffff;
  .fontStyle {
    font-size: 26px;
    font-weight: 500;
    color: #000000;
  }
  .imgStyle {
    position: absolute;
  }
  .continur {
    background-size: 100% auto;
    background-repeat: repeat-x;
    background-image: url('https://cdn.zzfzyc.com/erp/side_bg(2).png');
    // width: calc(100vw - 235px);
    width: 1450px;
    height: 630px;
    overflow: hidden;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    display: flex;
    .centet_box {
      display: flex;
      width: 800px;
      height: 540px;
      position: relative;
      padding: 10px;
      box-shadow: 0px 0px 11px 0px rgba(8, 8, 8, 0.13);
      border-radius: 16px;
      .left_side {
        width: 316px;
        height: 520px;
        background: #016aff;
        box-shadow: 0px 0px 11px 0px rgba(8, 8, 8, 0.13);
        border-radius: 16px;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        display: flex;
        .side_font {
          font-size: 30px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 38px;
          text-shadow: 0px 0px 11px rgba(8, 8, 8, 0.13);
        }
        .english_font {
          margin-bottom: 50px;
          font-size: 20px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #c0daff;
          text-shadow: 0px 0px 11px rgba(8, 8, 8, 0.13);
        }
      }
      .computer {
        position: absolute;
        top: -16px;
        right: -16px;
        cursor: pointer;
      }
      .scan {
        position: absolute;
        top: -17px;
        width: 150px;
        height: 120px;
        right: -16px;
        cursor: pointer;
      }
      .flex_chose {
        margin-top: 101px;
        margin-left: 54px;
        display: flex;
        align-items: center;
        .shu {
          margin-left: 16px;
          margin-right: 16px;
          width: 1px;
          height: 24px;
          background: #d8d8d8;
          box-shadow: 0px 0px 11px 0px rgba(8, 8, 8, 0.13);
        }
        .code_font {
          font-size: 18px;
          font-weight: 500;
          color: #ababab;
          line-height: 26px;
          text-shadow: 0px 0px 11px rgba(8, 8, 8, 0.13);
          cursor: pointer;
        }
        .code_font_active {
          font-size: 24px;
          font-weight: 600;
          color: #000000;
          line-height: 32px;
          text-shadow: 0px 0px 11px rgba(8, 8, 8, 0.13);
        }
      }
      .phone_box {
        width: 349px;
        display: flex;
        align-items: center;
        padding-bottom: 14px;
        border-bottom: 1px solid #e1e1e1;
        .phone_ast {
          display: flex;
          align-items: center;
          margin-right: 20px;
          font-size: 14px;
          font-weight: 600;
          color: #000000;
          cursor: pointer;
          text-shadow: 0px 0px 11px rgba(8, 8, 8, 0.13);
        }
        .code_font {
          min-width: 90px;
          font-size: 14px;
          font-weight: 500;
          color: #016aff;
          cursor: pointer;
          text-shadow: 0px 0px 11px rgba(8, 8, 8, 0.13);
        }
      }
      .respect_box {
        display: flex;
        align-items: center;
        .argree_font {
          font-size: 12px;
          font-weight: 400;
          color: #b4b4b4;
          text-shadow: 0px 0px 11px rgba(8, 8, 8, 0.13);
        }
      }
      .buttom {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 349px;
        height: 48px;
        background: #016aff;
        box-shadow: 0px 0px 11px 0px rgba(8, 8, 8, 0.13);
        border-radius: 8px;
        font-size: 16px;
        font-weight: 500;
        color: #ffffff;
        line-height: 48px;
        text-align: center;
        text-shadow: 0px 0px 11px rgba(8, 8, 8, 0.13);
        cursor: pointer;
      }
    }
    .scan_font {
      font-size: 24px;
      font-weight: 600;
      color: #000000;
      text-shadow: 0px 0px 11px rgba(8, 8, 8, 0.13);
      margin-top: 98px;
      margin-left: 54px;
    }
    .wechact_box {
      width: 234px;
      height: 270px;
      box-shadow: 0px 0px 11px 0px rgba(8, 8, 8, 0.13);
      border-radius: 4px;
      border: 1px solid #efefef;
      margin-top: 40px;
      margin-left: 112px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .scan_img {
        width: 196px;
        height: 196px;
      }
      .wechat_tips_box {
        display: flex;
        align-items: center;
        // margin-top: 10px;
        .login_font {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #b4b4b4;
          line-height: 20px;
          text-shadow: 0px 0px 11px rgba(8, 8, 8, 0.13);
        }
      }
    }
    .argree_box {
      display: flex;
      align-items: center;
      margin-left: 151px;
      margin-top: 13px;
      .argree_font {
        font-size: 12px;
        font-weight: 400;
        color: #b4b4b4;
        text-shadow: 0px 0px 11px rgba(8, 8, 8, 0.13);
      }
    }
  }
}
.main ::v-deep(.el-form-item__label) {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #000000 !important;
}
.main ::v-deep(.el-input__wrapper) {
  padding: 0 !important;
  border: none !important;
  box-shadow: none !important;
}
.main ::v-deep(.el-radio) {
  margin-right: 10px !important;
}
.register {
  transform: translate(-50%, -50%);
}
</style>
