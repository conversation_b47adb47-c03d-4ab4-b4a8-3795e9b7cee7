<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import Table from '@/components/Table.vue'
import { getSaleProductPlanOrderGfDropdownListUseNotify } from '@/api/productionNotice'
import { getSaleProductPlanOrderGfDropdownList } from '@/api/sheetOfProductionPlan'
import { debounce, getFilterData, resetData } from '@/common/util'
import { formatDate, formatTwoDecimalsDiv, formatWeightDiv } from '@/common/format'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'

export interface Props {
  multiple: boolean
  // eslint-disable-next-line vue/prop-name-casing
  sale_system_id: number
  type: string
}

const props = withDefaults(defineProps<Props>(), {
  multiple: true,
  sale_system_id: 0,
  type: 'productionChange',
})

const emits = defineEmits(['handleSure'])

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
})

const state = reactive<any>({
  filterData: {
    code: '',
    order_time: [],
  },
  showModal: false,
  modalName: '选择坯布',
  multipleSelection: [],
  apiString: '',
  // rowIndex: -1,
  tableData: [],
})
let {
  fetchData: fetchData1,
  data: data1,
  total: total1,
  loading: loading1,
  page: page1,
  size: size1,
  handleSizeChange: handleSizeChange1,
  handleCurrentChange: handleCurrentChange1,
} = getSaleProductPlanOrderGfDropdownListUseNotify()
if (props.type === 'sheetOfProductionPlan') {
  // const {
  //   fetchData: fetchData1,
  //   data: data1,
  //   total: total1,
  //   loading: loading1,
  //   page: page1,
  //   size: size1,
  //   handleSizeChange: handleSizeChange1,
  //   handleCurrentChange: handleCurrentChange1,
  // } = getSaleProductPlanOrderGfDropdownList()
  const obj = getSaleProductPlanOrderGfDropdownList()
  fetchData1 = obj.fetchData
  data1 = obj.data
  total1 = obj.total
  loading1 = obj.loading
  page1 = obj.page
  size1 = obj.size
  handleSizeChange1 = obj.handleSizeChange
  handleCurrentChange1 = obj.handleCurrentChange
}

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      state.filterData.order_time = [new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000), new Date()]
      // getData()
  },
)

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  {
    deep: true,
  },
)

const tableConfig = reactive<any>({
  loading: loading1,
  showPagition: true,
  showSlotNums: false,
  page: page1,
  size: size1,
  height: '100%',
  total: total1,
  showCheckBox: true,
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange1(val),
  handleCurrentChange: (val: number) => handleCurrentChange1(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

async function getData() {
  await fetchData1(
    getFilterData({
      ...state.filterData,
      sale_system_id: props.sale_system_id,
      start_order_time: formatDate(state.filterData.order_time[0]),
      end_order_time: formatDate(state.filterData.order_time[1]),
      order_time: undefined,
    }),
  )

  tableConfig.total = total1
  tableConfig.page = page1
  tableConfig.size = size1
}

watch(
  () => data1.value,
  () => {
    state.tableData
      = data1.value?.list?.map((item: any) => {
        return {
          ...item,
          sale_plan_order_item_id: item.id,
          is_auto: true,
          roll: formatTwoDecimalsDiv(item.roll),
          planed_roll: formatTwoDecimalsDiv(item.planed_roll),
          scheduling_roll: formatTwoDecimalsDiv(item.scheduling_roll),
          produced_roll: formatTwoDecimalsDiv(item.produced_roll),
          use_stock_roll: formatTwoDecimalsDiv(item.use_stock_roll),
          can_scheduling_roll: formatTwoDecimalsDiv(item.can_scheduling_roll),
          weight: formatWeightDiv(item.weight),
          planed_weight: formatWeightDiv(item.planed_weight),
          scheduling_weight: formatWeightDiv(item.scheduling_weight),
          produced_weight: formatWeightDiv(item.produced_weight),
        }
      }) || []
  },
  { deep: true },
)

const columnList = ref([
  {
    field: 'order_no',
    minWidth: 150,
    title: '销售计划单号',
  },
  {
    field: 'detail_order_no',
    minWidth: 150,
    title: '销售计划详情单号',
  },
  {
    field: 'customer_name',
    minWidth: 100,
    title: '客户名称',
  },
  {
    field: 'grey_fabric_code',
    minWidth: 100,
    title: '坯布编号',
  },
  {
    field: 'grey_fabric_name',
    minWidth: 100,
    title: '坯布名称',
  },
  {
    field: 'roll',
    minWidth: 100,
    title: '匹数',
  },
  {
    field: 'weight',
    soltName: 'weight',
    minWidth: 100,
    title: '数量',
  },
  {
    field: 'order_time',
    minWidth: 100,
    title: '订单日期',
    is_date: true,
  },
  {
    field: 'create_time',
    minWidth: 150,
    title: '创建时间',
    isDate: true,
  },
  {
    field: 'grey_fabric_gram_weight_and_unit_name',
    minWidth: 100,
    title: '坯布克重',
  },
  {
    field: 'grey_fabric_width_and_unit_name',
    minWidth: 100,
    title: '坯布幅宽',
  },
  {
    field: 'finish_product_gram_weight_and_unit_name',
    minWidth: 100,
    title: '成品克重',
  },
  {
    field: 'finish_product_width_and_unit_name',
    minWidth: 100,
    title: '成品幅宽',
  },
  {
    field: 'planed_roll',
    minWidth: 100,
    title: '已计划匹数',
  },
  {
    field: 'planed_weight',
    minWidth: 100,
    title: '已计划数量',
  },
  {
    field: 'scheduling_roll',
    minWidth: 100,
    title: '排产匹数',
  },
  {
    field: 'scheduling_weight',
    minWidth: 100,
    title: '排产数量',
  },
  {
    field: 'produced_roll',
    minWidth: 100,
    title: '已产匹数',
  },
  {
    field: 'produced_weight',
    minWidth: 100,
    title: '已产数量',
  },
  {
    field: 'use_stock_roll',
    minWidth: 100,
    title: '调库存匹数',
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handCancel() {
  state.showModal = false
}

function handleSure() {
  if (!state.multipleSelection.length) {
    return ElMessage.error('请选择一条数据')
  }
  else {
    if (!props.multiple) {
      // 需要判断坯布编号、坯布名称、客户名称，是否一致
      // 进行数据合并
      const obj = {}
      let unifyKeys = true
      state.multipleSelection.forEach((item: any) => {
        if (
          (obj.grey_fabric_code && obj.grey_fabric_code !== item.grey_fabric_code)
          || (obj.grey_fabric_name && obj.grey_fabric_name !== item.grey_fabric_name)
          || (obj.customer_name && obj.customer_name !== item.customer_name)
        ) {
          unifyKeys = false
        }
        else {
          obj.grey_fabric_code = item.grey_fabric_code
          obj.grey_fabric_name = item.grey_fabric_name
          obj.customer_name = item.customer_name
        }
      })
      if (!unifyKeys)
        return ElMessage.error('坯布编号、坯布名称、所属客户需与已选的坯布信息一致')

      const scheduling_roll = state.multipleSelection.reduce((pre: number, val: any) => pre + val.planed_roll, 0)
      emits('handleSure', [
        {
          ...state.multipleSelection[0],
          scheduling_roll,
          production_notify_grey_fabric_detail: state.multipleSelection.map((item: any) => {
            return {
              ...item,
              grey_fabric_color_id: 0, // 当前行带出
              grey_fabric_id: item.grey_fabric_id,
              grey_fabric_name: item.grey_fabric_name,
              sale_plan_order_gf_detail_id: item.id, // 当前行带出
              sale_plan_order_id: item.sale_product_plan_order_id,
              sale_plan_order_no: item.order_no,
              this_scheduling_roll: 0,
              sale_plan_order_item_no: item.detail_order_no,
              sale_plan_order_item_id: item.id,
            }
          }),
        },
      ])
    }
    else {
      emits('handleSure', state.multipleSelection)
    }
  }
}

// 重置
function handReset() {
  state.filterData = resetData(state.filterData)
}
defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="1200" height="700" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="list-page">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="销售计划单号:">
          <template #content>
            <vxe-input v-model="state.filterData.order_no" clearable style="width: 100%" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.customer_id"
              :query="{ name: componentRemoteSearch.customer_name }"
              api="GetCustomerEnumList"
              :column-list="[
                {
                  title: '客户编号',
                  minWidth: 100,
                  required: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '客户编号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '客户名称',
                  minWidth: 100,
                  colGroupHeader: true,
                  required: true,
                  childrenList: [
                    {
                      isEdit: true,
                      field: 'name',
                      title: '客户名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '电话',
                  colGroupHeader: true,
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'phone',
                      isEdit: true,
                      title: '电话',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '销售员',
                  minWidth: 100,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'seller_name',
                      title: '销售员',
                      soltName: 'seller_name',
                      isEdit: true,
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="val => (componentRemoteSearch.customer_name = val)"
            />
          <!-- <SelectComponents api="GetCustomerEnumList" label-field="name" value-field="id" v-model="state.filterData.customer_id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布编号:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.grey_fabric_id"
              label-field="code"
              api="GetGreyFabricInfoListUseByOthersMenu"
              :query="{ code: componentRemoteSearch.product_code }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'code',
                  title: '坯布编号',
                },
              ]"
              @change-input="val => (componentRemoteSearch.product_code = val)"
            />
          <!-- <SelectComponents api="GetGreyFabricInfoListUseByOthersMenu" label-field="code" value-field="id" v-model="state.filterData.grey_fabric_id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布名称:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.grey_fabric_id"
              api="GetGreyFabricInfoListUseByOthersMenu"
              :query="{ name: componentRemoteSearch.product_name }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'name',
                  title: '坯布名称',
                },
              ]"
              @change-input="val => (componentRemoteSearch.product_name = val)"
            />
          <!-- <SelectComponents api="GetGreyFabricInfoListUseByOthersMenu" label-field="name" value-field="id" v-model="state.filterData.grey_fabric_id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单日期:">
          <template #content>
            <el-date-picker v-model="state.filterData.order_time" type="daterange" placeholder="订单日期" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
      <div class="table-card-full">
        <Table :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
          <template #weight="{ row }">
            <div>{{ row.weight }}{{ row.unit_name }}</div>
          </template>
          <template #plan_weight="{ row }">
            <div>{{ row.plan_weight }}{{ row.unit_name }}</div>
          </template>
          <template #out_weight="{ row }">
            <div>{{ row.out_weight }}{{ row.unit_name }}</div>
          </template>
          <template #in_weight="{ row }">
            <div>{{ row.in_weight }}{{ row.unit_name }}</div>
          </template>
        </Table>
      </div>
    </div>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style>
.flex-end {
  display: flex;
  justify-content: flex-end;
}
</style>
