<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import Table from '@/components/Table.vue'
import { getProductionNotifyOrderList } from '@/api/greyClothProductionReturn'
import { debounce, getFilterData, getRecentDay_Date, resetData } from '@/common/util'
import SelectDate from '@/components/SelectDate/index.vue'
import { formatDate } from '@/common/format'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'

const emits = defineEmits(['handleSure'])

const state = reactive<any>({
  filterData: {
    order_no: '',
    grey_fabric_code: '',
    grey_fabric_name: '',
    date: [],
  },
  showModal: false,
  rowIndex: -1,
  multipleSelection: [],
})

const { fetchData, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = getProductionNotifyOrderList()

onMounted(() => {
  state.date = getRecentDay_Date(1)
})

const getData = debounce(async () => {
  const query = {
    start_notify_date: state.filterData.date && state.filterData.date !== '' && state.filterData.date.length ? formatDate(state.filterData.date[0]) : '',
    end_notify_date: state.filterData.date && state.filterData.date !== '' && state.filterData.date.length ? formatDate(state.filterData.date[1]) : '',
    ...state.filterData,
  }
  await fetchData(getFilterData(query))
}, 400)

const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  height: 400,
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})
watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)
function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handReset() {
  state.filterData = resetData(state.filterData)
}

const columnList = ref([
  {
    field: 'order_no',
    title: '生产通知单号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 100,
  },
  {
    field: 'finish_product_width',
    title: '成品幅宽',
    minWidth: 100,
  },
  {
    field: 'finish_product_gram_weight',
    title: '成品克重',
    minWidth: 100,
  },
  {
    field: 'needle_size',
    title: '针寸数',
    minWidth: 100,
  },
  {
    field: 'scheduling_roll',
    title: '排产匹数',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'grey_fabric_remark',
    title: '备注',
    minWidth: 100,
  },
  {
    field: 'create_time',
    title: '创建时间',
    minWidth: 140,
    isDate: true,
  },
  {
    field: 'produced_roll',
    title: '入库匹数',
    minWidth: 100,
    isPrice: true,
    fixed: 'right',
  },
])

function handCancel() {
  state.showModal = false
}

async function handleSure() {
  if (state.multipleSelection.length > 1)
    return ElMessage.error('只允许选择一条数据')

  emits('handleSure', state.multipleSelection, state.rowIndex)
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer title="关联生产通知单" width="1200" height="800" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="mb-[10px]">
      提示：若关联生产通知单，单据审核后会对应扣减生产通知单的“收货匹数”。若不关联，则不会扣减
    </div>
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="生产通知单号:">
        <template #content>
          <el-input v-model="state.filterData.order_no" clearable />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="坯布编号:">
        <template #content>
          <el-input v-model="state.filterData.grey_fabric_code" clearable />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="坯布名称:">
        <template #content>
          <el-input v-model="state.filterData.grey_fabric_name" clearable />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="通知日期:">
        <template #content>
          <SelectDate v-model="state.filterData.date" clearable />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="">
        <template #content>
          <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
            清除条件
          </el-button>
        </template>
      </DescriptionsFormItem>
    </div>
    <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList" />
    <template #footer>
      <el-button type="primary" @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
