<script setup lang="ts" name="FpSubscribeWarehouseOrder">
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import FineSizeDeliveryOrderDetail from '../components/FineSizeDeliveryOrderDetail.vue'
import {
  getFpmOutReservationOrder,
  getFpmOutReservationOrderList,
  updateFpmOutReservationOrderStatusPass,
  updateFpmOutReservationOrderStatusWait,
} from '@/api/fpSubscribeWarehouseOrder'
import { GetWarehouseGoodOutEnumTypeReverseIntMap } from '@/api/fpPurchaseReturnDeliverGodown'
import {
  formatDate,
  formatLengthDiv,
  formatTwoDecimalsDiv,
  formatWeightDiv,
} from '@/common/format'
import { debounce, deleteToast, getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import router from '@/router'
import { sumNum } from '@/util/tableFooterCount'
import { BusinessUnitIdEnum, WarehouseTypeIdEnum } from '@/common/enum'
import SelectBusinessDialog from '@/components/SelectBusinessDialog/index.vue'

const options = ref<any>([])
const FineSizeDeliveryOrderDetailRef = ref()
const modal = reactive<any>({
  showModal: false,
  modalName: '请选择预约单类型',
  reservation_type_id: '',
  reservation_type_name: '',
})
const filterData = reactive<any>({
  order_no: '',
  biz_unit_id: '',
  out_order_type: '',
  warehouse_id: '',
  audit_status: [],
})

const reservation_time = ref<any>([])
// 审核
const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = updateFpmOutReservationOrderStatusPass()
// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = updateFpmOutReservationOrderStatusWait()

const {
  fetchData: fetchDataList,
  data: mainList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
}: any = getFpmOutReservationOrderList()
const mainOptions = reactive<any>({
  tableConfig: {
    fieldApiKey: 'FpSubscribeWarehouseOrder',
    showSlotNums: true,
    loading: loadingList.value,
    showPagition: true,
    page: pageList,
    size: sizeList,
    total: totalList,
    showCheckBox: true,
    showOperate: true,
    operateWidth: '8%',
    height: '100%',
    showSort: false,
    handleSizeChange,
    handleCurrentChange,
  },
  mainList: [],
  columnList: [
    {
      sortable: true,
      field: 'order_no',
      soltName: 'order_no',
      title: '单据编号',
      fixed: 'left',
      width: '8%',
    },
    {
      sortable: true,
      field: 'biz_unit_name',
      title: '预约单位',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'out_order_type_name',
      title: '预约类型',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'warehouse_name',
      title: '仓库名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'in_warehouse_name',
      title: '调入仓库名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'out_warehouse_name',
      title: '调出仓库名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'sale_system_name',
      title: '营销体系名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'reservation_time',
      title: '预约日期',
      minWidth: 100,
      is_date: true,
    },
    {
      sortable: true,
      field: 'total_roll',
      title: '匹数总计',
      minWidth: 100,
    },

    {
      sortable: true,
      field: 'total_weight',
      title: '数量总计',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'unit_name',
      title: '单位',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_length',
      title: '辅助数量总计',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'creator_name',
      title: '创建人',
      width: 100,
    },
    {
      sortable: true,
      field: 'create_time',
      title: '创建时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'update_user_name',
      title: '修改人',
      width: 100,
    },
    {
      sortable: true,
      field: 'update_time',
      title: '修改时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'auditor_name',
      title: '审核人',
      width: 100,
    },
    {
      sortable: true,
      field: 'audit_time',
      title: '审核时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'audit_status',
      title: '单据状态',
      showOrder_status: true,
      soltName: 'audit_status',
      fixed: 'right',
      width: '5%',
    },
  ],
  showToolBar: true,
  //   导出
  handleExport: async () => {
    // if (mainOptions.mainList.length < 1) return ElMessage.warning('当前无数据可导出')
    // const { fetchData: getFetch, success: getSuccess, msg: getMsg } = getFpmOutReservationOrderList()
    // mainOptions.exportOptions.loadingExcel.value = true
    // await getFetch({
    //   ...getFilterData(mainOptions.mainList),
    //   download: 1,
    // })
    // if (getSuccess.value) {
    //   exportExcel()
    //   ElMessage({
    //     type: 'success',
    //     message: '成功',
    //   })
    // } else {
    //   ElMessage({
    //     type: 'error',
    //     message: getMsg.value,
    //   })
    // }
    // mainOptions.exportOptions.loadingExcel.value = false
  },
  // exportOptions: {
  //   loadingExport: false,
  //   handleExport: () => {},
  // },
})
// mainOptions.exportOptions.handleExport = mainOptions.handleExport

// const tableRef = ref<any>()
// const exportExcel = () => {
//   tableRef.value.tableRef.exportData({
//     filename: `成品采购退货出仓单列表${formatTime(new Date())}`,
//     // isFooter: true,
//   })
// }
async function getData() {
  const query = {
    ...filterData,
  }
  if (query.audit_status.length)
    query.audit_status = query.audit_status.join(',')

  if (reservation_time?.value?.length) {
    query.reservation_time_begin = formatDate(reservation_time.value[0])
    query.reservation_time_end = formatDate(reservation_time.value[1])
  }
  await fetchDataList(getFilterData({ ...query }))
  if (mainList.value?.list)
    showDetail(mainList.value.list[0])
}

watch(
  () => mainList.value,
  () => {
    mainOptions.mainList
      = mainList.value?.list?.map((item: any) => {
        return {
          ...item,
          total_roll: formatTwoDecimalsDiv(Number(item.total_roll)),
          total_weight: formatWeightDiv(Number(item.total_weight)),
          total_length: formatLengthDiv(Number(item.total_length)),
        }
      }) || []
  },
  { deep: true },
)

onMounted(async () => {
  await getData()
  const { fetchData: getOptions, data: optionsDatas }
    = GetWarehouseGoodOutEnumTypeReverseIntMap()
  await getOptions()
  options.value = optionsDatas.value.list
})

watch(
  filterData,
  debounce(() => {
    getData()
  }, 400),
)
onActivated(getData)

// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id.toString() })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}

function handDetail(row: any) {
  router.push({
    name: 'FpSubscribeWarehouseOrderDetail',
    query: {
      id: row.id,
    },
  })
}
function handEdit(row: any) {
  router.push({
    name: 'FpSubscribeWarehouseOrderEdit',
    query: {
      id: row.id,
    },
  })
}
function handAdd() {
  modal.showModal = true
}
// 选择预约类型

function selectRTI(id: any) {
  const selectItem = options.value?.filter((item: any) => item.id === id)[0]
  modal.reservation_type_id = id
  modal.reservation_type_name = selectItem?.name
}
function jumpPage() {
  if (!modal.reservation_type_id)
    return ElMessage.error('请选择预约类型')

  modal.showModal = false
  router.push({
    name: 'FpSubscribeWarehouseOrderAdd',
    query: {
      reservation_type_id: modal.reservation_type_id,
      reservation_type_name: modal.reservation_type_name,
    },
  })
}
// 成品信息
const finishProductionOptions = reactive({
  detailShow: false,
  tableConfig: {
    fieldApiKey: 'FpSubscribeWarehouseOrder_B',
    showSlotNums: false,
    showSpanHeader: true,
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 出仓数量 结算数量 辅助数量 其他金额 结算金额
        if (['reservation_weight'].includes(column.field))
          return sumNum(data, 'reservation_weight', '', 'float')

        if (['reservation_length'].includes(column.field))
          return sumNum(data, 'reservation_length', '', 'int')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      field: 'A',
      childrenList: [
        {
          sortable: true,
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'B',
      title: '成品信息',
      childrenList: [
        {
          sortable: true,
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },

        {
          sortable: true,
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'reservation_roll',
          title: '出仓匹数',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'C',
      title: '出仓数量信息',
      childrenList: [
        {
          sortable: true,
          field: 'reservation_weight',
          title: '总数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'unit_name',
          title: '单位',
          minWidth: '5%',
        },
      ],
    },
    {
      field: 'D',
      title: '出仓辅助数量信息',
      childrenList: [
        {
          sortable: true,
          field: 'reservation_length',
          title: '辅助数量',
          minWidth: 100,
        },
      ],
    },

    {
      field: 'E',
      title: '单据备注信息',
      childrenList: [
        {
          sortable: true,
          field: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
  ],
})

function changeDate() {
  // reservation_time.value = [row.date_min, row.date_max]
  getData()
}
// 获取成品信息
function showDetail(row: any) {
  finishProductionOptions.detailShow = true
  getFinishProductionData(row.id)
}

const { fetchData: DetailFetch, data: finishProData }
  = getFpmOutReservationOrder()
async function getFinishProductionData(id: string | number) {
  await DetailFetch({ id })
  finishProductionOptions.datalist = finishProData.value?.item_data?.map(
    (item: any) => {
      return {
        ...item,
        reservation_roll: formatTwoDecimalsDiv(item.reservation_roll),
        reservation_weight: formatWeightDiv(item.reservation_weight),
        reservation_length: formatLengthDiv(item.reservation_length),
      }
    },
  )
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="" class="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input
              v-model="filterData.order_no"
              placeholder="单据编号"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <!-- 5种类型的预约单位：客户、成品供应商、染厂/后整厂 -->
        <DescriptionsFormItem label="预约单位:">
          <template #content>
            <SelectBusinessDialog
              v-model="filterData.biz_unit_id"
              :query="{
                unit_type_id: `${BusinessUnitIdEnum.customer},${BusinessUnitIdEnum.finishedProduct},${BusinessUnitIdEnum.dyeFactory}`,
              }"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="预约类型:">
          <template #content>
            <SelectComponents
              v-model="filterData.out_order_type"
              style="width: 200px"
              api="GetWarehouseGoodOutEnumTypeReverseIntMap"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓库名称:">
          <template #content>
            <SelectComponents
              v-model="filterData.warehouse_id"
              style="width: 200px"
              api="GetPhysicalWarehouseDropdownList"
              :query="{ warehouse_type_id: WarehouseTypeIdEnum.finishProduction }"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="预约日期:">
          <template #content>
            <SelectDate v-model="reservation_time" @change-date="changeDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="filterData.audit_status"
              api="GetAuditStatusEnum"
              multiple
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard class="table-card-full" :tool-bar="mainOptions.showToolBar">
      <template #right-top>
        <el-button
          v-has="'FpSubscribeWarehouseOrder_add'"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handAdd"
        >
          新建
        </el-button>
      </template>
      <Table
        :config="mainOptions.tableConfig"
        :table-list="mainOptions.mainList"
        :column-list="mainOptions.columnList"
      >
        <template #order_no="{ row }">
          <el-link type="primary" @click="showDetail(row)">
            {{
              row.order_no
            }}
          </el-link>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'FpSubscribeWarehouseOrder_detail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'FpSubscribeWarehouseOrder_edit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.audit_status === 1"
              v-has="'FpSubscribeWarehouseOrder_pass'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'FpSubscribeWarehouseOrder_wait'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard
      title=""
      class="table-card-bottom"
    >
      <Table
        :config="finishProductionOptions.tableConfig"
        :table-list="finishProductionOptions.datalist"
        :column-list="finishProductionOptions.columnList"
      />
    </FildCard>
  </div>
  <FineSizeDeliveryOrderDetail ref="FineSizeDeliveryOrderDetailRef" />
  <vxe-modal
    v-model="modal.showModal"
    :title="modal.modalName"
    width="400"
    height="200"
    :mask="false"
    :lock-view="false"
    :esc-closable="true"
    resize
  >
    <el-form>
      <el-form-item label="预约类型">
        <!--        <SelectComponents -->
        <!--          @change-value="selectRTI" -->
        <!--          style="width: 200px" -->
        <!--          api="GetWarehouseGoodOutEnumTypeReverseIntMap" -->
        <!--          label-field="name" -->
        <!--          value-field="id" -->
        <!--          v-model="modal.reservation_type_id" -->
        <!--          clearable -->
        <!--        /> -->
        <el-select
          v-model="modal.reservation_type_id"
          style="width: 200px"
          clearable
          @change="selectRTI"
        >
          <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="flex_btns">
      <el-button type="primary" @click="jumpPage">
        新建
      </el-button>
    </div>
  </vxe-modal>
</template>

<style lang="scss" scoped>
.el-link {
  color: #0e7eff;
}

.flex_btns {
  display: flex;
  justify-content: center;
}
</style>
