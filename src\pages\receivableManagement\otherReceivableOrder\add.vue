<script setup lang="ts" name="OtherReceivableOrderAdd">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import currency from 'currency.js'
import { shouldCollectOrderAdd } from '@/api/otherReceivableOrder'
import { formatDate, formatTwoDecimalsMul, formatUnitPriceMul, formatWeightMul } from '@/common/format'
import { deleteToast, getDefaultSaleSystem } from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
// import { BusinessUnitIdEnum } from '@/common/enum'
import { EmployeeType } from '@/common/enum'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import useRouterList from '@/use/useRouterList'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'

const routerList = useRouterList()
const formRef = ref()
const state = reactive<any>({
  form: {
    sale_system_id: '',
    customer_id: '',
    sale_user_id: '',
    order_time: new Date(),
    order_remark: '',
  },
  formRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    customer_id: [{ required: true, message: '请选择客户名称', trigger: 'change' }],
    sale_user_id: [{ required: true, message: '请选择销售员', trigger: 'change' }],
    order_time: [{ required: true, message: '请选择单据日期', trigger: 'change' }],
  },
})

let uuid = 0
const materielOptions = reactive<any>({
  multipleSelection: [],
  rowIndex: 0,
  tableConfig: {
    showSlotNums: false,
    showCheckBox: true,
    footerMethod: (val: any) => materielOptions.footerMethod(val),
    handAllSelect: (val: any) => materielOptions.handAllSelect(val),
    handleSelectionChange: (val: any) => materielOptions.handleSelectionChange(val),
  },
  footerMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        if (['roll'].includes(column.field))
          return sumNum(data, 'roll', '')

        if (['origin_price'].includes(column.field))
          return sumNum(data, 'origin_price', '')

        if (['other_price'].includes(column.field))
          return sumNum(data, 'other_price', '')

        if (['settle_price'].includes(column.field))
          return sumNum(data, 'settle_price', '')

        if (['weight'].includes(column.field))
          return sumNum(data, 'weight', '')

        if (['settle_error_weight'].includes(column.field))
          return sumNum(data, 'settle_error_weight', '')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '')

        if (['sale_price'].includes(column.field))
          return sumNum(data, 'sale_price', '')

        return null
      }),
    ]
  },
  list: [],
  columnList: [
    {
      field: 'voucher_number',
      soltName: 'voucher_number',
      title: '来源单号',
      minWidth: 100,
    },
    {
      field: 'order_time',
      soltName: 'order_time',
      title: '日期',
      minWidth: 150,
    },
    {
      field: 'order_type',
      title: '单据类型',
      minWidth: 100,
      soltName: 'order_type',
    },
    {
      field: 'code',
      soltName: 'code',
      title: '项目编号',
      minWidth: 100,
    },
    {
      field: 'name',
      soltName: 'name',
      title: '项目名称',
      minWidth: 100,
    },
    // {
    //   field: 'product_color_code',
    //   soltName: 'product_color_code',
    //   title: '色号',
    //   minWidth: 100,
    // },
    // {
    //   field: 'product_color_name',
    //   soltName: 'product_color_name',
    //   title: '颜色',
    //   minWidth: 100,
    // },
    // {
    //   field: 'dyelot_number',
    //   soltName: 'dyelot_number',
    //   title: '缸号',
    //   minWidth: 100,
    // },
    // {
    //   field: 'roll',
    //   soltName: 'roll',
    //   title: '匹数',
    //   minWidth: 100,
    // },
    {
      field: 'weight',
      soltName: 'weight',
      title: '数量',
      minWidth: 100,
    },
    // {
    //   field: 'settle_error_weight',
    //   soltName: 'settle_error_weight',
    //   title: '空差',
    //   minWidth: 100,
    // },
    // {
    //   field: 'settle_weight',
    //   soltName: 'settle_weight',
    //   title: '结算数量',
    //   minWidth: 100,
    // },
    {
      field: 'sale_price',
      soltName: 'sale_price',
      title: '单价',
      minWidth: 100,
    },
    // {
    //   field: 'origin_price',
    //   soltName: 'origin_price',
    //   title: '原金额',
    //   minWidth: 100,
    // },
    {
      field: 'other_price',
      soltName: 'other_price',
      title: '其他应收',
      minWidth: 100,
    },
    {
      field: 'settle_price',
      soltName: 'settle_price',
      title: '金额',
      minWidth: 100,
      required: true,
    },
    {
      field: 'remark',
      soltName: 'remark',
      title: '备注',
      minWidth: 100,
    },
    {
      field: '',
      soltName: 'operate',
      title: '操作',
      width: 100,
    },
  ],
  //   删除
  handleRowDel: async ({ uuid }: any) => {
    const res = await deleteToast('是否确认删除该物料')
    if (!res)
      return
    const index = materielOptions.list.findIndex((item: any) => item.uuid === uuid)
    materielOptions.list.splice(index, 1)
  },
  handAllSelect: ({ records }: any) => {
    materielOptions.multipleSelection = records
  },
  handleSelectionChange: ({ records }: any) => {
    materielOptions.multipleSelection = records
  },
  handleAdd: () => {
    materielOptions.list.push({
      uuid: ++uuid,
      roll: 0,
      origin_price: 0,
      other_price: 0,
      settle_price: 0,
      weight: 0,
      settle_error_weight: 0,
      settle_weight: 0,
      sale_price: 0,
    })
  },
})

// 新增提交
const { fetchData: addFetch, data: successData, success: addSuccess, msg: addMsg } = shouldCollectOrderAdd()
// 提交所有数据
function submitAddAllData() {
  // 表单验证
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 必填项
      for (let i = 0; i < materielOptions.list.length; i++) {
        if (!Number(materielOptions.list[i].settle_price))
          return ElMessage.error('实收金额为必填项')
      }
      // 整理参数
      const query = {
        ...state.form,
        sale_user_id: state.form.sale_user_id || 0,
        order_time: formatDate(state.form.order_time),
        items: materielOptions.list.map((item: any) => {
          return {
            ...item,
            order_time: formatDate(item.order_time),
            roll: formatTwoDecimalsMul(Number(item.roll)), // 100
            origin_price: formatTwoDecimalsMul(Number(item.origin_price)), // 100
            other_price: formatTwoDecimalsMul(Number(item.other_price)), // 100
            settle_price: formatTwoDecimalsMul(Number(item.settle_price)), // 100
            weight: formatWeightMul(Number(item.weight)), // weight
            settle_error_weight: formatWeightMul(Number(item.settle_error_weight)), // weight
            settle_weight: formatWeightMul(Number(item.settle_weight)), // weight
            sale_price: formatUnitPriceMul(Number(item.sale_price)), // price
          }
        }),
      }
      await addFetch(query)
      if (addSuccess.value) {
        ElMessage.success('提交成功')
        // 跳转到列表页
        routerList.push({
          name: 'OtherReceivableOrderDetail',
          query: {
            id: successData.value.id,
          },
        })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}
const tablesRef = ref()

// 我操你妈的，写这种傻逼东西出来，不会写代码好早点回去耕田
// watch(
//   () => materielOptions.list,
//   () => {
//     if (materielOptions.list.length > 0) {
//       materielOptions.list.map((item: any) => {
//         item.settle_weight = new Decimal(Number(item.weight)).minus(new Decimal(Number(item.settle_error_weight)))
//         item.settle_weight = Number(item.settle_weight).toFixed(2)
//         return item
//       })
//     }
//
//     tablesRef.value.tableRef.updateFooter()
//   },
//   { deep: true },
// )

onMounted(() => {
  materielOptions.handleAdd()
  // 获取用户上的默认营销体系
  const res = getDefaultSaleSystem()

  state.form.sale_system_id = res.default_sale_system_id
})

// 计算结算数量：数量-空差
function computedCount(row: any) {
  row.settle_weight = currency(row.weight).subtract(row.settle_error_weight).format({ precision: 2, symbol: '', separator: '' })
  tablesRef.value.tableRef.updateFooter()
  computedPrice(row)
}

// 计算原金额：结算数量*单价
function computedPrice(row: any) {
  row.origin_price = currency(row.settle_weight).multiply(row.sale_price).format({ precision: 2, symbol: '', separator: '' })
  computedSettlePrice(row)
  tablesRef.value.tableRef.updateFooter()
}

// 计算应收金额：原金额-扣款金额
function computedSettlePrice(row: any) {
  row.settle_price = currency(row.origin_price).subtract(row.other_price).format({ precision: 2, symbol: '', separator: '' })
  tablesRef.value.tableRef.updateFooter()
}
function getCoustome(row: any) {
  state.form.sale_system_id = row.select_sale_system_id
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" :disabled="materielOptions.list.length ? false : true" @click="submitAddAllData">
        提交
      </el-button>
    </template>
    <el-form ref="formRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem :required="true" label="客户名称:">
          <template #content>
            <el-form-item prop="customer_id">
              <SelectCustomerDialog
                v-model="state.form.customer_id"
                field="name"
                is-merge
                show-choice-system
                @change-value="getCoustome"
              />
              <!--              <SelectDialog -->
              <!--                v-model="state.form.customer_id" -->
              <!--                api="GetCustomerEnumList" -->
              <!--                :query="{ name: componentRemoteSearch.customer_name }" -->
              <!--                :column-list="[ -->
              <!--                  { -->
              <!--                    title: '客户编号', -->
              <!--                    minWidth: 100, -->
              <!--                    required: true, -->
              <!--                    colGroupHeader: true, -->
              <!--                    childrenList: [ -->
              <!--                      { -->
              <!--                        field: 'code', -->
              <!--                        isEdit: true, -->
              <!--                        title: '客户编号', -->
              <!--                        minWidth: 100, -->
              <!--                      }, -->
              <!--                    ], -->
              <!--                  }, -->
              <!--                  { -->
              <!--                    title: '客户名称', -->
              <!--                    minWidth: 100, -->
              <!--                    colGroupHeader: true, -->
              <!--                    required: true, -->
              <!--                    childrenList: [ -->
              <!--                      { -->
              <!--                        isEdit: true, -->
              <!--                        field: 'name', -->
              <!--                        title: '客户名称', -->
              <!--                        minWidth: 100, -->
              <!--                      }, -->
              <!--                    ], -->
              <!--                  }, -->
              <!--                  { -->
              <!--                    title: '电话', -->
              <!--                    colGroupHeader: true, -->
              <!--                    minWidth: 100, -->
              <!--                    childrenList: [ -->
              <!--                      { -->
              <!--                        field: 'phone', -->
              <!--                        isEdit: true, -->
              <!--                        title: '电话', -->
              <!--                        minWidth: 100, -->
              <!--                      }, -->
              <!--                    ], -->
              <!--                  }, -->
              <!--                  { -->
              <!--                    title: '销售员', -->
              <!--                    minWidth: 100, -->
              <!--                    colGroupHeader: true, -->
              <!--                    childrenList: [ -->
              <!--                      { -->
              <!--                        field: 'seller_name', -->
              <!--                        title: '销售员', -->
              <!--                        soltName: 'seller_name', -->
              <!--                        isEdit: true, -->
              <!--                        minWidth: 100, -->
              <!--                      }, -->
              <!--                    ], -->
              <!--                  }, -->
              <!--                ]" -->
              <!--                @change-input="val => (componentRemoteSearch.customer_name = val)" -->
              <!--              /> -->
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="营销体系名称:">
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents v-model="state.form.sale_system_id" api="GetSaleSystemDropdownListApi" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="销售员:">
          <template #content>
            <el-form-item prop="sale_user_id">
              <SelectComponents
                v-model="state.form.sale_user_id"
                :query="{
                  duty: EmployeeType.salesman,
                }"
                api="GetEmployeeListEnum"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="单据日期:">
          <template #content>
            <el-form-item prop="order_time">
              <el-date-picker v-model="state.form.order_time" type="date" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="备注:" copies="2">
          <template #content>
            <el-form-item prop="order_remark">
              <vxe-textarea v-model="state.form.order_remark" style="width: 100%" maxlength="500" show-word-count />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="物料信息" class="mt-[5px]" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" @click="materielOptions.handleAdd">
        新建
      </el-button>
    </template>
    <div v-show="materielOptions.list.length">
      <Table ref="tablesRef" :config="materielOptions.tableConfig" :table-list="materielOptions.list" :column-list="materielOptions.columnList">
        <!-- 凭证单号 -->
        <template #voucher_number="{ row }">
          <vxe-input v-model="row.voucher_number" maxlength="50" />
        </template>
        <!-- 日期 -->
        <template #order_time="{ row }">
          <el-date-picker v-model="row.order_time" type="date" />
        </template>
        <!-- 单据类型 -->
        <template #order_type="{ row }">
          <vxe-input v-model="row.order_type" clearable />
        </template>
        <!-- 物料编号 -->
        <template #code="{ row }">
          <vxe-input v-model="row.code" maxlength="50" />
        </template>
        <!-- 物料名称 -->
        <template #name="{ row }">
          <vxe-input v-model="row.name" maxlength="50" />
        </template>
        <!-- 色号 -->
        <template #product_color_code="{ row }">
          <vxe-input v-model="row.product_color_code" maxlength="50" />
        </template>
        <!-- 颜色 -->
        <template #product_color_name="{ row }">
          <vxe-input v-model="row.product_color_name" maxlength="50" />
        </template>
        <!-- 缸号 -->
        <template #dyelot_number="{ row }">
          <vxe-input v-model="row.dyelot_number" maxlength="50" />
        </template>
        <!-- 匹数 -->
        <template #roll="{ row }">
          <vxe-input v-model="row.roll" type="float" @blur="computedCount(row)" />
        </template>
        <!-- 数量 -->
        <template #weight="{ row }">
          <vxe-input v-model="row.weight" type="float" @blur="computedCount(row)" />
        </template>
        <!-- 空差 -->
        <template #settle_error_weight="{ row }">
          <vxe-input v-model="row.settle_error_weight" type="float" @blur="computedCount(row)" />
        </template>
        <!-- 结算数量 -->
        <template #settle_weight="{ row }">
          <vxe-input v-model="row.settle_weight" type="float" @blur="computedPrice(row)" />
        </template>
        <!-- 单价 -->
        <template #sale_price="{ row }">
          <vxe-input v-model="row.sale_price" type="float" @blur="computedPrice(row)" />
        </template>
        <!-- 原金额 -->
        <template #origin_price="{ row }">
          <vxe-input v-model="row.origin_price" type="float" @blur="computedSettlePrice(row)" />
        </template>
        <!-- 折扣金额 -->
        <template #other_price="{ row }">
          <vxe-input v-model="row.other_price" type="float" @blur="computedSettlePrice(row)" />
        </template>
        <!-- 实收金额 -->
        <template #settle_price="{ row }">
          <vxe-input v-model="row.settle_price" type="float" :min="0" />
        </template>
        <!-- 备注 -->
        <template #remark="{ row }">
          <vxe-input v-model="row.remark" maxlength="200" type="text" />
        </template>
        <!-- 操作 -->
        <template #operate="{ row }">
          <el-button type="text" @click="materielOptions.handleRowDel(row)">
            删除
          </el-button>
        </template>
      </Table>
    </div>
    <div v-if="materielOptions.list.length === 0" class="no_data">
      <el-icon :size="80">
        <MessageBox />
      </el-icon>
      <div class="text">
        请添加物料信息
      </div>
    </div>
  </FildCard>
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}

.no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  user-select: none;
  color: #999;
}

.flex_button {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}
</style>
