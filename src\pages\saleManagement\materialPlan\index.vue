<script setup lang="ts" name="MaterialPlan">
import { ElMessage } from 'element-plus'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Delete } from '@element-plus/icons-vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'

// import router from '@/router'
import {
  getPmcGreyPlanOrderAndDetailList,
  getPmcGreyPlanOrderList,
  updatePmcGreyPlanOrderAuditStatusCancel,
  updatePmcGreyPlanOrderAuditStatusPass,
  updatePmcGreyPlanOrderAuditStatusWait,
} from '@/api/materialPlan'
import { formatDate } from '@/common/format'
import { debounce, deleteToast, getFilterData, resetData } from '@/common/util'
import { EmployeeType } from '@/common/enum'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'

const state = reactive({
  tableData: [],
  filterData: {
    order_no: '',
    sale_system_id: '',
    sale_order_no: '',
    src_order_no: '',
    order_date: '',
    auditor_id: '',
    updater_id: '',
    create_time: '',
    order_time: '',
    audit_time: '',
    edit_time: '',
    audit_status: '',
    voucher_number: '',
    sale_user_id: '',
    customer_id: '',
    customer_code: '',
    sale_product_plan_order_no: '',
    sale_plan_order_item_no: '',
    customer_name: '',
    plan_type: '',
    product_kind_name: '',
    product_code_and_name: '',
    color_code_and_name: '',
  },
  multipleSelection: [],
})
const mergeFlag = ref()

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = getPmcGreyPlanOrderList()

const {
  fetchData: ApiCustomerList1,
  data: data1,
  total: total1,
  loading: loading1,
  page: page1,
  size: size1,
  handleSizeChange: handleSizeChange1,
  handleCurrentChange: handleCurrentChange1,
} = getPmcGreyPlanOrderAndDetailList()
// 获取数据
const getData = debounce(async () => {
  const query: any = {
    start_create_time:
      state.filterData.create_time
      && state.filterData.create_time !== ''
      && state.filterData.create_time.length
        ? formatDate(state.filterData.create_time[0])
        : '',
    end_create_time:
      state.filterData.create_time
      && state.filterData.create_time !== ''
      && state.filterData.create_time.length
        ? formatDate(state.filterData.create_time[1])
        : '',
    start_order_time:
      state.filterData.order_time
      && state.filterData.order_time !== ''
      && state.filterData.order_time.length
        ? formatDate(state.filterData.order_time[0])
        : '',
    end_order_time:
      state.filterData.order_time
      && state.filterData.order_time !== ''
      && state.filterData.order_time.length
        ? formatDate(state.filterData.order_time[1])
        : '',
    start_update_time:
      state.filterData.edit_time
      && state.filterData.edit_time !== ''
      && state.filterData.edit_time.length
        ? formatDate(state.filterData.edit_time[0])
        : '',
    end_update_time:
      state.filterData.edit_time
      && state.filterData.edit_time !== ''
      && state.filterData.edit_time.length
        ? formatDate(state.filterData.edit_time[1])
        : '',
    start_audit_date:
      state.filterData.audit_time
      && state.filterData.audit_time !== ''
      && state.filterData.audit_time.length
        ? formatDate(state.filterData.audit_time[0])
        : '',
    end_audit_date:
      state.filterData.audit_time
      && state.filterData.audit_time !== ''
      && state.filterData.audit_time.length
        ? formatDate(state.filterData.audit_time[1])
        : '',
    ...state.filterData,
  }
  delete query.audit_time
  delete query.create_time
  delete query.order_time
  delete query.edit_time
  // ApiCustomerList(getFilterData(state.filterData))
  if (mergeFlag.value)
    await ApiCustomerList1(getFilterData(query))
  else
    await ApiCustomerList(getFilterData(query))
}, 400)
onMounted(() => {
  if (localStorage.getItem('materialPlanMergeFlag') === 'true')
    mergeFlag.value = true
  else
    mergeFlag.value = false

  getData()
})
const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  showCheckBox: true,
  page,
  size,
  total,
  height: '100%',
  showOperate: true,
  operateWidth: '8%',
  showSort: false,
  fieldApiKey: 'MaterialPlan',
  scrollY: { enabled: false },
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

const options = ref([
  {
    label: '成品',
    value: 1,
  },
  {
    label: '坯布',
    value: 2,
  },
  {
    label: '原料',
    value: 3,
  },
])

function handReset() {
  state.filterData = resetData(state.filterData)
}

const columnList = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '单据编号',
    width: '8%',
    soltName: 'link',
    fixed: 'left',
  },
  {
    sortable: true,
    field: 'order_jh_no',
    title: '销售计划单号',
    width: '8%',
    soltName: 'link1',
    fixed: 'left',
  },
  {
    sortable: true,
    field: 'plan_type_name',
    title: '销售类型',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'voucher_number',
    title: '凭证单号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'order_time',
    title: '订单日期',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'receipt_time',
    title: '交货日期',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'customer_name',
    title: '客户名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_user_name',
    title: '销售员',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'receipt_address',
    title: '交货地址',
    minWidth: 120,
    soltName: 'receipt_address',
  },
  {
    sortable: true,
    field: 'internal_remark',
    title: '单据备注',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    minWidth: 120,
    isDate: true,
  },
  {
    sortable: true,
    field: 'auditor_name',
    title: '审核人',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'audit_date',
    title: '审核时间',
    minWidth: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '最后修改时间',
    isDate: true,
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'order_status',
    title: '单据状态',
    fixed: 'right',
    width: '5%',
    soltName: 'audit_status',
    showOrder_status: true,
  },
])
const mergeRowMethod: any = ({ row, _rowIndex, column, visibleData }: any) => {
  const fields = [
    'sale_product_plan_order_no',
    'operate',
    'audit_status',
    'order_no',
    'order_no',
    'sale_system_name',
    'voucher_number',
    'order_time',
    'receipt_time',
    'order_customer_name',
    'sale_user_name',
    'receipt_address',
    'internal_remark',
    'order_create_time',
    'order_audit_date',
    'order_update_time',
    'plan_type_name',
  ]
  const cellValue = row.order_no + row[column.field]
  if (cellValue && fields.includes(column.field)) {
    const prevRow = visibleData[_rowIndex - 1]
    let nextRow = visibleData[_rowIndex + 1]
    if (prevRow && prevRow.order_no + prevRow[column.field] === cellValue) {
      return { rowspan: 0, colspan: 0 }
    }
    else {
      let countRowspan = 1
      while (
        nextRow
        && nextRow.order_no + nextRow[column.field] === cellValue
      )
        nextRow = visibleData[++countRowspan + _rowIndex]

      if (countRowspan > 1)
        return { rowspan: countRowspan, colspan: 1 }
    }
  }
}
const tableConfig1 = ref({
  showFixed: false,
  fieldApiKey: 'MaterialPlan_list',
  loading: loading1,
  showPagition: true,
  height: '100%',
  // showSlotNums: true,
  page: page1,
  size: size1,
  total: total1,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '220',
  showSort: false,
  scrollY: { enabled: false },
  colspanMethod: mergeRowMethod,
  handleSizeChange: (val: number) => handleSizeChange1(val),
  handleCurrentChange: (val: number) => handleCurrentChange1(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

const columnList1 = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '单据编号',
    width: '8%',
    soltName: 'link',
  },
  {
    sortable: true,
    field: 'sale_product_plan_order_no',
    title: '销售计划单号',
    minWidth: 150,
    soltName: 'sale_product_plan_order_no',
  },
  {
    sortable: true,
    field: 'plan_type_name',
    title: '销售类型',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'voucher_number',
    title: '凭证单号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'order_time',
    title: '订单日期',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'receipt_time',
    title: '交货日期',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'order_customer_name',
    title: '客户名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_user_name',
    title: '销售员',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'receipt_address',
    title: '交货地址',
    minWidth: 120,
    soltName: 'receipt_address',
  },
  {
    sortable: true,
    field: 'internal_remark',
    title: '单据备注',
    minWidth: 120,
  },
  {
    field: 'sale_plan_order_item_no',
    title: '销售计划详情单号',
    minWidth: 150,
  },
  {
    field: 'order_type_name',
    title: '订单类型',
    minWidth: 100,
  },
  {
    field: 'product_code',
    title: '成品编号',
    minWidth: 100,
  },
  {
    field: 'product_name',
    title: '成品名称',
    minWidth: 100,
  },
  {
    field: 'product_color_name',
    title: '颜色',
    minWidth: 100,
  },
  {
    field: 'product_color_code',
    title: '色号',
    minWidth: 100,
  },
  {
    field: 'measurement_unit_name',
    title: '单位',
    minWidth: 100,
  },
  {
    field: 'finish_product_gram_weight',
    title: '克重',
    minWidth: 100,
    soltName: 'finish_product_gram_weight',
  },
  {
    field: 'finish_product_width',
    title: '幅宽',
    minWidth: 100,
    soltName: 'finish_product_width',
  },
  {
    field: 'product_color_kind_name',
    title: '颜色类别',
    minWidth: '6%',
  },
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 100,
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 100,
  },
  {
    field: 'ingredient',
    title: '原料成分',
    minWidth: 100,
  },
  {
    field: 'product_level_name',
    title: '成品等级',
    minWidth: 100,
  },
  {
    field: 'roll',
    title: '匹数',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'weight',
    title: '数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'planed_total_roll',
    title: '已计划匹数',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'planed_total_weight',
    title: '已计划数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'order_create_time',
    title: '创建时间',
    minWidth: 120,
    isDate: true,
  },
  {
    sortable: true,
    field: 'order_audit_date',
    title: '审核时间',
    minWidth: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'order_update_time',
    title: '最后修改时间',
    isDate: true,
    minWidth: 150,
  },
  {
    sortable: true,
    field: '',
    title: '单据状态',
    fixed: 'right',
    width: '5%',
    soltName: 'audit_status',
    showOrder_status: true,
  },
])
function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// 导出
// const loadingExcel = ref(false)
// const handleExport = async () => {
//   if (!data?.value.list || data?.value.list.length <= 0) return ElMessage.warning('当前无数据可导出')
//   const name_str = '成品销售计划单'
//   const { fetchData: getFetch, success: getSuccess, msg: getMsg } = getSaleProductPlanOrderListExport({ nameFile: name_str })
//   loadingExcel.value = true
//   await getFetch({
//     ...getFilterData(state.filterData),
//     download: 1,
//   })
//   if (getSuccess.value) {
//     ElMessage({
//       type: 'success',
//       message: '成功',
//     })
//   } else {
//     ElMessage({
//       type: 'error',
//       message: getMsg.value,
//     })
//   }
//   loadingExcel.value = false
// }

const router = useRouter()

function handEdit(row: any) {
  if (row?.plan_type === 3) {
    router.push({
      name: 'RawMaterialPlanEdit',
      query: { id: mergeFlag.value ? row.order_id : row.id },
    })
  }
  else if (row?.plan_type === 2) {
    router.push({
      name: 'GreyMaterialPlanEdit',
      query: { id: mergeFlag.value ? row.order_id : row.id },
    })
  }
  else if (row?.plan_type === 1) {
    router.push({
      name: 'ProductMaterialPlanEdit',
      query: { id: mergeFlag.value ? row.order_id : row.id },
    })
  }
}

function handDetail(row: any) {
  if (row?.plan_type === 3) {
    router.push({
      name: 'RawMaterialPlanDetail',
      query: { id: mergeFlag.value ? row.order_id : row.id },
    })
  }
  else if (row?.plan_type === 2) {
    router.push({
      name: 'GreyMaterialPlanEdit',
      query: { id: mergeFlag.value ? row.order_id : row.id },
    })
  }
  else if (row?.plan_type === 1) {
    router.push({
      name: 'ProductMaterialPlanEdit',
      query: { id: mergeFlag.value ? row.order_id : row.id },
    })
  }
}
function handSalesPlanDetail(row: any) {
  if (row?.plan_type === 3) {
    router.push({
      name: 'RawSalePlanDetail',
      query: { id: row.sale_product_plan_order_id },
    })
  }
  else if (row?.plan_type === 2) {
    router.push({
      name: 'GreySalePlanDetail',
      query: { id: row.sale_product_plan_order_id },
    })
  }
  else if (row?.plan_type === 1) {
    router.push({
      name: 'ProductSalePlanDetail',
      query: { id: row.sale_product_plan_order_id },
    })
  }
}
const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = updatePmcGreyPlanOrderAuditStatusPass()

// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({
      audit_status: 2,
      id: mergeFlag.value ? row.order_id.toString() : row.id.toString(),
    })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = updatePmcGreyPlanOrderAuditStatusWait()

async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({
      audit_status: 1,
      id: mergeFlag.value ? row.order_id.toString() : row.id.toString(),
    })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}

// 清空所选数据
watch(() => [data.value.list, data1.value.list], () => {
  state.multipleSelection = []
})

// 批量作废
const { fetchData: batchCancelFetch, success: batchCancelSuccess, msg: batchCancelMsg, loading: batchCancelLoading } = updatePmcGreyPlanOrderAuditStatusCancel()
async function handleBatchCancel() {
  if (!state.multipleSelection.length)
    return ElMessage.warning('请选择数据')

  const res = await deleteToast('确认作废所选订单？')
  if (!res)
    return
  const ids = state.multipleSelection.map((item: any) => item.id)
  await batchCancelFetch({ id: ids.join(',') })
  if (batchCancelSuccess.value) {
    ElMessage.success('成功')
    getData()
  }
  else {
    ElMessage.error(batchCancelMsg.value)
  }
}

function mergeSpan() {
  mergeFlag.value = !mergeFlag.value
  localStorage.setItem('materialPlanMergeFlag', mergeFlag.value)
  getData()
}

onActivated(() => {
  getData()
})
</script>

<template>
  <div class="flex flex-col h-full overflow-hidden">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '76px' }">
        <DescriptionsFormItem label="单据编号">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售计划单号">
          <template #content>
            <el-input
              v-model="state.filterData.sale_product_plan_order_no"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售计划详情号">
          <template #content>
            <el-input
              v-model="state.filterData.sale_plan_order_item_no"
              clearable
            />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="客户编号:">
          <template #content>
            <SelectCustomerDialog v-model="state.filterData.customer_id" field="code" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:">
          <template #content>
            <SelectCustomerDialog v-model="state.filterData.customer_id" field="name" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单日期:" width="330">
          <template #content>
            <SelectDate v-model="state.filterData.order_time" />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="销售员名称:">
          <template #content>
            <el-form-item prop="sale_user_id">
              <SelectComponents v-model="state.filterData.sale_user_id" :query="{ duty: EmployeeType.salesman }" api="GetEmployeeListEnum" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="产品编号名称:">
          <template #content>
            <el-input
              v-model="state.filterData.product_code_and_name"
              clearable
            />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="颜色编号名称:">
          <template #content>
            <el-input
              v-model="state.filterData.color_code_and_name"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态">
          <template #content>
            <SelectComponents
              v-model="state.filterData.audit_status"
              api="GetAuditStatusEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售类型">
          <template #content>
            <el-select
              v-model="state.filterData.plan_type"
              clearable
              class="m-2"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="布种名称">
          <template #content>
            <el-input
              v-model="state.filterData.product_kind_name"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="mt-[5px] flex flex-col overflow-hidden h-full">
      <!-- <template v-slot:right-top> -->
      <!-- <BottonExcel v-has="'ProductSalePlanExport'" :loading="loadingExcel" @onClickExcel="handleExport" title="导出文件"></BottonExcel> -->
      <!-- <el-button v-has="'ProductSalePlanAdd'" style="margin-left: 10px" type="primary" :icon="Plus" @click="handleAdd">新建</el-button> -->
      <!-- </template> -->
      <template #right-top>
        <el-button type="primary" @click="mergeSpan">
          合并切换
        </el-button>
        <el-button type="primary" :loading="batchCancelLoading" plain :disabled="!state.multipleSelection.length" @click="handleBatchCancel">
          批量作废
        </el-button>
      </template>
      <Table
        v-if="!mergeFlag"
        :config="tableConfig"
        :table-list="data?.list"
        :column-list="columnList"
      >
        <template #link="{ row }">
          <el-link type="primary" :underline="false" @click="handDetail(row)">
            {{ row?.order_no }}
          </el-link>
        </template>
        <template #link1="{ row }">
          <el-link type="primary" :underline="false" @click="handSalesPlanDetail(row)">
            {{ row?.sale_product_plan_order_no }}
          </el-link>
        </template>
        <template #receipt_address="{ row }">
          {{ row?.location }}{{ row?.receipt_address }}
        </template>

        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'MaterialPlanDetail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'MaterialPlanEdit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.audit_status === 1"
              v-has="'MaterialPlanPass'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'MaterialPlanWait'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
      <Table
        v-if="mergeFlag"
        :config="tableConfig1"
        :table-list="data1?.list"
        :column-list="columnList1"
      >
        <template #link="{ row }">
          <el-link type="primary" :underline="false" @click="handDetail(row)">
            {{ row?.order_no }}
          </el-link>
        </template>
        <template #sale_product_plan_order_no="{ row }">
          <el-link type="primary" :underline="false" @click="handSalesPlanDetail(row)">
            {{ row?.sale_product_plan_order_no }}
          </el-link>
        </template>
        <template #receipt_address="{ row }">
          {{ row?.location }}{{ row?.receipt_address }}
        </template>
        <template #finish_product_gram_weight="{ row }">
          {{ row?.finish_product_gram_weight
          }}{{ row?.finish_product_gram_weight_unit_name }}
        </template>
        <template #finish_product_width="{ row }">
          {{ row?.finish_product_width
          }}{{ row?.finish_product_width_unit_name }}
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'MaterialPlanDetail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'MaterialPlanEdit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.audit_status === 1"
              v-has="'MaterialPlanPass'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'MaterialPlanWait'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
  </div>
</template>

<style lang="scss" scoped>
::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}

.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
