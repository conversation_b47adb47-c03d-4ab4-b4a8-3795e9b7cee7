<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import { GetProductionNotifyOrderDropdownList } from '@/api/productionNotice'
import { getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import Table from '@/components/Table.vue'

const emits = defineEmits(['handleSure'])

const state = reactive({
  filterData: {
    sale_system_id: '', // 营销体系
    weaving_mill_id: '', // 供方
    grey_fabric_code: '',
    grey_fabric_name: '',
    order_no: '',
    finish_status: 2,
  },
  showModal: false,
  modalName: '添加坯布',
  multipleSelection: [],
  sureMultipleSelection: [],
})

const { fetchData, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = GetProductionNotifyOrderDropdownList()

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

const tableConfig = reactive<any>({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  checkboxConfig: {
    trigger: 'row',
    checkField: 'selected',
    checkMethod: handleCheckMethod,
  },
  height: '100%',
  showCheckBox: true,
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})
function handleCheckMethod({ row }: any) {
  const isCheck = state.sureMultipleSelection.some((it: any) => it.id === row.id)
  if (isCheck) {
    // 已确认选择的数据--后续追加选择数据时，不再算入
    row.selected = true
    row.sureSelected = true
  }

  return !isCheck
}
function getData() {
  fetchData(getFilterData({ ...state.filterData, is_audit: true }))
}

const columnList = ref([
  {
    field: 'order_no',
    title: '生产通知单号',
    minWidth: 130,
  },
  // {
  //   field: 'sale_plan_order_item_no',
  //   title: '销售计划详情单号',
  //   minWidth: 150,
  // },
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    field: 'finish_product_width',
    soltName: 'finish_product_width',
    title: '成品幅宽',
    minWidth: 100,
  },
  {
    field: 'finish_product_gram_weight',
    soltName: 'finish_product_gram_weight',
    title: '成品克重',
    minWidth: 100,

    // isWeight: true,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    minWidth: 100,
  },
  {
    field: 'weight_of_fabric',
    title: '布匹定重',
    isWeight: true,
    minWidth: 100,
  },
  {
    field: 'unit_name',
    title: '单位',
    minWidth: 100,
  },
  {
    field: 'process_price',
    title: '加工单价',
    isUnitPrice: true,
    minWidth: 100,
  },
  {
    field: 'scheduling_roll',
    title: '排产匹数',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'scheduling_weight',
    title: '排产数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    field: 'produced_roll',
    title: '已产匹数',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'produced_weight',
    title: '已产数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    field: 'change_roll',
    title: '变更匹数',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'change_weight',
    title: '变更数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    field: 'producing_roll',
    title: '未产匹数',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'producing_weight',
    title: '未产数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
  {
    field: 'create_time',
    title: '创建时间',
    sortable: true,
    minWidth: 140,
    isDate: true,
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records.filter((e: any) => !e?.sureSelected)
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records.filter((e: any) => !e?.sureSelected)
}

function handCancel() {
  state.showModal = false
}

function handleSure() {
  if (!state.multipleSelection.length)
    return ElMessage.error('请选择一条数据')
  else
    emits('handleSure', state.multipleSelection)
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="80vw" height="80vh" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-col h-full overflow-hidden">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="生产通知单号:">
          <template #content>
            <el-input v-model="state.filterData.order_no" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布编号:">
          <template #content>
            <el-input v-model="state.filterData.grey_fabric_code" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布名称:">
          <template #content>
            <el-input v-model="state.filterData.grey_fabric_name" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem copies="2" label="完成状态：">
          <template #content>
            <div>
              <el-radio-group v-model="state.filterData.finish_status">
                <el-radio :label="1">
                  已完成
                </el-radio>
                <el-radio :label="2">
                  未完成
                </el-radio>
              </el-radio-group>
            </div>
          </template>
        </DescriptionsFormItem>
      </div>
      <div class="flex flex-col h-full overflow-hidden">
        <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList">
          <template #finish_product_width="{ row }">
            {{ row.finish_product_width }} {{ row.finish_product_width_unit_name }}
          </template>
          <template #finish_product_gram_weight="{ row }">
            {{ row.finish_product_gram_weight }} {{ row.finish_product_gram_weight_unit_name }}
          </template>
        </Table>
      </div>
    </div>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
