<script lang="ts" setup>
import { nextTick, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { VxeInputDefines } from 'vxe-pc-ui'
import { uniqBy } from 'lodash-es'
import currency from 'currency.js'
import { BusinessUnitIdEnum } from '@/common/enum'
import Table from '@/components/Table.vue'
import { list_enum } from '@/api/greyFabricPurchaseReturn'
import { debounce, getFilterData } from '@/common/util'
// import SelectDate from '@/components/SelectDate/index.vue'
// import SelectComponents from '@/components/SelectComponents/index.vue'
import { formatHashTag, formatWeightDiv, sumNum } from '@/common/format'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { autoCalcMaxLimit } from '@/pages/finishManagement/fpProcessingEntryOrder/useAutoEnter'

const emits = defineEmits(['handleSure'])

const state = reactive<any>({
  filterData: {
    raw_material_id: '',
    supplier_name: '',
    brand: '',
    batch_num: '',
    color_scheme: '',
    customer_id: '',
    unit_name: '',
    unit_id: '',
    stock_type: 0,
  },
  showModal: false,
  rowIndex: -1, // 用纱比例的数据的索引
  grayIndex: -1, // 坯布信息数据的数据的索引
  multipleSelection: [],
  list: [],
  parent_id: '', // 用纱比例的数据id
  grayId: '', // 坯布信息的数据id
  use_weight: 0,
})

const { fetchData, success, msg, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = list_enum()

const tableRef = ref()
const originTableRef = ref()

watch(
  () => state.list,
  () => {
    if (state.list?.length > 0) {
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)
const originList = ref([])
const getData = debounce(async () => {
  const query = getFilterData({
    ...state.filterData,
    is_stock_type_param: true,
    is_has_stock: true,
  })
  await fetchData(query)
  if (!success.value)
    return ElMessage.error(msg.value)

  originList.value = data.value.list?.map((item: any) => {
    item.selected
      = state.list?.some((citem: any) => {
        const isCheck = citem.rml_stock_id === item.id
        if (isCheck) {
          citem.max_use_yarn_quantity = formatWeightDiv(item.total_weight)
          citem.use_yarn_quantity = state.use_weight > formatWeightDiv(item.total_weight) ? formatWeightDiv(item.total_weight) : state.use_weight
        }
        return isCheck
      }) ?? false

    return item
  })
}, 400)

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  checkboxConfig: {
    trigger: 'row',
    checkField: 'selected',
  },
  showCheckBox: true,
  height: '100%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  unit_name: '',
  raw_name: '',
  raw_code: '',
})

const tableConfig_other = ref({
  height: '100%',
  showSort: false,
  showOperate: true,
  operateWidth: '80',
  footerMethod: (val: any) => FooterMethod(val),
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['use_yarn_quantity'].includes(column.property))
        return `${sumNum(data, 'use_yarn_quantity')}`

      return null
    }),
  ]
}

function handleSelectionChange({ checked, row }: any) {
  if (checked) {
    const { max } = autoCalcMaxLimit(row, 'use_yarn_quantity', state.list, state.use_weight)
    const total_weight = formatWeightDiv(row.total_weight)

    state.list.push({
      id: row.id,
      ...row,
      rml_stock_id: row.id,
      parent_id: state.parent_id,
      grayId: state.grayId,
      use_yarn_quantity: max > total_weight ? total_weight : max,
      total_weight,
      max_use_yarn_quantity: max > total_weight ? total_weight : max,
      raw_material_code: row.raw_material_code,
      raw_material_name: row.raw_material_name,
      unit_name: row.unit_name,
      supplier_name: row.supplier_name,
      customer_name: row.customer_name,
      brand: row.brand,
      batch_num: row.batch_num,
      color_scheme: row.color_scheme,
      level_name: row.level_name,
      raw_remark: row.remark,
      production_date: row.production_date,
      cotton_origin: row.cotton_origin,
      remark: row.remark,
      spinning_type: row.spinning_type,
      carton_num: row.carton_num,
      fapiao_num: row.fapiao_num,
    })
  }
  else {
    state.list = deleteById(row.id, state.list)
  }

  // state.multipleSelection = records
}

function handleBlurUseYarn(e: VxeInputDefines.BlurEventParams, rowIndex: number) {
  const row = state.list[rowIndex]
  const { max } = autoCalcMaxLimit(row, 'use_yarn_quantity', state.list, state.use_weight)
  row.max_use_yarn_quantity = max > row.total_weight ? row.total_weight : max
  row.use_yarn_quantity = Number(e.value) > row.max_use_yarn_quantity ? row.max_use_yarn_quantity : e.value
  state.list.forEach((item) => {
    if (item.id !== row.id)
      item.max_use_yarn_quantity = item.max_use_yarn_quantity > item.total_weight ? item.total_weight : item.max_use_yarn_quantity
  })
}

function handAllSelect({ checked, records }: any) {
  if (checked) {
    records.forEach((item: any) => {
      const { max } = autoCalcMaxLimit(item, 'use_yarn_quantity', state.list, state.use_weight)
      const total_weight = formatWeightDiv(item.total_weight)
      state.list.push({
        id: item.id,
        ...item,
        rml_stock_id: item.id,
        max_use_yarn_quantity: max > total_weight ? total_weight : max,
        total_weight,
        use_yarn_quantity: max > total_weight ? total_weight : max,
        raw_remark: item.remark,
      })
    })
    state.list = uniqBy(state.list, 'rml_stock_id') // 去重
  }
  else {
    state.list = []
  }

  // state.multipleSelection = records
}

const columnList = ref([
  {
    field: 'raw_material_code',
    title: '原料名称',
    minWidth: 140,
    soltName: 'raw_material_code',
  },
  // {
  //   field: 'raw_material_name',
  //   title: '原料名称',
  //   minWidth: 100,
  // },
  {
    field: 'unit_name',
    title: '织厂名称',
    minWidth: 100,
  },
  {
    field: 'supplier_name',
    title: '供应商',
    minWidth: 100,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    minWidth: 100,
  },
  {
    field: 'brand',
    title: '原料品牌',
    minWidth: 100,
  },
  {
    field: 'batch_num',
    title: '原料批号',
    minWidth: 100,
  },
  {
    field: 'color_scheme',
    title: '原料颜色',
    minWidth: 100,
  },
  {
    field: 'level_name',
    title: '原料等级',
    minWidth: 100,
  },
  {
    field: 'remark',
    title: '原料备注',
    minWidth: 100,
  },
  // {
  //   field: 'measurement_unit_name',
  //   title: '单位',
  //   minWidth: 100,
  // },
  {
    field: 'production_date',
    title: '生产日期',
    minWidth: 100,
    is_date: true,
  },
  {
    field: 'spinning_type',
    title: '纺纱类型',
    minWidth: 100,
  },
  {
    field: 'cotton_origin',
    title: '棉花产地',
    minWidth: 100,
  },
  {
    field: 'yarn_origin',
    title: '棉纱产地',
    minWidth: 100,
  },
  {
    field: 'carton_num',
    title: '装箱单号',
    minWidth: 100,
  },
  {
    field: 'fapiao_num',
    title: '发票号',
    minWidth: 100,
  },
  {
    field: 'total_weight',
    soltName: 'total_weight',
    title: '剩余用纱量',
    fixed: 'right',
    minWidth: 100,
    isWeight: true,
  },
])

const columnList_other = ref([
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 140,
    soltName: 'raw_material_code',
  },
  // {
  //   field: 'raw_material_name',
  //   title: '原料名称',
  //   minWidth: 100,
  // },
  {
    field: 'unit_name',
    title: '织厂名称',
    minWidth: 100,
  },
  {
    field: 'supplier_name',
    title: '供应商',
    minWidth: 100,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    minWidth: 100,
  },
  {
    field: 'brand',
    title: '原料品牌',
    minWidth: 100,
  },
  {
    field: 'batch_num',
    title: '原料批号',
    minWidth: 100,
  },
  {
    field: 'color_scheme',
    title: '原料颜色',
    minWidth: 100,
  },
  {
    field: 'level_name',
    title: '原料等级',
    minWidth: 100,
  },
  {
    field: 'raw_remark',
    title: '原料备注',
    minWidth: 100,
  },
  // {
  //   field: 'measurement_unit_name',
  //   title: '单位',
  //   minWidth: 100,
  // },
  {
    field: 'production_date',
    title: '生产日期',
    minWidth: 100,
    is_date: true,
  },
  {
    field: 'spinning_type',
    title: '纺纱类型',
    minWidth: 100,
  },
  {
    field: 'cotton_origin',
    title: '棉花产地',
    minWidth: 100,
  },
  {
    field: 'use_yarn_quantity',
    title: '用纱数量',
    minWidth: 100,
    fixed: 'right',
    soltName: 'use_yarn_quantity',
    required: true,
  },
  {
    field: 'remark',
    fixed: 'right',
    title: '备注',
    minWidth: 100,
    soltName: 'remark',
  },
])

function handCancel() {
  state.showModal = false
}

function handDelete(row: any, index: number) {
  state.list.splice(index, 1)
  originList.value?.map((item: any) => {
    if (item.id === row.rml_stock_id) {
      originTableRef.value.tableRef.setCheckboxRow(item, false)
      item.selected = false
      return item
    }
  })
}

async function handleSure() {
  if (!state.list.length)
    return ElMessage.error('至少选择一条数据')

  for (let i = 0; i < state.list.length; i++) {
    if (state.list[i].use_yarn_quantity === '')
      return ElMessage.error('用纱数量不可为空')
  }
  emits('handleSure', state)
}

function deleteById(id: number, list: any) {
  for (let index = list.length - 1; index >= 0; index--) {
    if (list[index] && list[index].rml_stock_id === id)
      list.splice(index, 1)
  }

  return list
}
// 自动分摊
function handleAutoEnter() {
  // 清空当前已选择的数据
  state.list = []
  originTableRef.value.tableRef.setAllCheckboxRow(false)
  // 按照total_weight从大到小排序，优先选择库存量大的原料
  // const sortedList = [...originList.value].sort((a, b) => {
  //   return formatWeightDiv(b.total_weight) - formatWeightDiv(a.total_weight)
  // })

  // 需要分摊的总量
  let remainingWeight = state.use_weight

  // 遍历排序后的列表，逐个选择直到达到需要的用纱量
  for (let i = 0; i < originList.value.length; i++) {
    const item = originList.value[i]

    // 如果已经达到需要的用纱量，则停止选择
    if (remainingWeight <= 0)
      break

    // 计算当前项可以分配的最大用纱量
    const total_weight = formatWeightDiv(item.total_weight)

    // 计算实际分配的用纱量（不超过剩余需要量和当前项的库存量）
    const useYarnQuantity = remainingWeight > total_weight ? total_weight : remainingWeight

    // 将当前项添加到已选列表
    state.list.push({
      id: item.id,
      ...item,
      rml_stock_id: item.id,
      parent_id: state.parent_id,
      grayId: state.grayId,
      use_yarn_quantity: useYarnQuantity,
      total_weight,
      max_use_yarn_quantity: useYarnQuantity,
      raw_material_code: item.raw_material_code,
      raw_material_name: item.raw_material_name,
      unit_name: item.unit_name,
      supplier_name: item.supplier_name,
      customer_name: item.customer_name,
      brand: item.brand,
      batch_num: item.batch_num,
      color_scheme: item.color_scheme,
      level_name: item.level_name,
      raw_remark: item.remark,
      production_date: item.production_date,
      cotton_origin: item.cotton_origin,
      remark: item.remark,
      spinning_type: item.spinning_type,
      carton_num: item.carton_num,
      fapiao_num: item.fapiao_num,
    })

    // 更新剩余需要分摊的用纱量
    remainingWeight = currency(remainingWeight).subtract(useYarnQuantity).value
  }

  // 更新originList中的selected状态
  originList.value = originList.value.map((item) => {
    item.selected = state.list.some(selectedItem => selectedItem.rml_stock_id === item.id)
    if (item.selected)
      originTableRef.value.tableRef.setCheckboxRow(item, true)

    return item
  })
}
defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer show-zoom title="用纱信息" width="90vw" height="80vh" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="list-page">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="原料编号:">
          <template #content>
            <!--          <SelectComponents api="rawmaterialMenu" label-field="code" value-field="id" v-model="state.filterData.raw_material_id" clearable /> -->
            <SelectDialog
              v-model="state.filterData.raw_material_id"
              label-field="code"
              value-field="id"
              :query="{ code: componentRemoteSearch.raw_code }"
              api="rawmaterialMenu"
              :column-list="[
                {
                  field: 'name',
                  title: '原料名称',
                  minWidth: 100,
                },
                {
                  field: 'code',
                  title: '原料编号',
                  minWidth: 100,
                },
              ]"
              :table-column="[
                {
                  field: 'code',
                  title: '编号',
                },
              ]"
              @on-input="val => (componentRemoteSearch.raw_code = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原料名称:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.raw_material_id"
              label-field="name"
              value-field="id"
              :query="{ name: componentRemoteSearch.raw_name }"
              api="rawmaterialMenu"
              :column-list="[
                {
                  field: 'name',
                  title: '原料名称',
                  colGroupHeader: true,
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'name',
                      title: '原料名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '原料编号',
                  minWidth: 100,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      title: '原料编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @on-input="val => (componentRemoteSearch.raw_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织厂名称:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.unit_id"
              api="business_unitlist"
              :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory, name: componentRemoteSearch.unit_name }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-value="val => (state.filterData.unit_name = val.name)"
              @change-input="val => (componentRemoteSearch.unit_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="供应商:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.supplier_name"
              :query="{
                unit_type_id: '11,20',
                name: componentRemoteSearch.name,
              }"
              api="BusinessUnitSupplierEnumlist"
              :column-list="[
                {
                  field: 'name',
                  title: '供应商名称',
                  minWidth: 100,
                  isEdit: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '供应商名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '供应商编号',
                  minWidth: 100,
                  isEdit: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '供应商编号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'address',
                  title: '地址',
                  minWidth: 100,
                  isEdit: true,
                  childrenList: [
                    {
                      field: 'address',
                      isEdit: true,
                      title: '地址',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :valid-config="{
                name: [
                  { required: true, message: '请输入名称' },
                  {
                    validator({ cellValue }) {
                      if (cellValue === '') {
                        new Error('供应商名称')
                      }
                    },
                  },
                ],
              }"
              :editable="true"
              @on-input="val => (componentRemoteSearch.name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="所属客户:">
          <template #content>
            <!-- <SelectComponents api="GetCustomerEnumList" label-field="name" value-field="id" v-model="state.filterData.customer_id" clearable /> -->
            <SelectDialog
              v-model="state.filterData.customer_id"
              :query="{ name: componentRemoteSearch.customer_name }"
              api="GetCustomerEnumList"
              :column-list="[
                {
                  field: 'name',
                  title: '客户名称',
                  colGroupHeader: true,
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'name',
                      title: '客户名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  colGroupHeader: true,
                  title: '客户编号',
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'code',
                      title: '客户编号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'address',
                  colGroupHeader: true,
                  title: '地址',
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'address',
                      title: '地址',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @on-input="val => (componentRemoteSearch.customer_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原料品牌:">
          <template #content>
            <el-input v-model="state.filterData.brand" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原料批号:">
          <template #content>
            <el-input v-model="state.filterData.batch_num" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原料颜色:">
          <template #content>
            <el-input v-model="state.filterData.color_scheme" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem copies="2" label="库存来源：">
          <template #content>
            <el-space>
              <el-radio-group v-model="state.filterData.stock_type">
                <el-radio :label="0">
                  全部
                </el-radio>
                <el-radio :label="1">
                  织厂纱库存
                </el-radio>
                <el-radio :label="2">
                  染纱厂坯纱库存
                </el-radio>
              </el-radio-group>
              <el-link :underline="false" type="primary" @click="handleAutoEnter">
                自动分摊
              </el-link>
            </el-space>
          </template>
        </DescriptionsFormItem>
      </div>
      <div class="table-card-full !flex-row ">
        <div class="left flex-1 overflow-hidden flex w-full">
          <Table ref="originTableRef" :config="tableConfig" :table-list="originList" :column-list="columnList">
            <template #raw_material_code="{ row }">
              {{ formatHashTag(row.raw_material_code, row.raw_material_name) }}
            </template>
            <template #total_weight="{ row }">
              {{ formatWeightDiv(row.total_weight) }} {{ row.measurement_unit_name }}
            </template>
          </Table>
        </div>
        <el-divider direction="vertical" class="h-full" />
        <div class="right flex-1 overflow-hidden flex  w-full">
          <Table ref="tableRef" :config="tableConfig_other" :table-list="state?.list" :column-list="columnList_other">
            <template #raw_material_code="{ row }">
              {{ formatHashTag(row.raw_material_code, row.raw_material_name) }}
            </template>
            <template #use_yarn_quantity="{ row, rowIndex }">
              <vxe-input v-model="row.use_yarn_quantity" type="float" :min="0" :max="row.max_use_yarn_quantity" placeholder="请输入" @blur="e => handleBlurUseYarn(e, rowIndex)">
                <template #suffix>
                  {{ row.measurement_unit_name }}
                </template>
              </vxe-input>
            </template>
            <template #remark="{ row }">
              <vxe-input v-model="row.remark" placeholder="请输入" />
            </template>
            <template #operate="{ row, rowIndex }">
              <el-button text type="danger" @click="handDelete(row, rowIndex)">
                删除
              </el-button>
            </template>
          </Table>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="flex justify-between">
        <div class="">
          已选 {{ state.list.length }} 条数据，需要录入数量为：{{ state.use_weight }}
        </div>
        <el-space>
          <el-button @click="handCancel">
            取消
          </el-button>
          <el-button type="primary" @click="handleSure">
            确认
          </el-button>
        </el-space>
      </div>
    </template>
  </vxe-modal>
</template>

<style scoped>
.container{
  display: flex;
  .left{

  }
  .right{

  }
}
</style>
