<script setup lang="ts" name="GreyClothOtherReceiptAdd">
import { ElMessage } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import AddFabricDialog from '../components/AddFabricDialog.vue'
import AddXimaDialog from '../components/AddXimaDialog.vue'
import { addGfmOtherReceiveOrder } from '@/api/greyClothOtherReceipt'
import { formatDate, formatPriceMul, formatWeightDiv, formatWeightMul, sumNum } from '@/common/format'
import { deepClone, getCurrentDate, getDefaultSaleSystem } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { BusinessUnitIdEnum, DictionaryType } from '@/common/enum'
import SelectDialog from '@/components/SelectDialog/index.vue'
import useRouterList from '@/use/useRouterList'
import { getDefaultCustomer } from '@/common/default'
import SelectRawMaterial from '@/components/SelectRawMaterial/index.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'

const routerList = useRouterList()

const bulkSetting = ref<any>({})

const state = reactive<any>({
  form: {
    marketingSystem: '', // 营销体系
    consignee: '', // 接收单位
    destprovidername: '', // 货源单位
    voucherNumber: '', // 凭证单号
    date: '', // 收货日期
    remark: '',
    dye_unit_use_order_no: '', // 染厂用坯单号
  },
  formRules: {
    marketingSystem: [{ required: true, message: '请选择营销体系', trigger: 'blur' }],
    consignee: [{ required: true, message: '请选择接收单位', trigger: 'change' }],
    destprovidername: [{ required: true, message: '请选择货源单位', trigger: 'change' }],
    date: [{ required: true, message: '请选择收货日期', trigger: 'blur' }],
  },
  tableList: [],
  isInformation: true,
  multipleSelection: [],
})

const componentRemoteSearch = reactive({
  name: '',
  unit_name: '',
})

onMounted(() => {
  state.form.date = getCurrentDate()
  const resDes = getDefaultSaleSystem()
  state.form.marketingSystem = resDes?.default_sale_system_id
})

const tableConfig = ref({
  // FooterMethod: (val: any) => FooterMethod(val),
  fieldApiKey: 'greyClothOtherReceiptEditAdd',
  showSlotNums: true,
  showOperate: true,
  operateWidth: '80',
  footerMethod: (val: any) => FooterMethod(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

const columnList = ref([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    fixed: 'left',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    fixed: 'left',
    minWidth: 150,
  },
  //   {
  //     field: 'document_code',
  //     title: '坯布采购单号',
  //     minWidth: 150,
  //   },
  {
    field: 'customer_id',
    title: '所属客户',
    minWidth: 150,
    soltName: 'SubordinateCustomer',
    required: true,
  },
  {
    field: 'raw_material_yarn_name',
    title: '原料纱名',
    minWidth: 150,
    soltName: 'raw_material_yarn_name',
  },
  {
    field: 'raw_material_batch_num',
    title: '原料批号',
    minWidth: 150,
    soltName: 'raw_material_batch_num',
  },
  {
    field: 'raw_material_batch_brand',
    title: '原料品牌',
    minWidth: 150,
    soltName: 'raw_material_batch_brand',
  },
  {
    field: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 180,
    soltName: 'breadth',
  },
  {
    field: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 180,
    soltName: 'weight',
  },
  {
    field: 'needle_size',
    title: '针寸数',
    minWidth: 150,
    soltName: 'needles',
  },
  //   {
  //     field: 'total_needle_size',
  //     title: '总针数',
  //     minWidth: 150,
  //     soltName: 'totalStitches',
  //   },
  {
    field: 'yarn_batch',
    title: '纱批',
    minWidth: 150,
    soltName: 'yarnBatch',
  },
  {
    field: 'gray_fabric_color_id',
    title: '织坯颜色',
    minWidth: 150,
    soltName: 'blankColor',
  },
  //   {
  //     field: 'weaving_process',
  //     title: '织造工艺',
  //     minWidth: 150,
  //     soltName: 'weavingProcess',
  //   },
  {
    field: 'grey_fabric_level_id',
    title: '坯布等级',
    minWidth: 150,
    soltName: 'greyFabricGrade',
  },
  {
    field: 'machine_number',
    title: '机台号',
    minWidth: 150,
    soltName: 'machineNumber',
  },
  {
    field: 'roll',
    title: '匹数',
    minWidth: 150,
    soltName: 'horsepower',
    required: true,
  },
  {
    field: 'remark',
    title: '细码',
    minWidth: 150,
    soltName: 'xima',
    required: true,
  },
  {
    field: 'total_weight',
    title: '总数量',
    minWidth: 150,
  },
  //   {
  //     field: 'single_price',
  //     title: '单价',
  //     minWidth: 150,
  //     soltName: 'unitPrice',
  //   },
  //   {
  //     field: 'other_price',
  //     title: '其他金额',
  //     minWidth: 150,
  //     soltName: 'otherPrice',
  //   },
  //   {
  //     field: 'total_price',
  //     title: '总金额',
  //     minWidth: 150,
  //     // soltName: 'totalPrice',
  //   },
  {
    field: 'grey_fabric_remark',
    title: '坯布备注',
    minWidth: 150,
    soltName: 'fabircRemark',
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 150,
    soltName: 'remark',
  },
])

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${sumNum(data, 'roll')}`

      if (['total_weight'].includes(column.property))
        return `${sumNum(data, 'total_weight')} kg`

      if (['other_price'].includes(column.property))
        return `￥${sumNum(data, 'other_price')}`

      if (['total_price'].includes(column.property))
        return `￥${sumNum(data, 'total_price')}`

      // if (['weight'].includes(column.property)) {
      //   return `${formatWeightDiv(sumNum(data, 'weight'))} KG`
      // }
      return null
    }),
  ]
}

const AddXimaDialogRef = ref()

function handWrite(row: any, rowIndex: number) {
  if (row.roll === '')
    return ElMessage.error('请先输入匹数')

  AddXimaDialogRef.value.state.showModal = true
  const arr = [
    {
      roll: row.roll,
      position: '',
      volume_number: 1,
      weight: formatWeightDiv(row?.weight_of_fabric || 0),
      uniq_id: AddXimaDialogRef.value.getUniqueId(),
    },
  ]
  if (!row?.item_fc_data?.length)
    AddXimaDialogRef.value.state.tableData = arr

  else
    AddXimaDialogRef.value.state.tableData = deepClone(row?.item_fc_data) || []

  AddXimaDialogRef.value.state.canEnter = row.roll
  AddXimaDialogRef.value.state.isAdd = true
  // AddXimaDialogRef.value.state.total_roll = row.roll
  AddXimaDialogRef.value.state.horsepower = row.roll
  AddXimaDialogRef.value.state.code = row.grey_fabric_code
  AddXimaDialogRef.value.state.name = row.grey_fabric_name
  AddXimaDialogRef.value.state.id = row.id
  AddXimaDialogRef.value.state.rowIndex = rowIndex

  AddXimaDialogRef.value.state.receivedRoll = Number(row.roll)
  AddXimaDialogRef.value.state.totalWeight = Number(row.total_weight)
  AddXimaDialogRef.value.state.weightOfFabric = formatWeightDiv(row.weight_of_fabric)
}

const AddFabricDialogRef = ref()
const showAdd = ref(false)

function handAdd(val: number) {
  if (val === 1) {
    state.isInformation = true
    showAdd.value = true
  }
  else {
    AddFabricDialogRef.value.state.showModal = true
    state.isInformation = false
    AddFabricDialogRef.value.state.apiString = val
  }
}

function handFabric(list: any) {
  showAdd.value = false
  list.forEach((item: any) => {
    // const idNum = Math.floor(Math.random() * 10000)
    state.tableList.push({
      ...item,
      grey_fabric_code: item?.code,
      grey_fabric_name: item?.name,
      //   document_code: item?.order_code || '',
      // customer_id: '',
      ...getDefaultCustomer(item?.customer_id),
      grey_fabric_width: item?.grey_fabric_width,
      grey_fabric_gram_weight: item?.grey_fabric_gram_weight,
      needle_size: item?.needle_size,
      //   total_needle_size: item?.total_needle_size,
      item_fc_data: [],
      yarn_batch: item?.yarn_batch,
      gray_fabric_color_id: item?.gray_fabric_color_id,
      //   weaving_process: item?.weaving_process,
      grey_fabric_level_id: item?.grey_fabric_level_id,
      machine_number: item?.machine_number,
      grey_fabric_remark: item.grey_fabric_remark,
      //   grey_fabric_purchase_code: item?.order_code,
      //   grey_fabric_purchase_item_id: item?.id,
      roll: 0,
      total_weight: 0,
      //   single_price: 0,
      //   other_price: 0,
      //   grey_fabric_remark: '',
      total_price: 0,
      //   item_source: state.isInformation ? 1 : 2,
      raw_material_yarn_name: '',
      raw_material_batch_num: '',
      raw_material_batch_brand: '',
      remark: '',
      grey_fabric_id: item?.id || 0,
      warehouse_sum_id: item.warehouse_sum_id || 0,
      selected: false,
      //   id: idNum,
    })
  })
  AddFabricDialogRef.value.state.showModal = false
}

// 录入细码
function handleSureXima(val: any) {
  state.tableList?.map((item: any, index: number) => {
    if (index === val.rowIndex) {
      item.item_fc_data = val.tableData
      item.total_weight = sumNum(val.tableData, 'weight')
      return item
    }
  })
  AddXimaDialogRef.value.state.showModal = false
}

const tableRef = ref()

watch(
  () => state.tableList,
  () => {
    if (state.tableList?.length > 0) {
      state.tableList?.map((item: any) => {
        item.total_price = Number(item?.single_price) * Number(item?.total_weight) + Number(item?.other_price) || 0
        return item
      })
      //

      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

// 选择客户
function handleChangeCustomer(val: any, row: any) {
  row.customer_name = val.name
  row.customer_code = val.code
}

const ruleFormRef = ref()

const { fetchData: addPost, data: addData, success: addSuccess, msg: addMsg } = addGfmOtherReceiveOrder()

// 提交数据
async function handleSure() {
  if (!state.tableList.length)
    return ElMessage.error('至少添加一条坯布信息')

  const list = deepClone(state.tableList)

  for (let i = 0; i < list.length; i++) {
    if (list[i].customer_id === '')
      return ElMessage.error('所属客户不可为空')

    if (list[i].roll === '')
      return ElMessage.error('匹数不可为空')

    if (Number(list[i].roll) - Number(sumNum(list[i]?.item_fc_data, 'roll')) !== 0 && Number(list[i].roll) !== 0)
      return ElMessage.error(`第${i + 1}行的细码必须录完`)

    // if (list[i].single_price === '') {
    //   return ElMessage.error('单价不可为空')
    // }

    list[i].grey_fabric_gram_weight = list[i]?.grey_fabric_gram_weight.toString()
    list[i].roll = Number(formatPriceMul(list[i].roll))
    // list[i].single_price = Number(formatPriceMul(list[i].single_price))
    // list[i].other_price = Number(formatPriceMul(list[i].other_price))
    list[i].roll = Number(list[i].roll)
    // 将坯布信息的数量乘以一千给后端
    for (let q = 0; q < list[i].item_fc_data?.length; q++) {
      if (list[i].item_fc_data[q].weight === '')
        return ElMessage.error('细码数量不可为空')

      if (list[i].item_fc_data[q].roll === '')
        return ElMessage.error('细码匹数不可为空')

      list[i].item_fc_data[q].weight = Number(formatWeightMul(list[i].item_fc_data[q].weight))
      list[i].item_fc_data[q].roll = Number(formatPriceMul(list[i].item_fc_data[q].roll))
    }
  }
  const query = {
    receive_date: formatDate(state.form.date),
    sale_system_id: state.form.marketingSystem,
    receive_unit_id: state.form.consignee,
    source_id: state.form.destprovidername,
    voucher_number: state.form.voucherNumber,
    remark: state.form.remark,
    dye_unit_use_order_no: state.form.dye_unit_use_order_no,
    item_data: list,
  }
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(query)

      if (addSuccess.value) {
        ElMessage.success('成功')
        routerList.push({ name: 'GreyClothOtherReceiptDetail', query: { id: addData.value.id } })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

function handDelete(index: number) {
  state.tableList.splice(index, 1)
}

const bulkShow = ref(false)

function bulkHand() {
  bulkShow.value = true
}

function handBulkClose() {
  bulkShow.value = false
}

async function bulkSubmit({ row, value }: any) {
  if (state.multipleSelection?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  state.tableList?.map((item: any) => {
    if (item?.selected) {
      item[row.field] = value[row.field]
      return item
    }
  })
  bulkShow.value = false
  ElMessage.success('设置成功')
}

const bulkList = reactive<any>([
  {
    field: 'customer_id',
    // field_name: 'customer_name',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
    query: { sale_system_id: state.form.marketingSystem },
  },
  {
    field: 'raw_material_yarn_name',
    title: '原料纱名',
    component: 'input',
    type: 'text',
  },
  {
    field: 'raw_material_batch_num',
    title: '原料批号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'raw_material_batch_brand',
    title: '原料品牌',
    component: 'input',
    type: 'text',
  },
  {
    field: 'grey_fabric_width',
    title: '幅宽',
    component: 'input',
    type: 'text',
  },
  {
    field: 'grey_fabric_gram_weight',
    title: '克重',
    component: 'input',
    type: 'float',
  },
  {
    field: 'needle_size',
    title: '针寸数',
    component: 'input',
    type: 'text',
  },
  //   {
  //     field: 'total_needle_size',
  //     title: '总针数',
  //     component: 'input',
  //     type: 'text',
  //   },
  {
    field: 'yarn_batch',
    title: '纱批',
    component: 'input',
    type: 'text',
  },
  {
    field: 'gray_fabric_color_id',
    title: '织坯颜色',
    component: 'select',
    api: 'getInfoProductGrayFabricColorList',
  },
  //   {
  //     field: 'weaving_process',
  //     title: '织造工艺',
  //     component: 'input',
  //     type: 'text',
  //   },
  {
    field: 'grey_fabric_level_id',
    title: '坯布等级',
    component: 'select',
    api: 'getInfoBaseGreyFabricLevelList',
  },
  {
    field: 'machine_number',
    title: '机台号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'roll',
    title: '匹数',
    component: 'input',
    type: 'float',
  },
  //   {
  //     field: 'single_price',
  //     title: '单价',
  //     component: 'input',
  //     type: 'float',
  //   },
  //   {
  //     field: 'other_price',
  //     title: '其他金额',
  //     component: 'input',
  //     type: 'float',
  //   },
  {
    field: 'grey_fabric_remark',
    title: '坯布备注',
    component: 'input',
    type: 'text',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])

function getMarketingSystem(val: any) {
  state.tableList.map((item: any) => {
    item.customer_id = ''
    return item
  })
  bulkList[0].query = { sale_system_id: val.id }
  bulkSetting.value.customer_id = ''
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem required label="营销体系:">
          <template #content>
            <el-form-item prop="marketingSystem">
              <SelectComponents
                v-model="state.form.marketingSystem"
                api="AdminsaleSystemgetSaleSystemDropdownList"
                label-field="name"
                value-field="id"
                clearable
                @select="val => getMarketingSystem(val)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="接收单位:">
          <template #content>
            <el-form-item prop="consignee">
              <SelectDialog
                v-model="state.form.consignee"
                :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory, name: componentRemoteSearch.unit_name }"
                api="BusinessUnitSupplierEnumlist"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-input="val => (componentRemoteSearch.unit_name = val)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="货源单位:">
          <template #content>
            <el-form-item prop="destprovidername">
              <SelectDialog
                v-model="state.form.destprovidername"
                :query="{ name: componentRemoteSearch.name }"
                api="BusinessUnitSupplierEnumlist"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-input="val => (componentRemoteSearch.name = val)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="收货日期:">
          <template #content>
            <el-form-item prop="date">
              <el-date-picker v-model="state.form.date" type="date" placeholder="收货日期" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据备注:" copies="2">
          <template #content>
            <el-form-item prop="remark">
              <el-input v-model="state.form.remark" placeholder="单据备注" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染厂用坯单号:" copies="1">
          <template #content>
            <el-form-item prop="dye_unit_use_order_no">
              <el-input v-model="state.form.dye_unit_use_order_no" placeholder="染厂用坯单号" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="坯布信息" class="mt-[5px]">
    <template #right-top>
      <el-button type="primary" @click="bulkHand">
        批量操作
      </el-button>
      <el-button type="primary" @click="handAdd(1)">
        从资料中添加
      </el-button>
      <!-- <el-button @click="handAdd(2)" :disabled="state.form.marketingSystem === '' || state.form.destprovidername === ''" type="primary">从坯布采购单中添加</el-button> -->
    </template>
    <Table ref="tableRef" :config="tableConfig" :table-list="state.tableList" :column-list="columnList">
      <template #SubordinateCustomer="{ row }">
        <SelectCustomerDialog
          v-model="row.customer_id"
          field="name"
          :query="{ sale_system_id: state.form.marketingSystem }"
          :default-value="{
            id: row.customer_id,
            name: row.customer_name,
            code: row.customer_code,
          }"
          @change-value="handleChangeCustomer($event, row)"
        />
      </template>
      <template #breadth="{ row }">
        <!-- <vxe-input v-model="row.grey_fabric_width" placeholder="请输入"></vxe-input> -->
        <el-input v-model="row.grey_fabric_width" clearable class="input-with-select">
          <template #append>
            <SelectComponents
              v-model="row.grey_fabric_width_unit_id"
              placeholder="单位"
              style="width: 80px"
              :query="{ dictionary_id: DictionaryType.width_unit }"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </el-input>
      </template>
      <template #weight="{ row }">
        <!-- <vxe-input type="number" v-model="row.grey_fabric_gram_weight" placeholder="请输入">
          <template #suffix>g</template>
        </vxe-input> -->
        <el-input v-model="row.grey_fabric_gram_weight" clearable class="input-with-select">
          <template #append>
            <SelectComponents
              v-model="row.grey_fabric_gram_weight_unit_id"
              placeholder="单位"
              style="width: 80px"
              :query="{ dictionary_id: DictionaryType.gram_weight_unit }"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </el-input>
      </template>
      <template #needles="{ row }">
        <vxe-input v-model="row.needle_size" placeholder="请输入" />
      </template>
      <template #totalStitches="{ row }">
        <vxe-input v-model="row.total_needle_size" placeholder="请输入" />
      </template>
      <template #yarnBatch="{ row }">
        <vxe-input v-model="row.yarn_batch" placeholder="请输入" />
      </template>
      <template #blankColor="{ row }">
        <SelectComponents v-model="row.gray_fabric_color_id" api="getInfoProductGrayFabricColorList" label-field="name" value-field="id" clearable />
      </template>
      <template #weavingProcess="{ row }">
        <vxe-input v-model="row.weaving_process" placeholder="请输入" />
      </template>
      <template #greyFabricGrade="{ row }">
        <SelectComponents
          v-model="row.grey_fabric_level_id"
          api="getInfoBaseGreyFabricLevelList"
          label-field="name"
          value-field="id"
          clearable
        />
      </template>
      <template #machineNumber="{ row }">
        <vxe-input v-model="row.machine_number" placeholder="请输入" />
      </template>
      <template #horsepower="{ row }">
        <vxe-input v-model="row.roll" :min="0" type="float" placeholder="必填" />
      </template>
      <template #xima="{ row, rowIndex }">
        <div class="flex items-center">
          <el-button type="primary" text link @click="handWrite(row, rowIndex)">
            录入
          </el-button>
          <div v-if="Number(sumNum(row?.item_fc_data, 'roll')) >= row.roll && row.roll > 0 && row.roll !== ''" class="text-[#b5b39f] ml-[5px]">
            (已录完)
          </div>
          <div v-else class="text-[#efa6ae] ml-[5px]">
            ({{ (row?.roll - Number(sumNum(row?.item_fc_data, 'roll'))).toFixed(2) }}匹未录)
          </div>
        </div>
      </template>
      <template #unitPrice="{ row }">
        <vxe-input v-model="row.single_price" type="float" placeholder="必填">
          <template #suffix>
            元/Kg
          </template>
        </vxe-input>
      </template>
      <template #otherPrice="{ row }">
        <vxe-input v-model="row.other_price" type="float" placeholder="请输入" />
      </template>
      <!-- <template #totalPrice="{ row }">
          {{ Number(row.single_price) * Number(row.total_weight) + Number(row.other_price) || 0 }}
        </template> -->
      <template #fabircRemark="{ row }">
        <vxe-input v-model="row.grey_fabric_remark" placeholder="请输入" />
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" placeholder="请输入" />
      </template>
      <template #raw_material_yarn_name="{ row }">
        <vxe-input v-model="row.raw_material_yarn_name" placeholder="请输入" />
      </template>
      <template #raw_material_batch_num="{ row }">
        <vxe-input v-model="row.raw_material_batch_num" placeholder="请输入" />
      </template>
      <template #raw_material_batch_brand="{ row }">
        <vxe-input v-model="row.raw_material_batch_brand" placeholder="请输入" />
      </template>
      <template #operate="{ rowIndex }">
        <el-button text type="danger" @click="handDelete(rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <SelectRawMaterial v-model="showAdd" @submit="handFabric" />
  <AddXimaDialog ref="AddXimaDialogRef" @handle-sure="handleSureXima" />
  <AddFabricDialog ref="AddFabricDialogRef" :api="state.isInformation ? 1 : 2" @handle-sure="handFabric" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
</template>

<style></style>
