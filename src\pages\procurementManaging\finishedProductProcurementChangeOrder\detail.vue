<template>
  <StatusColumn
    :order_no="detalData.order_num"
    :status="detalData.status"
    :status_name="detalData.status_name"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
    permission_cancel_key="FinishedProductProcurement_void"
    permission_reject_key="FinishedProductProcurement_reject"
    permission_pass_key="FinishedProductProcurement_PASS"
    permission_wait_key="FinishedProductProcurement_cancel"
  ></StatusColumn>
  <FildCard title="基础信息" :tool-bar="false">
    <div class="line"></div>
    <slot>
      <el-form ref="ruleFormRef" :model="state.form" label-width="60px" label-position="top">
        <el-descriptions :column="3" border size="small">
          <el-descriptions-item label="营销体系名称">
            {{ detalData.sale_system_name }}
          </el-descriptions-item>
          <el-descriptions-item label="供应商名称">
            {{ detalData.supplier_name }}
          </el-descriptions-item>
          <el-descriptions-item label="收货单位名称">
            {{ detalData.receipt_unit_name }}
          </el-descriptions-item>
          <el-descriptions-item label="采购日期">
            {{ formatDate(detalData.purchase_date) }}
          </el-descriptions-item>
          <el-descriptions-item label="收货日期">
            {{ formatDate(detalData.receipt_date) }}
          </el-descriptions-item>
          <el-descriptions-item label="收货地址">
            {{ detalData.receipt_Address }}
          </el-descriptions-item>
          <el-descriptions-item label="收货电话">
            {{ detalData.receipt_Phone }}
          </el-descriptions-item>
          <el-descriptions-item label="发票抬头">
            {{ detalData.fapiao_title }}
          </el-descriptions-item>
          <el-descriptions-item label="备注">
            {{ detalData.remark }}
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
    </slot>
  </FildCard>
  <FildCard title="成品信息" :tool-bar="false" class="mt-[10px]">
    <Table ref="tablesRef" :config="tableConfig" :tableList="detalData.items" :column-list="columnList"></Table>
  </FildCard>
</template>

<script lang="ts" setup name="FinishedProductProcurementChangeOrderDetail">
import { FinishProductCancel, FinishProductDetail, FinishProductPass, FinishProductReject, FinishProductVoid } from '@/api/finishedProductProcurement'
import { formatDate, formatPriceDiv, formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv, sumNum } from '@/common/format'
import { orderStatusConfirmBox } from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const router = useRoute()
const state = reactive({
  form: {
    sale_system_id: '',
    supplier_id: '',
    receipt_unit_id: '',
    purchase_date: '',
    receipt_date: '',
    fapiao_title: '',
    remark: '',
    sale_system_name: '',
    supplier_name: '',
    receipt_unit_name: '',
    receipt_address: '',
    receipt_phone: '',
    items: [] as any[],
  },
  tableData: [] as any[],
})

const { fetchData: detailFetch, data: detalData } = FinishProductDetail()

onMounted(async () => {
  getData()
})

const getData = async () => {
  await detailFetch({
    id: router.params.id,
  })
}

watch(
  () => detalData.value,
  () => {
    if (detalData.value) {
      detalData.value?.items?.map((item: any) => {
        item.piece_weight = formatWeightDiv(item.piece_weight)
        item.unit_price = formatUnitPriceDiv(item.unit_price)
        item.length = formatTwoDecimalsDiv(item.length)
        item.length_unit_price = formatUnitPriceDiv(item.length_unit_price)
        item.paper_tube_weight = formatWeightDiv(item.paper_tube_weight)
        item.piece_count = formatTwoDecimalsDiv(item.piece_count)
      })
    }
  }
)

const updateStatus = async (audit_status: number) => {
  const id: number = parseInt(router.params.id as string)
  if (audit_status === 4) {
    await orderStatusConfirmBox({ id, message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' }, api: FinishProductVoid })
  }
  if (audit_status === 3) {
    await orderStatusConfirmBox({ id, message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' }, api: FinishProductReject })
  }
  if (audit_status === 2) {
    await orderStatusConfirmBox({ id, message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' }, api: FinishProductPass })
  }
  if (audit_status === 1) {
    await orderStatusConfirmBox({ id, message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' }, api: FinishProductCancel })
  }
  getData()
}

const columnList = ref([
  {
    title: '',
    childrenList: [
      {
        field: 'finish_product_code',
        title: '成品编号',
        width: 150,
      },
      {
        field: 'finish_product_name',
        title: '成品名称',
        width: 100,
      },
      {
        field: 'customer_name',
        title: '所属客户',
        width: 100,
      },
      {
        field: 'color_type_name',
        title: '颜色类别',
        width: 100,
      },
      {
        field: 'color_code',
        title: '颜色编号',
        width: 100,
      },
      {
        field: 'fabric_type_name',
        title: '布种类型',
        width: 100,
      },
      {
        field: 'color_Name',
        title: '颜色名称',
        width: 100,
      },
      {
        field: 'dye_factory_color',
        title: '染厂颜色',
        width: 100,
      },
      {
        field: 'dye_factory_color_num',
        title: '染厂色号',
        width: 100,
      },
      {
        field: 'dye_factory_vat_code',
        title: '染厂缸号',
        width: 100,
        isPrice: true,
      },
      {
        field: 'finish_product_level',
        title: '成品等级',
        width: 100,
        isPrice: true,
      },
      {
        field: 'dye_craft',
        title: '染整工艺',
        width: 100,
        isPrice: true,
      },
      {
        field: 'finish_product_width',
        title: '成品幅宽',
        width: 100,
      },
      {
        field: 'finish_product_gram_weight',
        title: '成品克重',
        width: 100,
      },
      {
        field: 'paper_tube_weight',
        title: '纸筒重量',
        width: 100,
      },
      {
        field: 'finish_product_ingredient',
        title: '成品成分',
        width: 100,
      },
      {
        field: '', // TODO 后端没返回
        title: '成品工艺',
        width: 100,
      },
      {
        field: 'remark',
        title: '备注',
        width: 100,
      },
    ],
  },
  {
    title: '采购数',
    childrenList: [
      {
        field: 'piece_count',
        title: '匹数',
        width: 100,
        isPrice: true,
      },
      {
        field: 'piece_weight',
        title: '均重',
        width: 100,
        isWeight: true,
      },
      {
        field: '', // TODO 后端没返回
        title: '数量总计',
        width: 100,
        isWeight: true,
      },
      {
        field: 'unit',
        title: '单位',
        width: 100,
      },
      {
        field: 'length',
        title: '辅助数量',
        width: 100,
        isPrice: true,
      },
    ],
  },
  {
    title: '进仓数',
    childrenList: [
      {
        field: 'piece_count',
        title: '匹数',
        width: 100,
        isPrice: true,
      },
      {
        field: 'piece_weight',
        title: '均重',
        width: 100,
        isWeight: true,
      },
      {
        field: '', // TODO 后端没返回
        title: '数量总计',
        width: 100,
        isWeight: true,
      },
      {
        field: 'length',
        title: '辅助数量',
        width: 100,
        isPrice: true,
      },
    ],
  },
  {
    title: '变更数',
    childrenList: [
      {
        field: 'piece_count',
        title: '匹数',
        width: 100,
        isPrice: true,
      },
      {
        field: 'piece_weight',
        title: '均重',
        width: 100,
        isWeight: true,
      },
      {
        field: '', // TODO 后端没返回
        title: '数量总计',
        width: 100,
        isWeight: true,
      },
      {
        field: 'length',
        title: '辅助数量',
        width: 100,
        isPrice: true,
      },
    ],
  },
])

const tablesRef = ref()

const footerCellClassName = ref<any>((row: any) => {})
const FooterMethod = ({ columns, data }: any) => {
  const footData: any = {}
  const res = [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex)) {
        return '汇总'
      }
      if (['piece_count'].includes(column.property)) {
        footData[column.property] = formatPriceDiv(sumNum(data, 'piece_count') as any)
        return footData[column.property]
      }
    }),
  ]
  tableConfig.value.footerCellClassName = ({ column }: any) => {
    if (column.field === 'piece_count') {
      if (footData[column.field] > 0) {
        return 'col-red'
      } else if (footData[column.field] < 0) {
        return 'col-green'
      } else {
        return ''
      }
    }
  }
  return res
}

const tableConfig = ref({
  showSpanHeader: true,
  operateWidth: 100,
  footerMethod: (val: any) => FooterMethod(val),
  footerCellClassName: footerCellClassName.value,
})
</script>

<style lang="scss" scoped>
::v-deep(.col-green) {
  color: green;
}

::v-deep(.col-red) {
  color: red;
}
</style>
