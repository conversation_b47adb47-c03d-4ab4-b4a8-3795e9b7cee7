<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import FineSizeSelectStockDetail from '../components/FineSizeSelectStockDetail.vue'
import {
  getFpmPrtOutOrder,
  updateFpmPrtOutOrderStatusCancel,
  updateFpmPrtOutOrderStatusPass,
  updateFpmPrtOutOrderStatusReject,
  updateFpmPrtOutOrderStatusWait,
} from '@/api/fpPurchaseReturnDeliverGodown'
import { formatDate } from '@/common/format'
import { orderStatusConfirmBox } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import { processDataOut } from '@/common/handBinary'

const form_options = [
  {
    text: '营销体系名称',
    key: 'sale_system_name',
  },
  {
    text: '供应商名称',
    key: 'biz_unit_name',
  },
  {
    text: '仓库名称',
    key: 'warehouse_name',
  },
  {
    text: '出仓日期',
    key: 'warehouse_out_time',
  },
  {
    text: '仓管员',
    key: 'store_keeper_name',
  },
  {
    text: '凭证号',
    key: 'voucher_number',
  },
  {
    text: '订单类型',
    key: 'sale_mode_name',
  },
  {
    text: '备注',
    key: 'remark',
    copies: 2,
  },
]
const route = useRoute()

const state = reactive<any>({
  baseData: {
    order_no: '',
    audit_status_name: '',
    audit_status: 1,
    arrange_order_no: '',
  },
})
const { fetchData, data: detailData } = getFpmPrtOutOrder()
onMounted(() => {
  getData()
})

// 成品信息
const finishProductionOptions = reactive<any>({
  detailShow: false,
  tableConfig: {
    fieldApiKey: 'FpPurchaseReturnDeliverGodownDetail',
    showSlotNums: false,
    showSpanHeader: true,
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 总数量 纸筒总重（kg） 结算数量 进仓辅助数量 采购辅助数量 进仓金额
        if (['total_weight'].includes(column.field))
          return sumNum(data, 'total_weight', '', 'float')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '', 'float')

        if (['out_length'].includes(column.field))
          return sumNum(data, 'out_length', '', 'float')

        if (['other_price'].includes(column.field))
          return sumNum(data, 'other_price', '￥', 'float')

        if (['total_price'].includes(column.field))
          return sumNum(data, 'total_price', '￥', 'float')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      field: 'A',
      childrenList: [
        {
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'B',
      title: '成品信息',
      childrenList: [
        {
          field: 'quote_order_no',
          title: '采购退货单号',
          minWidth: 100,
        },
        {
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },
        {
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          field: 'out_roll',
          title: '出仓匹数',
          minWidth: 100,
        },
        {
          field: 'unit_name',
          title: '单位',
          width: 70,
        },
      ],
    },
    {
      title: '结算单位',
      field: 'F',
      childrenList: [
        {
          field: 'auxiliary_unit_name',
          title: '结算单位',
          width: 100,
        },
      ],
    },
    {
      field: 'C',
      title: '数量单价',
      childrenList: [
        {
          field: 'total_weight',
          title: '出仓数量',
          minWidth: 100,
        },
        {
          field: 'weight_error',
          title: '码单空差',
          minWidth: 100,
        },
        {
          field: 'settle_error_weight',
          title: '结算空差',
          minWidth: 100,
        },
        {
          field: 'settle_weight',
          title: '结算数量',
          minWidth: 100,
        },
        {
          field: 'unit_price',
          title: '基本单位单价',
          minWidth: 120,
        },
      ],
    },
    {
      field: 'D',
      title: '辅助数量单价',
      childrenList: [
        {
          field: 'out_length',
          title: '辅助数量',
          minWidth: 100,
        },
        {
          field: 'length_unit_price',
          title: '辅助数量单价',
          minWidth: 120,
        },
      ],
    },
    {
      field: 'E',
      title: '金额信息',
      childrenList: [
        {
          field: 'other_price',
          soltName: 'other_price',
          title: '其他金额',
          minWidth: 100,
        },
        {
          field: 'total_price',
          soltName: 'total_price',
          title: '进仓金额',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'F',
      title: '单据备注信息',
      childrenList: [
        {
          field: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'G',
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: 'xima',
          soltName: 'xima',
          title: '细码',
          width: 100,
        },
      ],
    },
  ],
})
async function getData() {
  await fetchData({ id: route.query.id })
  state.baseData = {
    ...detailData.value,
    warehouse_out_time: formatDate(detailData.value.warehouse_out_time),
  }
  finishProductionOptions.datalist = processDataOut(detailData.value?.item_data)
}

async function updateStatus(audit_status: number) {
  const id: any = route.query.id?.toString()
  const options: Record<number, any> = {
    1: {
      message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' },
      api: updateFpmPrtOutOrderStatusWait,
    },
    2: {
      message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' },
      api: updateFpmPrtOutOrderStatusPass,
    },
    3: {
      message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' },
      api: updateFpmPrtOutOrderStatusReject,
    },
    4: {
      message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' },
      api: updateFpmPrtOutOrderStatusCancel,
    },
  }
  const { message, api } = options[audit_status]
  await orderStatusConfirmBox({ id, audit_status, message, api })
  getData()
}

const FineSizeSelectStockDetailRef = ref()
function showDialog(row: any) {
  FineSizeSelectStockDetailRef.value.showDialog(row, true)
}
</script>

<template>
  <StatusColumn
    :order_no="state.baseData.order_no"
    :order_id="state.baseData.id"
    :cloth_order_no="state.baseData.arrange_order_no"
    :status="state.baseData.audit_status"
    :status_name="state.baseData.audit_status_name"
    permission_print_key=""
    permission_wait_key="FpPurchaseReturnDeliverGodown_wait"
    permission_reject_key="FpPurchaseReturnDeliverGodown_reject"
    permission_pass_key="FpPurchaseReturnDeliverGodown_pass"
    permission_cancel_key="FpPurchaseReturnDeliverGodown_cancel"
    permission_edit_key="FpPurchaseReturnDeliverGodown_edit"
    edit_router_name="FpPurchaseReturnDeliverGodownEdit"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  />
  <FildCard class="mt-[5px]" title="基础信息" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem v-for="(item, index) in form_options" :key="index" :copies="item.copies || 1" :label="`${item.text}:`">
        <template #content>
          {{ state.baseData[item.key] }}
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]">
    <Table :config="finishProductionOptions.tableConfig" :table-list="finishProductionOptions.datalist" :column-list="finishProductionOptions.columnList">
      <template #unit_price="{ row }">
        ￥{{ row.unit_price }}
      </template>
      <template #length_unit_price="{ row }">
        ￥{{ row.length_unit_price }}
      </template>
      <template #other_price="{ row }">
        ￥{{ row.other_price }}
      </template>
      <template #total_price="{ row }">
        ￥{{ row.total_price }}
      </template>
      <template #xima="{ row }">
        <el-link @click="showDialog(row)">
          查看
        </el-link>
      </template>
    </Table>
  </FildCard>
  <FineSizeSelectStockDetail ref="FineSizeSelectStockDetailRef" />
</template>

<style lang="scss" scoped>
.oreder_code {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.el-link {
  color: #0e7eff;
}
</style>
