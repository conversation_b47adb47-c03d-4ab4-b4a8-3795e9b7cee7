<script setup lang="ts" name="FpSaleEntryOrderAdd">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import AccordingLibAdd from '../components/AccordingLibAdd.vue'
import AccordingSaleAdd from '../components/AccordingSaleAdd.vue'
import FineSizeAdd from '../components/FineSizeSaleEntryAdd.vue'
import FineSizeSaleReturnAdd from '../components/FineSizeSaleReturnAdd.vue'
import { FinishedProductTable } from './pullData' // 涉及的表格元信息
import { addFpmSaleReturnInOrder } from '@/api/fpSaleEntryOrder'
import { DictionaryType, EmployeeType, WarehouseTypeIdEnum } from '@/common/enum'
import { formatDate } from '@/common/format'
import { deleteToast, getDefaultSaleSystem, isMainUnit } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'
import { sumNum } from '@/util/tableFooterCount'
// import { useRouter } from 'vue-router'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { processDataIn } from '@/common/handBinary'
import { SaleModeEnum } from '@/enum/orderEnum'
import SelectSaleMode from '@/components/SelectSaleMode/index.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'

const routerList = useRouterList()
// const router = useRouter()
const AccordingLibAddRef = ref()
const FineSizeAddRef = ref()
const AccordingSaleAddRef = ref()
const FineSizeSaleReturnAddRef = ref()
const formRef = ref()
const state = reactive<any>({
  formInline: {
    sale_system_id: '',
    customer_id: '',
    voucher_number: '',
    warehouse_id: '',
    store_keeper_id: '',
    warehouse_in_time: new Date(),
    remark: '',
    return_remark: '',
    sale_mode: SaleModeEnum.Bulk,
  },
  formRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    customer_id: [{ required: true, message: '请选择客户名称', trigger: 'change' }],
    warehouse_id: [{ required: true, message: '请选择仓库', trigger: 'change' }],
    warehouse_in_time: [{ required: true, message: '请选择进仓日期', trigger: 'change' }],
    return_remark: [{ required: true, message: '请输入退货原因', trigger: 'blur' }],
    sale_mode: [{ required: true, message: '请选择订单类型', trigger: 'change' }],
  },
  defaultData: {
    customer_id: 0,
  },
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  customer_name_row: '',
  product_code: '',
  product_name: '',
  color_name: '',
  color_code: '',
})

let uuid = 0
const finishProductionOptions = reactive<any>({
  multipleSelection: [],
  rowIndex: 0,
  tableConfig: {
    fieldApiKey: 'FinishedProductTableApiKeyAdd',
    showSlotNums: false,
    showSpanHeader: true,
    showCheckBox: true,
    filterStatus: false,
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
    handAllSelect: (val: any) => handAllSelect(val),
    handleSelectionChange: (val: any) => handleSelectionChange(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 采购匹数 采购总数量 进仓数量 空差 结算数量 采购辅助数量 进仓辅助数量 进仓金额
        if (['out_length'].includes(column.field))
          return sumNum(data, 'out_length', '', 'float')

        if (['in_weight'].includes(column.field))
          return sumNum(data, 'in_weight', '', 'float')

        if (['weight_error'].includes(column.field))
          return sumNum(data, 'weight_error', '', 'float')

        if (['actually_weight'].includes(column.field))
          return sumNum(data, 'actually_weight', '', 'float')

        if (['settle_error_weight'].includes(column.field))
          return sumNum(data, 'settle_error_weight', '', 'float')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '', 'float')

        return null
      }),
    ]
  },
  datalist: [],
  // 确认添加带出数据
  handleSure: (list: any) => {
    // 自动带出字段
    list = list.map((item: any) => {
      const temp = {
        ...item,
        uuid: ++uuid,
        customer_id: state.defaultData.customer_id,
        product_id: item.id,
        product_code: item.finish_product_code, // 成品编号
        product_name: item.finish_product_name, // 成品名称
        product_width: item.finish_product_width, // 成品幅宽
        product_gram_weight: item.finish_product_gram_weight, // 成品克重
        product_craft: item.finish_product_craft, // 成品工艺
        product_ingredient: item.finish_product_ingredient, // 成品成分
        product_level_id: item.finish_product_level_id || undefined, // 成品等级
        product_remark: item.finish_product_remark, // 成品备注
        unit_name: item.measurement_unit_name, // 单位
        unit_id: item.measurement_unit_id, // 单位
        item_fc_data: [],
        in_roll: undefined,
        out_roll: 0,
        out_weight: 0,
        out_length: 0,
        in_weight: 0,
        weight_error: 0,
        settle_error_weight: 0,
        actually_weight: 0,
        settle_weight: 0,
        in_length: 0,
        return_price: '',
        length_cut_return_price: '',
      }
      return conductUnitPrice(temp, true)
    })
    finishProductionOptions.datalist = [...finishProductionOptions.datalist, ...list]
  },
  // 确认录入，需要统计部分字段
  handleSureFineSize: (list: any) => {
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].item_fc_data = list
    // 自动计算
    let in_weight = 0
    let in_length = 0
    let weight_error = 0
    list.forEach((item: any) => {
      in_weight += Number(item.base_unit_weight)
      in_length += Number(item.length)
      weight_error += Number(item.weight_error)
    })
    // const in_weight = list.reduce((pre: any, val: any) => pre + Number(val.base_unit_weight), 0)
    // const in_length = list.reduce((pre: any, val: any) => pre + Number(val.length), 0)
    // const weight_error = list.reduce((pre: any, val: any) => pre + Number(val.weight_error), 0)
    const actually_weight = in_weight - weight_error
    // 所有细码的结算空差
    const settle_error_weight = list.reduce((pre: any, val: any) => pre + Number(val.settle_error_weight), 0)
    const settle_weight = actually_weight - settle_error_weight
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].in_weight = Number(in_weight.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].weight_error = Number(weight_error.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].actually_weight = Number(actually_weight.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].settle_error_weight = Number(settle_error_weight.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].settle_weight = Number(settle_weight.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].in_length = Number(in_length.toFixed(2))
  },
  // 确认添加带出数据
  handleSureFinishProduciton(list: any) {
    list = list.map((item: any) => {
      let temp = {
        ...item,
        // 成品编号  成品名称 色号 颜色 成品等级 成品备注 进仓匹数 出货匹数 出货数量 出货辅助数量
        // 不需要了 细码带出  匹数 仓位 卷号  基本单位数量 码单空差 结算空差 纸筒重量 辅助数量 成品幅宽 成品克重 染厂色号 染厂缸号
        quote_order_no: item.order_no,
        quote_order_item_id: item.id,
        customer_id: item.customer_id,
        dye_factory_dyelot_number: item.dyelot_number,
        product_code: item.product_code,
        product_name: item.product_name,
        product_id: item.product_id,
        product_craft: item.product_craft,
        product_ingredient: item.product_ingredient,
        product_color_name: item.product_color_name,
        product_color_code: item.product_color_code,
        product_color_id: item.product_color_id,
        finishProductionColorId: item.product_color_id,
        product_level_id: item.product_level_id,
        // product_remark: item.remark,
        in_roll: item.roll ? item.roll : undefined,
        out_roll: item.roll,
        out_weight: item.weight,
        out_length: item.length,
        item_fc_data: [],
        product_gram_weight: item.product_gram_weight,
        product_width: item.product_width,
        product_remark: item.remark,
      }
      temp = conductUnitPrice(temp, true)
      temp.return_price = isMainUnitFormat(temp) ? item.sale_price : 0
      temp.length_cut_return_price = isMainUnitFormat(item) ? item.length_cut_return_price : 0
      return temp
    })
    finishProductionOptions.datalist = [...finishProductionOptions.datalist, ...list]
  },
  //   删除
  handleRowDel: async ({ id }: any) => {
    const res = await deleteToast('是否确认删除该成品')
    if (!res)
      return
    const index = finishProductionOptions.datalist.findIndex((item: any) => item.id === id)
    finishProductionOptions.datalist.splice(index, 1)
  },
  //   批量操作
  handEdit: () => handEdit,
})
// 计算进仓金额
// const computedKeys = () => {}
// 色号和颜色联动
function setFinishProductColor(item: any, row: any) {
  row.finishProductionColorId = item?.id
  row.product_color_id = item?.id
  row.product_color_name = item?.product_color_name
  row.product_color_code = item?.product_color_code
}

// 表格选中事件
function handAllSelect({ records }: any) {
  finishProductionOptions.multipleSelection = records
}
function handleSelectionChange({ records }: any) {
  finishProductionOptions.multipleSelection = records
}

// 批量操作
const bulkShow = ref(false)
function handEdit() {
  if (finishProductionOptions.multipleSelection.length < 1)
    return ElMessage.error('请选择数据')

  bulkShow.value = true
}
const bulkSetting = ref<any>({})
const bulkList = reactive<any>([
  {
    field: 'customer_id',
    field_name: 'customer_id',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
    query: {
      sale_system_id: state.formInline.sale_system_id,
    },
  },
  // {
  //   field: 'dye_factory_color_code',
  //   title: '染厂色号',
  //   component: 'input',
  // },
  {
    field: 'dye_factory_dyelot_number',
    title: '染厂缸号',
    component: 'input',
    type: 'text',
  },
  // {
  //   field: 'product_width',
  //   title: '成品幅宽',
  //   component: 'input',
  // },
  // {
  //   field: 'product_gram_weight',
  //   title: '成品克重(g)',
  //   component: 'input',
  //   type: 'integer',
  // },
  {
    field: 'product_level_id',
    field_name: 'product_level_id',
    title: '成品等级',
    component: 'select',
    api: 'GetInfoBaseFinishedProductLevelEnumList',
  },
  {
    field: 'product_remark',
    title: '成品备注',
    component: 'input',
    type: 'text',
  },
  {
    field: 'in_roll',
    title: '进仓匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'return_price',
    title: '数量退货单价',
    component: 'input',
    type: 'float',
  },
  {
    field: 'in_length',
    title: '进仓辅助数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'length_cut_return_price',
    title: '辅助数量退货单价',
    component: 'input',
    type: 'float',
  },
  // {
  //   field: 'other_price',
  //   title: '其他金额',
  //   component: 'input',
  //   type: 'float',
  // },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])
async function bulkSubmit({ row, value }: any, val: any) {
  const ids = finishProductionOptions.multipleSelection.map((item: any) => item.uuid)
  finishProductionOptions.datalist.map((item: any) => {
    if (ids.includes(item.uuid)) {
      item[row.field] = value[row.field]
      if (row.field === 'finishProductionColorId') {
        item.product_color_name = val?.product_color_name
        item.product_color_code = val?.product_color_code
        item.product_color_id = val?.id
      }
    }
  })
  if (row.field)
    ElMessage.success('设置成功')
  handBulkClose()
}
function handBulkClose() {
  bulkShow.value = false
}
// 根据资料添加
function showLibDialog() {
  AccordingLibAddRef.value.state.showModal = true
}

// 默认生成出仓条数数据
function showFineSizeDialog(row: any, rowIndex: number) {
  if (!state.formInline.warehouse_id) // 仓库必选
    return ElMessage.error('请先选择仓库')

  // FineSizeAddRef.value.state.showModal = true
  finishProductionOptions.rowIndex = rowIndex
  if (row.quote_order_no)
    FineSizeSaleReturnAddRef.value.showDialog(row, state.formInline)
  else
    FineSizeAddRef.value.showDialog(row, state.formInline)
}
// 根据销售送货单添加
function showFinishProductionDialog() {
  AccordingSaleAddRef.value.showModal({
    customer_id: state.formInline.customer_id,
    sale_system_id: state.formInline.sale_system_id,
  })
}

function getSFRoll(row: any) {
  return row.item_fc_data.reduce((pre: any, val: any) => pre + val.roll, 0)
}

function getUnInRoll(row: any) {
  if (row.in_roll === '' || row.in_roll === null || row.in_roll === undefined)
    return false

  else
    return true
}
// 新增提交
const { fetchData: addFetch, data: successData, success: addSuccess, msg: addMsg } = addFpmSaleReturnInOrder()
// 提交所有数据
function submitAddAllData() {
  // 表单验证
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 成品信息必填项
      // 判断戏码匹数和还有辅助数量和
      // let ximaLengthFlag = true // xima必须和进仓匹数一致
      // let lengthFlag = true // 辅助数量和与分录行辅助数量不一致
      for (let i = 0; i < finishProductionOptions.datalist.length; i++) {
        const item = finishProductionOptions.datalist[i]
        if (!finishProductionOptions.datalist[i].product_code)
          return ElMessage.error('成品编号为必填项')

        if (!finishProductionOptions.datalist[i].product_name)
          return ElMessage.error('成品名称为必填项')

        if (!finishProductionOptions.datalist[i].product_color_name)
          return ElMessage.error('色号为必填项')

        if (!finishProductionOptions.datalist[i].product_color_code)
          return ElMessage.error('颜色为必填项')

        if (!finishProductionOptions.datalist[i].dye_factory_dyelot_number && !finishProductionOptions.datalist[i]?.quote_order_no?.length)
          return ElMessage.error('染厂缸号为必填项')

        if (finishProductionOptions.datalist[i].in_roll === '' || finishProductionOptions.datalist[i].in_roll === undefined || finishProductionOptions.datalist[i].in_roll === null)
          return ElMessage.error('进仓匹数为必填项')

        if (!Number(finishProductionOptions.datalist[i].in_weight))
          return ElMessage.error('进仓数量为必填项且不能为0')

        if (!item.auxiliary_unit_id)
          return ElMessage.error(`成品编号为${item.product_code}的数据,结算单位不能为空`)

        if (!Number(item.in_length) && !isMainUnitFormat(item))
          return ElMessage.error(`成品编号为${item.product_code}的数据,辅助数量不能为空且不能为0`)

        if (item.return_price === '' && isMainUnitFormat(item))
          return ElMessage.error(`成品编号为${item.product_code}的数据,退货单价不能为空`)

        if (item.length_cut_return_price === '' && !isMainUnitFormat(item))
          return ElMessage.error(`成品编号为${item.product_code}的数据,退货单价不能为空`)

        // 如果是送货单进仓匹数不能大于出库单
        if (finishProductionOptions.datalist[i].quote_order_no?.length && Number(finishProductionOptions.datalist[i]?.in_roll) > Number(finishProductionOptions.datalist[i]?.out_roll))
          return ElMessage.error('销售送货单的进仓匹数不能大于出库单')

        if (finishProductionOptions.datalist[i]?.item_fc_data?.length) {
          let roll = 0
          let length = 0
          finishProductionOptions.datalist[i].item_fc_data.forEach((item: any) => {
            roll += Number(item.roll)
            length += Number(item.length)
          })
          roll = Number(roll.toFixed(2))
          length = Number(length.toFixed(2))
          // const roll = finishProductionOptions.datalist[i].item_fc_data.reduce((pre: any, val: any) => pre + Number(val.roll), 0)
          if (Number(finishProductionOptions.datalist[i].in_roll) !== Number(roll))
            return ElMessage.error('进仓匹数与细码匹数总量不一致')

          // const length = finishProductionOptions.datalist[i].item_fc_data.reduce((pre: any, val: any) => pre + Number(val.length), 0)
          if (Number(finishProductionOptions.datalist[i].in_length) !== Number(length))
            return ElMessage.error('进仓辅助数量与细码辅助数量总量不一致')
        }
        else {
          return ElMessage.error('进仓匹数与细码匹数总量不一致')
        }
      }
      // if (!ximaLengthFlag) {
      //   return ElMessage.error('进仓匹数与细码匹数总量不一致')
      // }
      // if (!lengthFlag) {
      //   return ElMessage.error('进仓辅助数量与细码辅助数量总量不一致')
      // }
      // 整理参数
      const query = {
        ...state.formInline,
        store_keeper_id: state.formInline.store_keeper_id || 0,
        warehouse_in_time: formatDate(state.formInline.warehouse_in_time),
        item_data: processDataIn(finishProductionOptions.datalist),

      }
      await addFetch(query)
      if (addSuccess.value) {
        ElMessage.success('提交成功')
        // 跳转到列表页
        routerList.push({
          name: 'FpSaleEntryOrderDetail',
          query: {
            id: successData.value.id,
          },
        })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}
const tablesRef = ref()
watch(
  () => finishProductionOptions.datalist,
  () => {
    tablesRef.value.tableRef.updateFooter()
  },
  { deep: true },
)

function clearCustomer(item: any) {
  state.formInline.warehouse_id = item?.default_physical_warehouse || ''
  state.defaultData.customer_id = item?.default_customer_id || 0
}

onMounted(() => {
  // 获取用户上的默认营销体系
  const res = getDefaultSaleSystem()
  state.formInline.sale_system_id = res?.default_sale_system_id
})
// 处理字段不一致
function isMainUnitFormat(item: any) {
  return isMainUnit(item, 'unit_id')
}
/**
 * 根据结算单位是否为主单位显示单价
 * @param item 成品数据
 * @param isInit 是否需要初始化单位
 */
function conductUnitPrice(item: any, isInit = false) {
  // 初始化结算单位
  if (isInit && !item.auxiliary_unit_id) {
    item.auxiliary_unit_id = item.unit_id
    item.auxiliary_unit_name = item.unit_name
  }
  if (isMainUnitFormat(item))
    item.length_cut_return_price = 0 // 主单位-把辅助单价置0

  else
    item.return_price = 0 // 辅助单位-把单价置0
  return item
}
function changeCustomer(item) {
  state.formInline.sale_system_id = item.select_sale_system_id
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" :disabled="finishProductionOptions.datalist.length ? false : true" @click="submitAddAllData">
        提交
      </el-button>
    </template>
    <el-form ref="formRef" :model="state.formInline" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem :required="true" label="客户名称:">
          <template #content>
            <el-form-item prop="customer_id">
              <!--              <SelectDialog -->
              <!--                v-model="state.formInline.customer_id" -->
              <!--                api="GetCustomerEnumList" -->
              <!--                :query="{ name: componentRemoteSearch.customer_name }" -->
              <!--                :column-list="MetaIIformationServers" -->
              <!--                @change-input="val => (componentRemoteSearch.customer_name = val)" -->
              <!--              /> -->
              <SelectCustomerDialog
                v-model="state.formInline.customer_id"
                is-merge
                field="name"
                :default-value="{
                  id: state.formInline.customer_id,
                  name: state.formInline.customer_name,
                  code: state.formInline.customer_code,
                }"
                show-choice-system
                @change-value="changeCustomer"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="营销体系:">
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents
                v-model="state.formInline.sale_system_id"
                default-status
                api="GetSaleSystemDropdownListApi"
                label-field="name"
                value-field="id"
                clearable
                @change-value="clearCustomer"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="仓库名称:">
          <template #content>
            <el-form-item prop="warehouse_id">
              <SelectComponents v-model="state.formInline.warehouse_id" api="GetPhysicalWarehouseDropdownList" :query="{ warehouse_type_id: WarehouseTypeIdEnum.finishProduction }" label-field="name" value-field="id" :clearable="false" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="进仓日期:">
          <template #content>
            <el-form-item prop="warehouse_in_time">
              <el-date-picker v-model="state.formInline.warehouse_in_time" type="date" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓管员:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.formInline.store_keeper_id"
                :query="{
                  duty: EmployeeType.warehouseManager,
                }"
                api="GetEmployeeListEnum"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="退货原因:">
          <template #content>
            <el-form-item prop="return_remark">
              <vxe-input v-model="state.formInline.return_remark" maxlength="100" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证单号:">
          <template #content>
            <el-input v-model="state.formInline.voucher_number" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单类型:" :copies="2">
          <template #content>
            <SelectSaleMode v-model="state.formInline.sale_mode" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="备注:" copies="2">
          <template #content>
            <el-form-item prop="remark">
              <vxe-textarea v-model="state.formInline.remark" style="width: 100%" maxlength="500" show-word-count />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" @click="handEdit">
        批量操作
      </el-button>
      <el-button type="primary" @click="showLibDialog">
        根据资料添加
      </el-button>
      <el-button :disabled="!(state.formInline.customer_id && state.formInline.sale_system_id)" type="primary" @click="showFinishProductionDialog">
        根据销售送货单添加
      </el-button>
    </template>
    <div v-show="finishProductionOptions.datalist.length">
      <Table ref="tablesRef" :config="finishProductionOptions.tableConfig" :table-list="finishProductionOptions.datalist" :column-list="FinishedProductTable">
        <!-- 所属客户 -->
        <template #customer_id="{ row }">
          <SelectDialog
            v-model="row.customer_id"
            :disabled="row?.quote_order_no?.length ? true : false"
            api="GetCustomerEnumList"
            :query="{ name: componentRemoteSearch.customer_name_row }"
            :column-list="[
              {
                title: '客户编号',
                minWidth: 100,
                required: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '客户编号',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '客户名称',
                minWidth: 100,
                colGroupHeader: true,
                required: true,
                childrenList: [
                  {
                    isEdit: true,
                    field: 'name',
                    title: '客户名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '电话',
                colGroupHeader: true,
                minWidth: 100,
                childrenList: [
                  {
                    field: 'phone',
                    isEdit: true,
                    title: '电话',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '销售员',
                minWidth: 100,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'seller_name',
                    title: '销售员',
                    soltName: 'seller_name',
                    isEdit: true,
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.customer_name_row = val)"
          />
        </template>
        <!-- 色号 -->
        <template #product_color_code="{ row }">
          <SelectDialog
            v-model="row.finishProductionColorId"
            :column-list="[
              {
                field: 'product_color_code',
                title: '色号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'product_color_code',
                    isEdit: true,
                    title: '色号',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'product_color_name',
                title: '颜色',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'product_color_name',
                    isEdit: true,
                    title: '颜色',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :table-column="[
              {
                field: 'product_color_code',
                title: '色号',
              },
            ]"
            api="GetFinishProductColorDropdownList"
            label-field="product_color_code"
            :label-name="row.product_color_code"
            :query="{
              finish_product_id: row.product_id,
              product_color_name: row.search_color_code,
            }"
            @change-input="val => (row.search_color_code = val)"
            @change-value="
              item => {
                setFinishProductColor(item, row)
              }
            "
          />
          <!-- <SelectComponents
            @change-value="
              item => {
                setFinishProductColor(item, row)
              }
            "
            :query="{
              finish_product_id: row.product_id,
            }"
            api="GetFinishProductColorDropdownList"
            label-field="product_color_code"
            value-field="id"
            v-model="row.finishProductionColorId"
            clearable
          /> -->
        </template>
        <!-- 颜色 -->
        <template #product_color_id="{ row }">
          <SelectDialog
            v-model="row.finishProductionColorId"
            :column-list="[
              {
                field: 'product_color_code',
                title: '色号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'product_color_code',
                    isEdit: true,
                    title: '色号',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'product_color_name',
                title: '颜色',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'product_color_name',
                    isEdit: true,
                    title: '颜色',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :table-column="[
              {
                field: 'product_color_name',
                title: '颜色',
              },
            ]"
            api="GetFinishProductColorDropdownList"
            label-field="product_color_name"
            :label-name="row.product_color_name"
            :query="{
              finish_product_id: row.product_id,
              product_color_name: row.search_color_name,
            }"
            @change-input="val => (row.search_color_name = val)"
            @change-value="
              item => {
                setFinishProductColor(item, row)
              }
            "
          />
          <!-- <SelectComponents
            @change-value="
              item => {
                setFinishProductColor(item, row)
              }
            "
            :query="{
              finish_product_id: row.product_id,
            }"
            api="GetFinishProductColorDropdownList"
            label-field="product_color_name"
            value-field="id"
            v-model="row.finishProductionColorId"
            clearable
          /> -->
        </template>
        <!-- 染厂色号 -->
        <template #dye_factory_color_code="{ row }">
          <span v-if="row?.quote_order_no?.length">{{ row.dye_factory_color_code }}</span>
          <vxe-input v-else v-model="row.dye_factory_color_code" maxlength="200" clearable />
        </template>
        <!-- 染厂缸号 -->
        <template #dye_factory_dyelot_number="{ row }">
          <span v-if="row?.quote_order_no?.length">{{ row.dye_factory_dyelot_number }}</span>
          <vxe-input v-else v-model="row.dye_factory_dyelot_number" maxlength="200" clearable />
        </template>
        <!-- 成品幅宽 -->
        <template #product_width="{ row }">
          <!-- <vxe-input maxlength="50" clearable v-model="row.product_width"></vxe-input> -->
          <el-input v-model="row.product_width" maxlength="50" clearable placeholder="成品幅宽">
            <template #append>
              <SelectComponents
                v-model="row.finish_product_width_unit_id"
                placeholder="单位"
                style="width: 80px"
                :query="{ dictionary_id: DictionaryType.width_unit }"
                api="GetDictionaryDetailEnumListApi"
                label-field="name"
                value-field="id"
                clearable
              />
            </template>
          </el-input>
        </template>
        <!-- 成品克重 -->
        <template #product_gram_weight="{ row }">
          <!-- <vxe-input maxlength="50" clearable v-model="row.product_gram_weight" type="text"></vxe-input> -->
          <el-input v-model="row.product_gram_weight" maxlength="50" clearable placeholder="成品克重">
            <template #append>
              <SelectComponents
                v-model="row.finish_product_gram_weight_unit_id"
                placeholder="单位"
                style="width: 80px"
                :query="{ dictionary_id: DictionaryType.gram_weight_unit }"
                api="GetDictionaryDetailEnumListApi"
                label-field="name"
                value-field="id"
                clearable
              />
            </template>
          </el-input>
        </template>
        <!-- 成品等级 -->
        <template #product_level_id="{ row }">
          <SelectComponents v-model="row.product_level_id" api="GetInfoBaseFinishedProductLevelEnumList" label-field="name" value-field="id" clearable size="small" />
        </template>
        <!-- 成品备注 -->
        <template #product_remark="{ row }">
          <vxe-input v-model="row.product_remark" maxlength="200" clearable />
        </template>
        <!-- 进仓匹数 -->
        <template #in_roll="{ row }">
          <vxe-input v-model="row.in_roll" :max="row.quote_order_no ? row.out_roll : 999999" type="float" />
        </template>
        <!-- 结算单位 -->
        <template #auxiliary_unit_id="{ row }">
          <SelectComponents v-model="row.auxiliary_unit_id" size="small" style="width: 100%;" api="getInfoBaseMeasurementUnitList" label-field="name" value-field="id" @select="conductUnitPrice(row)" />
        </template>
        <!-- 单价 -->
        <template #return_price="{ row }">
          <vxe-input v-show="isMainUnitFormat(row)" v-model="row.return_price" type="float" />
          <span v-show="!isMainUnitFormat(row)">{{ row.return_price }}</span>
        </template>
        <!-- 进仓辅助数量 -->
        <template #in_length="{ row }">
          <vxe-input v-model="row.in_length" type="float" />
        </template>
        <!-- 辅助数量单价 -->
        <template #length_cut_return_price="{ row }">
          <vxe-input v-show="!isMainUnitFormat(row)" v-model="row.length_cut_return_price" type="float" />
          <span v-show="isMainUnitFormat(row)">{{ row.length_cut_return_price }}</span>
        </template>
        <!-- 其他金额 -->
        <template #other_price="{ row }">
          <vxe-input v-model="row.other_price" type="float" />
        </template>
        <!-- 进仓金额 -->
        <template #total_price="{ row }">
          ￥{{ row.total_price }}
        </template>
        <!-- 备注 -->
        <template #remark="{ row }">
          <vxe-input v-model="row.remark" maxlength="200" clearable type="text" />
        </template>
        <!-- 细码 -->
        <template #xima="{ row, rowIndex }">
          <!--  1,如果进仓匹数为空，那么右侧提示字样为未录入
                2,如果进仓匹数为0，
                        细码条数为0时，那么右侧提示字样为录入(未录入)
                        细码条数不为0时，那么右侧提示字样显示未 录入(已录入)
                3,如果进仓匹数大于0，
                          细码匹数与详情进仓匹数不一致时，右侧提示字样为 录入(多少条未录入)
                          细码匹数与详情进仓匹数一致时，那么右侧提示字样为 录入(已录入) -->
          <!-- 进仓数量 -->

          <!-- 如果进仓匹数为0 -->
          <el-button v-if="getUnInRoll(row) && Number(row.in_roll) === 0" type="primary" text link @click="showFineSizeDialog(row, rowIndex)">
            录入
            <el-text v-if="row.item_fc_data?.length === 0" style="color: red">
              (未录入)
            </el-text>
            <el-text v-else style="color: #ccc">
              (已录入)
            </el-text>
          </el-button>
          <!-- 如果进仓匹数大于0 -->
          <el-button v-else-if="getUnInRoll(row) && Number(row.in_roll) > 0" type="primary" text link @click="showFineSizeDialog(row, rowIndex)">
            录入
            <span v-if="row.in_roll - getSFRoll(row) > 0" style="color: red">({{ row.in_roll - getSFRoll(row) }}条未录)</span>
            <span v-if="Number(row.in_roll) === Number(getSFRoll(row))" style="color: #ccc">(已录入)</span>
          </el-button>
          <!-- 如果进仓匹数为空，那么右侧提示字样为未录入 -->
          <el-text v-else style="color: red">
            未录入
          </el-text>
        </template>
        <!-- 操作 -->
        <template #operate="{ row }">
          <el-button type="primary" text link @click="finishProductionOptions.handleRowDel(row)">
            删除
          </el-button>
        </template>
      </Table>
    </div>
    <div v-if="finishProductionOptions.datalist.length === 0" class="no_data">
      <el-icon :size="80">
        <MessageBox />
      </el-icon>
      <div class="text">
        请选择仓库
      </div>
    </div>
  </FildCard>
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose">
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
  </BulkSetting>
  <AccordingLibAdd ref="AccordingLibAddRef" @handle-sure="finishProductionOptions.handleSure" />
  <AccordingSaleAdd ref="AccordingSaleAddRef" :tip="false" @handle-sure="finishProductionOptions.handleSureFinishProduciton" />
  <FineSizeAdd ref="FineSizeAddRef" @handle-sure="finishProductionOptions.handleSureFineSize" />
  <FineSizeSaleReturnAdd ref="FineSizeSaleReturnAddRef" @handle-sure="finishProductionOptions.handleSureFineSize" />
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}

.no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  user-select: none;
  color: #999;
}

.flex_button {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}
</style>
