<script lang="ts" setup name="ProductOutStockAdd">
import { ElMessage } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { omit } from 'lodash-es'
import AccordingLibAdd from '../../finishManagement/components/AccordingLibAdd.vue'
import BulkSet from './components/BulkSet.vue'
import { addShortageProductOrder } from '@/api/productOutStock'
import { EmployeeType, WarehouseTypeIdEnum } from '@/common/enum'
import { formatHashTag, sumNum } from '@/common/format'
import { deepClone, getCurrentDate, getDefaultSaleSystem, strIsEmpty } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'
import { formValidatePass } from '@/common/rule'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import { processDataIn } from '@/common/handBinary'
import SelectMergeComponent from '@/components/SelectMergeComponent/index.vue'

const routerList = useRouterList()

const state = reactive<any>({
  form: {
    sale_system_id: '', // 营销体系
    voucher_number: '', // 凭证单号
    shipment_type: '', // 出货类型
    warehousing: '', // 调入仓库
    customer_code: '', // 客户编号
    customer_name: '', // 客户名称
    sale_user_id: '', // 销售员
    sale_follow_id: '', // 销售跟单
    date: '', // 订单日期
    seetlements_type: '', // 结算类型
    processing_plant: '', // 加工厂名称
    user: '', // 联系人
    userPhone: '', // 联系电话
    logistics_company: '', // 物流公司
    logistics_region: '', // 物流区域
    receiver_address: '', // 收货地址
    tax_included: '', // 含税项目
    tax_rate: '', // 税率
    is_with_tax_rate: true, // 是否含税
    postage_item: '', // 邮费项目
    shipping_label: '', // 出货标签
    internal_remark: '', // 内部备注
    shipping_remark: '', // 出货备注
    customer_id: '',
    sale_group_name: '',
    sale_group_id: '',
  },
  formRules: {
    sale_system_id: [{ required: true, validator: formValidatePass.sale_system_id(), message: '请选择营销体系', trigger: 'blur' }],
    shipment_type: [{ required: true, message: '请选择出货类型', trigger: 'blur' }],
    sale_user_id: [{ required: true, message: '请选择销售员', trigger: 'blur' }],
    warehousing: [{ trigger: 'blur', validator: checkWarehousing }],
    customer_code: [{ required: true, message: '请选择客户编号', trigger: 'blur' }],
    userPhone: [{ trigger: 'blur', validator: checkPhone }],
  },
  tableList: [],
  multipleSelection: [],
})

function checkPhone(rule: any, value: any, callback: any) {
  const reg = /^(?:(?:\+|00)86)?1\d{10}$/
  if (reg.test(value))
    callback()
  else
    callback(new Error('请输入正确的手机号码'))
}

function checkWarehousing(rule: any, value: any, callback: any) {
  if (state.form.shipment_type !== 1 && state.form.warehousing === '')
    callback(new Error('请选择调入仓库'))
  else
    callback()
}

onMounted(() => {
  const resDes = getDefaultSaleSystem()
  state.form.sale_system_id = resDes?.default_sale_system_id

  state.form.date = getCurrentDate()
  state.form.delivery_date = getCurrentDate()
})

const tableConfig = ref({
  showSlotNums: true,
  showOperate: true,
  operateWidth: '80',
  footerMethod: (val: any) => FooterMethod(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  showSpanHeader: true,
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['shortage_roll'].includes(column.property))
        return `${sumNum(data, 'shortage_roll')}`

      if (['shortage_weight'].includes(column.property))
        return `${sumNum(data, 'shortage_weight')}`

      if (['shortage_length'].includes(column.property))
        return `${sumNum(data, 'shortage_length')}`

      return null
    }),
  ]
}

const ruleFormRef = ref()

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handDelete(index: number) {
  const multipleSelectionIndex = state.multipleSelection.findIndex((item: any) => item._X_ROW_KEY_TABLE === state.tableList[index]._X_ROW_KEY_TABLE)
  if (multipleSelectionIndex > -1)
    state.multipleSelection.splice(multipleSelectionIndex, 1)
  state.tableList.splice(index, 1)
}

const tableRef = ref()

watch(
  () => state.tableList,
  () => {
    if (state.tableList?.length > 0) {
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

// 根据资料添加
const AccordingLibAddRef = ref()
function showLibDialog() {
  AccordingLibAddRef.value.state.showModal = true
}
// 确定从资料中添加
function handleLibSure(list: any) {
  const addList = list.map((item: any) => {
    const temp = {
      ...omit(item, ['id', 'selected', '_X_ROW_KEY_TABLE']),
      product_id: item.id,
      product_code: item.finish_product_code, // 成品编号
      product_name: item.finish_product_name, // 成品名称
      // 色号？
      product_level_id: item.finish_product_level_id || '', // 成品级别
      product_level_name: item.finish_product_level_name, // 成品级别
      product_remark: item.remark,
      warehouse_id: item.warehouse_id || '', // 仓库
      shortage_length: '',
      shortage_roll: '',
      shortage_weight: '',
      purchase_length: '',
      remark: '',
      customer_account_num: '',
    }

    return temp
  })
  state.tableList = [
    ...state.tableList,
    ...addList,
  ]
}

// 选择客户
function handleChangeCustomer(val: any) {
  state.form.sale_system_id = val.select_sale_system_id
  state.form.customer_name = val.name
  state.form.customer_code = val.code
  state.form.customer_phone = val.phone
  state.form.sale_user_id = val.seller_id
  state.form.receipt_address = val.address
  // 清空加工厂
  state.form.processing_plant = ''
}

// 选择行色号
function handChangeColorNo(val: any, row: any) {
  row.product_color_kind_name = val.type_finished_product_kind_name || ''
  row.product_color_kind_id = val.type_finished_product_kind_id || ''
}

const { fetchData: addPost, data: addData, success: addSuccess, msg: addMsg } = addShortageProductOrder()
// 提交
async function handleSure() {
  if (!state.tableList.length)
    return ElMessage.error('至少添加一条成品信息')

  const list = deepClone(state.tableList)
  for (let i = 0; i < list.length; i++) {
    if (!list[i].customer_id)
      return ElMessage.error(`成品信息第${i + 1}行:请选择客户`)
    else if (!list[i].product_color_id)
      return ElMessage.error(`成品信息第${i + 1}行:请选择色号颜色`)
    else if (!list[i].warehouse_id)
      return ElMessage.error(`成品信息第${i + 1}行:请选择出货仓库`)
    else if (strIsEmpty(list[i].shortage_roll))
      return ElMessage.error(`成品信息第${i + 1}行:请填写匹数`)
  }

  const query = {
    sale_group_id: state.form.sale_group_id || 0,
    contact_phone: state.form.userPhone,
    contacts: state.form.user,
    customer_id: state.form.customer_id,
    info_sale_taxable_item_id: state.form.tax_included || 0,
    internal_remark: state.form.internal_remark,
    logistics_area: state.form.logistics_region,
    logistics_company_id: state.form.logistics_company || 0,
    order_time: state.form.date,
    postage_items: state.form.postage_item || 0,
    print_tag: state.form.shipping_label,
    process_factory_id: state.form.processing_plant || 0,
    receipt_address: state.form.receiver_address,
    sale_follower_id: state.form.sale_follow_id || 0,
    sale_system_id: state.form.sale_system_id || 0,
    sale_tax_rate: state.form.tax_rate,
    is_with_tax_rate: state.form.is_with_tax_rate,
    sale_user_id: state.form.sale_user_id,
    send_product_remark: state.form.shipping_remark,
    send_product_type: state.form.shipment_type || 0,
    settle_type: state.form.seetlements_type || 0,
    voucher_number: state.form.voucher_number,
    warehouse_id: state.form.warehousing || 0,
    item_data: list,
  }
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(processDataIn(query))
      if (addSuccess.value) {
        ElMessage.success('成功')
        routerList.push({ name: 'ProductOutStockDetail', query: { id: addData.value.id } })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

// 批量设置
const bulkShow = ref(false)
function bulkHand() {
  bulkShow.value = true
}

async function bulkSubmit({ row, value, quickInputResult }: any) {
  if (state.multipleSelection?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')
  let quickInputIndex = 0
  state.tableList?.map((item: any) => {
    if (item?.selected) {
      if (row.quickInput && quickInputResult?.[quickInputIndex]) {
        item[row.field] = quickInputResult[quickInputIndex++]
        return item
      }

      item[row.field] = value[row.field]
      return item
    }
  })
  bulkShow.value = false
  ElMessage.success('设置成功')
}

const columnList = ref([
  {
    title: '',
    childrenList: [
      {
        field: 'product_name',
        title: '成品名称',
        minWidth: 100,
        soltName: 'product_name',
      },
      {
        field: 'customer_name',
        title: '所属客户',
        minWidth: 100,
        soltName: 'customer_name',
        required: true,
      },
      {
        field: 'customer_account_num',
        title: '款号',
        minWidth: 100,
        soltName: 'customer_account_num',
      },
      {
        field: 'product_color_code',
        title: '色号颜色',
        minWidth: 100,
        required: true,
        soltName: 'product_color_code',
      },
      {
        field: 'product_color_kind_name',
        title: '颜色类别',
        minWidth: 100,
      },
      // {
      //   field: 'dyelot_number',
      //   title: '缸号',
      //   minWidth: 100,
      // },
      {
        field: 'product_level_name',
        title: '成品等级',
        minWidth: 100,
        soltName: 'product_level_id',
      },
      {
        field: 'product_remark',
        title: '成品备注',
        minWidth: 100,
        soltName: 'product_remark',
      },
      {
        field: 'measurement_unit_name',
        title: '单位',
        minWidth: 100,
      },
    ],
  },
  {
    title: '欠货信息',
    childrenList: [
      {
        field: 'warehouse_name',
        title: '出货仓库',
        minWidth: 100,
        required: true,
        soltName: 'warehouse_name',
      },
      {
        field: 'shortage_roll',
        title: '匹数',
        minWidth: 100,
        soltName: 'shortage_roll',
        required: true,
      },
      {
        field: 'shortage_weight',
        title: '数量',
        minWidth: 100,
        soltName: 'shortage_weight',
      },
      {
        field: 'shortage_length',
        title: '辅助数量',
        minWidth: 100,
        soltName: 'shortage_length',
      },
    ],
  },
  {
    title: '其他信息',
    childrenList: [
      {
        field: 'remark',
        title: '备注',
        minWidth: 100,
        soltName: 'remark',
      },
    ],
  },
])
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem required label="客户名称:">
          <template #content>
            <el-form-item prop="customer_id">
              <SelectCustomerDialog
                v-model="state.form.customer_id"
                is-merge
                show-choice-system
                :default-value="{
                  id: state.form.customer_id,
                  name: state.form.customer_name,
                  code: state.form.customer_code,
                }"
                @change-value="handleChangeCustomer"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系:" required>
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents v-model="state.form.sale_system_id" api="AdminsaleSystemgetSaleSystemDropdownList" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证单号:">
          <template #content>
            <el-input v-model="state.form.voucher_number" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="出货类型:" required>
          <template #content>
            <el-form-item prop="shipment_type">
              <SelectComponents v-model="state.form.shipment_type" api="GetSendProductTypeReverseIntMap" label-field="name" value-field="id" clearable @change-value="state.form.warehousing = ''" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="调入仓库:" required>
          <template #content>
            <el-form-item prop="warehousing">
              <SelectComponents
                v-model="state.form.warehousing"
                :disabled="state.form.shipment_type === 1 || state.form.shipment_type === ''"
                api="GetDropdownListWithoutDS"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单日期:" required>
          <template #content>
            <el-form-item prop="date">
              <el-date-picker v-model="state.form.date" type="date" placeholder="订单日期" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <!--        <DescriptionsFormItem required label="客户编号:"> -->
        <!--          <template #content> -->
        <!--            <el-form-item prop="customer_id"> -->
        <!--              <SelectCustomerDialog -->
        <!--                v-model="state.form.customer_id" -->
        <!--                :query="{ sale_system_id: state.form.sale_system_id }" -->
        <!--                field="code" -->
        <!--                :default-value="{ -->
        <!--                  id: state.form.customer_id, -->
        <!--                  name: state.form.customer_name, -->
        <!--                  code: state.form.customer_code, -->
        <!--                }" -->
        <!--                @change-value="handleChangeCustomer" -->
        <!--              /> -->
        <!--            </el-form-item> -->
        <!--          </template> -->
        <!--        </DescriptionsFormItem> -->
        <DescriptionsFormItem label="销售群体:">
          <template #content>
            <el-input v-model="state.form.sale_group_name" disabled />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售员:">
          <template #content>
            <el-form-item prop="sale_user_id">
              <SelectComponents v-model="state.form.sale_user_id" :query="{ duty: EmployeeType.salesman }" api="Adminemployeelist" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售跟单:">
          <template #content>
            <el-form-item prop="sale_follow_id">
              <SelectComponents v-model="state.form.sale_follow_id" :query="{ duty: EmployeeType.follower }" api="Adminemployeelist" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="结算类型:">
          <template #content>
            <SelectComponents v-model="state.form.seetlements_type" api="GetSettleTypeReverseIntMap" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="加工厂名称:">
          <template #content>
            <el-form-item prop="processing_plant">
              <SelectComponents
                v-model="state.form.processing_plant"
                :disabled="state.form.customer_id === ''"
                :query="{ customer_id: state.form.customer_id }"
                api="Logistics_enum_list"
                label-field="name"
                value-field="id"
                clearable
                @change-value="val => ((state.form.user = val.contact_name), (state.form.userPhone = val.phone), (state.form.shipping_label = val.print_tag))"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="联系人:">
          <template #content>
            <el-input v-model="state.form.user" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="联系电话:">
          <template #content>
            <el-input v-model="state.form.userPhone" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="物流公司:">
          <template #content>
            <SelectComponents
              v-model="state.form.logistics_company"
              api="GetInfoSaleLogisticsCompanyEnumList"
              label-field="name"
              value-field="id"
              clearable
              @change-value="val => (state.form.logistics_region = val.logistics_area)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="物流区域:">
          <template #content>
            <el-input v-model="state.form.logistics_region" disabled />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货地址:">
          <template #content>
            <el-input v-model="state.form.receiver_address" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="含税项目:">
          <template #content>
            <el-form-item prop="tax_included">
              <SelectComponents v-model="state.form.tax_included" api="GetInfoSaleTaxableItemEnumList" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="税率:">
          <template #content>
            <vxe-input v-model="state.form.tax_rate" :min="0" type="float" clearable :disabled="!state.form.is_with_tax_rate">
              <template #suffix>
                %
              </template>
            </vxe-input>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-checkbox v-model="state.form.is_with_tax_rate" label="是否含税" size="large" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="邮费项目:">
          <template #content>
            <el-form-item prop="postage_item">
              <SelectComponents v-model="state.form.postage_item" api="GetPostageItemsReverseIntMap" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="出货标签:">
          <template #content>
            <el-form-item prop="shipping_label">
              <el-input v-model="state.form.shipping_label" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="内部备注:" copies="2">
          <template #content>
            <el-input v-model="state.form.internal_remark" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="出货备注:" copies="2">
          <template #content>
            <el-input v-model="state.form.shipping_remark" clearable />
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="成品信息" :tool-bar="false" class="mt-[5px]">
    <template #right-top>
      <el-button type="primary" @click="bulkHand">
        批量操作
      </el-button>
      <!-- <el-button type="primary" :disabled="state.form.sale_system_id === ''" @click="handStockAdd">
        从库存中添加
      </el-button> -->
      <el-button type="primary" :disabled="state.form.sale_system_id === ''" @click="showLibDialog">
        从资料中添加
      </el-button>
    </template>
    <Table ref="tableRef" :config="tableConfig" :table-list="state.tableList" :column-list="columnList">
      <template #product_name="{ row }">
        {{ formatHashTag(row.product_code, row.product_name) }}
      </template>
      <template #customer_name="{ row }">
        <SelectCustomerDialog v-model="row.customer_id" />
      </template>
      <template #product_color_code="{ row }">
        <SelectMergeComponent
          v-model="row.product_color_id"
          :custom-label="(e:any) => `${formatHashTag(e.product_color_code, e.product_color_name)}`"
          :multiple="false"
          :disabled="!row.product_id"
          :query="{
            finish_product_id: row.product_id,
          }"
          api-name="GetFinishProductColorDropdownList"
          remote
          remote-key="product_color_code_or_name"
          remote-show-suffix
          placeholder="成品颜色、色号"
          value-field="id"
          :default-value="formatHashTag(row.product_color_code, row.product_color_name)"
          @change="(val) => handChangeColorNo(val, row)"
        />
      </template>
      <template #product_level_id="{ row }">
        <SelectComponents
          v-model="row.product_level_id"
          placeholder="成品等级"
          api="GetInfoBaseFinishedProductLevelEnumList"
          label-field="name"
          value-field="id"
          clearable
          size="small"
        />
      </template>
      <template #product_remark="{ row }">
        <vxe-input v-model="row.product_remark" clearable />
      </template>
      <template #warehouse_name="{ row }">
        <SelectComponents
          v-model="row.warehouse_id"
          api="GetPhysicalWarehouseDropdownList"
          label-field="name"
          value-field="id"
          clearable
          :query="{ warehouse_type_id: WarehouseTypeIdEnum.finishProduction }"
        />
      </template>
      <template #customer_account_num="{ row }">
        <vxe-input v-model="row.customer_account_num" clearable />
      </template>
      <template #shortage_roll="{ row }">
        <vxe-input v-model="row.shortage_roll" :min="0" type="float" clearable />
      </template>
      <template #shortage_weight="{ row }">
        <vxe-input v-model="row.shortage_weight" :min="0" type="float" clearable />
      </template>
      <template #shortage_length="{ row }">
        <vxe-input v-model="row.shortage_length" :min="0" type="float" clearable />
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" clearable />
      </template>
      <template #operate="{ rowIndex }">
        <el-button text type="danger" @click="handDelete(rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <AccordingLibAdd
    ref="AccordingLibAddRef"
    @handle-sure="handleLibSure"
  />
  <BulkSet v-model="bulkShow" @submit="bulkSubmit" />
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}
</style>
