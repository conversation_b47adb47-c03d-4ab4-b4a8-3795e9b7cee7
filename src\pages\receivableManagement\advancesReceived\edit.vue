<script setup lang="ts" name="AdvancesReceivedEdit">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { getAdvanceCollectOrder, updateAdvanceCollectOrder } from '@/api/advancesReceived'
import { formatDate, formatTwoDecimalsDiv, formatTwoDecimalsMul } from '@/common/format'
import { deleteToast, getFilterData } from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import useRouterList from '@/use/useRouterList'
import SelectSettleAccountDialog from '@/components/SelectSettleAccountDialog/index.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
// import { BusinessUnitIdEnum } from '@/common/enum'

const routerList = useRouterList()
const formRef = ref()
const route = useRoute()
const state = reactive<any>({
  form: {
    sale_system_id: '',
    biz_unit_id: '',
    warehouse_id: '',
    store_keeper_id: '',
    receive_collect_date: new Date(),
    remark: '',
  },
  formRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    settle_type_id: [{ required: true, message: '请选择收款账户', trigger: 'change' }],
    customer_id: [{ required: true, message: '请选择客户名称', trigger: 'change' }],
    receive_collect_date: [{ required: true, message: '请选择收款日期', trigger: 'change' }],
  },
})

let uuid = 0
const advancesReceivedOptions = reactive<any>({
  multipleSelection: [],
  rowIndex: 0,
  tableConfig: {
    showSlotNums: false,
    showCheckBox: true,
    footerMethod: (val: any) => advancesReceivedOptions.footerMethod(val),
    handAllSelect: (val: any) => advancesReceivedOptions.handAllSelect(val),
    handleSelectionChange: (val: any) => advancesReceivedOptions.handleSelectionChange(val),
  },
  footerMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        if (['price'].includes(column.field))
          return sumNum(data, 'price', '')

        return null
      }),
    ]
  },
  list: [],
  columnList: [
    {
      field: 'voucher_number',
      soltName: 'voucher_number',
      title: '凭证单号',
      minWidth: 100,
    },
    {
      field: 'price',
      soltName: 'price',
      title: '金额',
      minWidth: 100,
      required: true,
    },
    {
      field: 'remark',
      soltName: 'remark',
      title: '备注',
      minWidth: 100,
    },
    {
      field: '',
      soltName: 'operate',
      title: '操作',
      width: 100,
    },
  ],
  //   删除
  handleRowDel: async ({ uuid }: any) => {
    const res = await deleteToast('是否确认删除该预收信息')
    if (!res)
      return
    const index = advancesReceivedOptions.list.findIndex((item: any) => item.uuid === uuid)
    advancesReceivedOptions.list.splice(index, 1)
  },
  handAllSelect: ({ records }: any) => {
    advancesReceivedOptions.multipleSelection = records
  },
  handleSelectionChange: ({ records }: any) => {
    advancesReceivedOptions.multipleSelection = records
  },
  handAdd: () => {
    advancesReceivedOptions.list.push({ uuid: ++uuid, price: 0 })
  },
})

// 新增提交
const { fetchData: addFetch, data: successData, success: addSuccess, msg: addMsg } = updateAdvanceCollectOrder()
// 提交所有数据
function submitAddAllData() {
  // 表单验证
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 必填项
      for (let i = 0; i < advancesReceivedOptions.list.length; i++) {
        if (!Number(advancesReceivedOptions.list[i].price))
          return ElMessage.error('金额为必填项')
      }
      // 整理参数
      const query = {
        ...state.form,
        id: Number(route.query.id),
        operator_id: state.form.operator_id || 0,
        receive_collect_date: formatDate(state.form.receive_collect_date),
        item_data: advancesReceivedOptions.list.map((item: any) => {
          return {
            ...item,
            price: formatTwoDecimalsMul(Number(item.price)),
          }
        }),
      }
      await addFetch(getFilterData(query))
      if (addSuccess.value) {
        ElMessage.success('提交成功')
        // 跳转到列表页
        getData()
        routerList.push({
          name: 'AdvancesReceivedDetail',
          query: {
            id: successData.value.id,
          },
        })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}
const tablesRef = ref()
watch(
  () => advancesReceivedOptions.list,
  () => {
    //

    tablesRef.value.tableRef.updateFooter()
  },
  { deep: true },
)
// 获取详情
const { fetchData: DetailFetch, data: detailData } = getAdvanceCollectOrder()

// 获取数据
async function getData() {
  await DetailFetch({ id: route.query.id })
  state.form = {
    ...detailData.value,
    receive_collect_date: formatDate(detailData.value.receive_collect_date),
  }
  advancesReceivedOptions.list
    = detailData.value?.item_data?.map((item: any) => {
      return {
        ...item,
        price: formatTwoDecimalsDiv(Number(item.price)), // 100
      }
    }) || []
}
onMounted(() => {
  getData()
})

function getCoustome(row: any) {
  state.form.sale_system_id = row.select_sale_system_id
  state.form.customer_name = row.name
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" :disabled="advancesReceivedOptions.list.length ? false : true" @click="submitAddAllData">
        提交
      </el-button>
    </template>
    <el-form ref="formRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem :required="true" label="客户名称:">
          <template #content>
            <el-form-item prop="customer_id">
              <SelectCustomerDialog
                v-model="state.form.customer_id"
                field="name"
                is-merge
                show-choice-system
                :default-value="{
                  id: detailData.customer_id,
                  name: detailData.customer_name,
                }"
                @change-value="getCoustome"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="营销体系名称:">
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents v-model="state.form.sale_system_id" api="GetSaleSystemDropdownListApi" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="收款账户:">
          <template #content>
            <el-form-item prop="settle_type_id">
              <SelectSettleAccountDialog
                v-model="state.form.settle_type_id"
                field="name"
              />
              <!--              <SelectComponents v-model="state.form.settle_type_id" api="GetTypeSettleAccountsEnumList" label-field="name" value-field="id" clearable /> -->
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="收款日期:">
          <template #content>
            <el-form-item prop="receive_collect_date">
              <el-date-picker v-model="state.form.receive_collect_date" type="date" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="经手员:">
          <template #content>
            <el-form-item>
              <SelectComponents v-model="state.form.operator_id" api="GetEmployeeListEnum" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="备注:" copies="2">
          <template #content>
            <el-form-item prop="remark">
              <vxe-textarea v-model="state.form.remark" style="width: 100%" maxlength="500" show-word-count />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="预收信息" class="mt-[5px]" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" @click="advancesReceivedOptions.handAdd">
        新建
      </el-button>
    </template>
    <div v-show="advancesReceivedOptions.list.length">
      <Table ref="tablesRef" :config="advancesReceivedOptions.tableConfig" :table-list="advancesReceivedOptions.list" :column-list="advancesReceivedOptions.columnList">
        <!-- 凭证单号 -->
        <template #voucher_number="{ row }">
          <vxe-input v-model="row.voucher_number" maxlength="50" type="text" />
        </template>
        <!-- 金额 -->
        <template #price="{ row }">
          <vxe-input v-model="row.price" type="float" />
        </template>
        <!-- 备注 -->
        <template #remark="{ row }">
          <vxe-input v-model="row.remark" maxlength="200" type="text" />
        </template>
        <!-- 操作 -->
        <template #operate="{ row }">
          <el-button type="text" @click="advancesReceivedOptions.handleRowDel(row)">
            删除
          </el-button>
        </template>
      </Table>
    </div>
    <div v-if="advancesReceivedOptions.list.length === 0" class="no_data">
      <el-icon :size="80">
        <MessageBox />
      </el-icon>
      <div class="text">
        请添加预收信息
      </div>
    </div>
  </FildCard>
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}

.no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  user-select: none;
  color: #999;
}

.flex_button {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}
</style>
