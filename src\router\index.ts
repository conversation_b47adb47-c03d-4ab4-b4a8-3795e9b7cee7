import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { createRouter, createWebHistory } from 'vue-router'
import routes from './routes'
import pinia, { useKeepAliveStore } from '@/stores'
import { routeListStore } from '@/stores/routerList'
import { removeSuffix } from '@/common/stringFun'

NProgress.configure({ showSpinner: true })
// 科顿易布 前缀 相应的在vite.config.ts里面也要改 base
let historyPrefix = import.meta.env.BASE_URL.includes('kdyb')
  ? '/kdyb'
  : undefined

if (!historyPrefix) {
  historyPrefix = import.meta.env.BASE_URL.includes('mes')
    ? '/hcscm/mes'
    : undefined
}

const router = createRouter({
  history: createWebHistory(historyPrefix),
  // history: createWebHistory(),
  routes,
})

const routerList = routeListStore(pinia)
const store = useKeepAliveStore(pinia)
const keepAliveList: any[] = []
function GetRoutesKeepAliveList(data: any) {
  data?.map((item: any) => {
    if (item.children)
      GetRoutesKeepAliveList(item.children)
    else
      if (item.name && item.meta?.keepAlive)
        keepAliveList.push(item.name)
  })
}

GetRoutesKeepAliveList(routes)
store.setData(keepAliveList)

function handRefresh(to: any) {
  if (!to?.meta?.keepAlive)
    return

  if (!store.list.length)
    return

  if (to?.title?.indexOf('新增') !== -1 || to?.title?.indexOf('新建') !== -1) {
    // 如果不在tab，就刷新，如果在tab，就不刷新
    const index = routerList.tabs.findIndex(
      (item: any) => item.title === to?.meta?.title,
    )

    if (index === -1) {
      // 在store的list里面去掉这个路由name，并在异步里面重新添加
      store.removeCacheRoute(to?.name)
      setTimeout(() => {
        store.addCacheRoute(to?.name)
      }, 0)
    }
  }
}

/**
 * 从新增或编辑页跳转到详情页时，去掉tab中的来源页
 * @param from
 * @param to
 */
function handClearOldPage(from: any, to: any) {
  // 不清除tab
  if (to.query?.isClearOldPage === '0' || to.params?.isClearOldPage === '0')
    return
  // 如果from的meta里面没有title，就直接return
  if (!from?.meta?.title)
    return

  const fromTitle = from?.meta?.title
  const toTitle = to?.meta?.title
  // 判断来源是否为新增或编辑页,如果不是就直接return
  if (
    fromTitle?.indexOf('新增') === -1
    && fromTitle?.indexOf('新建') === -1
    && fromTitle?.indexOf('编辑') === -1
  )
    return

  // 判断目标页是否为详情页，如果不是就直接return
  if (toTitle?.indexOf('详情') === -1)
    return

  // 判断目标的详情页是否已经打开，如果已经打开就直接return
  if (routerList.tabs.findIndex((item: any) => item.title === toTitle) !== -1)
    return

  // 如果目标详情与当前页不一致则return
  let formPreName = removeSuffix(from?.name, 'Add')
  formPreName = removeSuffix(formPreName, 'Edit')
  const toPreName = removeSuffix(to?.name, 'Detail')
  if (formPreName !== toPreName)
    return

  const deleteIndex = routerList.tabs.findIndex((item: any) => item.title === fromTitle)
  // 从tab中去掉来源页
  deleteIndex > 0 && routerList.tabs.splice(deleteIndex, 1)
}

function beforePush(from: any, to: any) {
  handRefresh(to)
  handClearOldPage(from, to)
}

// https://router.vuejs.org/zh/guide/advanced/navigation-guards.html
router.beforeEach((to, from: any) => {
  NProgress.start()
  const token = localStorage.getItem('token')

  if (!token) {
    if (!['Login'].includes(to.name as string)) {
      return {
        name: 'Login',
        query: {
          redirect: to.fullPath,
        },
      }
    }
    beforePush(from, to)
    return true
  }
  else {
    // 检查router是否有初始化，没有就初始化
    if (!routerList.isInit)
      routerList.init()

    // if (!userInfo?.user?.resource_router_names?.includes(to.name as string) && !['Login', 'Dashboard', '404'].includes(to.name as string)) {
    //   return { name: '404' }
    // }
    beforePush(from, to)
    return true
  }
})
router.afterEach(() => {
  NProgress.done()
})

export default router
