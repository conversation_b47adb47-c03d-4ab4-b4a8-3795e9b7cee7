<script lang="ts" setup>
import { onMounted, onUnmounted, provide, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import Alert from './components/alert/index.vue'

import Card from './components/card/index.vue'
import CommonFunctions from './components/CommonFunctions/index.vue'

import Version from './components/Version/index.vue'
import OperationalGuide from './components/OperationalGuide/index.vue'

const clientWidth = ref<number>(document.body.clientWidth)
const state = reactive<
  { type: 'see' | 'edit' }
>({
  type: 'see',
})

onMounted(() => {
  window.onresize = () => {
    clientWidth.value = document.body.clientWidth
  }
})
onUnmounted(() => {
  window.onresize = null
})

const router = useRouter()
function toPage({ path }: { path: string }) {
  router.push({
    name: path,
  })
}
provide('changeSize', clientWidth)
</script>

<template>
  <div class="min-w-[1501px] overflow-y-scroll">
    <!-- <Alert /> -->
    <div class="flex justify-between">
      <div class="flex-1">
        <Card title="常用功能">
          <template #right_title>
            <div v-if="state.type === 'edit'" class="flex">
              <p class="text-[13px] cursor-pointer ml-[12px] flex items-center text-[#0E7EFFFF] font-bold" @click="state.type = 'see'">
                <svg-icon name="huishouzhan" size="16" color="#0E7EFFFF" />
                <span class="ml-[6px]">取消</span>
              </p>
            </div>
            <p v-else class="text-[13px] cursor-pointer flex items-center text-[#0E7EFFFF] font-bold" @click="state.type = 'edit'">
              <svg-icon name="setting" size="16" />
              <span class="ml-[6px]">自定义常用功能</span>
            </p>
          </template>
          <template #default>
            <CommonFunctions :type="state.type" @click="toPage" />
          </template>
        </Card>
        <!-- <div class="mt-[24px]">
          <Card title="代办事项">
            <template #default>
              <AgencyMattersa :list="agency_mattersa" />
            </template>
          </Card>
        </div> -->
        <div class="mt-[24px]">
          <Card title="操作指引">
            <template #default>
              <OperationalGuide />
            </template>
          </Card>
        </div>
      </div>
    </div>
  </div>
  <Version />
</template>

<style scoped lang="scss">
.left_two {
  width: 32%;
}
.message_notification_more {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  cursor: pointer;
}
</style>
