<script setup lang="ts">
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import {
  debounce,
  deepClone,
  deleteToast,
  getFilterData,
  resetData,
} from '@/common/util'
import { formatDate } from '@/common/format'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import {
  CancelRawMaterialDmh,
  GetRawMaterialDmhList,
  PassRawMaterialDmh,
} from '@/api/rawMaterial'

const state = reactive<any>({
  filterData: {
    order_no: '',
    src_order_no: '',
    sale_system_id: '',
    voucher_num: '',
    supplier_id: '',
    handler_id: '',
    auditor_id: '',
    updater_id: '',
    status: '',
    create_time: '',
    audit_time: '',
    edit_time: '',
    devierDate: '',
  },
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
})

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = GetRawMaterialDmhList()

onMounted(() => {
  getData()
})

const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '220',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
})

// 获取数据
const getData = debounce(() => {
  const obj = deepClone(state.filterData)
  if (state.filterData.status.length)
    obj.status = obj.status.join(',')

  const query: any = {
    pay_start_date:
      state.filterData.devierDate
      && state.filterData.devierDate !== ''
      && state.filterData.devierDate.length
        ? formatDate(state.filterData.devierDate[0])
        : '',
    pay_end_date:
      state.filterData.devierDate
      && state.filterData.devierDate !== ''
      && state.filterData.devierDate.length
        ? formatDate(state.filterData.devierDate[1])
        : '',
    create_start_time:
      state.filterData.create_time
      && state.filterData.create_time !== ''
      && state.filterData.create_time.length
        ? formatDate(state.filterData.create_time[0])
        : '',
    create_end_time:
      state.filterData.create_time
      && state.filterData.create_time !== ''
      && state.filterData.create_time.length
        ? formatDate(state.filterData.create_time[1])
        : '',
    update_start_time:
      state.filterData.edit_time
      && state.filterData.edit_time !== ''
      && state.filterData.edit_time.length
        ? formatDate(state.filterData.edit_time[0])
        : '',
    update_end_time:
      state.filterData.edit_time
      && state.filterData.edit_time !== ''
      && state.filterData.edit_time.length
        ? formatDate(state.filterData.edit_time[1])
        : '',
    audit_start_time:
      state.filterData.audit_time
      && state.filterData.audit_time !== ''
      && state.filterData.audit_time.length
        ? formatDate(state.filterData.audit_time[0])
        : '',
    audit_end_time:
      state.filterData.audit_time
      && state.filterData.audit_time !== ''
      && state.filterData.audit_time.length
        ? formatDate(state.filterData.audit_time[1])
        : '',
    ...state.filterData,
    status: obj.status,
  }
  delete query.audit_time
  delete query.create_time
  delete query.edit_time
  delete query.devierDate
  ApiCustomerList(getFilterData(query))
}, 400)
onActivated(getData)

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

function handReset() {
  state.filterData = resetData(state.filterData)
}

const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = PassRawMaterialDmh()

// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: Number(row.id) })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}

// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = CancelRawMaterialDmh()

async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: Number(row.id) })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}

const router = useRouter()

function handDetail(row: any) {
  router.push({
    name: 'RawMaterialDmhDetail',
    query: { id: row.id },
  })
}

function handEdit(row: any) {
  router.push({
    name: 'RawMaterialDmhEdit',
    query: { id: row.id },
  })
}

const columnList = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '单据编号',
    fixed: 'left',
    width: '8%',
    soltName: 'link',
  },
  {
    sortable: true,
    field: 'src_order_no',
    title: '源单单号',
    fixed: 'left',
    width: '8%',
  },
  {
    sortable: true,
    field: 'src_order_type_name',
    title: '源单类型',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'voucher_num',
    title: '凭证单号',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'supplier_name',
    title: '供方名称',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'pay_date',
    title: '应付日期',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'handler_name',
    title: '经手人',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'remark',
    title: '单据备注',
    minWidth: 150,
  },
  {
    sortable: true,
    field: '',
    title: '单据状态',
    fixed: 'right',
    soltName: 'status',
    showOrder_status: true,
    width: '5%',
  },
])
</script>

<template>
  <FildCard :tool-bar="false" title="">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="单据编号:">
        <template #content>
          <el-input v-model="state.filterData.order_no" clearable />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="源单单号:">
        <template #content>
          <el-input
            v-model="state.filterData.src_order_no"
            clearable
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="营销体系:">
        <template #content>
          <SelectComponents
            v-model="state.filterData.sale_system_id"
            api="AdminsaleSystemgetSaleSystemDropdownList"
            label-field="name"
            value-field="id"
            clearable
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="凭证单号:">
        <template #content>
          <el-input v-model="state.filterData.voucher_num" clearable />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="供方名称:">
        <template #content>
          <SelectDialog
            v-model="state.filterData.supplier_id"
            api="BusinessUnitSupplierEnumlist"
            :query="{ name: componentRemoteSearch.name }"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :valid-config="{
              name: [
                { required: true, message: '请输入名称' },
                {
                  validator({ cellValue }) {
                    if (cellValue === '') {
                      new Error('供应商名称');
                    }
                  },
                },
              ],
            }"
            :editable="true"
            @change-input="(val) => (componentRemoteSearch.name = val)"
          />
          <!-- <SelectComponents api="BusinessUnitSupplierEnumlist" label-field="name" value-field="id" v-model="state.filterData.supplier_id" clearable /> -->
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="应付日期:">
        <template #content>
          <SelectDate v-model="state.filterData.devierDate" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="经手人:">
        <template #content>
          <SelectComponents
            v-model="state.filterData.handler_id"
            api="GetEmployeeListEnum"
            label-field="name"
            value-field="id"
            clearable
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="创建时间:" width="330">
        <template #content>
          <SelectDate v-model="state.filterData.create_time" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="审核人:">
        <template #content>
          <SelectComponents
            v-model="state.filterData.auditor_id"
            api="GetUserDropdownList"
            label-field="name"
            value-field="id"
            clearable
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="审核时间:" width="330">
        <template #content>
          <SelectDate v-model="state.filterData.audit_time" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="最后修改人:">
        <template #content>
          <SelectComponents
            v-model="state.filterData.updater_id"
            api="GetUserDropdownList"
            label-field="name"
            value-field="id"
            clearable
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="最后修改时间:" width="330">
        <template #content>
          <SelectDate v-model="state.filterData.edit_time" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="单据状态:">
        <template #content>
          <SelectComponents
            v-model="state.filterData.status"
            api="GetAuditStatusEnum"
            multiple
            label-field="name"
            value-field="id"
            clearable
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="">
        <template #content>
          <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
            清除条件
          </el-button>
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard title="" class="mt-[5px]">
    <Table
      ref="tableRef"
      :config="tableConfig"
      :table-list="data?.list"
      :column-list="columnList"
    >
      <template #link="{ row }">
        <el-link type="primary" :underline="false" @click="handDetail(row)">
          {{ row.order_no }}
        </el-link>
      </template>
      <template #operate="{ row }">
        <el-button
          v-has="'DyeingPayDetail'"
          type="text"
          @click="handDetail(row)"
        >
          查看
        </el-button>
        <el-button
          v-if="row.status === 1 || row.status === 3"
          v-has="'DyeingPayEdit'"
          type="text"
          @click="handEdit(row)"
        >
          编辑
        </el-button>
        <el-button
          v-if="row.status === 1"
          v-has="'DyeingPayPass'"
          type="text"
          @click="handAudit(row)"
        >
          审核
        </el-button>
        <el-button
          v-if="row.status === 2"
          v-has="'DyeingPayWait'"
          type="text"
          @click="handApproved(row)"
        >
          消审
        </el-button>
      </template>
    </Table>
  </FildCard>
</template>
