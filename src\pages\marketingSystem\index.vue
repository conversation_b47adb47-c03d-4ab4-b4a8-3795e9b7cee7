<script setup lang="ts" name="MarketingSystem">
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { DeleteSaleSystemApi, GetSaleSystemListApi, UpdateSaleSystemStatusApi, getSaleSystemDropdownListExport } from '@/api/marketingSystem'
import { formatTime } from '@/common/format'
import {
  debounce,
  deleteToast,
  deleteToastWithRiskWarning,
  disabledConfirmBox,
  getFilterData,
} from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import StatusTag from '@/components/StatusTag/index.vue'
import Table from '@/components/Table.vue'
import router from '@/router'

const state = reactive<any>({
  tableData: [],
})

const columnList = ref<any>([
  {
    sortable: true,
    field: 'code',
    title: '编号',
  },
  {
    sortable: true,
    field: 'name',
    title: '营销体系名称',
    fixed: true,
  },
  {
    sortable: true,
    field: 'address',
    title: '地址',
  },
  {
    sortable: true,
    field: 'phone',
    title: '联系电话',
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    isDate: true,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '最后修改人',
  },
  {
    sortable: true,
    field: 'update_time',
    title: '修改时间',
    isDate: true,
  },
  {
    sortable: true,
    field: 'status_name',
    title: '状态',
    align: 'center',
    fixed: true,
    width: '5%',
    soltName: 'status_name',
  },
])

onMounted(() => {
  getList()
})

// 获取列表
const { fetchData: fetchDataList, data: listData, total, page, size, handleSizeChange, handleCurrentChange } = GetSaleSystemListApi()

const searchData = ref({
  name: '',
  code: '',
  address: '',
  phone: '',
  status: '',
})

async function getList() {
  await fetchDataList(getFilterData(searchData.value))
  state.tableData = listData.value.list
}

watch(
  () => listData.value,
  () => {
    state.tableData = listData.value.list
  },
)

// 查看
function handleDetail(row: any) {
  router.push({ name: 'MarketingSystemDetail', params: { id: row.id } })
}

// 新建
function handleAdd() {
  router.push({ name: 'MarketingSystemAdd' })
}

// 编辑
function handleEdit(row: any) {
  router.push({ name: 'MarketingSystemEdit', params: { id: row.id } })
}
const { fetchData, success, msg } = DeleteSaleSystemApi()

// 删除
async function handDelete(row: any) {
  const res = await deleteToastWithRiskWarning(row.name)
  if (res) {
    let ids = []
    if (!Array.isArray(row))
      ids = [row.id]
    else ids = row.map((item: any) => item.id)

    await fetchData({ id: ids.join(',') })
    if (success.value) {
      getList()
      ElMessage.success('删除成功')
    }

    else { ElMessage.error(msg.value) }
  }
}

function hadleRecycle() {
  router.push({
    name: 'ContactUnitRecycle',
  })
}

async function updateStatus(row: any) {
  disabledConfirmBox({ row, api: UpdateSaleSystemStatusApi }).then(() => {
    getList()
  })
}

watch(
  () => searchData.value,
  debounce(() => getList(), 300),
  {
    deep: true,
  },
)

const selectRow = ref<any[]>([])
function handAllSelect({ records }: any) {
  selectRow.value = records
}

function handleSelectionChange({ records }: any) {
  selectRow.value = records
}

function onDelBulk() {
  if (selectRow.value.length <= 0)
    return ElMessage.error('请先勾选数据')
  handDelete(selectRow.value)
}

// function onStopBulk() {
//   if (selectRow.value.length <= 0)
//     return ElMessage.error('请先勾选数据')
//   updateStatus(selectRow.value)
// }

const { fetchData: statusFetch, msg: StatusMsg, success: StatusSuccess } = UpdateSaleSystemStatusApi()

// 批量修改状态
async function handAll(val: number) {
  if (!selectRow.value.length)
    return ElMessage.warning('请至少选择一条数据！')

  const res = await deleteToast('确认修改状态嘛？')
  if (res) {
    const ids: any[] = []
    selectRow.value.forEach((item: any) => {
      ids.push(item.id)
    })
    await statusFetch({ id: ids.toString(), status: val === 1 ? 1 : 2 })
    if (StatusSuccess.value) {
      ElMessage.success('成功')
      getList()
      selectRow.value = []
    }
    else {
      ElMessage.error(StatusMsg.value)
    }
  }
}

// 导出
const loadingExcel = ref(false)
async function handleExport() {
  if (!listData?.value.list || listData?.value.list.length <= 0)
    return ElMessage.warning('当前无数据可导出')
  const name_str = '营销体系管理'
  const { fetchData: getFetch, success: getSuccess, msg: getMsg } = getSaleSystemDropdownListExport({ nameFile: name_str })
  loadingExcel.value = true
  await getFetch({
    ...getFilterData(searchData.value),
    download: 1,
  })
  if (getSuccess.value) {
    ElMessage({
      type: 'success',
      message: '成功',
    })
  }
  else {
    ElMessage({
      type: 'error',
      message: getMsg.value,
    })
  }
  loadingExcel.value = false
}

const tableConfig = ref({
  fieldApiKey: 'MarketingSystem',
  loading: false,
  showPagition: true,
  page,
  size,
  total,
  height: '100%',
  showCheckBox: true,
  showOperate: true,
  operateWidth: '10%',
  showSort: false,
  handleSizeChange,
  handleCurrentChange,
  handAllSelect,
  handleSelectionChange,
  showSlotNums: true,
})

const FildCardRef = ref<any>()
const tablesRef = ref()

nextTick(() => {
  setTimeout(() => {
    // 将表格和工具栏进行关联
    const $table = tablesRef.value.tableRef
    const $toolbar = FildCardRef.value.xToolbar

    $table.connect($toolbar)
  }, 100)
})
</script>

<template>
  <div class="list-page">
    <FildCard title="" :tool-bar="false">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="编号:">
          <template #content>
            <el-input v-model="searchData.code" placeholder="请输入编号" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系名称:">
          <template #content>
            <el-input v-model="searchData.name" placeholder="请输入营销体系名称" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="地址:">
          <template #content>
            <el-input v-model="searchData.address" placeholder="请输入地址" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="联系电话:">
          <template #content>
            <el-input v-model="searchData.phone" placeholder="请输入联系电话" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="状态:">
          <template #content>
            <SelectComponents v-model="searchData.status" api="StatusListApi" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard ref="FildCardRef" title="" class="table-card-full" @hadle-recycle="hadleRecycle">
      <template #right-top>
        <el-button v-has="'MarketingSystem_add'" style="margin-right: 10px" type="primary" :icon="Plus" @click="handleAdd">
          新建
        </el-button>
        <el-dropdown>
          <span class="el-dropdown-link">
            <el-button>批量操作</el-button>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="onDelBulk">
                批量删除
              </el-dropdown-item>
              <!-- <el-dropdown-item @click="onStopBulk">批量禁用</el-dropdown-item> -->
              <el-dropdown-item @click="handAll(1)">
                批量启用
              </el-dropdown-item>
              <el-dropdown-item @click="handAll(2)">
                批量禁用
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <BottonExcel v-has="`MarketingSystem_export`" :loading="loadingExcel" title="导出文件" @on-click-excel="handleExport" />
      </template>
      <Table ref="tablesRef" :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
        <template #status_name="{ row }">
          <StatusTag :status="row.status" :name="row.status_name" />
        </template>
        <template #update_time="{ row }">
          {{ formatTime(row.update_time) }}
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link v-has="`MarketingSystem_detail`" type="primary" :underline="false" @click="handleDetail(row)">
              查看
            </el-link>
            <el-link v-has="`MarketingSystem_edit`" type="primary" :underline="false" @click="handleEdit(row)">
              编辑
            </el-link>
            <el-link v-has="`MarketingSystem_del`" type="primary" :underline="false" @click.stop="handDelete(row)">
              删除
            </el-link>
            <el-link v-has="`MarketingSystem_status`" type="primary" :underline="false" @click="updateStatus(row)">
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
  </div>
</template>

<style lang="scss" scoped>
.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.conditions {
  @apply flex items-center;
  span {
    font-size: 13px;
    @apply mr-2;
  }

  &:nth-last-child(n + 1) {
    @apply mr-5;
  }
}
</style>
