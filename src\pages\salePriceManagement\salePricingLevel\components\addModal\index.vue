<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import { AddSaleLevel, GetSaleLevel, UpdateSaleLevel } from '@/api/salePriceManagement'
import { getFilterData } from '@/common/util'
import SelectComponents from '@/components/SelectComponents/index.vue'

export interface Props {
  modelValue: boolean
  id?: number
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  id: 0,
})
const emit = defineEmits(['update:modelValue', 'submit'])
const state = reactive({
  form: {
    name: '',
    remark: '',
    sort: 0,
    status: 1,
  },
  fromRules: {
    name: [{ required: true, message: '销售定价等级', trigger: 'blur' }],
    sort: [{ required: true, message: '排序', trigger: 'blur' }],
  },
})

const showModal = ref<boolean>(false)
watch(
  () => [props.modelValue, props.id],
  () => {
    showModal.value = props.modelValue
    if (!props.modelValue) {
      state.form.name = ''
      state.form.remark = ''
      state.form.sort = 0
    }
    if (props.id && props.modelValue)
      getData()
  },
)

const { fetchData: fetchDataDetail, data: dataDetail } = GetSaleLevel()
async function getData() {
  await fetchDataDetail({ id: props.id })
  state.form.name = dataDetail.value.name
  state.form.remark = dataDetail.value.remark
  state.form.sort = dataDetail.value.sort
  state.form.status = dataDetail.value.status
}

const { fetchData: fetchDataAdd, success: successAdd, msg: msgAdd } = AddSaleLevel()
const { fetchData: fetchDataUpdate, success: successUpdate, msg: msgUpdate } = UpdateSaleLevel()
const ruleFormRef = ref()
function submit() {
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      if (props.id) {
        await fetchDataUpdate(getFilterData({ ...state.form, id: props.id }))
        if (successUpdate.value) {
          ElMessage.success('编辑成功')
          emit('submit')
          emit('update:modelValue', false)
        }
        else {
          ElMessage.error(msgUpdate.value)
        }
      }
      else {
        await fetchDataAdd(getFilterData({ ...state.form }))
        if (successAdd.value) {
          ElMessage.success('添加成功')
          emit('submit')
          emit('update:modelValue', false)
        }
        else {
          ElMessage.error(msgAdd.value)
        }
      }
    }
  })
}

function onClose() {
  emit('update:modelValue', false)
}
</script>

<template>
  <vxe-modal v-model="showModal" show-footer title="销售定价等级" width="500" height="auto" :mask="false" resize @close="onClose">
    <el-form ref="ruleFormRef" size="default" :model="state.form" label-width="120px" label-position="left" :rules="state.fromRules">
      <el-form-item label="销售定价等级" prop="name">
        <el-input v-model="state.form.name" placeholder="销售定价等级" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="state.form.sort" :precision="0" :step="1" :min="0" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <SelectComponents v-model="state.form.status" api="StatusListApi" label-field="name" value-field="id" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="state.form.remark" :rows="2" type="textarea" placeholder="请填写备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" size="small" @click="submit">
        提交
      </el-button>
    </template>
  </vxe-modal>
</template>
