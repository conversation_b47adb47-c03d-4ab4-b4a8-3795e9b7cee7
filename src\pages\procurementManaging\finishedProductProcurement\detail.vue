<script lang="ts" setup name="FinishedProductProcurementDetail">
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import {
  FinishProductBusinessClose,
  FinishProductCancel,
  FinishProductDetail,
  FinishProductPass,
  FinishProductReject,
  FinishProductVoid,
} from '@/api/finishedProductProcurement'
import {
  formatDate,
  formatLengthDiv,
  formatPriceDiv,
  formatTwoDecimalsDiv,
  formatUnitPriceDiv,
  formatWeightDiv,
  sumNum,
} from '@/common/format'
import { orderStatusConfirmBox } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'

const router = useRoute()
const state = reactive({
  form: {
    sale_system_id: '',
    supplier_id: '',
    receipt_unit_id: '',
    purchase_date: '',
    receipt_date: '',
    fapiao_title: '',
    remark: '',
    sale_system_name: '',
    supplier_name: '',
    receipt_unit_name: '',
    receipt_address: '',
    receipt_phone: '',
    tax_rate: '', // 税率
    items: [] as any[],
  },
  tableData: [] as any[],
})

const { fetchData: detailFetch, data: detalData } = FinishProductDetail()

onActivated(() => {
  getData()
})
onMounted(() => {
  getData()
})

async function getData() {
  await detailFetch({
    id: router.params.id,
  })
}

watch(
  () => detalData.value,
  () => {
    if (detalData.value) {
      detalData.value?.items?.map((item: any) => {
        item.piece_weight = formatWeightDiv(item.piece_weight)
        item.unit_price = formatUnitPriceDiv(item.unit_price)
        item.upper_limit = formatPriceDiv(item.upper_limit)
        item.lower_limit = formatPriceDiv(item.lower_limit)
        item.length = formatLengthDiv(item.length)
        item.length_unit_price = formatUnitPriceDiv(item.length_unit_price)
        item.paper_tube_weight = formatWeightDiv(item.paper_tube_weight)
        item.piece_count = formatTwoDecimalsDiv(item.piece_count)
        item.tax_rate = formatTwoDecimalsDiv(item.tax_rate)
      })
    }
  },
)

const closeLoading = ref(false)
const cancelLoading = ref(false)
const rejectLoading = ref(false)
const auditLoading = ref(false)
const eliminateLoading = ref(false)
async function updateStatus(audit_status: number) {
  const id = Number(router.params.id)
  if (audit_status === 5) {
    await orderStatusConfirmBox({
      id,
      message: { desc: '点击确定后订单将被关闭', title: '是否关闭该订单？' },
      api: FinishProductBusinessClose,
      loadingRef: closeLoading,
      params: {
        business_close: 2,
      },
    })
  }
  if (audit_status === 4) {
    await orderStatusConfirmBox({
      id,
      message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' },
      api: FinishProductVoid,
    })
  }
  if (audit_status === 3) {
    await orderStatusConfirmBox({
      id,
      message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' },
      api: FinishProductReject,
    })
  }
  if (audit_status === 2) {
    await orderStatusConfirmBox({
      id,
      message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' },
      api: FinishProductPass,
    })
  }
  if (audit_status === 1) {
    await orderStatusConfirmBox({
      id,
      message: {
        desc: '点击消审后订单将变为待审核状态',
        title: '是否消审该订单？',
      },
      api: FinishProductCancel,
    })
  }
  getData()
}

const columnList = ref([
  {
    field: 'sale_plan_order_item_no',
    title: '销售计划详情单号',
    fixed: 'left',
    width: 150,
  },
  {
    field: 'finish_product_code',
    title: '成品编号',
    fixed: 'left',
    width: 150,
  },
  {
    field: 'finish_product_name',
    title: '成品名称',
    fixed: 'left',
    width: 100,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    width: 100,
  },
  {
    field: 'color_code',
    title: '颜色编号',
    width: 100,
  },
  {
    field: 'color_type_name',
    title: '颜色类别',
    width: 100,
  },

  {
    field: 'fabric_type_name',
    title: '布种类型',
    width: 100,
  },
  {
    field: 'color_Name',
    title: '颜色名称',
    width: 100,
  },
  {
    field: 'dye_factory_color',
    title: '染厂颜色',
    width: 100,
  },
  {
    field: 'dye_factory_color_num',
    title: '染厂色号',
    width: 100,
  },
  {
    field: 'dye_factory_vat_code',
    title: '染厂缸号',
    width: 100,
  },
  {
    field: 'finish_product_level',
    title: '成品等级',
    width: 100,
  },
  // {
  //   field: 'dye_craft',
  //   title: '染整工艺',
  //   width: 100,
  // },
  {
    field: 'finish_product_width',
    soltName: 'finish_product_width',
    title: '成品幅宽',
    width: 100,
  },
  {
    field: 'finish_product_gram_weight',
    soltName: 'finish_product_gram_weight',
    title: '成品克重',
    width: 100,
  },
  {
    field: 'paper_tube_weight',
    title: '纸筒重量',
    width: 100,
  },
  {
    field: 'finish_product_ingredient',
    title: '成品成分',
    width: 100,
  },
  {
    field: 'finish_product_craft', // TODO 后端没定义
    title: '成品工艺',
    width: 100,
  },
  {
    field: 'remark',
    title: '备注',
    width: 200,
  },
  {
    field: 'piece_count',
    title: '匹数',
    fixed: 'right',
    width: 70,
  },
  {
    field: 'piece_weight',
    title: '均重',
    fixed: 'right',
    width: 70,
  },
  {
    field: 'total_weight',
    title: '采购数量',
    fixed: 'right',
    width: 100,
    isWeight: true,
  },
  // {
  //   field: 'unit',
  //   title: '单位',
  //   fixed: 'right',
  //   width: 70,
  // },
  {
    field: 'auxiliary_unit_name',
    title: '结算单位',
    fixed: 'right',
    width: 100,
  },
  {
    field: 'unit_price',
    title: '采购单价',
    fixed: 'right',
    width: 100,
  },
  {
    field: 'upper_limit',
    title: '上限',
    fixed: 'right',
    width: 70,
  },
  {
    field: 'lower_limit',
    title: '下限',
    fixed: 'right',
    width: 70,
  },
  {
    field: 'length',
    title: '辅助数量',
    fixed: 'right',
    width: 100,
  },
  // {
  //   field: 'length_unit_price',
  //   title: '辅助数量单价',
  //   fixed: 'right',
  //   width: 120,
  // },
  {
    field: 'total_price',
    title: '采购金额',
    fixed: 'right',
    width: 100,
    isPrice: true,
  },
])

const tablesRef = ref()

function footerMethod({ columns, data }: { columns: any, data: any }) {
  const footerData = [
    columns.map((column: any, _columnIndex: number) => {
      if (['finish_product_code'].includes(column.field))
        return '合计'

      if (['total_weight'].includes(column.field))
        return formatWeightDiv(sumNum(data, column.field))

      if (['total_price'].includes(column.field))
        return formatPriceDiv(sumNum(data, column.field))

      if (['piece_count', 'piece_weight', 'length'].includes(column.field))
        return sumNum(data, column.field)
    }),
  ]
  return footerData
}
const tableConfig = ref({
  fieldApiKey: 'FinishedProductProcurementDetail',
  operateWidth: 100,
  filterStatus: false,
  footerMethod,
})
</script>

<template>
  <StatusColumn
    :close-loading="closeLoading"
    :cancel-loading="cancelLoading"
    :reject-loading="rejectLoading"
    :audit-loading="auditLoading"
    :eliminate-loading="eliminateLoading"
    :order_no="detalData.order_num"
    :order_id="router.params.id"
    :status="detalData.status"
    :status_name="detalData.status_name"
    v-bind="detalData"
    permission_close_key="FinishedProductProcurement_close"
    permission_cancel_key="FinishedProductProcurement_void"
    permission_reject_key="FinishedProductProcurement_reject"
    permission_pass_key="FinishedProductProcurement_PASS"
    permission_wait_key="FinishedProductProcurement_cancel"
    permission_edit_key="FinishedProductProcurementEdit"
    edit_router_name="FinishedProductProcurementEdit"
    edit_router_type="params"
    @eliminate="updateStatus"
    @close="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  >
    <template #print>
      <PrintPopoverBtn
        :id="router.params.id"
        is-link
        style="width: auto"
        print-btn-text="打印"
        api="FinishProductDetail"
        :print-type="PrintType.PrintTemplateTypePurOrder"
        :data-type="PrintDataType.Product"
      />
      <!--      <PrintBtn type="finishedProductProcurement" api="FinishProductDetail" :tid="1656351675572480" :id="router.params.id" /> -->
    </template>
  </StatusColumn>
  <FildCard title="基础信息" :tool-bar="false">
    <div class="line" />
    <slot>
      <el-form
        :model="state.form"
        label-width="60px"
        label-position="top"
      >
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem label="营销体系名称:">
            <template #content>
              {{ detalData?.sale_system_name }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="供应商名称:">
            <template #content>
              {{ detalData?.supplier_name }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="收货单位名称:">
            <template #content>
              {{ detalData?.receipt_unit_name }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="采购日期:">
            <template #content>
              {{ formatDate(detalData?.purchase_date) }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="收货日期:">
            <template #content>
              {{ formatDate(detalData?.receipt_date) }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="收货地址:">
            <template #content>
              {{ detalData?.receipt_Address }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="收货电话:">
            <template #content>
              {{ detalData?.receipt_Phone }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="发票抬头:">
            <template #content>
              {{ detalData?.fapiao_title }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem v-if="detalData?.include_tax" label="税率:">
            <template #content>
              {{ formatPriceDiv(detalData?.tax_rate) }}%
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="是否含税:">
            <template #content>
              {{ detalData?.include_tax ? "是" : "否" }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="订单类型:">
            <template #content>
              {{ detalData?.sale_mode_name }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="备注:" copies="2">
            <template #content>
              {{ detalData?.remark }}
            </template>
          </DescriptionsFormItem>
        </div>
      </el-form>
    </slot>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]">
    <Table
      ref="tablesRef"
      :config="tableConfig"
      :table-list="detalData.items"
      :column-list="columnList"
    >
      <template #finish_product_width="{ row }">
        {{ row?.finish_product_width }}
        {{ row?.finish_product_width_unit_name }}
      </template>
      <template #finish_product_gram_weight="{ row }">
        {{ row?.finish_product_gram_weight }}
        {{ row?.finish_product_gram_weight_unit_name }}
      </template>
    </Table>
  </FildCard>
</template>

<style lang="scss" scoped></style>
