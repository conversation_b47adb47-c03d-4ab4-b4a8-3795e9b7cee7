<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import FineSizeDeliveryOrderDetail from '../components/FineSizeSelectStockDetail.vue'
import {
  getFpmSaleOutOrder,
  updateFpmSaleOutOrderStatusCancel,
  updateFpmSaleOutOrderStatusPass,
  updateFpmSaleOutOrderStatusReject,
  updateFpmSaleOutOrderStatusWait,
} from '@/api/fpSaleDeliverFromGodownOrder'
import { formatDate, formatLengthDiv, formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'
import { orderStatusConfirmBox } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'

const form_options = [
  {
    text: '营销体系名称',
    key: 'sale_system_name',
  },
  {
    text: '客户名称',
    key: 'customer_name',
  },
  {
    text: '仓库名称',
    key: 'warehouse_name',
  },
  {
    text: '出仓日期',
    key: 'warehouse_out_time',
  },
  {
    text: '加工厂名称',
    key: 'process_factory_name',
  },
  {
    text: '收货地址',
    key: 'receive_addr',
  },
  {
    text: '收货电话',
    key: 'receive_phone',
  },
  {
    text: '收货标签',
    key: 'receive_tag',
  },
  {
    text: '仓管员',
    key: 'store_keeper_name',
  },
  {
    text: '配布员',
    key: 'arrange_user_name',
  },
  {
    text: '销售员',
    key: 'sale_user_name',
  },
  {
    text: '销售跟单',
    key: 'sale_follower_name',
  },
  {
    text: '司机名称',
    key: 'driver_name',
  },
  {
    text: '物流公司',
    key: 'logistics_company_name',
  },
  {
    text: '物流区域',
    key: 'logistics_company_area',
  },
  {
    text: '订单类型',
    key: 'sale_mode_name',
  },
  {
    text: '内部备注',
    key: 'internal_remark',
    copies: '2',
  },
  {
    text: '销售备注',
    key: 'sale_remark',
    copies: '2',
  },
]
const route = useRoute()

const state = reactive<any>({
  baseData: {
    order_no: '',
    audit_status_name: '',
    audit_status: 1,
    arrange_order_no: '',
    unit_name: '',
  },
})

// 成品信息
const finishProductionOptions = reactive<any>({
  detailShow: false,
  tableConfig: {
    fieldApiKey: 'FpSaleDeliverFromGodownOrderDetail',
    showSlotNums: false,
    showSpanHeader: true,
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 总数量 纸筒总重（kg） 结算数量 进仓辅助数量 采购辅助数量 进仓金额
        if (['actually_weight', 'out_roll', 'settle_weight', 'out_length', 'total_weight'].includes(column.field))
          return sumNum(data, column.field, '', 'float')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      field: 'A',
      childrenList: [
        {
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
        },
      ],
    },
    {
      title: '成品信息',
      field: 'B',
      childrenList: [
        {
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },

        {
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          field: 'out_roll',
          title: '出仓匹数',
          minWidth: 100,
        },
        {
          field: 'unit_name',
          title: '单位',
          minWidth: 70,
        },
      ],
    },
    {
      title: '结算单位',
      field: 'F',
      childrenList: [
        {
          field: 'auxiliary_unit_name',
          title: '结算单位',
          width: 100,
        },
      ],
    },
    {
      field: 'C',
      title: '数量信息',
      childrenList: [
        {
          field: 'total_weight',
          title: '出仓数量',
          minWidth: 100,
        },
        {
          field: 'weight_error',
          title: '码单空差',
          minWidth: 100,
        },
        {
          field: 'actually_weight',
          title: '码单数量',
          minWidth: 100,
        },
        {
          field: 'settle_error_weight',
          title: '结算空差',
          minWidth: 100,
        },
        {
          field: 'settle_weight',
          title: '结算数量',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'D',
      title: '辅助数量单信息',
      childrenList: [
        {
          field: 'out_length',
          title: '辅助数量',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'E',
      title: '单据备注信息',
      childrenList: [
        {
          field: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'F',
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: 'xima',
          soltName: 'xima',
          title: '细码',
          minWidth: 100,
        },
      ],
    },
  ],
})
const { fetchData, data: detailData } = getFpmSaleOutOrder()
onMounted(() => {
  getData()
})
async function getData() {
  await fetchData({ id: route.query.id })
  state.baseData = {
    ...detailData.value,
    warehouse_out_time: formatDate(detailData.value.warehouse_out_time),
  }
  finishProductionOptions.datalist = detailData.value?.item_data?.map((item: any) => {
    return {
      ...item,
      out_roll: formatTwoDecimalsDiv(Number(item.out_roll)),
      sum_stock_roll: formatTwoDecimalsDiv(Number(item.sum_stock_roll)),
      sum_stock_weight: formatWeightDiv(Number(item.sum_stock_weight)),
      sum_stock_length: formatLengthDiv(Number(item.sum_stock_length)),
      total_weight: formatWeightDiv(Number(item.total_weight)),
      weight_error: formatWeightDiv(Number(item.weight_error)),
      settle_weight: formatWeightDiv(Number(item.settle_weight)),
      unit_price: formatUnitPriceDiv(Number(item.unit_price)),
      out_length: formatLengthDiv(Number(item.out_length)),
      length_unit_price: formatUnitPriceDiv(Number(item.length_unit_price)),
      other_price: formatTwoDecimalsDiv(Number(item.other_price)),
      total_price: formatTwoDecimalsDiv(Number(item.total_price)),
      actually_weight: formatWeightDiv(Number(item.actually_weight)),
      settle_error_weight: formatWeightDiv(Number(item.settle_error_weight)),
    }
  })
}

async function updateStatus(audit_status: number) {
  const id: any = route.query.id?.toString()
  const options: Record<number, any> = {
    1: {
      message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' },
      api: updateFpmSaleOutOrderStatusWait,
    },
    2: {
      message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' },
      api: updateFpmSaleOutOrderStatusPass,
    },
    3: {
      message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' },
      api: updateFpmSaleOutOrderStatusReject,
    },
    4: {
      message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' },
      api: updateFpmSaleOutOrderStatusCancel,
    },
  }
  const { message, api } = options[audit_status]
  //   const fn = (data: any) => {
  //     if (audit_status == 2) {
  //       // 审核通过自动生产进仓单
  //       router.push({
  //         name: 'FpInteriorAllotWarehouseEntryOrderEdit',
  //         query: {
  //           id: data.create_id,
  //         },
  //       })
  //     }
  //   }
  await orderStatusConfirmBox({ id, audit_status, message, api })
  getData()
}

const FineSizeDeliveryOrderDetailRef = ref()
function showDialog(row: any) {
  FineSizeDeliveryOrderDetailRef.value.showDialog(row)
}
</script>

<template>
  <StatusColumn
    :order_no="state.baseData.order_no"
    :order_id="state.baseData.id"
    :cloth_order_no="state.baseData.arrange_order_no"
    :status="state.baseData.audit_status"
    :status_name="state.baseData.audit_status_name"
    permission_print_key=""
    permission_wait_key="FpSaleDeliverFromGodownOrder_wait"
    permission_reject_key="FpSaleDeliverFromGodownOrder_reject"
    permission_pass_key="FpSaleDeliverFromGodownOrder_pass"
    permission_cancel_key="FpSaleDeliverFromGodownOrder_cancel"
    permission_edit_key="FpSaleDeliverFromGodownOrder_edit"
    edit_router_name="FpSaleDeliverFromGodownOrderEdit"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  />
  <FildCard class="mt-[5px]" title="基础信息" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem v-for="(item, index) in form_options" :key="index" :copies="item.copies || 1" :label="`${item.text}:`">
        <template #content>
          {{ state.baseData[item.key] }}
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]">
    <Table :config="finishProductionOptions.tableConfig" :table-list="finishProductionOptions.datalist" :column-list="finishProductionOptions.columnList">
      <template #unit_price="{ row }">
        ￥{{ row.unit_price }}
      </template>
      <template #length_unit_price="{ row }">
        ￥{{ row.length_unit_price }}
      </template>
      <template #other_price="{ row }">
        ￥{{ row.other_price }}
      </template>
      <template #total_price="{ row }">
        ￥{{ row.total_price }}
      </template>
      <template #xima="{ row }">
        <el-link @click="showDialog(row)">
          查看
        </el-link>
      </template>
    </Table>
  </FildCard>
  <FineSizeDeliveryOrderDetail ref="FineSizeDeliveryOrderDetailRef" />
</template>

<style lang="scss" scoped>
.oreder_code {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.form_container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  color: #666;

  .form_container_item {
    display: flex;
    margin-bottom: 15px;

    .label {
      color: #ccc;
      margin-right: 10px;
    }
  }
}

.el-link {
  color: #0e7eff;
}
</style>
