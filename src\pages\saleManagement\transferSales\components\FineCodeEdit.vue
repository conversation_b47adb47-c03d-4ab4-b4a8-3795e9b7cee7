<script setup lang="ts">
import { h, nextTick, ref, watch } from 'vue'
import { VxeInput } from 'vxe-pc-ui'
import currency from 'currency.js'
import GridTable from '@/components/GridTable/index.vue'
import { formatWeightDiv, formatWeightMul, sumNum } from '@/common/format'

interface propsType {
  modelValue: boolean
  data: any
}

const props = withDefaults(defineProps<propsType>(), {
  modelValue: false,
  data: () => {},
})

const emit = defineEmits(['confirm', 'cancel', 'update:modelValue'])

const showModal = ref(false)
const GridTableRef = ref<any>()
const fineCodeData = ref<any>([])

const headerColumn = [
  {
    field: 'product_code',
    title: '面料编号',
  },
  {
    field: 'product_name',
    title: '面料名称',
  },
  {
    field: 'product_color_code',
    title: '色号',
  },
  {
    field: 'product_color_name',
    title: '颜色',
  },
  {
    field: 'dyelot_number',
    title: '缸号',
  },
  {
    field: 'supplier_name',
    title: '供应商',
  },
  {
    field: 'measurement_unit_name',
    title: '单位',
  },
  {
    field: 'roll',
    title: '匹数',
  },
]

function initFineCodeItem() {
  if (fineCodeData.value.length === 0) {
    return {
      sale_weight: 0,
      sale_weight_error: 0,
      sale_weight_total: 0,
      supplier_weight: 0,
      supplier_weight_error: 0,
      supplier_weight_total: 0,
    }
  }
  const lastRow = fineCodeData.value[fineCodeData.value.length - 1]
  return {
    sale_weight: lastRow.sale_weight,
    sale_weight_error: lastRow.sale_weight_error,
    sale_weight_total: lastRow.sale_weight_total,
    supplier_weight: lastRow.supplier_weight,
    supplier_weight_error: lastRow.supplier_weight_error,
    supplier_weight_total: lastRow.supplier_weight_total,
  }
}

function fourDigitNum(num: number) {
  return currency(num, { precision: 4 }).value
}

function addNewRow() {
  fineCodeData.value.push(initFineCodeItem())
  nextTick(() => {
    GridTableRef.value.TableRef.loadData(fineCodeData.value)
  })
}

function deleteAll() {
  fineCodeData.value = []
  nextTick(() => {
    GridTableRef.value.TableRef.loadData(fineCodeData.value)
  })
}

function addOrNextLine(row: any, colName: any) {
  const index = fineCodeData.value.findIndex((item: any) => item === row)
  if (index === fineCodeData.value.length - 1 || index === -1)
    addNewRow()

  const field = GridTableRef.value.TableRef?.getColumnByField(colName)
  const nextRow = fineCodeData.value[index + 1]
  GridTableRef.value.TableRef.setEditCell(nextRow, field)

  setTimeout(() => {
    const input: any = document.getElementById(`${colName}_${nextRow.id}`)?.querySelector('.vxe-input--inner')
    input?.focus()
  }, 30)
}

const fineCodeColumnList = [
  { type: 'seq', width: 50, title: '序号' },
  {
    field: 'sale_weight',
    title: '细码',
    editRender: {},
    slots: {
      edit: ({ row }: any) => {
        return h(VxeInput, {
          'id': `sale_weight_${row.id}`,
          'modelValue': row.sale_weight,
          'onUpdate:modelValue': (val: any) => {
            row.sale_weight = fourDigitNum(val)
          },
          'onChange': (event: any) => {
            row.supplier_weight = fourDigitNum(event.value)
          },
          'onKeyup': (val) => {
            // 不用管输入了什么东西，添加的新行和数组的末尾行一致就行
            if (val.$event.keyCode === 13)
              addOrNextLine(row, 'sale_weight')
          },
        })
      },
    },
  },
  {
    field: 'sale_weight_error',
    title: '空差',
    editRender: {},
    slots: {
      edit: ({ row }: any) => {
        return h(VxeInput, {
          'id': `sale_weight_error_${row.id}`,
          'modelValue': row.sale_weight_error,
          'onUpdate:modelValue': (val: any) => {
            row.sale_weight_error = fourDigitNum(val)
          },
          'onChange': (event: any) => {
            row.supplier_weight_error = fourDigitNum(event.value)
          },
          'onKeyup': (val) => {
            // 不用管输入了什么东西，添加的新行和数组的末尾行一致就行
            if (val.$event.keyCode === 13)
              addOrNextLine(row, 'sale_weight_error')
          },
        })
      },
    },
  },
  {
    field: 'sale_weight_total',
    title: '结算数量',
    slots: {
      default: ({ row }: any) => {
        return currency(row.sale_weight, { precision: 4 }).subtract(row.sale_weight_error).value
      },
    },
  },
  {
    field: 'supplier_weight',
    title: '供应商细码',
    editRender: {},
    slots: {
      edit: ({ row }: any) => {
        return h(VxeInput, {
          'id': `supplier_weight_${row.id}`,
          'modelValue': row.supplier_weight,
          'onUpdate:modelValue': (val: any) => {
            row.supplier_weight = fourDigitNum(val)
          },
          'onKeyup': (val) => {
            // 不用管输入了什么东西，添加的新行和数组的末尾行一致就行
            if (val.$event.keyCode === 13)
              addOrNextLine(row, 'supplier_weight')
          },
        })
      },
    },
  },
  {
    field: 'supplier_weight_error',
    title: '供应商空差',
    editRender: {},
    slots: {
      edit: ({ row }: any) => {
        return h(VxeInput, {
          'id': `supplier_weight_error_${row.id}`,
          'modelValue': row.supplier_weight_error,
          'onUpdate:modelValue': (val: any) => {
            row.supplier_weight_error = fourDigitNum(val)
          },
          'onKeyup': (val) => {
            // 不用管输入了什么东西，添加的新行和数组的末尾行一致就行
            if (val.$event.keyCode === 13)
              addOrNextLine(row, 'supplier_weight_error')
          },
        })
      },
    },
  },
  {
    field: 'supplier_weight_total',
    title: '供应商结算数量',
    slots: {
      default: ({ row }: any) => {
        return currency(row.supplier_weight, { precision: 4 }).subtract(row.supplier_weight_error).value
      },
    },
  },
  {
    // 删除
    type: 'html',
    title: '操作',
    width: 100,
    slots: {
      default: ({ row }: any) => {
        return h('div', [
          h(
            'span',
            {
              style: 'color: red;cursor: pointer;',
              onClick: () => {
                const index = fineCodeData.value.findIndex((item: any) => item === row)
                fineCodeData.value.splice(index, 1)
                nextTick(() => {
                  GridTableRef.value.TableRef.loadData(fineCodeData.value)
                })
              },
            },
            '删除',
          ),
        ])
      },
      footer: () => {
        return h(
          'span',
          {
            style: 'color: red;cursor: pointer;',
            onClick: () => {
              deleteAll()
            },
          },
          '全部删除',
        )
      },
    },
  },
]

/**
 * 表尾方法
 * @param columns
 * @param data
 */
function footerMethod({ columns, data }: any) {
  const keyList = ['sale_weight', 'sale_weight_error', 'supplier_weight', 'supplier_weight_error']
  return [
    columns.map((column: any, columnIndex: number) => {
      if (columnIndex === 0)
        return '汇总'

      if (keyList.includes(column.property)) {
        return sumNum(data, column.property)
      }

      else if (['sale_weight_total'].includes(column.property)) {
        const sale_weight_total = sumNum(data, 'sale_weight')
        const sale_weight_error_total = sumNum(data, 'sale_weight_error')
        return currency(sale_weight_total).subtract(sale_weight_error_total).value
      }

      else if (['supplier_weight_total'].includes(column.property)) {
        const supplier_weight_total = sumNum(data, 'supplier_weight')
        const supplier_weight_error_total = sumNum(data, 'supplier_weight_error')
        return currency(supplier_weight_total).subtract(supplier_weight_error_total).value
      }

      return null
    }),
  ]
}

const headerData = ref<any>([])
const GridHeaderRef = ref<any>()
watch(
  () => props.modelValue,
  (val, _oldVal) => {
    // if (!val.modelValue) {
    //   return
    // }
    // GridTableRef.value.TableRef.loadData(fineCodeData.value)
    // 打开弹框
    showModal.value = val
    if (val) {
      // 由上级打开弹框时，初始化数据
      // 初始化头部的数据
      headerData.value[0] = JSON.parse(JSON.stringify(props.data))
      // 初始化细码的数据
      const itemData = props.data?.item_data?.map((item: any, index: number) => {
        return {
          id: index,
          ...(item.stock_id ? { stock_id: item.stock_id } : {}),
          ...(item.sum_stock_id ? { sum_stock_id: item.sum_stock_id } : {}),
          sale_weight: formatWeightDiv(item.sale_weight),
          sale_weight_error: formatWeightDiv(item.sale_weight_error),
          sale_weight_total: formatWeightDiv(item.sale_weight_total),
          supplier_weight: formatWeightDiv(item.supplier_weight),
          supplier_weight_error: formatWeightDiv(item.supplier_weight_error),
          supplier_weight_total: formatWeightDiv(item.supplier_weight_total),
        }
      })
      fineCodeData.value = itemData || [initFineCodeItem()]
      if (fineCodeData.value.length === 0) {
        fineCodeData.value = [
          {
            id: 0,
            sale_weight: 0,
            sale_weight_error: 0,
            sale_weight_total: 0,
            supplier_weight: 0,
            supplier_weight_error: 0,
            supplier_weight_total: 0,
          },
        ]
      }
      nextTick(() => {
        GridHeaderRef.value.TableRef.loadData(headerData.value)
        GridTableRef.value.TableRef.loadData(fineCodeData.value)
      })
    }
  },
  { immediate: true, deep: true },
)

function confirmModal() {
  fineCodeData.value.map((item: any) => {
    item.sale_weight = formatWeightMul(item.sale_weight)
    item.sale_weight_error = formatWeightMul(item.sale_weight_error)
    item.sale_weight_total = formatWeightMul(item.sale_weight_total)
    item.supplier_weight = formatWeightMul(item.supplier_weight)
    item.supplier_weight_error = formatWeightMul(item.supplier_weight_error)
    item.supplier_weight_total = formatWeightMul(item.supplier_weight_total)
  })
  emit('confirm', fineCodeData.value)
  fineCodeData.value = [initFineCodeItem()]
}

function cancelModal() {
  emit('cancel')
  emit('update:modelValue', false)
}

const tableConfig = {
  footerMethod,
}
</script>

<template>
  <vxe-modal v-model="showModal" show-footer title="新增退货商品" width="1310" height="700" :mask="false" :lock-view="false" :esc-closable="true" resize @close="cancelModal">
    <div class="h-full flex flex-col">
      <div class="h-[100px]">
        <GridTable ref="GridHeaderRef" :columns="headerColumn" :data="headerData" :show-pagition="false" />
      </div>
      <div class="mt-2 flex justify-end">
        <el-button type="primary" @click="addNewRow">
          新增
        </el-button>
      </div>
      <div class="flex-1 overflow-y-hidden">
        <GridTable ref="GridTableRef" :columns="fineCodeColumnList" :data="fineCodeData" :config="tableConfig" :show-pagition="false" />
      </div>
    </div>

    <template #footer>
      <el-button @click="cancelModal">
        取消
      </el-button>
      <el-button type="primary" @click="confirmModal">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>
