<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import Table from '@/components/Table.vue'
import { getPurchaseGreyFabricItemList } from '@/api/greyFabricPurchaseReturn'
import { debounce, getFilterData, resetData } from '@/common/util'
import SelectDate from '@/components/SelectDate/index.vue'
import { formatDate } from '@/common/format'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'

const emits = defineEmits(['handleSure'])

const tableRef = ref()

const state = reactive<any>({
  filterData: {
    order_code: '',
    code: '',
    name: '',
    date: [],
  },
  showModal: false,
  rowIndex: -1,
  multipleSelection: [],
})

const { fetchData, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = getPurchaseGreyFabricItemList()

const getData = debounce(async () => {
  const query = {
    purchase_time_begin: state.filterData.date && state.filterData.date !== '' && state.filterData.date.length ? formatDate(state.filterData.date[0]) : '',
    purchase_time_end: state.filterData.date && state.filterData.date !== '' && state.filterData.date.length ? formatDate(state.filterData.date[1]) : '',
    is_show_all: true,
    ...state.filterData,
  }
  await fetchData(getFilterData(query))
}, 400)

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  height: 400,
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handReset() {
  state.filterData = resetData(state.filterData)
}

const columnList = ref<any>([
  {
    field: 'order_code',
    title: '坯布采购单号',
    minWidth: 140,
  },
  {
    field: 'code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 100,
  },
  {
    field: 'finish_product_width',
    title: '成品幅宽',
    minWidth: 100,
  },
  {
    field: 'finish_product_gram_weight',
    title: '成品克重',
    minWidth: 100,
  },
  {
    field: 'needle_size',
    title: '针寸数',
    minWidth: 100,
  },
  {
    field: 'number',
    title: '匹数',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
  {
    field: 'purchase_time',
    title: '采购日期',
    minWidth: 100,
    is_date: true,
  },
  {
    field: 'create_time',
    title: '创建时间',
    minWidth: 140,
    isDate: true,
  },
  {
    field: 'in_roll',
    title: '收货匹数',
    minWidth: 100,
    isPrice: true,
    fixed: 'right',
  },
])

function handCancel() {
  state.showModal = false
}

async function handleSure() {
  if (state.multipleSelection.length > 1)
    return ElMessage.error('只允许选择一条数据')

  if (!state.multipleSelection.length)
    return ElMessage.error('请选择一条数据')

  emits('handleSure', state.multipleSelection, state.rowIndex)
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer title="关联采购订单" width="1200" height="700" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div>
      <div class="buttom-oper" style="margin-top: 10px;display: flex;justify-content: space-between;">
        <span>提示：若关联采购订单，单据审核后会对应扣减采购订单的“收货匹数”。若不关联，则不会扣减</span>
      </div>
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="坯布采购单号:" width="280">
          <template #content>
            <el-input v-model="state.filterData.order_code" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布编号:" width="230">
          <template #content>
            <el-input v-model="state.filterData.code" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布名称:" width="230">
          <template #content>
            <el-input v-model="state.filterData.name" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="采购日期:" width="350">
          <template #content>
            <SelectDate v-model="state.filterData.date" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
      <Table ref="tableRef" :config="tableConfig" :table-list="data?.list" :column-list="columnList" />
    </div>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
