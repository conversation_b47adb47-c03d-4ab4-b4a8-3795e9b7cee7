<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import Table from '@/components/Table.vue'
import { getMergeOrderList } from '@/api/saleDeliver'

const emits = defineEmits(['handleSure'])

const state = reactive<any>({
  showModal: false,
  multipleSelection: [],
  sale_id: 0,
})

const { fetchData, data, loading } = getMergeOrderList()

watch(
  () => state.showModal,
  async () => {
    if (state.showModal)
      await fetchData({ id: state.sale_id })
  },
)

const tableConfig = ref({
  loading,
  showSlotNums: true,
  showCheckBox: true,
  height: 400,
  showSort: false,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handCancel() {
  state.showModal = false
}

async function handleSure() {
  if (!state.multipleSelection.length)
    return ElMessage.error('请至少选择一条数据')

  const ids: any = []
  state.multipleSelection.forEach((item: any) => {
    ids.push(item.id)
  })
  emits('handleSure', ids)
}

const columnList = ref([
  {
    field: 'sale_order_no',
    title: '成品销售单号',
    minWidth: 100,
  },
  {
    field: 'order_no',
    title: '销售送货单号',
    minWidth: 100,
  },
  {
    field: 'src_order_no',
    title: '配布单号',
    minWidth: 100,
  },
  {
    field: 'order_time',
    title: '订单日期',
    minWidth: 100,
    is_date: true,
  },
  {
    field: 'process_factory_name',
    title: '加工厂名称',
    minWidth: 100,
  },
  {
    field: 'receive_addr',
    title: '收货地址',
    minWidth: 100,
  },
  {
    field: 'total_sale_money',
    title: '总金额',
    minWidth: 100,
    isPrice: true,
  },
])

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.title" width="1250" height="800" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="mt-[20px] mb-[20px]">
      请勾选需要撤销合并的销售送货单：
    </div>
    <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList" />
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
