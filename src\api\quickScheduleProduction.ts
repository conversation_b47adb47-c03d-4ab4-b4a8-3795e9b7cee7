import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取快速排产单列表
export function getQuickScheduleProductionList() {
  return useRequest<Api.QuickScheduleProduction.ListRequest, ResponseList<Api.QuickScheduleProduction.ListResponse>>({
    url: '/admin/v1/produce/quickScheduleProduction/getQuickScheduleProductionList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出快速排产单列表
export function getQuickScheduleProductionListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/produce/quickScheduleProduction/getQuickScheduleProductionList',
    method: 'get',
    nameFile,
  })
}

// 获取快速排产单详情
export function getQuickScheduleProductionDetail() {
  return useRequest<Api.QuickScheduleProduction.DetailRequest, Api.QuickScheduleProduction.DetailResponse>({
    url: '/admin/v1/produce/quickScheduleProduction/getQuickScheduleProductionDetail',
    method: 'get',
  })
}

// 新增快速排产单
export function addQuickScheduleProduction() {
  return useRequest<Api.QuickScheduleProduction.AddRequest, void>({
    url: '/admin/v1/produce/quickScheduleProduction/addQuickScheduleProduction',
    method: 'post',
  })
}

// 编辑快速排产单
export function editQuickScheduleProduction() {
  return useRequest<Api.QuickScheduleProduction.EditRequest, void>({
    url: '/admin/v1/produce/quickScheduleProduction/updateQuickScheduleProduction',
    method: 'put',
  })
}

// 删除快速排产单
export function deleteQuickScheduleProduction() {
  return useRequest<Api.QuickScheduleProduction.DeleteRequest, void>({
    url: '/admin/v1/produce/quickScheduleProduction/deleteQuickScheduleProduction',
    method: 'delete',
  })
}

// 批量删除快速排产单
export function batchDeleteQuickScheduleProduction() {
  return useRequest<Api.QuickScheduleProduction.BatchDeleteRequest, void>({
    url: '/admin/v1/produce/quickScheduleProduction/batchDeleteQuickScheduleProduction',
    method: 'delete',
  })
}

// 审核快速排产单
export function auditQuickScheduleProduction() {
  return useRequest<Api.QuickScheduleProduction.AuditRequest, void>({
    url: '/admin/v1/produce/quickScheduleProduction/auditQuickScheduleProduction',
    method: 'put',
  })
}

// 取消审核快速排产单
export function cancelAuditQuickScheduleProduction() {
  return useRequest<Api.QuickScheduleProduction.CancelAuditRequest, void>({
    url: '/admin/v1/produce/quickScheduleProduction/cancelAuditQuickScheduleProduction',
    method: 'put',
  })
}

// 获取生产通知单下拉列表（用于快速排产）
export function getProductionNoticeDropdownForQuick() {
  return useRequest<void, ResponseList<Api.QuickScheduleProduction.ProductionNoticeOption>>({
    url: '/admin/v1/produce/productionNotifyOrder/getProductionNotifyOrderDropdownListForQuick',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取机台下拉列表
export function getMachineDropdownList() {
  return useRequest<void, ResponseList<Api.QuickScheduleProduction.MachineOption>>({
    url: '/admin/v1/basic_data/machine/getMachineDropdownList',
    method: 'get',
  })
}

// 获取织工下拉列表
export function getWeaverDropdownList() {
  return useRequest<void, ResponseList<Api.QuickScheduleProduction.WeaverOption>>({
    url: '/admin/v1/basic_data/weaver/getWeaverDropdownList',
    method: 'get',
  })
}

// 批量分配机台
export function batchAssignMachine() {
  return useRequest<Api.QuickScheduleProduction.BatchAssignMachineRequest, void>({
    url: '/admin/v1/produce/quickScheduleProduction/batchAssignMachine',
    method: 'put',
  })
}

// 批量分配织工
export function batchAssignWeaver() {
  return useRequest<Api.QuickScheduleProduction.BatchAssignWeaverRequest, void>({
    url: '/admin/v1/produce/quickScheduleProduction/batchAssignWeaver',
    method: 'put',
  })
}

// 批量设置开始时间
export function batchSetStartTime() {
  return useRequest<Api.QuickScheduleProduction.BatchSetStartTimeRequest, void>({
    url: '/admin/v1/produce/quickScheduleProduction/batchSetStartTime',
    method: 'put',
  })
}

// 获取快速排产统计信息
export function getQuickScheduleStatistics() {
  return useRequest<Api.QuickScheduleProduction.StatisticsRequest, Api.QuickScheduleProduction.StatisticsResponse>({
    url: '/admin/v1/produce/quickScheduleProduction/getQuickScheduleStatistics',
    method: 'get',
  })
}
