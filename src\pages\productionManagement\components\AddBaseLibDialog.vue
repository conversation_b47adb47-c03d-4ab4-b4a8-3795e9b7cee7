<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import Table from '@/components/Table.vue'
import { GetGreyFabricInfoListUseByOthers, GetPurchaseGreyFabricItemList } from '@/api/greyFabricPurchase'
import { debounce, getFilterData } from '@/common/util'

export interface Props {
  api: number
}

const props = withDefaults(defineProps<Props>(), {
  api: -1,
})

const emits = defineEmits(['handleSure'])

const state = reactive({
  filterData: {
    code: '',
    name: '',
    order_code: '',
  },
  showModal: false,
  modalName: '添加坯布',
  multipleSelection: [],
  apiString: '',
  // rowIndex: -1,
})

const {
  fetchData: fetchData1,
  data: data1,
  total: total1,
  loading: loading1,
  page: page1,
  size: size1,
  handleSizeChange: handleSizeChange1,
  handleCurrentChange: handleCurrentChange1,
} = GetGreyFabricInfoListUseByOthers()

const {
  fetchData: fetchData2,
  data: data2,
  total: total2,
  loading: loading2,
  page: page2,
  size: size2,
  handleSizeChange: handleSizeChange2,
  handleCurrentChange: handleCurrentChange2,
} = GetPurchaseGreyFabricItemList()

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  {
    deep: true,
  },
)

const tableConfig = reactive<any>({
  loading: props.api === 1 ? loading1 : loading2,
  showPagition: true,
  showSlotNums: true,
  page: props.api === 1 ? page1 : page2,
  size: props.api === 1 ? size1 : size2,
  total: props.api === 1 ? total1 : total2,
  showCheckBox: true,
  showSort: false,
  handleSizeChange: (val: number) => (props.api === 1 ? handleSizeChange1(val) : handleSizeChange2(val)),
  handleCurrentChange: (val: number) => (props.api === 1 ? handleCurrentChange1(val) : handleCurrentChange2(val)),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

function getData() {
  props.api === 1 ? fetchData1(getFilterData(state.filterData)) : fetchData2(getFilterData(state.filterData))
  tableConfig.total = props.api === 1 ? total1 : total2
  tableConfig.page = props.api === 1 ? page1 : page2
  tableConfig.size = props.api === 1 ? size1 : size2
}

const columnList = ref([
  {
    field: 'order_code',
    title: '坯布采购单号',
  },
  {
    field: 'code',
    title: '坯布编号',
  },
  {
    field: 'name',
    title: '坯布名称',
  },
  {
    field: 'grey_fabric_width',
    title: '坯布幅宽',
  },
  {
    field: 'grey_fabric_gram_weight',
    title: '坯布克重',
  },
  {
    field: 'finish_product_width',
    title: '成品幅宽',
  },
  {
    field: 'finish_product_gram_weight',
    title: '成品克重',
    isWeight: true,
  },
  {
    field: 'needle_size',
    title: '针寸数',
  },
  {
    field: 'number',
    title: '匹数',
    isPrice: true,
  },
  // {
  //   field: '',
  //   title: '备注',
  // },
  {
    field: 'create_time',
    title: '创建时间',
    isDate: true,
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handCancel() {
  state.showModal = false
}

function handleSure() {
  if (!state.multipleSelection.length)
    return ElMessage.error('请选择一条数据')
  else
    emits('handleSure', state.multipleSelection)
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="1200" height="700" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <el-descriptions class="mb-[20px]" :column="4" border size="small">
      <el-descriptions-item label="坯布采购单号">
        <el-input v-model="state.filterData.order_code" />
      </el-descriptions-item>
      <el-descriptions-item label="坯布编号">
        <el-input v-model="state.filterData.code" />
      </el-descriptions-item>
      <el-descriptions-item label="坯布名称">
        <el-input v-model="state.filterData.name" />
      </el-descriptions-item>
    </el-descriptions>
    <Table :config="tableConfig" :table-list="props.api === 1 ? data1?.list : data2?.list" :column-list="columnList" />
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
