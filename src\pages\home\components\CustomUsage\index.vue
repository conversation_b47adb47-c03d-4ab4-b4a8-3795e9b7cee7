<script setup lang="ts" name="CustomUsage">
import { nextTick, reactive, ref } from 'vue'
import { ElTree } from 'element-plus'
import { routeListStore } from '@/stores/routerList'

const emits = defineEmits(['handleConfirm'])

const routerList = routeListStore()

const treeRef = ref<InstanceType<typeof ElTree>>()
const state = reactive({
  showModal: false,
  modalName: '自定义常用功能',
  loading: false,
})

async function handleConfirm() {
  const checkedKey = treeRef.value!.getCheckedKeys()
  const checkedKeyNode = treeRef.value!.getCheckedNodes().filter((item: any) => {
    return item.sub_menu.length === 0
  }).map((item: any) => {
    return {
      id: item.id,
      icon: item.avatar_url,
      path: item.resource_router_name,
      sort: 0,
      title: item.name,
    }
  })
  emits('handleConfirm', checkedKey, checkedKeyNode)
}

/** 关闭弹窗 */
function handleClose() {
  state.showModal = false
}

function setCheckedKeys(list: any) {
  nextTick(() => treeRef.value!.setCheckedKeys(list, true))
}

defineExpose({
  state,
  setCheckedKeys,
})
</script>

<template>
  <vxe-modal
    v-model="state.showModal"
    show-footer
    :title="state.modalName"
    width="700" height="50vh"
    :mask="false"
    :lock-view="false"
    :esc-closable="true"
    resize
    :loading="false"
    @close="handleClose"
  >
    <ElTree
      ref="treeRef"
      :data="routerList.list" node-key="id" :props="{
        children: 'sub_menu',
        value: 'id',
        label: 'name',
      }"
      show-checkbox
    >
      <template #default="{ node, data }">
        <span class="flex items-center custom-tree-node">
          <svg-icon :name="data.avatar_url" size="15" />
          <span class="ml-[6px]">{{ node.label }}</span>
        </span>
      </template>
    </ElTree>
    <template #footer>
      <el-button @click="handleClose">
        取消
      </el-button>
      <el-button type="primary" @click="handleConfirm">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style scoped lange="scss"></style>
