<script setup lang="ts" name="FundsReceived">
import { onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { Plus } from '@element-plus/icons-vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import { debounce, deleteToast, getFilterData } from '@/common/util'
import { formatDate, formatTwoDecimalsDiv } from '@/common/format'
import { getActuallyCollectOrderList, updateActuallyCollectOrderStatusPass, updateActuallyCollectOrderStatusWait } from '@/api/fundsReceived'
import FildCard from '@/components/FildCard.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import SelectSettleAccountDialog from '@/components/SelectSettleAccountDialog/index.vue'

// import { BusinessUnitIdEnum } from '@/common/enum'

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
})

const router = useRouter()
const { fetchData, data, total, page, size, loading, handleSizeChange, handleCurrentChange } = getActuallyCollectOrderList()
const mainOptions = reactive<any>({
  multipleSelection: [],
  tableConfig: {
    showSlotNums: true,
    loading,
    showPagition: true,
    page,
    size,
    total,
    showCheckBox: true,
    showOperate: true,
    operateWidth: '8%',
    height: '100%',
    showSort: false,
    handleSizeChange,
    handleCurrentChange,
    handAllSelect: (val: any) => mainOptions.handAllSelect(val),
    handleSelectionChange: (val: any) => mainOptions.handleSelectionChange(val),
  },
  mainList: [],
  columnList: [
    {
      sortable: true,
      field: 'order_no',
      title: '单据编号',
      fixed: 'left',
      width: '8%',
      soltName: 'order_no',
    },
    {
      sortable: true,
      field: 'customer_name',
      title: '客户名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'sale_system_name',
      title: '营销体系名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'seller_name',
      title: '销售员',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'collect_price',
      title: '实收金额',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'settle_type_name',
      title: '收款账户',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'offset_price',
      title: '优惠金额',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'discount_price',
      title: '折扣金额',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'deduction_price',
      title: '扣款金额',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'receive_collect_date',
      title: '收款日期',
      minWidth: 100,
      is_date: true,
    },
    {
      sortable: true,
      field: 'remark',
      title: '备注',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'creator_name',
      title: '创建人',
      width: 100,
    },
    {
      sortable: true,
      field: 'create_time',
      title: '创建时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'update_user_name',
      title: '修改人',
      width: 100,
    },
    {
      sortable: true,
      field: 'update_time',
      title: '修改时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'auditor_name',
      title: '审核人',
      width: 100,
    },
    {
      sortable: true,
      field: 'audit_time',
      title: '审核时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'audit_status',
      title: '单据状态',
      showOrder_status: true,
      soltName: 'audit_status',
      fixed: 'right',
      width: '5%',
    },
  ],
  // 表格选中事件
  handAllSelect: ({ records }: any) => {
    mainOptions.multipleSelection = records
  },
  handleSelectionChange: ({ records }: any) => {
    mainOptions.multipleSelection = records
  },
})
// 审核
const { fetchData: auditFetch, success: auditSuccess, msg: auditMsg } = updateActuallyCollectOrderStatusPass()
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id.toString() })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
const { fetchData: cancelFetch, success: cancelSuccess, msg: cancelMsg } = updateActuallyCollectOrderStatusWait()
async function handUnApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
// 详情
function handAdd() {
  router.push({
    name: 'FundsReceivedAdd',
  })
}
// 详情
function handDetail(row: any) {
  router.push({
    name: 'FundsReceivedDetail',
    query: {
      id: row.id,
    },
  })
}
// 编辑
function handEdit(row: any) {
  router.push({
    name: 'FundsReceivedEdit',
    query: {
      id: row.id,
    },
  })
}
// 侦听参数获取数据
const filterData = reactive<any>({
  audit_status: [],
})
// 日期回调
const warehousing_date = ref()
// 获取列表数据
async function getData() {
  const query = {
    ...filterData,
  }
  if (query.audit_status.length)
    query.audit_status = query.audit_status.join(',')

  if (warehousing_date?.value?.length) {
    query.begin_receive_collect_date = formatDate(warehousing_date.value[0])
    query.end_receive_collect_date = formatDate(warehousing_date.value[1])
  }
  await fetchData(getFilterData({ ...query }))
}

watch(
  () => data.value,
  () => {
    mainOptions.mainList
      = data.value?.list?.map((item: any) => {
        return {
          ...item,
          should_collect_price: formatTwoDecimalsDiv(item.should_collect_price),
          collect_price: formatTwoDecimalsDiv(item.collect_price),
          offset_price: formatTwoDecimalsDiv(item.offset_price),
          discount_price: formatTwoDecimalsDiv(item.discount_price),
          deduction_price: formatTwoDecimalsDiv(item.deduction_price),
        }
      }) || []
  },
  { deep: true },
)

function changeDate() {
  // warehousing_date.value = [row.date_min, row.date_max]
  getData()
}
// 初始化数据
onMounted(() => {
  getData()
})
watch(
  filterData,
  debounce(() => {
    getData()
  }, 400),
)
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="" class="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <vxe-input v-model="filterData.order_no" style="width: 100%" placeholder="单据编号" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:">
          <template #content>
            <SelectDialog
              v-model="filterData.customer_id"
              api="GetCustomerEnumList"
              :editable="true"
              :query="{ name: componentRemoteSearch.customer_name }"
              :column-list="[
                {
                  title: '客户编号',
                  minWidth: 100,
                  required: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '客户编号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '客户名称',
                  minWidth: 100,
                  colGroupHeader: true,
                  required: true,
                  childrenList: [
                    {
                      isEdit: true,
                      field: 'name',
                      title: '客户名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '电话',
                  colGroupHeader: true,
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'phone',
                      isEdit: true,
                      title: '电话',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '销售员',
                  minWidth: 100,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'seller_name',
                      title: '销售员',
                      soltName: 'seller_name',
                      isEdit: true,
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="val => (componentRemoteSearch.customer_name = val)"
            />
            <!-- <SelectComponents style="width: 100%" api="GetCustomerEnumList" label-field="name" value-field="id" v-model="filterData.customer_id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收款账户:">
          <template #content>
            <SelectSettleAccountDialog
              v-model="filterData.settle_type_id"
              field="name"
            />
            <!--            <SelectComponents v-model="filterData.settle_type_id" style="width: 100%" api="GetTypeSettleAccountsEnumList" label-field="name" value-field="id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收款时间:">
          <template #content>
            <SelectDate v-model="warehousing_date" style="width: 100%" @change-date="changeDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents v-model="filterData.audit_status" multiple api="GetAuditStatusEnum" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <!-- 表格信息 -->
    <FildCard class="table-card-full" :tool-bar="true">
      <template #right-top>
        <el-button v-has="'FundsReceived_add'" type="primary" :icon="Plus" @click="handAdd">
          新建
        </el-button>
      </template>
      <Table :config="mainOptions.tableConfig" :table-list="mainOptions.mainList" :column-list="mainOptions.columnList">
        <template #order_no="{ row }">
          <el-link type="primary" :underline="false" @click="handDetail(row)">
            {{ row?.order_no }}
          </el-link>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link v-has="'FundsReceived_detail'" type="primary" :underline="false" @click="handDetail(row)">
              查看
            </el-link>
            <el-link v-if="row.audit_status === 1 || row.audit_status === 3" v-has="'FundsReceived_edit'" type="primary" :underline="false" @click="handEdit(row)">
              编辑
            </el-link>
            <el-link v-if="row.audit_status === 1" v-has="'FundsReceived_pass'" type="primary" :underline="false" @click="handAudit(row)">
              审核
            </el-link>
            <el-link v-if="row.audit_status === 2" v-has="'FundsReceived_wait'" type="primary" :underline="false" @click="handUnApproved(row)">
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
  </div>
</template>

<style></style>
