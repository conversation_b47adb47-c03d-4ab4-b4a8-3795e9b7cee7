<template>
  <FildCard title="基础信息" :tool-bar="false">
    <div class="line"></div>
    <template v-slot:right-top>
      <el-button type="primary" v-btnAntiShake="handSubmit">提交</el-button>
    </template>
    <slot>
      <el-form ref="ruleFormRef" :model="state.form" label-width="60px" label-position="top">
        <el-descriptions :column="3" border size="small">
          <el-descriptions-item label="营销体系名称">
            {{ detalData.sale_system_name }}
          </el-descriptions-item>
          <el-descriptions-item label="供应商名称">
            {{ detalData.supplier_name }}
          </el-descriptions-item>
          <el-descriptions-item label="收货单位名称">
            {{ detalData.receipt_unit_name }}
          </el-descriptions-item>
          <el-descriptions-item label="采购日期">
            {{ formatDate(detalData.purchase_date) }}
          </el-descriptions-item>
          <el-descriptions-item label="收货日期">
            {{ formatDate(detalData.receipt_date) }}
          </el-descriptions-item>
          <el-descriptions-item label="收货地址">
            {{ detalData.receipt_Address }}
          </el-descriptions-item>
          <el-descriptions-item label="收货电话">
            {{ detalData.receipt_Phone }}
          </el-descriptions-item>
          <el-descriptions-item label="发票抬头">
            {{ detalData.fapiao_title }}
          </el-descriptions-item>
          <el-descriptions-item label="备注">
            {{ detalData.remark }}
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
    </slot>
  </FildCard>
  <FildCard title="成品信息" :tool-bar="false" class="mt-[10px]">
    <Table ref="tablesRef" :config="tableConfig" :tableList="state.tableData" :column-list="columnList"></Table>
  </FildCard>
</template>

<script lang="ts" setup name="FinishedProductProcurementChangeOrderEdit">
import { FinishProductDetail, FinishProductEdit } from '@/api/finishedProductProcurement'
import { formatDate, formatPriceDiv, formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv, sumNum } from '@/common/format'
import { formValidatePass } from '@/common/rule'
import { filterNumber, getFilterData } from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import Big from 'big.js'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const router = useRoute()
const state = reactive({
  form: {
    sale_system_id: '',
    supplier_id: '',
    receipt_unit_id: '',
    purchase_date: dayjs().format('YYYY-MM-DD'),
    receipt_date: '',
    fapiao_title: '',
    remark: '',
    sale_system_name: '',
    supplier_name: '',
    receipt_unit_name: '',
    receipt_address: '',
    receipt_phone: '',
    items: [] as any[],
  },
  tableData: [] as any[],
  fromRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    supplier_id: [{ required: true, message: '请选择供应商', trigger: 'change' }],
    receipt_unit_id: [{ required: true, message: '请选择收货单位', trigger: 'change' }],
    purchase_date: [{ required: true, message: '请选择采购日期', trigger: 'change' }],
    receipt_phone: [{ validator: formValidatePass.phone('收货电话'), trigger: 'change' }],
  },
})

const { fetchData: detailFetch, data: detalData } = FinishProductDetail()

onMounted(async () => {
  getData()
})

const getData = async () => {
  await detailFetch({
    id: router.params.id,
  })
}

watch(
  () => detalData.value,
  () => {
    if (detalData.value) {
      state.form.sale_system_id = detalData.value.sale_system_id
      state.form.supplier_id = detalData.value.supplier_id
      state.form.receipt_unit_id = detalData.value.receipt_unit_id
      state.form.purchase_date = detalData.value.purchase_date
      state.form.receipt_date = detalData.value.receipt_date
      state.form.fapiao_title = detalData.value.fapiao_title
      state.form.remark = detalData.value.remark
      state.form.sale_system_name = detalData.value.sale_system_name
      state.form.supplier_name = detalData.value.supplier_name
      state.form.receipt_unit_name = detalData.value.receipt_unit_name
      state.form.receipt_address = detalData.value.receipt_address
      state.form.receipt_phone = detalData.value.receipt_phone
      state.tableData = detalData.value?.items?.map((item: any) => {
        return {
          ...item,
          piece_weight: formatWeightDiv(item.piece_weight),
          unit_price: formatUnitPriceDiv(item.unit_price),
          length: formatTwoDecimalsDiv(item.length),
          length_unit_price: formatUnitPriceDiv(item.length_unit_price),
          paper_tube_weight: formatWeightDiv(item.paper_tube_weight),
          piece_count: formatTwoDecimalsDiv(item.piece_count),
          total_weight: formatWeightDiv(item.total_weight),
          total_price: formatPriceDiv(item.total_price),
        }
      })
    }
  }
)

const showAdd = ref(false)

const columnList = ref([
  {
    title: '',
    childrenList: [
      {
        field: 'finish_product_code',
        title: '成品编号',
        width: 150,
      },
      {
        field: 'finish_product_name',
        title: '成品名称',
        width: 100,
      },
      {
        field: 'customer_name',
        title: '所属客户',
        width: 100,
      },
      {
        field: 'color_type_name',
        title: '颜色类别',
        width: 100,
      },
      {
        field: 'color_code',
        title: '颜色编号',
        width: 100,
      },
      {
        field: 'fabric_type_name',
        title: '布种类型',
        width: 100,
      },
      {
        field: 'color_Name',
        title: '颜色名称',
        width: 100,
      },
      {
        field: 'dye_factory_color',
        title: '染厂颜色',
        width: 100,
      },
      {
        field: 'dye_factory_color_num',
        title: '染厂色号',
        width: 100,
      },
      {
        field: 'dye_factory_vat_code',
        title: '染厂缸号',
        width: 100,
        isPrice: true,
      },
      {
        field: 'finish_product_level',
        title: '成品等级',
        width: 100,
        isPrice: true,
      },
      {
        field: 'dye_craft',
        title: '染整工艺',
        width: 100,
        isPrice: true,
      },
      {
        field: 'finish_product_width',
        title: '成品幅宽',
        width: 100,
      },
      {
        field: 'finish_product_gram_weight',
        title: '成品克重',
        width: 100,
      },
      {
        field: 'paper_tube_weight',
        title: '纸筒重量',
        width: 100,
      },
      {
        field: 'finish_product_ingredient',
        title: '成品成分',
        width: 100,
      },
      {
        field: '', // TODO 后端没返回
        title: '成品工艺',
        width: 100,
      },
      {
        field: 'remark',
        title: '备注',
        width: 100,
      },
    ],
  },
  {
    title: '采购数',
    childrenList: [
      {
        field: 'piece_count',
        title: '匹数',
        width: 100,
        isPrice: true,
      },
      {
        field: 'piece_weight',
        title: '均重',
        width: 100,
        isWeight: true,
      },
      {
        field: '', // TODO 后端没返回
        title: '数量总计',
        width: 100,
        isWeight: true,
      },
      {
        field: 'unit',
        title: '单位',
        width: 100,
      },
      {
        field: 'length',
        title: '辅助数量',
        width: 100,
        isPrice: true,
      },
    ],
  },
  {
    title: '进仓数',
    childrenList: [
      {
        field: 'piece_count',
        title: '匹数',
        width: 100,
        isPrice: true,
      },
      {
        field: 'piece_weight',
        title: '均重',
        width: 100,
        isWeight: true,
      },
      {
        field: '', // TODO 后端没返回
        title: '数量总计',
        width: 100,
        isWeight: true,
      },
      {
        field: 'length',
        title: '辅助数量',
        width: 100,
        isPrice: true,
      },
    ],
  },
  {
    title: '变更数',
    childrenList: [
      {
        field: 'piece_count',
        title: '匹数',
        width: 100,
        soltName: 'piece_count',
      },
      {
        field: 'piece_weight',
        title: '均重',
        width: 100,
        soltName: 'piece_weight',
      },
      {
        field: '', // TODO 后端没返回
        title: '数量总计',
        width: 100,
      },
      {
        field: 'length',
        title: '辅助数量',
        width: 100,
        soltName: 'length',
      },
    ],
  },
  {
    title: '',
    childrenList: [
      {
        field: 'remark',
        title: '备注',
        width: 130,
      },
    ],
  },
])

const tablesRef = ref()

const onSubmit = (row: any) => {
  showAdd.value = false
  const val = row?.map((item: any) => {
    return {
      ...item,
      paper_tube_weight: formatWeightDiv(item.paper_tube_weight),
      unit: item.measurement_unit_name,
      fabric_type_name: item.type_grey_fabric_name,
      finish_product_id: parseInt(item.id),
    }
  })
  state.tableData = [...state.tableData, ...val]
  

}

const { fetchData: fetchDataAdd, success: successAdd, msg: msgAdd } = FinishProductEdit()
const ruleFormRef = ref()
const handSubmit = async () => {
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      state.form.items = state.tableData
      await fetchDataAdd(
        getFilterData({ ...state.form, purchase_date: formatDate(state.form.purchase_date), receipt_date: formatDate(state.form.receipt_date), id: parseInt(router.params.id as string) })
      )
      if (successAdd.value) {
        ElMessage.success('编辑成功')
        getData()
      } else {
        ElMessage.error(msgAdd.value)
      }
    }
  })
}

const computedData = async (changeRow: any) => {
  

  // 计算总数量
  if (filterNumber(changeRow?.piece_count) && filterNumber(changeRow?.piece_weight)) {
    

    changeRow.total_weight = parseFloat(Big(changeRow.piece_count).times(changeRow?.piece_weight).toFixed(2))
  }
  // 计算金额
  if (filterNumber(changeRow?.unit_price) && filterNumber(changeRow?.total_weight) && filterNumber(changeRow?.length) && filterNumber(changeRow?.length_unit_price)) {
    const price_weight = Big(changeRow.unit_price).times(changeRow?.total_weight)
    const price_length = Big(changeRow.length).times(changeRow?.length_unit_price)
    changeRow.total_price = parseFloat(price_weight.plus(price_length).toFixed(2))
  }
  await tablesRef.value.tableRef.updateFooter()
}

const footerCellClassName = ref<any>((row: any) => {
  

})
const FooterMethod = ({ columns, data }: any) => {
  const footData: any = {}
  const res = [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex)) {
        return '汇总'
      }
      if (['piece_count'].includes(column.property)) {
        footData[column.property] = formatPriceDiv(sumNum(data, 'piece_count') as any)
        return footData[column.property]
      }
    }),
  ]
  tableConfig.value.footerCellClassName = ({ column }: any) => {
    if (column.field === 'piece_count') {
      if (footData[column.field] > 0) {
        return 'col-red'
      } else if (footData[column.field] < 0) {
        return 'col-green'
      } else {
        return ''
      }
    }
  }
  return res
}

const tableConfig = ref({
  showSpanHeader: true,
  operateWidth: 100,
  footerMethod: (val: any) => FooterMethod(val),
  footerCellClassName: footerCellClassName.value,
})
</script>

<style lang="scss" scoped>
::v-deep(.col-green) {
  color: green;
}

::v-deep(.col-red) {
  color: red;
}
</style>
