<script setup lang="ts">
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import Table from '@/components/Table.vue'
import { getGfmSaleDeliveryOrderItemFineCodeByItemID } from '@/api/greyFabricSalesReturn'
import { debounce, deepClone, filterDataList } from '@/common/util'
import { formatPriceDiv, formatWeightDiv, sumNum, sumTotal } from '@/common/format'

const emits = defineEmits(['handleSure'])

const state = reactive<any>({
  filterData: {
    source_num: '',
    source_code: '',
  },
  showModal: false,
  modalName: '细码录入',
  multipleSelection: [],
  ximaList: [],
  canEnter: 0,
  rowIndex: -1,
  total_roll: 0,
  gfm_sale_delivery_item_id: '',
})

const { fetchData, data, total, loading, page, size } = getGfmSaleDeliveryOrderItemFineCodeByItemID()

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

onMounted(() => {
  getData()
})

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  {
    deep: true,
  },
)

const tableConfig = reactive<any>({
  showSlotNums: true,
  page,
  size,
  loading,
  total,
  showCheckBox: true,
  showSort: false,
  showOperate: true,
  height: 300,
  backSelection: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  footerMethod: (val: any) => FooterMethod1(val),
})

const tableConfig_xima = reactive<any>({
  showSlotNums: true,
  showOperate: true,
  height: 300,
  footerMethod: (val: any) => FooterMethod(val),
})

function FooterMethod1({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'roll') as any)}`

      if (['weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'weight') as any)} kg`

      return null
    }),
  ]
}

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property)) {
        state.total_roll = sumTotal(data, 'roll')
        return `${sumNum(data, 'roll')}`
      }
      if (['weight'].includes(column.property))
        return `${sumNum(data, 'weight')} kg`

      return null
    }),
  ]
}

async function getData() {
  await fetchData({ gfm_sale_delivery_item_id: Number(state.gfm_sale_delivery_item_id) })
  data.value.list?.map((item: any) => {
    item.selected = state.ximaList?.some((citem: { id: any }) => citem.id === item.id) ?? false
  })
}

const columnList = ref([
  // {
  //   field: 'grey_fabric_code',
  //   title: '坯布编号',
  // },
  // {
  //   field: 'grey_fabric_name',
  //   title: '来源类型',
  // },
  // {
  //   field: 'source_code',
  //   title: '来源单号',
  // },
  {
    field: 'roll',
    title: '匹数',
    isPrice: true,
  },
  {
    field: 'has_selected',
    title: '已选匹数',
  },
  {
    field: 'volume_number',
    title: '卷号',
  },
  {
    field: 'warehouse_bin_Name',
    title: '仓位',
  },
  {
    field: 'weight',
    title: '数量',
    isWeight: true,
  },
])

const columnList_xima = reactive([
  {
    field: 'warehouse_bin_Name',
    title: '仓位',
    minWidth: 100,
    // soltName: 'shippingSpace',
  },
  {
    field: 'volume_number',
    title: '卷号',
    minWidth: 100,
    // soltName: 'reelNumber',
  },
  //   {
  //     field: '',
  //     title: '条码',
  //     minWidth: 100,
  //     soltName: 'barCode',
  //   },
  {
    field: 'roll',
    title: '匹数',
    minWidth: 100,
    soltName: 'horsepower',
  },
  {
    field: 'weight',
    title: '数量',
    minWidth: 100,
    soltName: 'weight',
  },
])

function handAllSelect({ checked, selectCheck }: any) {
  data.value.list?.map((item: any) => {
    if (!checked) {
      item.selected = false
      item.has_selected = ''
      return item
    }
    else {
      item.has_selected = formatPriceDiv(item.roll)
      item.selected = true
      return item
    }
  })
  state.ximaList = deepClone(selectCheck)
  for (let i = 0; i < state.ximaList.length; i++) {
    state.ximaList[i].weight = Number(formatWeightDiv(state.ximaList[i].weight))
    state.ximaList[i].roll = Number(formatPriceDiv(state.ximaList[i].roll))
  }
}

function handleSelectionChange({ checked, row }: any) {
  data.value.list?.map((item: any) => {
    if (item.id === row.id) {
      if (!checked) {
        item.selected = false
        item.has_selected = ''
      }
      else {
        item.has_selected = formatPriceDiv(item.roll)
        item.selected = true
      }
      return item
    }
  })

  let filterList = []
  filterList = state.ximaList.filter((item: any) => {
    return item.id === row.id
  })

  if (!filterList.length) {
    state.ximaList.push({
      id: row.id,
      position: row?.warehouse_bin_Name,
      volume_number: row?.volume_number,
      roll: formatPriceDiv(row?.roll),
      weight: formatWeightDiv(row?.weight),
    })
  }
  else {
    state.ximaList = state.ximaList.filter((item: any) => {
      return item.id !== row.id
    })
  }

  //   state.ximaList = deepClone(selectCheck)

  //   for (let i = 0; i < state.ximaList.length; i++) {
  //     state.ximaList[i].weight = Number(formatWeightDiv(state.ximaList[i].weight))
  //     state.ximaList[i].roll = state.ximaList[i].num
  //   }
}

function handCancel() {
  state.showModal = false
}

function handleSure() {
  if (state.total_roll > state.canEnter)
    return ElMessage.error('匹数总和不可超过当前分录行填写的匹数')

  // TODO:判断已录入的匹数有没有超过上面表格可录入的匹数

  // TODO:首先将细码列表的数据去重得到对应存在的ids

  const idList: any = Array.from(new Set(state.ximaList.map((item: any) => item.id))).map(id => ({ id }))

  // TODO:根据id取出来获取录入数据汇总的匹数和数量
  for (let i = 0; i < idList?.length; i++) {
    let filterArr = []
    filterArr = state.ximaList.filter((item: any) => {
      return item.id === idList[i].id
    })

    idList[i].total_roll = sumTotal(filterArr, 'roll')
    idList[i].total_weight = sumTotal(filterArr, 'weight')
  }

  // TODO:判断录入的匹数是否大于表格最大的匹数
  for (let i = 0; i < data.value.list.length; i++) {
    const matchingObject = idList.find(obj => obj.id === data.value.list[i].id)
    if (matchingObject && matchingObject.total_roll > Number(formatPriceDiv(data.value.list[i].roll)))
      return ElMessage.error('不可大于录入大于坯布信息的匹数')

    if (matchingObject && matchingObject.total_weight > Number(formatWeightDiv(data.value.list[i]?.weight)))
      return ElMessage.error('不可大于录入大于坯布信息的总数量')
  }

  emits('handleSure', state)
}

const tableRef = ref()

watch(
  () => state.ximaList,
  () => {
    if (state.ximaList.length > 0) {
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

function handDelete(row: any, rowIndex: number) {
  state.ximaList.splice(rowIndex, 1)
  //  TODO:找出当前数据对应上面表格的索引
  const idIndex = data.value.list?.findIndex((item: any) => {
    return item.id === row.id
  })
  // TODO:将已选的数据减掉
  data.value.list[idIndex].has_selected = Number(data.value.list[idIndex].has_selected) - Number(row.roll)
}

const countNums = ref(0)

// 点击分开录入
function handEntry(row: any) {
  countNums.value = 0
  data.value.list?.map((item: any) => {
    if (row.id === item.id) {
      item.visible = true
      return item
    }
    else {
      item.visible = false
    }
  })
}

function handCancelWrite(row: any) {
  data.value.list?.map((item: any) => {
    if (row.id === item.id) {
      item.visible = false
      return item
    }
  })
}
function handSureWrite(row: any) {
  if (countNums.value === 0 || countNums.value === '')
    return ElMessage.error('录入匹数不可为0或者为空')

  if (Number(countNums.value) > Number(formatPriceDiv(row.roll))) {
    //  TODO:首先判断录入的数字是否超过当前总的匹数
    return ElMessage.error('不允许超过总匹数')
  }
  // TODO:判断当前数据是否已选择进去
  if (row.selected) {
    // TODO:将删除原本是否存在的id数据
    state.ximaList = filterDataList('id', row.id, state?.ximaList)
  }
  for (let i = 0; i < countNums.value; i++) {
    state.ximaList.push({
      id: row.id,
      position: row?.warehouse_bin_Name,
      volume_number: row?.volume_number,
      roll: '1.00',
      weight: 0,
    })
  }
  data.value.list?.map((item: any) => {
    if (item.id === row.id) {
      item.has_selected = Number(countNums.value)
      item.visible = false
      // item.visible = false
      return item
    }
  })
}

watch(
  () => state.ximaList,
  () => {
    data.value.list?.map((item: any) => {
      item.selected = state.ximaList?.some((citem: { id: any }) => citem.id === item.id) ?? false
      return item
    })
    if (!state.ximaList.length) {
      data.value.list?.map((item: any) => {
        item.has_selected = ''
        return item
      })
    }
  },
  {
    deep: true,
  },
)

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="1500" height="900" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <!-- <el-descriptions class="mb-[20px]" :column="4" border size="small">
      <el-descriptions-item label="来源类型">
        <SelectComponents api="getSourceWarehouseTypeEnum" label-field="name" value-field="id" v-model="state.filterData.source_num" clearable />
      </el-descriptions-item>
      <el-descriptions-item label="来源单号"><el-input v-model="state.filterData.source_code"></el-input></el-descriptions-item>
    </el-descriptions> -->
    <!-- <div class="flex"> -->
    <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList">
      <template #operate="{ row, rowIndex }">
        <el-popover :visible="row.visible" placement="top" :width="250">
          <div class="flex items-center">
            <p>选择</p>
            <vxe-input v-model="countNums" style="width: 100px; margin-left: 10px; margin-right: 10px" clearable type="float" :min="0" />
            <p>匹分开录入</p>
          </div>
          <div style="text-align: right; margin: 0">
            <el-button size="small" text @click="handCancelWrite(row)">
              取消
            </el-button>
            <el-button size="small" type="primary" @click="handSureWrite(row)">
              确定
            </el-button>
          </div>
          <template #reference>
            <el-button type="text" @click="handEntry(row, rowIndex)">
              分开录入
            </el-button>
          </template>
        </el-popover>
      </template>
    </Table>
    <div class="mt-[20px]">
      <Table ref="tableRef" :config="tableConfig_xima" :table-list="state?.ximaList" :column-list="columnList_xima">
        <!-- <template #barCode="{ row }">
          <vxe-input v-model="row.aa"></vxe-input>
        </template> -->
        <template #horsepower="{ row }">
          <vxe-input v-model="row.roll" type="float" placeholder="必填" />
        </template>

        <template #weight="{ row }">
          <vxe-input v-model="row.weight" :min="0" type="float" placeholder="必填">
            <template #suffix>
              Kg
            </template>
          </vxe-input>
        </template>
        <template #operate="{ row, rowIndex }">
          <el-button type="text" @click="handDelete(row, rowIndex)">
            删除
          </el-button>
        </template>
      </Table>
    </div>
    <!-- </div> -->
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
