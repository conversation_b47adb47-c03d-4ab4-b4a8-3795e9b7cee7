<script lang="ts" setup name="TransferSalesAdd">
import { nextTick, onMounted, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import currency from 'currency.js'
import XimaDialog from './components/XimaDialog.vue'
import AddSupplierDialog from './components/AddSupplierDialog.vue'
import { allBulkList } from './columnList/BulkList'
import Table from '@/components/Table.vue'
import FildCard from '@/components/FildCard.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import useRouterList from '@/use/useRouterList'
import { formatDate, sumNum } from '@/common/format'
import { EmployeeType } from '@/common/enum'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { deepClone, getCurrentDate, getDefaultSaleSystem, getFilterData } from '@/common/util'
import { addSaleTransferOrder } from '@/api/transferSales'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import { processDataIn } from '@/common/handBinary'
import SelectSaleMode from '@/components/SelectSaleMode/index.vue'
import { SaleModeEnum } from '@/enum/orderEnum'
import { SplitTypeEnum } from '@/enum'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'
import SelectProductColorDialog from '@/components/SelectProductColorDialog/index.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'

const routerList = useRouterList()

const state = reactive<any>({
  form: {
    customer_id: '',
    order_time: '',
    sale_user_id: '',
    address: '',
    contact_name: '',
    order_remark: '',
    sale_system_id: '',
    phone: '',
    sale_mode: SaleModeEnum.Bulk,
    customer_name: '',
  },
  formRules: {
    // customer_id: [{ required: true, message: '请选择客户名称', trigger: 'blur' }],
  },
  tableList: [],
  multipleSelection: [],
})

const tableConfig = ref({
  showSlotNums: true,
  showOperate: true,
  operateWidth: '80',
  footerMethod: (val: any) => FooterMethod(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

const users = JSON.parse(localStorage.getItem('user') as string)

onMounted(() => {
  const resDes = getDefaultSaleSystem()
  handAddData()
  state.form.sale_system_id = resDes?.default_sale_system_id
  state.form.sale_user_id = users.user.employee_id

  state.form.order_time = getCurrentDate()
})

const bulkSetting = ref<any>({})

const totalSaleSettleWeight = ref(0)

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${sumNum(data, 'roll')}`

      if (['sale_settle_weight'].includes(column.property)) {
        totalSaleSettleWeight.value = `${sumNum(data, 'sale_settle_weight')}`
        return totalSaleSettleWeight.value
      }
      if (['weighted_price'].includes(column.property))
        return `${sumNum(data, 'weighted_price')}`

      if (['total_sale_price'].includes(column.property))
        return `${sumNum(data, 'total_sale_price')}`

      if (['total_purchase_price'].includes(column.property))
        return `${sumNum(data, 'total_purchase_price')}`

      if (['total_weighted_price'].includes(column.property))
        return `${sumNum(data, 'total_weighted_price')}`

      if (['other_price'].includes(column.property))
        return `${sumNum(data, 'other_price')}`

      return null
    }),
  ]
}

const componentRemoteSearch = reactive({
  customer_name: '',
  customer_code: '',
})

// 选择成品
function changeProductSelect(val: any, row: any) {
  row.product_id = val?.id || ''
  row.product_code = val?.finish_product_code || ''
  row.product_name = val?.finish_product_name || ''

  // 清空色号
  row.product_color_id = ''
  row.product_color_code = ''
  row.product_color_name = ''
}
// 选择色号
function changeProductColorSelect(val: any, row: any) {
  row.product_color_id = val?.id || ''
  row.product_color_code = val?.product_color_code || ''
  row.product_color_name = val?.product_color_name || ''
}

const XimaDialogRef = ref()

function handWrite(row: any, rowIndex: number) {
  XimaDialogRef.value.state.showModal = true
  XimaDialogRef.value.state.rowIndex = rowIndex
  XimaDialogRef.value.state.isDisabled = false
  XimaDialogRef.value.state.horsepower = row.roll
  XimaDialogRef.value.state.info = {
    finish_product_code: row.product_code,
    finish_product_name: row.product_name,
    product_color_code: row.product_color_code,
    product_color_name: row.product_color_name,
    dyelot: row.dyelot_number,
    supplier_id: Number(row?.measurement_unit_id) || 0,
    supplier_name: row.supplier_name,
    measurement_unit_name: row.measurement_unit_name,
    totalSaleSettleWeight: row.sale_settle_weight,
  }

  if (Array.isArray(row?.sale_transfer_order_weight_param_list) && row?.sale_transfer_order_weight_param_list.length > 0) {
    XimaDialogRef.value.tableData = row?.sale_transfer_order_weight_param_list
  }
  else {
    //   未录入细码
    XimaDialogRef.value.tableData = []
    const sale_settle_weight = Number(row.sale_settle_weight) || 0
    XimaDialogRef.value.tableData.push({
      sale_weight: sale_settle_weight,
      sale_weight_error: 0,
      sale_settle_weight,
      supplier_weight: sale_settle_weight,
      supplier_weight_error: 0,
      supplier_selltle_weight: 0,
    })
    // XimaDialogRef.value.state.tableData[0].selltle_weight = row.sale_settle_weight
    // XimaDialogRef.value.state.tableData[0].supplier_weight = row.sale_settle_weight
    // XimaDialogRef.value.state.tableData[0].sale_weight = row.sale_settle_weight
  }
}

function handAddData() {
  state.tableList.push({
    product_id: '',
    product_color_id: '',
    dyelot_number: '',
    sale_price: '',
    roll: '',
    sale_settle_weight: '',
    supplier_id: '',
    other_price: '',
    remark: '',
    cost_price: '',
    sale_transfer_order_weight_param_list: [],
    measurement_unit_id: '',
    product_code: '',
    product_name: '',
    product_color_code: '',
    product_color_name: '',
    supplier_measurement_unit_id: '',
  })
}

const ruleFormRef = ref()

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handDelete(index: number) {
  state.tableList.splice(index, 1)
}

const tableRef = ref()

function updateFootMethod(item: any) {
  item.weighted_price = currency(item.sale_price).subtract(item.cost_price).value
  item.total_sale_price = currency(item.sale_price).multiply(item.sale_settle_weight).value
  item.total_purchase_price = currency(item.cost_price).multiply(item.sale_settle_weight).value
  item.total_weighted_price = currency(item.total_sale_price).subtract(item.total_purchase_price).value

  nextTick(() => {
    tableRef.value.tableRef?.updateFooter()
  })
}
const bulkShow = ref(false)

function bulkHand() {
  if (state.multipleSelection?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  bulkShow.value = true
}

function handBulkClose() {
  bulkShow.value = false
}

async function bulkSubmit({ row, value, quickInputResult }: any, val: any) {
  if (row?.field === 'product_code' || row?.field === 'product_name') {
    state.tableList?.map((item: any) => {
      if (item?.selected) {
        item.product_id = val.id
        item.product_code = val.finish_product_code
        item.product_name = val.finish_product_name
        return item
      }
    })
  }
  else if (row?.field === 'measurement_unit_id') {
    state.tableList?.map((item: any) => {
      if (item?.selected) {
        item.measurement_unit_id = val.id
        item.measurement_unit_name = val.name
        return item
      }
    })
  }
  else if (row?.field === 'supplier_id') {
    state.tableList?.map((item: any) => {
      if (item?.selected) {
        item.supplier_id = val.id
        item.supplier_name = val.name
        return item
      }
    })
  }
  else {
    let selectIndex = 0
    state.tableList?.map((item: any) => {
      if (item?.selected) {
        if (row.quickInput && quickInputResult?.[selectIndex]) {
          item[row.field] = quickInputResult[selectIndex]
          selectIndex++
          return item
        }

        item[row.field] = value[row.field]
        return item
      }
    })
  }
  bulkShow.value = false
  ElMessage.success('设置成功')
}

const AddSupplierDialogRef = ref()

function handAddSuppier() {
  AddSupplierDialogRef.value.state.showModal = true
  AddSupplierDialogRef.value.state.form.code = ''
  AddSupplierDialogRef.value.state.form.name = ''
  AddSupplierDialogRef.value.state.form.address = ''
  AddSupplierDialogRef.value.state.form.phone = ''
}

function handleSureXima(val: any) {
  state.tableList.map((item: any, index: number) => {
    if (index === val.rowIndex) {
      // 条码信息有变动才会触发保存 供应商单位
      item.supplier_measurement_unit_id = val.info.supplier_id
      item.sale_transfer_order_weight_param_list = val.tableData
      item.sale_settle_weight = sumNum(val.tableData, 'sale_settle_weight')
      item.roll = val.horsepower
      updateFootMethod(item)
      return item
    }
  })
  XimaDialogRef.value.state.showModal = false
}

const { fetchData: addPost, data: addData, success: addSuccess, msg: addMsg } = addSaleTransferOrder()

async function handleSure() {
  if (state.form.customer_id === '' && state.form.customer_name === '')
    return ElMessage.error('请选择/输入客户名称')

  if (state.form.customer_id === '')
    delete state.form.customer_id
  else
    delete state.form.customer_name

  if (!state.tableList.length)
    return ElMessage.error('至少添加一条调货信息')

  const list = deepClone(state.tableList)

  for (let i = 0; i < list.length; i++) {
    // if (Number(list[i].roll) < list[i].sale_transfer_order_weight_param_list?.length) {
    //   return ElMessage.error('匹数不可以超过录入的细码')
    // }
    // if (Number(list[i].roll) > list[i].sale_transfer_order_weight_param_list?.length) {
    //   return ElMessage.error('未录入全部细码')
    // }
    if (list[i].product_id === '') {
      delete list[i].product_id
    }
    else {
      delete list[i].product_code
      delete list[i].product_name
    }
    if (list[i].product_color_id === '') {
      delete list[i].product_color_id
    }
    else {
      delete list[i].product_color_code
      delete list[i].product_color_name
    }

    // 判断大货还是散剪,匹数大于0为大货，否则为散剪
    list[i].split_type = Number(list[i].roll) > 0 ? SplitTypeEnum.Bulk : SplitTypeEnum.Plate

    list[i] = processDataIn(list[i])

    for (let q = 0; q < list[i].sale_transfer_order_weight_param_list?.length; q++) {
      if (list[i].sale_transfer_order_weight_param_list[q].sale_weight === '')
        return ElMessage.error('细码数量不可为空')

      if (list[i].sale_transfer_order_weight_param_list[q].supplier_weight === '')
        return ElMessage.error('供应商细码数量不可为空')
    }
  }

  state.form.order_time = formatDate(state.form.order_time)

  const query = {
    ...state.form,
    sale_transfer_order_detail_param_list: list,
  }
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(getFilterData(query))
      if (addSuccess.value) {
        ElMessage.success('成功')
        routerList.push({ name: 'TransferSalesDetail', query: { id: addData.value.id, order_type: 1 } })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

const SelectComponentsRef = ref()

function handleSureSupplier() {
  SelectComponentsRef.value.getFetch()
}

const columnList = ref([
  {
    field: 'product_code',
    title: '面料编号',
    minWidth: 140,
    soltName: 'product_code',
    required: true,
  },
  {
    field: 'product_name',
    title: '面料名称',
    minWidth: 140,
    soltName: 'product_name',
    required: true,
  },
  {
    field: 'color_code',
    title: '色号',
    minWidth: 140,
    soltName: 'color_code',
  },
  {
    field: 'color_name',
    title: '颜色',
    minWidth: 140,
    soltName: 'color_name',
  },
  {
    field: 'dyelot',
    title: '缸号',
    minWidth: 100,
    soltName: 'dyelot',
  },
  {
    field: 'sale_price',
    title: '销售单价',
    minWidth: 100,
    soltName: 'sale_price',
    required: true,
  },
  {
    field: 'cost_price',
    title: '采购价',
    minWidth: 100,
    soltName: 'cost_price',
  },
  {
    field: 'measurement_unit_id',
    title: '单位',
    minWidth: 100,
    soltName: 'measurement_unit_id',
  },
  {
    field: 'roll',
    title: '匹数',
    minWidth: 100,
    soltName: 'roll',
  },
  {
    field: 'sale_settle_weight',
    title: '结算数量',
    minWidth: 100,
    soltName: 'sale_settle_weight',
    required: true,
  },
  {
    field: 'supplier_id',
    title: '供应商',
    minWidth: 100,
    required: true,
    soltName: 'supplier_id',
  },
  {
    field: 'weighted_price',
    title: '单位毛利',
    minWidth: 100,
    soltName: 'weighted_price',
  },
  {
    field: 'total_sale_price',
    title: '销售总额',
    minWidth: 100,
  },
  {
    field: 'total_purchase_price',
    title: '采购总额',
    minWidth: 100,
  },
  {
    field: 'total_weighted_price',
    title: '总毛利',
    minWidth: 100,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
    soltName: 'remark',
  },
  {
    field: 'other_price',
    title: '其他金额',
    minWidth: 100,
    soltName: 'other_price',
  },
  {
    field: '',
    title: '细码',
    fixed: 'right',
    minWidth: 120,
    soltName: 'xima',
  },
])

const bulkList = reactive<any>(allBulkList)

// 选择客户
function handleChangeCustomer(val: any) {
  state.form.sale_user_id = val.seller_id ? val.seller_id : ''
  state.form.contact_name = val.contact_name
  state.form.address = val.address
  state.form.phone = val.phone
  componentRemoteSearch.customer_name = val.name
  componentRemoteSearch.customer_code = val.code
}

/**
 * 1、行细码是0且框内只有一条全是0的信息
 * 2、只要结算数量和行的相等就是录完了
 * @param row
 */
function 录完了没(row: any) {
  if (row.roll === '')
    return false

  if (Number(row.roll) !== 0) {
    const 框内总结算数量 = sumNum(row.sale_transfer_order_weight_param_list, 'sale_settle_weight')
    const 当前行结算数量 = Number(row.sale_settle_weight) || 0
    return 当前行结算数量 === 框内总结算数量
  }
  const 框内总匹数 = sumNum(row.sale_transfer_order_weight_param_list, 'horsepower')
  return 框内总匹数 === 0
}

function 未录匹数(row: any) {
  if (row.roll === '')
    return '请输入匹数'

  const 框内总匹数 = sumNum(row.sale_transfer_order_weight_param_list, 'horsepower')
  const 当前行匹数 = Number(row.roll) || 0
  return `${当前行匹数 - 框内总匹数}匹未录`
}
</script>

<template>
  <FildCard title="客户信息" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="客户名称" required>
          <template #content>
            <el-form-item prop="customer_id">
              <SelectCustomerDialog
                v-model="state.form.customer_id"
                field="name"
                :default-value="{
                  id: state.form.customer_id,
                  name: componentRemoteSearch.customer_name,
                  code: componentRemoteSearch.customer_code,
                }"
                @change-value="handleChangeCustomer"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单日期">
          <template #content>
            <el-form-item prop="date">
              <el-date-picker v-model="state.form.order_time" type="date" placeholder="订单日期" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售员">
          <template #content>
            <SelectComponents
              v-model="state.form.sale_user_id"
              api="Adminemployeelist"
              :query-config="{ pagination: false }"
              :query="{ duty: EmployeeType.salesman }"
              label-field="name"
              value-field="id"
              clearable
              virtual
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单备注" copies="2">
          <template #content>
            <el-input v-model="state.form.order_remark" show-word-limit clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="联系人员">
          <template #content>
            <el-input v-model="state.form.contact_name" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="联系电话">
          <template #content>
            <el-input v-model="state.form.phone" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单类型">
          <template #content>
            <SelectSaleMode v-model="state.form.sale_mode" :show-customer-book="false" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货地址" copies="2">
          <template #content>
            <el-input v-model="state.form.address" clearable />
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="调货信息" :tool-bar="false" class="mt-[5px]">
    <template #right-top>
      <el-button type="danger" plain @click="handAddSuppier">
        新增供应商
      </el-button>
      <el-button @click="handAddData">
        新增
      </el-button>
      <el-button type="primary" @click="bulkHand">
        批量操作
      </el-button>
    </template>
    <Table ref="tableRef" :config="tableConfig" :table-list="state.tableList" :column-list="columnList">
      <template #product_code="{ row }">
        <SelectProductDialog
          v-model="row.product_id"
          field="finish_product_code"
          :default-value="{
            id: row.product_id,
            finish_product_name: row.product_name,
            finish_product_code: row.product_code,
          }"
          @change-value="(val: any) => changeProductSelect(val, row)"
        />
      </template>
      <template #product_name="{ row }">
        <SelectProductDialog
          v-model="row.product_id"
          :default-value="{
            id: row.product_id,
            finish_product_name: row.product_name,
            finish_product_code: row.product_code,
          }"
          @change-value="(val: any) => changeProductSelect(val, row)"
        />
      </template>
      <template #color_code="{ row }">
        <SelectProductColorDialog
          v-model="row.product_color_id"
          field="product_color_code"
          :query="{
            finish_product_id: row.product_id,
          }"
          :disabled="!row.product_id"
          :default-value="{
            id: row.product_color_id,
            product_color_name: row.product_color_name,
            product_color_code: row.product_color_code,
          }"
          @change-value="(val: any) => changeProductColorSelect(val, row)"
        />
      </template>
      <template #color_name="{ row }">
        <SelectProductColorDialog
          v-model="row.product_color_id"
          :query="{
            finish_product_id: row.product_id,
          }"
          :disabled="!row.product_id"
          :default-value="{
            id: row.product_color_id,
            product_color_name: row.product_color_name,
            product_color_code: row.product_color_code,
          }"
          @change-value="(val: any) => changeProductColorSelect(val, row)"
        />
      </template>
      <template #dyelot="{ row }">
        <vxe-input v-model="row.dyelot_number" maxlength="20" clearable />
      </template>
      <template #sale_price="{ row }">
        <vxe-input v-model="row.sale_price" :min="0" type="float" clearable @change="updateFootMethod(row)" />
      </template>
      <template #cost_price="{ row }">
        <vxe-input v-model="row.cost_price" :min="0" type="float" clearable @change="updateFootMethod(row)" />
      </template>
      <template #measurement_unit_id="{ row }">
        <SelectComponents
          v-model="row.measurement_unit_id"
          api="getInfoBaseMeasurementUnitEnumList"
          id-select-default-value
          label-field="name"
          value-field="id"
          clearable
          @change-value="val => (row.measurement_unit_name = val.name)"
        />
      </template>
      <template #roll="{ row }">
        <vxe-input v-model="row.roll" :min="0" type="float" clearable />
      </template>
      <template #sale_settle_weight="{ row }">
        <vxe-input v-model="row.sale_settle_weight" :min="0" type="float" clearable @change="updateFootMethod(row)" />
      </template>
      <template #supplier_id="{ row }">
        <SelectComponents
          ref="SelectComponentsRef"
          v-model="row.supplier_id"
          api="BusinessUnitSupplierEnumAll"
          label-field="name"
          value-field="id"
          clearable
          @change-value="val => (row.supplier_name = val.name)"
        />
      </template>
      <template #other_price="{ row }">
        <vxe-input v-model="row.other_price" type="float" clearable />
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" maxlength="64" clearable />
      </template>
      <template #xima="{ row, rowIndex }">
        <div class="flex items-center">
          <el-button type="primary" text link @click="handWrite(row, rowIndex)">
            录入
          </el-button>
          <div v-if="录完了没(row)" class="text-[#b5b39f] ml-[5px]">
            (已录完)
          </div>
          <div v-else class="text-[#efa6ae] ml-[5px]">
            ({{ 未录匹数(row) }})
          </div>
        </div>
      </template>
      <template #weighted_price="{ row }">
        <div v-if="Number(row.weighted_price) < 0" class="red_font">
          {{ row.weighted_price }}
        </div>
        <div v-else>
          {{ row.weighted_price }}
        </div>
      </template>
      <template #operate="{ rowIndex }">
        <el-button text type="danger" @click="handDelete(rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <AddSupplierDialog ref="AddSupplierDialogRef" @handle-sure="handleSureSupplier" />
  <XimaDialog ref="XimaDialogRef" @handle-sure="handleSureXima" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
</template>

<style lang="scss" scoped>
.red_font {
  color: red;
  font-weight: 500;
}
</style>
