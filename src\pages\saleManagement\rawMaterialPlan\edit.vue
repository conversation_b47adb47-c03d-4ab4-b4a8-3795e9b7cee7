<script lang="ts" setup name="RawMaterialPlanEdit">
import { onActivated, onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import PlanDialog from './components/PlanDialog.vue'
import Table from '@/components/Table.vue'
import FildCard from '@/components/FildCard.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { deepClone, deleteToast, orderStatusConfirmBox } from '@/common/util'
import {
  formatDate,
  formatPriceDiv,
  formatPriceMul,
  formatRateDiv,
  formatWeightDiv,
  formatWeightMul,
  sumNum,
} from '@/common/format'
import StatusColumn from '@/components/StatusColumn/index.vue'
import {
  getPmcGreyPlanOrder,
  saveGfDetailDetails,
  updatePmcGreyPlanOrderAuditStatusCancel,
  updatePmcGreyPlanOrderAuditStatusPass,
  updatePmcGreyPlanOrderAuditStatusReject,
  updatePmcGreyPlanOrderAuditStatusWait,
} from '@/api/materialPlan'
import useRouterList from '@/use/useRouterList'

const { fetchData: getFetch, data: fabricList } = getPmcGreyPlanOrder()

const route = useRoute()

onMounted(() => {
  getData()
})

async function getData() {
  await getFetch({ id: route.query.id })
}

watch(
  () => fabricList.value,
  () => {
    if (fabricList.value) {
      fabricList.value.items?.map((item: any) => {
        item.gf_detail_items?.map((it: any) => {
          it.count = 0
          it.stock_message_list?.map((edg: any) => {
            edg.pmc_weight = formatWeightDiv(edg.pmc_weight)
            return edg
          })
        })
      })
    }
  },
)

const tableConfig = ref({
  showSlotNums: true,
  showOperate: true,
  operateWidth: '80',
  //   footerMethod: (val: any) => FooterMethod(val),
  fieldApiKey: 'RawMaterialPlanEdit',
  footerMethod: (val: any) => FooterMethod(val),
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'roll') as any)}`

      if (['weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'weight') as any)} kg`

      if (['other_price'].includes(column.property))
        return `￥${formatPriceDiv(sumNum(data, 'other_price') as any)}`

      if (['total_price'].includes(column.property))
        return `￥${formatPriceDiv(sumNum(data, 'total_price') as any)}`

      if (['yarn_ratio'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'yarn_ratio') as any)}%`

      if (['yarn_loss'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'yarn_loss') as any)}%`

      if (['use_yarn_quantity'].includes(column.property)) {
        return `${formatWeightDiv(
          sumNum(data, 'use_yarn_quantity') as any,
        )} kg`
      }
      return null
    }),
  ]
}

const PlanDialogRef = ref()

function handEdit(row: any, rowIndex: number) {
  const { customer_name, customer_id } = fabricList.value
  PlanDialogRef.value.state.showModal = true
  PlanDialogRef.value.state.info = row
  PlanDialogRef.value.state.rowIndex = rowIndex
  PlanDialogRef.value.state.row_id = row?.id
  PlanDialogRef.value.state.customer_name = customer_name
  PlanDialogRef.value.state.customer_id = customer_id

  const arr = deepClone(row?.gf_detail_items)
  arr.map((item: any) => {
    item.roll = formatPriceDiv(item.roll)
    item.weight = formatWeightDiv(item.weight)
    item.raw_material_weight = formatWeightDiv(item.raw_material_weight)
    item.delivery_time = formatDate(item.delivery_time)
    item.old_push_type = item.push_type
    item.push_type = !item.push_type ? null : item.push_type

    return item
  })
  PlanDialogRef.value.state.tableData = deepClone(arr)
}

const {
  fetchData: putFetch,
  success: putSuccess,
  msg: putMsg,
} = saveGfDetailDetails()

async function handleSureAdd(val: any) {
  const res = await deleteToast('是否确认提交?')
  const list = deepClone(val?.tableData)

  if (res) {
    if (!list?.length)
      return ElMessage.error('至少要有一行计划')

    for (let i = 0; i < list.length; i++) {
      if (list[i].push_type === '')
        return ElMessage.error('请选择计划类型')

      if (list[i]?.stock_message_list?.length) {
        if (
          list[i].push_type === 1
          && Number(list[i].weight)
          !== Number(sumNum(list[i]?.stock_message_list, 'pmc_weight'))
        ) {
          return ElMessage.error(
            '每一行“现货出货”填写的数量需要与对应选取库存中的总数量相等',
          )
        }
      }

      list[i].roll = formatPriceMul(list[i].roll)
      list[i].weight = formatWeightMul(list[i].weight)
      list[i].raw_material_id = Number(list[i].raw_material_id)
      list[i].raw_material_color_id = Number(list[i].raw_material_color_id)
      list[i].raw_material_weight = formatWeightMul(
        list[i].raw_material_weight,
      )
      list[i].dye_raw_material_factory_id = Number(
        list[i].dye_raw_material_factory_id,
      )
      list[i].raw_material_supplier_id = Number(
        list[i].raw_material_supplier_id,
      )
      list[i].shipping_unit_id = Number(list[i].shipping_unit_id)
      list[i].push_type = Number(list[i].push_type)
      list[i].delivery_time = formatDate(list[i].delivery_time)
      for (let j = 0; j < list[i]?.stock_message_list?.length; j++) {
        if (list[i].stock_message_list[j].pmc_weight === '')
          return ElMessage.error('请输入选取库存中的数量')

        list[i].stock_message_list[j].pmc_whole_piece_count = formatPriceMul(
          list[i].stock_message_list[j].pmc_whole_piece_count,
        )
        list[i].stock_message_list[j].pmc_bulk_piece_count = formatPriceMul(
          list[i].stock_message_list[j].pmc_bulk_piece_count,
        )
        list[i].stock_message_list[j].pmc_weight = formatWeightMul(
          list[i].stock_message_list[j].pmc_weight,
        )
      }
    }

    const query = {
      gf_detail_items: list,
      id: Number(val.row_id),
    }
    await putFetch(query)
    if (putSuccess.value) {
      ElMessage.success('成功')
      getData()
      PlanDialogRef.value.state.showModal = false
    }
    else {
      ElMessage.error(putMsg.value)
    }
  }
}

const tableRef = ref()

const router = useRouterList()

async function updateStatus(audit_status: number) {
  const id: any = route.query.id?.toString()
  if (audit_status === 4) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' },
      api: updatePmcGreyPlanOrderAuditStatusCancel,
    })
  }
  if (audit_status === 3) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' },
      api: updatePmcGreyPlanOrderAuditStatusReject,
    })
  }
  if (audit_status === 2) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' },
      api: updatePmcGreyPlanOrderAuditStatusPass,
    })
    router.push({
      name: 'RawMaterialPlanDetail',
      query: {
        id: route.query.id,
      },
    })
  }
  if (audit_status === 1) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: {
        desc: '点击消审后订单将变为待审核状态',
        title: '是否消审该订单？',
      },
      api: updatePmcGreyPlanOrderAuditStatusWait,
    })
  }
  getData()
}

const columnList = ref([
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 100,
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 100,
  },
  {
    field: 'type_name',
    title: '原料类型',
    minWidth: 100,
  },
  {
    field: 'ingredient',
    title: '原料成分',
    minWidth: 100,
  },
  {
    field: 'craft',
    title: '原料工艺',
    minWidth: 100,
  },
  {
    field: 'count',
    title: '原料支数',
    minWidth: 100,
  },
  {
    field: 'measurement_unit_name',
    title: '单位',
    minWidth: 100,
  },
  {
    field: 'raw_material_color_code',
    title: '颜色编号',
    minWidth: 100,
  },
  {
    field: 'raw_material_color_name',
    title: '颜色名称',
    minWidth: 100,
  },
  {
    field: 'weight',
    title: '数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'piece_count',
    title: '件数',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'upper_limit',
    title: '上限',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'lower_limit',
    title: '下限',
    minWidth: 100,
    isPrice: true,
  },
  // {
  //   field: 'unit_price',
  //   title: '单价',
  //   minWidth: 100,
  //   isUnitPrice: true,
  // },
  // {
  //   field: 'other_price',
  //   title: '其他金额',
  //   minWidth: 100,
  //   isPrice: true,
  // },
  // {
  //   field: 'total_price',
  //   title: '总金额',
  //   isPrice: true,
  //   minWidth: 100,
  // },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
  {
    field: 'planed_sale_weight',
    title: '已计划现货出货数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'planed_rm_pur_weight',
    title: '已计划原料采购数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'planed_rm_process_weight',
    title: '已计划原料加工数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'planed_total_weight',
    title: '已计划总数量',
    minWidth: 100,
    isWeight: true,
  },
])

onActivated(() => {
  getData()
})
</script>

<template>
  <StatusColumn
    :order_no="fabricList?.order_no"
    :status="fabricList?.audit_status"
    :status_name="fabricList?.audit_status_name"
    permission_wait_key="MaterialPlanWait"
    permission_reject_key="MaterialPlanReject"
    permission_pass_key="MaterialPlanPass"
    permission_cancel_key="MaterialPlanCancel"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  >
    <template #custom>
      <div
        class="mr-[90px] ml-[40px] justify-center flex flex-col items-center"
      >
        <div class="mb-[10px] font-medium">
          销售类型
        </div>
        <div class="text-[14px] text-[#0077ff]">
          {{ fabricList.plan_type_name }}
        </div>
      </div>
    </template>
  </StatusColumn>
  <FildCard title="基础信息" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="销售计划单号:">
        <template #content>
          {{ fabricList.sale_product_plan_order_no }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="营销体系:">
        <template #content>
          {{ fabricList.sale_system_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="客户编号:">
        <template #content>
          {{ fabricList.customer_code }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="客户名称:">
        <template #content>
          {{ fabricList.customer_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="客户电话:">
        <template #content>
          {{ fabricList.customer_phone }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="销售员:">
        <template #content>
          {{ fabricList.sale_user_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="客户合同编号:">
        <template #content>
          {{ fabricList.voucher_number }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="订单日期:">
        <template #content>
          {{ formatDate(fabricList.order_time) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="交货日期:">
        <template #content>
          {{ formatDate(fabricList.receipt_time) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="定金:">
        <template #content>
          {{ fabricList.deposit || 0 }}元
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="税率:">
        <template #content>
          {{ fabricList?.is_with_tax_rate ? "是" : "否" }}
          <span v-if="fabricList.is_with_tax_rate">，税率{{ formatRateDiv(fabricList?.sale_tax_rate) }}%</span>
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="收货地址:">
        <template #content>
          {{ fabricList?.location }}{{ fabricList.receipt_address }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="单据备注:">
        <template #content>
          {{ fabricList.internal_remark }}
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard title="原料信息" class="mt-[5px]">
    <Table
      ref="tableRef"
      :config="tableConfig"
      :table-list="fabricList?.items"
      :column-list="columnList"
    >
      <template #operate="{ row, rowIndex }">
        <el-button
          v-if="fabricList.audit_status === 1"
          type="primary"
          text
          link
          @click="handEdit(row, rowIndex)"
        >
          编辑计划
        </el-button>
      </template>
    </Table>
  </FildCard>
  <PlanDialog ref="PlanDialogRef" @handle-sure="handleSureAdd" />
</template>

<style></style>
