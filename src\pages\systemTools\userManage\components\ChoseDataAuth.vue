<script setup lang="ts">
import { ref, watch } from 'vue'
import DataAuth from '@/components/DataAuth/index.vue'
import DataAuthDetail from '@/components/DataAuth/DataAuthDetail.vue'

const props = defineProps({
  title: {
    type: Boolean,
  },
  list: {
    type: Array,
    default: () => [],
  },
  isDetail: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['submit'])

const showModal = defineModel({
  default: false,
  required: true,
})

const BackgroundDataPermission = ref([])

watch(showModal, (val) => {
  if (val)
    BackgroundDataPermission.value = props.list
})

async function handleSure() {
  emits('submit', BackgroundDataPermission.value)
}

function handCancel() {
  showModal.value = false
}
</script>

<template>
  <vxe-modal v-model="showModal" show-footer :title="title ? '新增数据权限' : '禁用数据权限'" width="350" height="700" :mask="false" :lock-view="false" :esc-closable="true" resize @close="handClose">
    <div v-if="!isDetail">
      <DataAuth v-model="BackgroundDataPermission" />
    </div>
    <div v-else>
      <DataAuthDetail v-model="BackgroundDataPermission" />
    </div>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button v-if="!isDetail" type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>
