<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import Table from '@/components/Table.vue'
import { debounce, getFilterData, resetData } from '@/common/util'
import { getShortageProductOrderDropdownList } from '@/api/productSale'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'

const emits = defineEmits(['handleSure'])

const state = reactive<any>({
  filterData: {
    order_no: '',
    sale_product_order_no: '',
    product_name: '',
    warehouse_id: '',
    product_code: '',
    product_id: '',
  },
  showModal: false,
  multipleSelection: [],
  sale_system_id: '',
  sale_customer_id: '',
  title: '添加成品',
})

const { fetchData, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = getShortageProductOrderDropdownList()

const getData = debounce(async () => {
  const query = {
    ...state.filterData,
    sale_system_id: state.sale_system_id,
    sale_customer_id: state.sale_customer_id,
  }
  await fetchData(getFilterData(query))
}, 400)

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

const componentRemoteSearch = reactive({
  finish_product_code: '',
  finish_product_name: '',
})

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  height: '100%',
  filterStatus: false,
  //   showOperate: true,
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

function handReset() {
  state.filterData = resetData(state.filterData)
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handCancel() {
  state.showModal = false
}

async function handleSure() {
  if (!state.multipleSelection.length)
    return ElMessage.error('请至少选择一条数据')

  loading.value = true
  // 这里用setTimeout是因为loading.value = true后，表格数据还没加载出来，会导致表格数据丢失，所以用setTimeout延迟一下
  setTimeout(() => {
    emits('handleSure', state.multipleSelection)
  }, 0)
}

const columnList = ref([
  {
    field: 'order_no',
    title: '成品欠货单号',
    minWidth: 100,
  },
  {
    field: 'sale_product_order_no',
    title: '成品销售单号',
    minWidth: 100,
  },
  {
    field: 'voucher_number',
    title: '凭证单号',
    minWidth: 100,
  },
  //   {
  //     field: '',
  //     title: '客户编号',
  //     minWidth: 100,
  //   },
  {
    field: 'customer_name',
    title: '客户名称',
    minWidth: 100,
  },
  {
    field: 'sale_user_name',
    title: '销售员',
    minWidth: 100,
  },
  {
    field: 'warehouse_name',
    title: '仓库名称',
    minWidth: 100,
  },
  {
    field: 'product_code',
    title: '成品编号',
    minWidth: 100,
  },
  {
    field: 'product_name',
    title: '成品名称',
    minWidth: 100,
  },
  // {
  //   field: 'finish_product_width',
  //   title: '成品幅宽',
  //   minWidth: 100,
  //   soltName: 'finish_product_width',
  // },
  // {
  //   field: 'finish_product_gram_weight',
  //   title: '成品克重',
  //   minWidth: 100,
  //   soltName: 'finish_product_gram_weight',
  // },
  {
    field: 'customer_name',
    title: '所属客户',
    minWidth: 100,
  },
  {
    field: 'customer_account_num',
    title: '款号',
    minWidth: 100,
  },
  {
    field: 'product_color_code',
    title: '色号',
    minWidth: 100,
  },
  {
    field: 'product_color_name',
    title: '颜色',
    minWidth: 100,
  },
  {
    field: 'dyelot_number',
    title: '缸号',
    minWidth: 100,
  },
  {
    field: 'product_level_name',
    title: '成品等级',
    minWidth: 100,
  },
  {
    field: 'product_remark',
    title: '成品备注',
    minWidth: 100,
  },
  {
    field: 'measurement_unit_name',
    title: '单位',
    minWidth: 100,
  },
  {
    field: 'shortage_roll',
    title: '匹数',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'shortage_weight',
    title: '数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'shortage_length',
    title: '辅助数量',
    minWidth: 100,
    isLength: true,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
  {
    field: 'create_time',
    title: '创建时间',
    minWidth: 100,
    isDate: true,
  },
])

function changePro(data: any) {
  componentRemoteSearch.finish_product_name = data.finish_product_name
  componentRemoteSearch.finish_product_code = data.finish_product_code
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.title" width="80vw" height="70vh" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-col h-full overflow-hidden">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="成品欠货单号:">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品销售单号:">
          <template #content>
            <el-input v-model="state.filterData.sale_product_order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品编号:">
          <template #content>
            <SelectProductDialog
              v-model="state.filterData.product_id"
              :label-name="componentRemoteSearch.finish_product_code"
              field="finish_product_code"
              :query="{
                finish_product_code: componentRemoteSearch.finish_product_code,
              }"
              @on-input="(val) => (componentRemoteSearch.finish_product_code = val)"
              @change-value="changePro"
            />
            <!--            <SelectDialog -->
            <!--              ref="proRef1" -->
            <!--              v-model="state.filterData.product_id" -->
            <!--              label-field="finish_product_code" -->
            <!--              :query="{ -->
            <!--                finish_product_code: componentRemoteSearch.finish_product_code, -->
            <!--              }" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品名称', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      title: '成品名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品编号', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      title: '成品编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '成品编号', -->
            <!--                }, -->
            <!--              ]" -->
            <!--              @on-input="inputValue => handleSearch('proRef1', inputValue)" -->
            <!--              @change-value="changePro('proRef1')" -->
            <!--            /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称:">
          <template #content>
            <SelectProductDialog
              v-model="state.filterData.product_id"
              :label-name="componentRemoteSearch.finish_product_name"
              field="finish_product_name"
              :query="{
                finish_product_name: componentRemoteSearch.finish_product_name,
              }"
              @on-input="(val) => (componentRemoteSearch.finish_product_name = val)"
              @change-value="changePro"
            />
            <!--            <SelectDialog -->
            <!--              ref="proRef2" -->
            <!--              v-model="state.filterData.product_id" -->
            <!--              :query="{ -->
            <!--                finish_product_name: componentRemoteSearch.finish_product_name, -->
            <!--              }" -->
            <!--              label-field="finish_product_name" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  title: '成品名称', -->
            <!--                  colGroupHeader: true, -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      title: '成品名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '成品编号', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      title: '成品编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  title: '成品名称', -->
            <!--                }, -->
            <!--              ]" -->
            <!--              @change-value="changePro('proRef2')" -->
            <!--              @on-input="inputValue => handleSearch('proRef2', inputValue)" -->
            <!--            /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
      <FildCard title="" class="mt-[10px] flex flex-1 flex-col h-full overflow-hidden" :tool-bar="false" no-shadow>
        <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList">
          <template #finish_product_gram_weight="{ row }">
            {{ row?.finish_product_gram_weight }}{{ row?.finish_product_gram_weight_unit_name }}
          </template>
          <template #finish_product_width="{ row }">
            {{ row?.finish_product_width }}{{ row?.finish_product_width_unit_name }}
          </template>
        </Table>
      </FildCard>
    </div>

    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
