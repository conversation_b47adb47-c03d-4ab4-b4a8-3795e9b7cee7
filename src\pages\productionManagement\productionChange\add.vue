<script setup lang="ts" name="ProductionChangeAdd">
import { onMounted, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import currency from 'currency.js'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import { formatDate, formatPriceDiv, formatTwoDecimalsDiv, formatTwoDecimalsMul, formatUnitPriceDiv, formatWeightDiv, formatWeightMul } from '@/common/format'
import { getProductionNotifyOrder } from '@/api/productionNotice'
import { sumNum } from '@/util/tableFooterCount'
import { AddProductionChange } from '@/api/productionChange'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import useRouterList from '@/use/useRouterList'

const routerList = useRouterList()
const route = useRoute()
const form_options = [
  {
    text: '源单单号',
    key: 'order_no',
    order_no: true,
  },
  {
    text: '生产计划单号',
    key: 'production_plan_order_no',
    pro_plan_order_no: true,
  },
  {
    text: '通知日期',
    key: 'notify_date',
  },
  {
    text: '营销体系',
    key: 'sale_system_name',
  },
  {
    text: '织厂名称',
    key: 'weaving_mill_name',
  },
  {
    text: '织厂跟单',
    key: 'weaving_mill_order_follower_name',
  },
  {
    text: '跟单电话',
    key: 'weaving_mill_order_follower_phone',
  },
  {
    text: '客户名称',
    key: 'customer_name',
  },
  {
    text: '销售员',
    key: 'sale_user_name',
  },
  {
    text: '跟单QC员',
    key: 'order_qc_user_name',
  },
  {
    text: '付款期限',
    key: 'payment_term_name',
  },
  {
    text: '交坯日期',
    key: 'receipt_grey_fabric_date',
    copies: 2,
  },
  {
    text: '收坯地址',
    key: 'receipt_grey_fabric_address',
    copies: 2,
  },
  {
    text: '发票抬头',
    key: 'invoice_header_name',
    copies: 2,
  },
  {
    text: '单据备注',
    key: 'order_remark',
    copies: 2,
  },
]
const required_options = [
  {
    text: '织造规格',
    key: 'weaving_specifications_name_list',
  },
  {
    text: '针寸数',
    key: 'needle_size',
  },
  {
    text: '总针数',
    key: 'total_needle_size',
  },
  {
    text: '织造损耗',
    key: 'weaving_loss',
    unit: '%',
  },
  {
    text: '织机品牌',
    key: 'loom_brand',
  },
  {
    text: '织机机型',
    key: 'loom_model_name',
  },
  {
    text: '安排机台数',
    key: 'machines_num',
  },
  {
    text: '上针',
    key: 'upper_needle',
    copies: 2,
  },
  {
    text: '纱批',
    key: 'yarn_batch',
    copies: 2,
  },
  {
    text: '下针',
    key: 'lower_needle',
    copies: 2,
  },
  {
    text: '排纱',
    key: 'yarn_arrange',
    copies: 2,
  },
  {
    text: '纱长',
    key: 'yarn_length',
    copies: 2,
  },
  {
    text: '包装要求',
    key: 'packaging_require',
    copies: 2,
  },
]
const state = reactive<any>({
  baseDataForm: {},
  greyList: [],
  technologicalRequirementForm: {},
  masterList: [],
})

const { fetchData: detailFetch, data, success: detailSuccess, msg: detailMsg } = getProductionNotifyOrder()

onMounted(() => {
  getData()
})

async function getData() {
  await detailFetch({ id: route.query.id })
  if (detailSuccess.value) {
    // 基础信息
    state.baseDataForm = {
      ...data.value,
      notify_date: formatDate(data.value.notify_date),
      receipt_grey_fabric_date: formatDate(data.value.receipt_grey_fabric_date),
    }
    // 坯布信息
    state.greyList = [data.value].map((item: any) => {
      return {
        ...item,
        process_price: formatUnitPriceDiv(item.process_price),
        weight_of_fabric: formatWeightDiv(item.weight_of_fabric),
        scheduling_weight: formatWeightDiv(currency(item.scheduling_weight).add(item.change_weight).value),
        scheduling_roll: Number((formatTwoDecimalsDiv(Number(item.scheduling_roll)) + formatTwoDecimalsDiv(Number(item.change_roll))).toFixed(2)),
        final_roll: formatTwoDecimalsDiv(Number(item.final_roll)),
        change_roll: 0,
        change_weight: 0,
      }
    })
    // 用料比例
    state.masterList
      = data.value.production_notify_material_ratio?.map((item: any) => {
        return {
          ...item,
          yarn_ratio: formatTwoDecimalsDiv(item.yarn_ratio),
          yarn_loss: formatTwoDecimalsDiv(item.yarn_loss),
          grey_fabric_color_name: data.value.grey_fabric_color_name,
          use_yarn_quantity: formatWeightDiv(currency(item.use_yarn_quantity).add(item.change_use_yarn_quantity).value), // 原用纱量 = 用纱量 + 变更变更用纱量
          send_yarn_quantity: formatWeightDiv(currency(item.send_yarn_quantity).add(item.change_send_yarn_quantity).value), // 原发纱量 = 发纱量 + 变更发纱量
          change_use_yarn_quantity: formatWeightDiv(item.change_use_yarn_quantity),
          change_send_yarn_quantity: formatWeightDiv(item.change_send_yarn_quantity),
          yarn_length: data.value.yarn_length,
        }
      }) || []
    // 工艺要求
    state.technologicalRequirementForm = {
      ...data.value,
      weaving_loss: `${formatTwoDecimalsDiv(data.value.weaving_loss)}%`,
      weaving_specifications_name_list: data.value.weaving_specifications.map((v: any) => v.weaving_specifications_name).join(','),
    }
  }
  else {
    ElMessage.error(detailMsg.value)
  }
}

// 坯布信息表格配置
const columnList_fabic_config = ref({
  showSlotNums: false,
})
const columnList_fabic = ref([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_width',
    soltName: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_gram_weight',
    soltName: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 100,
  },
  {
    field: 'finish_product_width',
    soltName: 'finish_product_width',
    title: '成品幅宽',
    minWidth: 100,
  },
  {
    field: 'finish_product_gram_weight',
    soltName: 'finish_product_gram_weight',
    title: '成品克重',
    minWidth: 100,
  },
  {
    field: 'weight_of_fabric',
    soltName: 'weight_of_fabric',
    title: '布匹定重',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_color_name',
    title: '织坯颜色',
    minWidth: 100,
  },
  {
    field: 'process_price',
    soltName: 'process_price',
    title: '加工单价',
    minWidth: 100,
  },
  {
    field: 'scheduling_roll',
    title: '原匹数',
    minWidth: 100,
  },
  {
    field: 'change_roll',
    soltName: 'change_roll',
    title: '变更匹数',
    minWidth: 100,
  },
  // {
  //   field: '',
  //   soltName: 'final_roll',
  //   title: '最终匹数',
  //   minWidth: 100,
  // },
  {
    field: '',
    soltName: 'scheduling_weight',
    title: '排产数量',
    minWidth: 200,
  },
])

function validReg(row: any) {
  const all = Number(row.scheduling_roll) + Number(row.change_roll)
  if (all < 0)
    ElMessage.error('原匹数 + 变更匹数不可为负数')

  row.scheduling_weight_all = formatWeightDiv(row.scheduling_weight) + formatWeightDiv(Number(row.weight_of_fabric)) * Number(row.change_roll)
  row.final_roll = row.scheduling_roll + Number(row.change_roll)
  row.change_weight = currency(row.change_roll).multiply(row.weight_of_fabric)
  setUseYarnQua(row)
}

// 用料比例表格配置
const columnList_master_config = ref({
  showSlotNums: true,
  footerMethod: (val: any) => FooterMethod(val),
})
const columnList_master = ref([
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 100,
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 100,
  },
  {
    field: 'weaving_category',
    title: '织造类别',
    minWidth: 100,
  },
  {
    field: 'yarn_length',
    title: '纱长',
    minWidth: 100,
  },
  {
    field: 'raw_material_brand',
    title: '原料品牌',
    minWidth: 100,
  },
  {
    field: 'raw_material_batch_number',
    title: '原料批号',
    minWidth: 100,
  },
  {
    field: 'color_scheme',
    title: '原料颜色',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_color_name',
    title: '织坯颜色',
    minWidth: 100,
  },
  {
    field: 'supplier_name',
    title: '供方',
    minWidth: 100,
  },
  {
    field: 'mill_private_yarn',
    soltName: 'mill_private_yarn',
    title: '织厂出料',
    minWidth: 100,
  },
  {
    field: 'yarn_ratio',
    soltName: 'yarn_ratio',
    title: '用纱比例',
    minWidth: 100,
  },
  {
    field: 'yarn_loss',
    soltName: 'yarn_loss',
    title: '用纱损耗',
    minWidth: 100,
  },
  {
    field: 'use_yarn_quantity',
    title: '原用纱量',
    minWidth: 100,
  },
  {
    field: 'change_use_yarn_quantity',
    soltName: 'change_use_yarn_quantity',
    title: '变更用纱量',
    minWidth: 130,
  },
  {
    field: 'send_yarn_quantity',
    title: '原发纱量',
    minWidth: 100,
  },
  {
    field: 'change_send_yarn_quantity',
    soltName: 'change_send_yarn_quantity',
    title: '变更发纱量',
    minWidth: 130,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
])
function FooterMethod({ columns, data }: any) {
  const footerData = [
    columns.map((column: any, columnIndex: number) => {
      if (columnIndex === 0)
        return '汇总'

      if (['change_use_yarn_quantity'].includes(column.field))
        return sumNum(data, 'change_use_yarn_quantity', '')

      if (['change_send_yarn_quantity'].includes(column.field))
        return sumNum(data, 'change_send_yarn_quantity', '')

      return null
    }),
  ]
  return footerData
}
const masterTableRef = ref()
watch(
  () => state.masterList,
  () => {
    masterTableRef.value.tableRef.updateFooter()
  },
  { deep: true },
)

// 设置发纱量
function setSendYarnQua(row: any) {
  row.change_send_yarn_quantity = row.mill_private_yarn ? 0 : row.change_use_yarn_quantity > 0 ? row.change_use_yarn_quantity : 0
}
// 设置用纱量
function setUseYarnQua(row: any) {
  const changeWeight = row.change_weight
  state.masterList.map((item: any) => {
    // 变更用纱量 = 变更重量总计 * 用纱比例 * （1 + 用纱损耗）
    const yarn_ratio_format = currency(item.yarn_ratio).divide(100)
    const yarn_loss_format = currency(item.yarn_loss).divide(100).add(1)
    const change_use_yarn_quantity = currency(changeWeight).multiply(yarn_ratio_format).multiply(yarn_loss_format).value
    item.change_use_yarn_quantity = change_use_yarn_quantity
    // 变更发纱量：
    // ①若变更用纱量为正数，则默认等于用纱量
    // ②若变更用纱量为负数，则默认为空
    item.change_send_yarn_quantity = change_use_yarn_quantity > 0 ? change_use_yarn_quantity : 0
  })
}

const { fetchData: AddFetch, data: successData, success: AddSuccess, msg: AddMsg } = AddProductionChange()
async function submitData() {
  const formData = {
    change_roll: formatTwoDecimalsMul(Number(state.greyList[0].change_roll)),
    // change_weight: formatWeightMul(state.greyList[0].weight_of_fabric * state.greyList[0].change_roll),
    change_weight: formatWeightMul(state.greyList[0].change_weight),
    final_roll: formatTwoDecimalsMul(Number(state.greyList[0].final_roll)),
    material_ratio_items: state.masterList.map((item: any) => {
      return {
        production_notify_material_ratio_id: item.id,
        use_yarn_quantity: formatWeightMul(item.use_yarn_quantity),
        send_yarn_quantity: formatWeightMul(item.send_yarn_quantity),
        change_use_yarn_quantity: formatWeightMul(item.change_use_yarn_quantity),
        change_send_yarn_quantity: formatWeightMul(item.change_send_yarn_quantity),
      }
    }),
    notify_date: state.baseDataForm.notify_date,
    production_notify_order_id: state.baseDataForm.id,
    scheduling_roll: formatTwoDecimalsMul(Number(state.greyList[0].scheduling_roll)),
    scheduling_weight: formatWeightMul(state.greyList[0].scheduling_weight),
  }
  await AddFetch(formData)
  if (AddSuccess.value) {
    ElMessage.success('提交成功')
    routerList.push({
      name: 'ProductionChangeDetail',
      query: {
        id: successData.value.id,
      },
    })
  }
  else {
    ElMessage.error(AddMsg.value)
  }
}
const router = useRouter()
// 跳转单号详情
async function jumpPlanDetail(id: number, type: number) {
  const options: any = {
    1: 'ProductionNoticeDetail',
    2: 'SheetOfProductionPlanDetail',
  }
  router.push({
    name: options[type],
    query: {
      id,
    },
  })
}

const customActiveName = ref('first')
const tableConfig1 = ref({
  showPagition: false,
  showSlotNums: true,
  showCheckBox: false,
  showOperate: false,
  height: 300,
  operateWidth: '100',
  showSort: false,
})
const columnList1 = ref([
  {
    field: 'remark',
    title: '经纱组合',
  },
  {
    field: 'total_use_weight',
    title: '总用量',
    isWeight: true,
  },
  {
    field: 'net_use_weight',
    title: '净用量',
    isWeight: true,
  },
])
const columnList2 = ref([
  {
    field: 'remark',
    title: '纬纱组合',
  },
  {
    field: 'total_use_weight',
    title: '总用量',
    isWeight: true,
  },
  {
    field: 'net_use_weight',
    title: '净用量',
    isWeight: true,
  },
])
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" @click="submitData">
        提交
      </el-button>
    </template>
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem v-for="(item, index) in form_options" :key="index" :label="`${item.text}:`">
        <template #content>
          <el-link v-if="item.order_no" @click="jumpPlanDetail(state.baseDataForm.id, 1)">
            {{ state.baseDataForm[item.key] }}
          </el-link>
          <el-link v-else-if="item.pro_plan_order_no" @click="jumpPlanDetail(state.baseDataForm.production_plan_order_id, 2)">
            {{ state.baseDataForm[item.key] }}
          </el-link>
          <span v-else>{{ state.baseDataForm[item.key] }}</span>
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard title="坯布信息" class="mt-[5px]" :tool-bar="false">
    <Table :config="columnList_fabic_config" :table-list="state.greyList" :column-list="columnList_fabic">
      <!-- 坯布克重 -->
      <!-- <template #grey_fabric_gram_weight="{ row }">{{ row.grey_fabric_gram_weight }}</template> -->
      <!-- 成品克重 -->
      <!-- <template #finish_product_gram_weight="{ row }">{{ row.finish_product_gram_weight }}</template> -->
      <!-- 布匹定重 -->
      <template #weight_of_fabric="{ row }">
        {{ row.weight_of_fabric }}{{ row.unit_name }}
      </template>
      <!-- 加工单价 -->
      <template #process_price="{ row }">
        {{ row.process_price }}{{ row.unit_name ? `元/${row.unit_name}` : '' }}
      </template>
      <!-- 排产数量 -->
      <template #scheduling_weight="{ row }">
        <div class="flex items-center">
          <span>{{ currency(row.scheduling_weight).add(row.change_weight) }}{{ row.unit_name }}</span>
          <span v-if="row.change_roll" class="ml-[5px]">
            <vxe-input
              v-model="row.change_weight" type="float" size="mini"
              :min="0 - row.scheduling_weight"
              @blur="setUseYarnQua(row)"
              @next-number="setUseYarnQua(row)"
              @prev-number="setUseYarnQua(row)"
            >
              <template #prefix>
                <el-icon v-if="row.change_roll > 0" color="green"><Top /></el-icon>
                <el-icon v-else-if="row.change_roll < 0" color="red"><Bottom /></el-icon>
              </template>
              <template #suffix> {{ row.unit_name }} </template>
            </vxe-input>
          </span>
        </div>
      </template>
      <!-- 变更匹数 -->
      <template #change_roll="{ row }">
        <vxe-input v-model="row.change_roll" type="float" :min="0 - row.scheduling_roll" @blur="validReg(row)" @next-number="validReg(row)" @prev-number="validReg(row)" />
      </template>
      <!-- 最终匹数 -->
      <template #final_roll="{ row }">
        <span>{{ row.scheduling_roll + Number(row.change_roll) }}</span>
        <span v-if="row.change_roll">
          <span v-if="row.change_roll > 0">
            （
            <span style="color: green">
              <el-icon><Top /></el-icon>
              {{ Number(row.change_roll) }}
            </span>
            ）
          </span>
          <span v-if="row.change_roll < 0">
            （
            <span style="color: red">
              <el-icon><Bottom /></el-icon>
              {{ Number(row.change_roll) }}
            </span>
            ）
          </span>
        </span>
      </template>
      <template #grey_fabric_width="{ row }">
        {{ row.grey_fabric_width }} {{ row.grey_fabric_width_unit_name }}
      </template>
      <template #grey_fabric_gram_weight="{ row }">
        {{ row.grey_fabric_gram_weight }} {{ row.grey_fabric_gram_weight_unit_name }}
      </template>
      <template #finish_product_width="{ row }">
        {{ row.finish_product_width }} {{ row.finish_product_width_unit_name }}
      </template>
      <template #finish_product_gram_weight="{ row }">
        {{ row.finish_product_gram_weight }} {{ row.finish_product_gram_weight_unit_name }}
      </template>
    </Table>
  </FildCard>
  <FildCard title="用料比例" class="mt-[5px]" :tool-bar="false">
    <Table ref="masterTableRef" :config="columnList_master_config" :table-list="state.masterList" :column-list="columnList_master">
      <!-- 用纱比例 -->
      <template #yarn_ratio="{ row }">
        {{ row.yarn_ratio }}%
      </template>
      <!-- 用纱损耗 -->
      <template #yarn_loss="{ row }">
        {{ row.yarn_loss }}%
      </template>
      <!-- 用纱量 -->
      <template #change_use_yarn_quantity="{ row }">
        {{ row.change_use_yarn_quantity }}{{ row.unit_name }}
      </template>
      <!-- 发纱量 -->
      <template #change_send_yarn_quantity="{ row }">
        <vxe-input v-model="row.change_send_yarn_quantity" type="number" :min="0 - row.send_yarn_quantity" :disabled="row.mill_private_yarn">
          <template #suffix>
            {{ row.unit_name }}
          </template>
        </vxe-input>
      </template>
      <!-- 织厂出料 -->
      <template #mill_private_yarn="{ row }">
        <el-checkbox v-model="row.mill_private_yarn" @change="setSendYarnQua(row)" />
      </template>
    </Table>
  </FildCard>
  <FildCard title="工艺要求" class="mt-[5px]" :tool-bar="false">
    <el-tabs v-model="customActiveName" class="demo-tabs">
      <el-tab-pane label="织造工艺" name="first">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem v-for="(item, index) in required_options" :key="index" :label="`${item.text}:`">
            <template #content>
              {{ state.technologicalRequirementForm[item.key] }}
            </template>
          </DescriptionsFormItem>
        </div>
      </el-tab-pane>
      <el-tab-pane label="梭织工艺" name="second">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem label="经密:">
            <template #content>
              {{ formatPriceDiv(data.warp_density) }}根/cm
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="纬密:">
            <template #content>
              {{ formatPriceDiv(data.weft_density) }}根/cm
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="钢筘内幅:">
            <template #content>
              {{ formatPriceDiv(data.reed_inner_width) }}cm
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="钢筘边幅:">
            <template #content>
              {{ formatPriceDiv(data.reed_outer_width) }}cm
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="筘号:">
            <template #content>
              {{ formatPriceDiv(data.reed_no) }}齿/cm
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="穿入数:">
            <template #content>
              {{ formatPriceDiv(data.penetration_number) }}穿
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="上机纬密:">
            <template #content>
              {{ formatPriceDiv(data.upper_weft_density) }}牙
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="下机纬密:">
            <template #content>
              {{ formatPriceDiv(data.lower_weft_density) }}根/cm
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="理论坯布克重:">
            <template #content>
              {{ formatPriceDiv(data.gf_theory_gram_width) }}g/m
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="总经根数:">
            <template #content>
              {{ formatPriceDiv(data.total_warp_pieces) }}根
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="经纱排列:" copies="2">
            <template #content>
              {{ data.warp_arrangement }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="纬纱排列:" copies="2">
            <template #content>
              {{ data.weft_arrangement }}
            </template>
          </DescriptionsFormItem>
        </div>
        <Table :config="tableConfig1" :table-list="data.warp_datas" :column-list="columnList1" />
        <Table :config="tableConfig1" :table-list="data.weft_datas" :column-list="columnList2" />
      </el-tab-pane>
    </el-tabs>
  </FildCard>
</template>

<style lang="scss" scoped>
.form_container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  color: #666;

  .form_container_item {
    display: flex;
    margin-bottom: 15px;

    .label {
      color: #ccc;
      margin-right: 10px;
      min-width: 100px;
      text-align: right;
    }
  }
}

.flex_center {
  display: flex;
  justify-content: center;
  margin: 20px 0 10px;
}

.el-link {
  color: #0e7eff;
}
</style>
