<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import Table from '@/components/Table.vue'
import { getGfmWarehouseSumList } from '@/api/greyFabricPurchaseReturn'
import { debounce, getFilterData } from '@/common/util'
import SelectComponents from '@/components/SelectComponents/index.vue'

const emits = defineEmits(['handleSure'])

const state = reactive({
  filterData: {
    grey_fabric_code: '',
    grey_fabric_name: '',
    custom_id: '',
    supplier_id: '',
  },
  showModal: false,
  modalName: '从库存中添加坯布',
  multipleSelection: [],
})

const { fetchData, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = getGfmWarehouseSumList()

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  {
    deep: true,
  },
)

const tableConfig = reactive<any>({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showSort: false,
  keyField: 'id',
  checkRowKeys: [],
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

function getData() {
  fetchData(getFilterData(state.filterData))
}

const columnList = ref([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
  },
  //   {
  //     field: 'name',
  //     title: '供方名称',
  //   },
  {
    field: 'customer_name',
    title: '所属客户',
  },
  {
    field: 'yarn_batch',
    title: '纱批',
  },
  {
    field: 'machine_number',
    title: '机台号',
  },
  {
    field: 'gray_fabric_color_name',
    title: '织坯颜色',
  },
  {
    field: 'grey_fabric_level_name',
    title: '坯布等级',
  },
  //   {
  //     field: 'warehouse_bin_Name',
  //     title: '仓位',
  //   },
  //   {
  //     field: 'finish_product_width',
  //     title: '成品幅宽',
  //   },
  //   {
  //     field: 'finish_product_gram_weight',
  //     title: '成品克重',
  //   },
  {
    field: 'needle_size',
    title: '针寸数',
  },
  {
    field: 'source_remark',
    title: '坯布备注',
  },
  {
    field: 'num',
    title: '匹数',
  },
  {
    field: 'weight',
    title: '数量',
    isWeight: true,
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handCancel() {
  state.showModal = false
}

function handleSure() {
  if (!state.multipleSelection.length)
    return ElMessage.error('请选择一条数据')
  else
    emits('handleSure', state.multipleSelection)
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="1500" height="700" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <el-descriptions class="mb-[20px]" :column="4" border size="small">
      <el-descriptions-item label="坯布编号">
        <el-input v-model="state.filterData.grey_fabric_code" />
      </el-descriptions-item>
      <el-descriptions-item label="坯布名称">
        <el-input v-model="state.filterData.grey_fabric_name" />
      </el-descriptions-item>
      <!-- <el-descriptions-item label="供方名称">
        <SelectComponents api="BusinessUnitSupplierEnumlist" label-field="name" value-field="id" v-model="state.filterData.supplier_id" clearable />
      </el-descriptions-item> -->
      <el-descriptions-item label="所属客户">
        <SelectComponents v-model="state.filterData.custom_id" api="BusinessUnitSupplierEnumlist" label-field="name" value-field="id" clearable />
      </el-descriptions-item>
    </el-descriptions>
    <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList" />
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
