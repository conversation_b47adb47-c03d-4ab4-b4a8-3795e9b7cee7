<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import Table from '@/components/Table.vue'
import { debounce, getFilterData, getRecentDay_Date, resetData } from '@/common/util'
import { getProductSaleShouldCollectOrderDropdownList } from '@/api/saleDeliver'
import { formatDate, formatPriceDiv, sumNum } from '@/common/format'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'

const emits = defineEmits(['handleSure'])

const shortcuts: any = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

const state = reactive<any>({
  filterData: {
    date: [],
    sale_order_no: '',
    src_order_no: '',
    order_no: '',
  },
  info: {},
  showModal: false,
  multipleSelection: [],
  title: '',
  count: -1,
  should_collect_order_id: '',
  sale_system_id: '',
  customer_id: '',
})

const { fetchData, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = getProductSaleShouldCollectOrderDropdownList()

// onMounted(() => {
//   state.filterData.date = getRecentDay_Date(1)
// })

const getData = debounce(async () => {
  const query = {
    ...state.filterData,
    sale_system_id: state.sale_system_id,
    start_order_time: formatDate(state.filterData.date?.[0]),
    end_order_time: formatDate(state.filterData.date?.[1]),
    should_collect_order_id: state.should_collect_order_id,
    customer_id: state.customer_id,
  }
  delete query.date
  await fetchData(getFilterData(query))
}, 400)
watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

watch(
  () => state.showModal,
  () => {
    if (state.showModal) {
      state.filterData.date = getRecentDay_Date(1)
      state.count += 1
    }
    if (state.showModal && state.count > 0)
      getData()
  },
)
const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  height: 400,
  //   showOperate: true,
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  footerMethod: (val: any) => FooterMethod(val),
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['total_sale_money'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'total_sale_money') as any)}`

      return null
    }),
  ]
}

function handReset() {
  state.filterData = resetData(state.filterData)
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handCancel() {
  state.showModal = false
}

async function handleSure() {
  if (!state.multipleSelection.length)
    return ElMessage.error('请至少选择一条数据')

  const ids: any = []
  state.multipleSelection.forEach((item: any) => {
    ids.push(item.id)
  })
  emits('handleSure', ids)
}

const columnList = ref([
  {
    field: 'sale_order_no',
    title: '成品销售单号',
    minWidth: 100,
  },
  {
    field: 'order_no',
    title: '销售送货单号',
    minWidth: 100,
  },
  {
    field: 'src_order_no',
    title: '配布单号',
    minWidth: 100,
  },
  {
    field: 'order_time',
    title: '订单日期',
    minWidth: 100,
    is_date: true,
  },
  {
    field: 'process_factory_name',
    title: '加工厂名称',
    minWidth: 100,
  },
  {
    field: 'receive_addr',
    title: '收货地址 ',
    minWidth: 100,
  },
  {
    field: 'total_sale_money',
    title: '总金额',
    minWidth: 100,
    isPrice: true,
  },
])

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer title="合并销售送货单" width="1250" height="800" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="成品销售单号:">
        <template #content>
          <el-input v-model="state.filterData.sale_order_no" clearable />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="销售送货单号:">
        <template #content>
          <el-input v-model="state.filterData.order_no" clearable />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="配布单号:">
        <template #content>
          <el-input v-model="state.filterData.src_order_no" clearable />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="订单日期:" width="330">
        <template #content>
          <el-date-picker
            v-model="state.filterData.date"
            type="daterange"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :shortcuts="shortcuts"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="客户编号:">
        <template #content>
          {{ state.info.customer_code }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="客户名称:">
        <template #content>
          {{ state.info.customer_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="">
        <template #content>
          <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
            清除条件
          </el-button>
        </template>
      </DescriptionsFormItem>
    </div>
    <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList" />
    <template #footer>
      <el-button type="primary" @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
