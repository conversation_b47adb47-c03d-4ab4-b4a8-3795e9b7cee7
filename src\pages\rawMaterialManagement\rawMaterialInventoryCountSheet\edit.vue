<script lang="ts" setup name="RawMaterialInventoryCountSheetEdit">
import { Plus } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import currency from 'currency.js'
import SelectInformation from './components/SelectInformation/index.vue'
import SelectPurchasing from './components/SelectPurchasing/index.vue'
import { StockCheckOrderDetail, StockCheckOrderEdit } from '@/api/rawMaterialInventoryCountSheet'
import { BusinessUnitIdEnum, EmployeeType } from '@/common/enum'
import { formatDate, formatPriceDiv, formatUnitPriceDiv, formatUnitPriceMul, formatWeightDiv, formatWeightMul, sumNum } from '@/common/format'
import { getFilterData, strIsEmpty } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DataChange from '@/components/DataChange/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'
import { processDataIn, processDataOut } from '@/common/handBinary'

const routerList = useRouterList()

const router = useRoute()
const state = reactive({
  form: {
    sale_system_id: '',
    sale_system_name: '',
    check_unit_id: '',
    check_unit_name: '',
    check_date: dayjs().format('YYYY-MM-DD'),
    warehouse_manager_name: '',
    warehouse_manager_id: '',
    remark: '',
  },
  tableData: [] as any[],
  fromRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    check_unit_id: [{ required: true, message: '请选择盘点单位', trigger: 'change' }],
    check_date: [{ required: true, message: '请选择盘点日期', trigger: 'change' }],
  },
})

onMounted(() => {
  getDataDetail()
})

// 获取订单详情
const { fetchData: fetchDataDetail, data: dataDetail } = StockCheckOrderDetail()
async function getDataDetail() {
  await fetchDataDetail({ id: router.params.id })
}

watch(
  () => dataDetail.value,
  (val) => {
    if (val) {
      state.form.sale_system_id = val.sale_system_id
      state.form.sale_system_name = val.sale_system_name
      state.form.check_unit_id = val.check_unit_id
      state.form.check_unit_name = val.check_unit_name
      state.form.check_date = val.check_date
      state.form.warehouse_manager_name = val.warehouse_manager_name
      state.form.warehouse_manager_id = val.warehouse_manager_id
      state.form.remark = val.remark
      // state.tableData = formatPriceData(val.items || [])
      state.tableData = processDataOut(val.items || [])
    }
  },
)

// 整理金额和数量
// function formatPriceData(data: any[]) {
//   return data.map((item) => {
//     return {
//       ...item,
//       total_weight: formatWeightDiv(item.total_weight),
//       total_weight_diff: formatWeightDiv(item.total_weight_diff),
//       unit_price: formatUnitPriceDiv(item.unit_price),
//       total_price_diff: formatPriceDiv(item.total_price_diff),
//       production_date: formatDate(item.production_date),
//       total_weight_stock: formatWeightDiv(item.total_weight_stock),
//     }
//   })
// }

const showAdd = ref(false)
const showPurchasing = ref(false)
const selectStatus = ref<1 | 2>(2) // 1 点击盘盈新增， 2 点击库存添加

function handDel(row: any, rowIndex: number) {
  ElMessageBox.confirm('是否删除该数据?', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      state.tableData.splice(rowIndex, 1)
    })
    .catch(() => {})
}

function handAdd() {
  if (!state.form.check_unit_id)
    return ElMessage.error('请选择盘点单位')
  showAdd.value = true
}

function handPlateProfitAdd() {
  selectStatus.value = 1
  showPurchasing.value = true
}

const columnList = ref([
  {
    title: '原料信息',
    childrenList: [
      {
        field: 'raw_material_code',
        title: '原料编号',
        fixed: 'left',
        width: 90,
        // soltName: 'raw_material_code',
      },
      {
        field: 'raw_material_name',
        title: '原料名称',
        fixed: 'left',
        width: 100,
        // soltName: 'raw_material_name',
      },
      {
        field: 'customer_id',
        title: '所属客户',
        width: 120,
        soltName: 'customer_id',
      },
      {
        field: 'brand',
        title: '原料品牌',
        width: 100,
        soltName: 'brand',
      },
      {
        field: 'craft',
        title: '原料工艺',
        width: 100,
      },
      {
        field: 'color_scheme',
        title: '原料颜色',
        width: 130,
        soltName: 'color_scheme',
        required: true,
      },
      {
        field: 'measurement_unit_name',
        title: '计量单位',
        width: 100,
      },
      {
        field: 'dyelot_number',
        title: '缸号',
        width: 80,
        soltName: 'dyelot_number',
      },
      {
        field: 'supplier_id',
        title: '供应商',
        width: 120,
        required: true,
        soltName: 'supplier_id',
      },
      {
        field: 'batch_num',
        title: '原料批号',
        width: 100,
        soltName: 'batch_num',
      },
      {
        field: 'level',
        title: '原料等级',
        width: 100,
        soltName: 'level',
      },
      {
        field: 'raw_material_remark',
        title: '原料备注',
        width: 100,
        soltName: 'raw_material_remark',
      },
      {
        field: 'production_date',
        title: '生产日期',
        width: 140,
        soltName: 'production_date',
      },
      {
        field: 'spinning_type',
        title: '纺纱类型',
        width: 100,
        soltName: 'spinning_type',
      },
      {
        field: 'cotton_origin',
        title: '棉花产地',
        width: 100,
        soltName: 'cotton_origin',
      },
      {
        field: 'yarn_origin',
        title: '棉纱产地',
        width: 100,
        soltName: 'yarn_origin',
      },
      {
        field: 'carton_num',
        title: '装箱单号',
        width: 100,
        soltName: 'carton_num',
      },
      {
        field: 'fapiao_num',
        title: '发票号',
        width: 100,
        soltName: 'fapiao_num',
      },
    ],
  },
  {
    title: '库存信息',
    childrenList: [
      {
        field: 'whole_piece_count_stock',
        title: '整件件数',
        width: 75,
      },
      {
        field: 'bulk_piece_count_stock',
        title: '散件件数',
        width: 75,
      },
      {
        field: 'total_weight_stock',
        title: '数量总计(kg)',
        width: 100,
      },
    ],
  },
  {
    title: '盘点信息',
    childrenList: [
      {
        field: 'whole_piece_count',
        title: '盘点整件件数',
        width: 110,
        soltName: 'whole_piece_count',
        required: true,
      },
      {
        field: 'bulk_piece_count',
        title: '盘点散件件数',
        width: 110,
        soltName: 'bulk_piece_count',
        required: true,
      },
      {
        field: 'total_weight',
        title: '盘点数量总计(kg)',
        width: 120,
        soltName: 'total_weight',
        required: true,
      },
    ],
  },
  {
    title: '盈亏信息',
    childrenList: [
      {
        field: 'whole_piece_count_diff',
        title: '盈亏整件件数',
        width: 110,
        soltName: 'whole_piece_count_diff',
      },
      {
        field: 'bulk_piece_count_diff',
        title: '盈亏散件件数',
        width: 110,
        soltName: 'bulk_piece_count_diff',
      },
      {
        field: 'total_weight_diff',
        title: '盈亏数量总计(kg)',
        width: 120,
        soltName: 'total_weight_diff',
      },
      {
        field: 'unit_price',
        title: '单价',
        width: 100,
        soltName: 'unit_price',
      },
      {
        field: 'total_price',
        title: '盈亏金额',
        width: 90,
        soltName: 'total_price',
      },
    ],
  },
  {
    title: '',
    childrenList: [
      {
        field: 'remark',
        title: '备注',
        width: 150,
        soltName: 'remark',
      },
    ],
  },
])

const tablesRef = ref()

const saleSaleSystemInfo = ref()
function getSaleSystem(row: any) {
  state.form.sale_system_name = row?.name
  saleSaleSystemInfo.value = row
  clearData()
}

function computedData(row: any) {
  // 盈亏整件件数=盘点整件件数-库存整件件数
  row.whole_piece_count_diff = currency(row.whole_piece_count || 0).subtract(row.whole_piece_count_stock || 0).value

  // 盈亏整件件数=盘点散件件数-库存散件件数
  row.bulk_piece_count_diff = currency(row.bulk_piece_count || 0).subtract(row.bulk_piece_count_stock || 0).value

  // 盈亏数量总计:盘点数量总计-库存数量总计
  row.total_weight_diff = currency(row.total_weight || 0).subtract(row.total_weight_stock || 0).value

  // 盈亏金额=盈亏数量总计*单价
  row.total_price = currency(row.total_weight_diff || 0).multiply(row.unit_price || 0).value

  tablesRef.value.tableRef.updateFooter()
  return row
}

const bulkShow = ref(false)
const bulkSetting = ref<any>({
  address: {},
})

const multipleSelection = ref<any[]>([])
async function bulkSubmit({ row, value }: any) {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  if (!value[row.field])
    return ElMessage.error('请输入参数')

  multipleSelection.value?.map((item: any) => {
    if (item.add_method === 1)
      item[row.field] = value[row.field]
    item = computedData(item)
  })
  ElMessage.success('设置成功')
}
// 选择客户
function handleChangeCustomer(val: any, row: any) {
  row.customer_name = val.name
  row.customer_code = val.code
}

// 从库存中选择
function onSubmit(row: any) {
  showAdd.value = false
  const data = row?.map((item: any) => {
    const temp = {
      raw_material_code: item.raw_material_code,
      raw_material_name: item.raw_material_name,
      craft: item.craft,
      // raw_material_id: item.id,
      customer_id: item.customer_id,
      brand: item.brand,
      color_scheme: item.color_scheme,
      batch_num: item.batch_num,
      supplier_name: item.supplier_name,
      level_id: item.level_id,
      level_name: item.level_name,
      raw_material_remark: item.remark,
      production_date: item.production_date,
      spinning_type: item.spinning_type,
      cotton_origin: item.cotton_origin,
      yarn_origin: item.yarn_origin,
      carton_num: item.carton_num,
      fapiao_num: item.fapiao_num,
      whole_weight: formatWeightDiv(item.whole_weight),
      bulk_piece_count_stock: formatPriceDiv(item.bulk_piece_count),
      bulk_stock_weight: formatWeightDiv(item.bulk_weight),
      stock_item_id: item.id,
      total_weight_stock: formatWeightDiv(item.total_weight),
      whole_piece_count_stock: formatPriceDiv(item.whole_piece_count),
      customer_name: item.customer_name,
      remark: item.remark,
      unit_price: formatUnitPriceDiv(item.unit_price),
      add_method: 2,
      whole_piece_count: '',
      bulk_piece_count: '',
      total_weight: '',
      color_id: item.color_id || '',
      color_name: item.color_name,
      measurement_unit_id: item.measurement_unit_id,
      measurement_unit_name: item.measurement_unit_name,
      dyelot_number: '',
    }
    return temp
  })
  state.tableData = [...state.tableData, ...data]
}

// 盘盈新增
function onSubmitPurchasing(row: any) {
  showPurchasing.value = false
  const data = row?.map((item: any) => {
    return {
      raw_material_code: item.code,
      raw_material_name: item.name,
      raw_material_id: item.id,
      craft: item.craft,
      add_method: 1,
      whole_piece_count_stock: 0,
      bulk_piece_count_stock: 0,
      total_weight_stock: 0,
      customer_id: saleSaleSystemInfo.value?.default_customer_id || '',
      customer_name: saleSaleSystemInfo.value?.default_customer_name || '',
      color_id: '',
      measurement_unit_id: item.unit_id,
      measurement_unit_name: item.unit_name,
      dyelot_number: item.dyelot_number,
      whole_piece_count: '',
      bulk_piece_count: '',
      total_weight: '',
    }
  })
  state.tableData = [...state.tableData, ...data]
  return data
}

const { fetchData: fetchDataAdd, data: addData, success: successAdd, msg: msgAdd } = StockCheckOrderEdit()
const ruleFormRef = ref()
async function handSubmit() {
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      if (validateList())
        return false
      const res = fomatData()

      await fetchDataAdd(getFilterData({ ...state.form, id: Number.parseInt(router.params.id as string), check_date: formatDate(state.form.check_date), items: res }))
      if (successAdd.value) {
        ElMessage.success('编辑成功')
        getDataDetail()
        routerList.push({
          name: 'RawMaterialInventoryCountSheetDetail',
          params: { id: addData.value.id },
        })
      }
      else {
        ElMessage.error(msgAdd.value)
      }
    }
  })
}

// 验证坯布信息字段
function validateList() {
  let msg = ''
  if (!state.tableData || state.tableData.length === 0) {
    msg = '原料信息不能为空'
  }
  else {
    state.tableData?.some((item: any) => {
      if (!item.color_id) {
        msg = `编号为${item.raw_material_code}的数据,原料颜色不能为空`
        return true
      }
      if (item.add_method === 1 && !item.supplier_id) {
        msg = `编号为${item.raw_material_code}的数据,供应商不能为空`
        return true
      }
      if (strIsEmpty(item.whole_piece_count)) {
        msg = `编号为${item.raw_material_code}的数据,盘点整件件数不能为空`
        return true
      }
      if (strIsEmpty(item.bulk_piece_count)) {
        msg = `编号为${item.raw_material_code}的数据,盘点散件件数不能为空`
        return true
      }
      if (!item.total_weight) {
        msg = `编号为${item.raw_material_code}的数据,盘点数量总计不能为空`
        return true
      }
      if (item.color_id === '') {
        msg = `编号为${item.raw_material_code}的数据,颜色不能为空`
        return true
      }
    })
  }
  msg && ElMessage.error(msg)
  return msg
}

// // 整理提交信息
function fomatData() {
  let query: any = state.tableData?.map((item: any) => {
    item.production_date = formatDate(item.production_date)
    return {
      ...item,
      stock_info: item,
    }
  })
  query = processDataIn(query)
  return query
}

const bulkList = reactive<any>([
  {
    field: 'raw_material_code',
    title: '原料编号',
    fixed: 'left',
    component: 'input',
    type: 'text',
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    fixed: 'left',
    component: 'input',
    type: 'text',
  },
  {
    field: 'customer_id',
    field_name: 'customer_name',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
    query: { sale_system_id: state.form.sale_system_id },
  },
  {
    field: 'brand',
    title: '原料品牌',
    component: 'input',
    type: 'text',
  },
  // {
  //   field: 'color_id',
  //   title: '原料颜色',
  //   component: 'select',
  //   api: 'GetRawMaterialColor',
  // },
  {
    field: 'dyelot_number',
    title: '缸号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'supplier_id',
    field_name: 'supplier_name',
    title: '供应商',
    component: 'select',
    api: 'BusinessUnitSupplierEnumlist',
  },
  {
    field: 'batch_num',
    title: '原料批号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'level',
    title: '原料等级',
    component: 'select',
    api: 'GetInfoBaseRawMaterialLevelEnumList',
  },
  {
    field: 'raw_material_remark',
    title: '原料备注',
    component: 'input',
    type: 'text',
  },
  {
    field: 'spinning_type',
    title: '纺纱类型',
    component: 'input',
    type: 'text',
  },
  {
    field: 'cotton_origin',
    title: '棉花产地',
    component: 'input',
    type: 'text',
  },
  {
    field: 'yarn_origin',
    title: '棉纱产地',
    component: 'input',
    type: 'text',
  },
  {
    field: 'carton_num',
    title: '装箱单号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'fapiao_num',
    title: '发票号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'whole_piece_count',
    title: '盘点整件件数',
    component: 'input',
    type: 'integer',
  },
  {
    field: 'bulk_piece_count',
    title: '盘点散件件数',
    component: 'input',
    type: 'integer',
  },
  {
    field: 'total_weight',
    title: '盘点数量总计(kg)',
    component: 'input',
    type: 'float',
  },
  {
    field: 'total_weight',
    title: '盘点数量总计(kg)',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])

function handAllSelect({ records }: any) {
  multipleSelection.value = records
}

function handleSelectionChange({ records }: any) {
  multipleSelection.value = records
}

function footerMethod({ columns, data }: { columns: any, data: any }) {
  const footerData = [
    columns.map((column: any, _columnIndex: number) => {
      if (['raw_material_code'].includes(column.field))
        return '合计'

      if (
        [
          'whole_piece_count_stock',
          'total_weight_stock',
          'whole_piece_count',
          'bulk_piece_count',
          'total_weight',
          'whole_piece_count_diff',
          'bulk_piece_count_diff',
          'total_weight_diff',
          'total_price',
        ].includes(column.field)
      )
        return sumNum(data, column.field)
    }),
  ]
  return footerData
}

function handBulkClose() {
  bulkShow.value = false
}

let oneStatus = true
function clearData() {
  if (!oneStatus) {
    state.tableData?.map((item) => {
      item.customer_id = saleSaleSystemInfo.value?.default_customer_id || ''
      item.customer_name = saleSaleSystemInfo.value?.default_customer_name || ''
    })
  }
  oneStatus = false
  bulkList[0].query = { sale_system_id: state.form.sale_system_id }
  bulkSetting.value.customer_id = ''
}

const tableConfig = ref({
  showOperate: true,
  operateWidth: 100,
  bulkSubmit,
  showCheckBox: true,
  filterStatus: false,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  footerMethod,
  showSpanHeader: true,
})
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <div class="line" />
    <template #right-top>
      <el-button v-btnAntiShake="handSubmit" type="primary">
        提交
      </el-button>
    </template>
    <slot>
      <el-form ref="ruleFormRef" :model="state.form" label-width="60px" label-position="top" :rules="state.fromRules">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem required label="营销体系名称:">
            <template #content>
              <el-form-item prop="sale_system_id">
                <SelectComponents v-model="state.form.sale_system_id" api="GetSaleSystemDropdownListApi" label-field="name" value-field="id" @change-value="getSaleSystem" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="盘点单位名称:">
            <template #content>
              <el-form-item prop="check_unit_id">
                <SelectComponents
                  v-model="state.form.check_unit_id"
                  :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory }"
                  api="GetBusinessUnitListApi"
                  label-field="name"
                  value-field="id"
                  @change-value="row => (state.form.check_unit_name = row?.name)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="盘点日期:">
            <template #content>
              <el-form-item prop="check_date">
                <SelectDate v-model="state.form.check_date" type="date" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="仓管员:">
            <template #content>
              <el-form-item prop="warehouse_manager_id">
                <SelectComponents
                  v-model="state.form.warehouse_manager_id"
                  api="Adminemployeelist"
                  :query="{ duty: EmployeeType.warehouseManager }"
                  label-field="name"
                  value-field="id"
                  @change-value="row => (state.form.warehouse_manager_name = row?.name)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="备注:" copies="2">
            <template #content>
              <el-form-item prop="remark">
                <el-input v-model="state.form.remark" type="textarea" placeholder="请输入备注" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
        </div>
      </el-form>
    </slot>
  </FildCard>
  <FildCard title="原料信息" :tool-bar="false" class="mt-[5px]">
    <template #right-top>
      <!-- <el-button style="margin-left: 10px" type="primary" @click="bulkHand">批量操作</el-button> -->
      <el-button style="margin-left: 10px" type="primary" :icon="Plus" @click="handPlateProfitAdd">
        盘盈新增
      </el-button>
      <el-button style="margin-left: 10px" type="primary" :icon="Plus" @click="handAdd">
        根据库存添加
      </el-button>
    </template>
    <Table ref="tablesRef" :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
      <template #raw_material_code="{ row }">
        <vxe-input v-if="row.add_method === 1" v-model="row.raw_material_code" maxlength="200" size="mini" />
        <span v-else>{{ row.raw_material_code }}</span>
      </template>
      <template #raw_material_name="{ row }">
        <vxe-input v-if="row.add_method === 1" v-model="row.raw_material_name" maxlength="200" size="mini" />
        <span v-else>{{ row.raw_material_name }}</span>
      </template>
      <template #customer_id="{ row }">
        <SelectCustomerDialog
          v-if="row.add_method === 1"
          v-model="row.customer_id"
          field="name" :sale_system_id="state.form.sale_system_id"
          :default-value="{
            id: row.customer_id,
            name: row.customer_name,
            code: row.customer_code,
          }"
          @change-value="handleChangeCustomer($event, row)"
        />
        <span v-else>{{ row.customer_name }}</span>
      </template>
      <template #brand="{ row }">
        <vxe-input v-if="row.add_method === 1" v-model="row.brand" maxlength="200" size="mini" />
        <span v-else>{{ row.brand }}</span>
      </template>
      <template #color_scheme="{ row }">
        <SelectComponents
          v-model="row.color_id" size="small" api="GetRawMaterialColor" label-field="name" value-field="id" clearable :query="{
            raw_matl_id: row.raw_material_id,
          }"
        />
      </template>
      <template #measurement_unit_id="{ row }">
        <SelectComponents v-model="row.measurement_unit_id" disabled size="small" api="getInfoBaseMeasurementUnitEnumList" label-field="name" value-field="id" clearable />
      </template>
      <template #dyelot_number="{ row }">
        <vxe-input v-if="row.add_method === 1" v-model="row.dyelot_number" clearable />
        <span v-else>{{ row.dyelot_number }}</span>
      </template>
      <template #supplier_id="{ row }">
        <SelectComponents
          v-if="row.add_method === 1"
          v-model="row.supplier_id"
          api="BusinessUnitSupplierEnumAll"
          label-field="name"
          value-field="id"
          size="small"
          @change-value="item => (row.supplier_name = item.name)"
        />
        <span v-else>{{ row.supplier_name }}</span>
      </template>
      <template #batch_num="{ row }">
        <vxe-input v-if="row.add_method === 1" v-model="row.batch_num" maxlength="200" size="mini" />
        <span v-else>{{ row.batch_num }}</span>
      </template>
      <template #level="{ row }">
        <SelectComponents v-if="row.add_method === 1" v-model="row.level_id" size="small" api="GetInfoBaseRawMaterialLevelEnumList" label-field="name" value-field="id" />
        <span v-else>{{ row.level_name }}</span>
      </template>
      <template #raw_material_remark="{ row }">
        <vxe-input v-if="row.add_method === 1" v-model="row.raw_material_remark" maxlength="200" size="mini" />
        <span v-else>{{ row.raw_material_remark }}</span>
      </template>
      <template #production_date="{ row }">
        <SelectDate v-if="row.add_method === 1" v-model="row.production_date" size="small" type="date" />
        <span v-else>{{ formatDate(row.production_date) }}</span>
      </template>
      <template #spinning_type="{ row }">
        <vxe-input v-if="row.add_method === 1" v-model="row.spinning_type" maxlength="200" size="mini" />
        <span v-else>{{ row.spinning_type }}</span>
      </template>
      <template #cotton_origin="{ row }">
        <vxe-input v-if="row.add_method === 1" v-model="row.cotton_origin" maxlength="200" size="mini" />
        <span v-else>{{ row.cotton_origin }}</span>
      </template>
      <template #yarn_origin="{ row }">
        <vxe-input v-if="row.add_method === 1" v-model="row.yarn_origin" maxlength="200" size="mini" />
        <span v-else>{{ row.yarn_origin }}</span>
      </template>
      <template #carton_num="{ row }">
        <vxe-input v-if="row.add_method === 1" v-model="row.carton_num" maxlength="200" size="mini" />
        <span v-else>{{ row.carton_num }}</span>
      </template>
      <template #fapiao_num="{ row }">
        <vxe-input v-if="row.add_method === 1" v-model="row.fapiao_num" maxlength="200" size="mini" />
        <span v-else>{{ row.fapiao_num }}</span>
      </template>
      <template #unit_price="{ row }">
        <vxe-input v-if="row.add_method === 1" v-model="row.unit_price" maxlength="200" size="mini" @input="computedData(row)" />
        <span v-else>{{ row.unit_price }}</span>
      </template>
      <template #whole_piece_count="{ row }">
        <vxe-input v-model="row.whole_piece_count" maxlength="200" type="integer" :min="0" size="mini" @input="computedData(row)" />
      </template>
      <template #bulk_piece_count="{ row }">
        <vxe-input v-model="row.bulk_piece_count" maxlength="200" type="integer" :min="0" size="mini" @input="computedData(row)" />
      </template>
      <template #total_weight="{ row }">
        <vxe-input v-model="row.total_weight" maxlength="200" type="float" :digits="2" :min="0" size="mini" @input="computedData(row)" />
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" maxlength="200" size="mini" />
      </template>
      <template #whole_piece_count_diff="{ row }">
        <DataChange :val="row.whole_piece_count_diff" />
      </template>
      <template #bulk_piece_count_diff="{ row }">
        <DataChange :val="row.bulk_piece_count_diff" />
      </template>
      <template #total_weight_diff="{ row }">
        <DataChange :val="row.total_weight_diff" />
      </template>
      <template #total_price="{ row }">
        <DataChange :val="row.total_price" />
      </template>
      <template #operate="{ row, rowIndex }">
        <el-button type="text" @click="handDel(row, rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <SelectInformation v-model="showAdd" :unit_id="state.form.check_unit_id" @submit="onSubmit" />
  <SelectPurchasing v-model="showPurchasing" @submit="onSubmitPurchasing" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
</template>

<style lang="scss" scoped></style>
