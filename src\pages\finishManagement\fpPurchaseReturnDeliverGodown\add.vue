<script setup lang="ts" name="FpPurchaseReturnDeliverGodownAdd">
import { ElMessage } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import AccordingLibAdd from '../components/AccordingRepertoryAdd.vue'
import AssociatedPurchasingSalesReturn from '../components/AssociatedPurchasingSalesReturn.vue'
import FineSizeAdd from '../components/FineSizeDeliveryOrderAdd.vue'
import { addFpmPrtOutOrder } from '@/api/fpPurchaseReturnDeliverGodown'
import { BusinessUnitIdEnum, EmployeeType } from '@/common/enum'
import { formatDate, formatLengthMul, formatTwoDecimalsMul, formatUnitPriceMul, formatWeightMul } from '@/common/format'
import { deleteToast, getDefaultSaleSystem, isMainUnit } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import useRouterList from '@/use/useRouterList'
import SelectSaleMode from '@/components/SelectSaleMode/index.vue'
import { SaleModeEnum } from '@/enum/orderEnum'
import SelectBusinessDialog from '@/components/SelectBusinessDialog/index.vue'
import { processDataIn } from '@/common/handBinary'

// 备注: 配布单号
// 带出 采购退货单号
// 出仓匹数不可修改
// form 营销体系 供应商 仓库名称 不可修改 仓管员可修改
const routerList = useRouterList()
let uuid = 0
const AccordingLibAddRef = ref()
const FineSizeAddRef = ref()
const state = reactive<any>({
  formInline: {
    arrange_order_no: '',
    sale_system_id: '',
    biz_unit_id: '',
    warehouse_id: '',
    store_keeper_id: '',
    sale_mode: SaleModeEnum.Bulk, // 订单类型
    voucher_number: '',
    remark: '',
    warehouse_out_time: new Date(),
  },
  formRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    warehouse_out_time: [{ required: true, message: '请选择出仓日期', trigger: 'change' }],
    biz_unit_id: [{ required: true, message: '请选择供应商名称', trigger: 'change' }],
    warehouse_id: [{ required: true, message: '请选择仓库名称', trigger: 'change' }],
    sale_mode: [{ required: true, message: '请选择订单类型', trigger: 'change' }],
  },
})

const bulkShow = ref(false)
const finishProductionOptions = reactive<any>({
  multipleSelection: [],
  rowIndex: 0,
  tableConfig: {
    fieldApiKey: 'FpPurchaseReturnDeliverGodownAdd',
    showSlotNums: false,
    showSpanHeader: true,
    showCheckBox: true,
    handAllSelect: (val: any) => handAllSelect(val),
    handleSelectionChange: (val: any) => handleSelectionChange(val),
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 总数量 纸筒总重（kg） 结算数量 进仓辅助数量 采购辅助数量 进仓金额
        if (['total_weight'].includes(column.field))
          return sumNum(data, 'total_weight', '', 'float')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '', 'float')

        if (['out_length'].includes(column.field))
          return sumNum(data, 'out_length', '', 'float')

        if (['other_price'].includes(column.field))
          return sumNum(data, 'other_price', '￥', 'float')

        if (['total_price'].includes(column.field))
          return sumNum(data, 'total_price', '￥', 'float')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      childrenList: [
        {
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
          required: true,
        },
        {
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
          required: true,
        },
      ],
    },
    {
      title: '成品信息',
      childrenList: [
        {
          field: 'quote_order_no',
          title: '采购退货单号',
          minWidth: 100,
        },
        {
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },
        {
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          field: 'out_roll',
          soltName: 'out_roll',
          title: '出仓匹数',
          minWidth: 100,
          required: true,
        },
        {
          field: 'unit_name',
          title: '单位',
          minWidth: 100,
        },
      ],
    },
    {
      title: '库存信息',
      childrenList: [
        {
          field: 'sum_stock_roll',
          title: '库存匹数',
          minWidth: 100,
        },
        {
          field: 'sum_stock_weight',
          title: '库存数量',
          minWidth: 100,
        },
        {
          field: 'sum_stock_length',
          title: '库存辅助数量',
          minWidth: 100,
        },
      ],
    },
    {
      title: '结算单位',
      field: 'F',
      childrenList: [
        {
          field: 'auxiliary_unit_id',
          title: '结算单位',
          width: 100,
          soltName: 'auxiliary_unit_id',
        },
      ],
    },
    {
      title: '数量单价',
      childrenList: [
        {
          field: 'total_weight',
          title: '出仓数量',
          minWidth: 100,
          required: true,
        },
        {
          field: 'weight_error',
          title: '码单空差',
          minWidth: 100,
        },
        {
          field: 'settle_error_weight',
          title: '结算空差',
          minWidth: 100,
        },
        {
          field: 'settle_weight',
          title: '结算数量',
          minWidth: 100,
        },
        {
          field: 'unit_price',
          soltName: 'unit_price',
          title: '基本单位单价',
          minWidth: 120,
          required: true,
        },
      ],
    },
    {
      title: '辅助数量单价',
      childrenList: [
        {
          field: 'out_length',
          soltName: 'out_length',
          title: '辅助数量',
          minWidth: 100,
        },
        {
          field: 'length_unit_price',
          soltName: 'length_unit_price',
          title: '辅助数量单价',
          minWidth: 120,
        },
      ],
    },
    {
      title: '金额信息',
      childrenList: [
        {
          field: 'other_price',
          soltName: 'other_price',
          title: '其他金额',
          minWidth: 100,
        },
        {
          field: 'total_price',
          soltName: 'total_price',
          title: '结算金额',
          minWidth: 100,
        },
      ],
    },
    {
      title: '单据备注信息',
      childrenList: [
        {
          field: 'remark',
          soltName: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: '',
          soltName: 'xima',
          title: '细码',
          width: 100,
        },
      ],
    },
    {
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: '',
          soltName: 'operate',
          title: '操作',
          width: 100,
        },
      ],
    },
  ],
  handleSure: (list: any) => {
    // 成品成分
    // 取配布单不可修改
    list = list.map((item: any) => {
      const temp = {
        // ...item,
        uuid: ++uuid,
        sum_stock_id: item.stock_product_id,
        quote_order_no: '',
        product_code: item.product_code,
        product_name: item.product_name,
        product_id: item.product_id,
        customer_name: item.customer_name,
        customer_id: item.customer_id,
        product_color_code: item.product_color_code,
        product_color_id: item.product_color_id,
        product_color_name: item.product_color_name,
        dye_factory_dyelot_number: item.dyelot_number,
        product_level_name: item.product_level_name,
        product_level_id: item.product_level_id,
        product_remark: item.product_remark,
        product_craft: item.finish_product_craft,
        sum_stock_roll: item.roll,
        sum_stock_weight: item.weight,
        sum_stock_length: item.length,
        unit_name: item.measurement_unit_name,
        unit_id: item.measurement_unit_id,
        product_ingredient: item.finish_product_ingredient,
        finish_product_width_and_unit_name: item.finish_product_width_and_unit_name,
        finish_product_gram_weight_and_unit_name: item.finish_product_gram_weight_and_unit_name,
        id: item.id,
        item_fc_data: [],
        remark: '',
        total_price: 0,
        other_price: 0,
        length_unit_price: '',
        out_length: 0,
        unit_price: '',
        total_weight: 0,
        weight_error: 0,
        settle_weight: 0,
        settle_error_weight: 0,
        out_roll: undefined,
      }
      return conductUnitPrice(temp, true)
    })
    finishProductionOptions.datalist = [...finishProductionOptions.datalist, ...list]
  },
  handleSureFineSize: (list: any) => {
    let total_weight = 0
    let weight_error = 0
    let settle_weight = 0
    let settle_error_weight = 0
    let out_length = 0
    list.forEach((item: any) => {
      total_weight += Number(item.base_unit_weight)
      weight_error += Number(item.weight_error)
      settle_weight += Number(item.settle_weight)
      settle_error_weight += Number(item.settle_error_weight)
      out_length += Number(item.length)
    })
    // const total_weight = list.reduce((pre: any, val: any) => pre + Number(val.base_unit_weight), 0)
    // const weight_error = list.reduce((pre: any, val: any) => pre + Number(val.weight_error), 0)
    // const settle_weight = list.reduce((pre: any, val: any) => pre + Number(val.settle_weight), 0)
    // const out_length = list.reduce((pre: any, val: any) => pre + Number(val.length), 0)
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].item_fc_data = list
    // 出仓数量 = 细码数量之和
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].total_weight = Number(total_weight.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].out_length = Number(out_length.toFixed(2))
    // 空差 = 细码空差之和
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].weight_error = Number(weight_error.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].settle_error_weight = Number(settle_error_weight.toFixed(2))
    // 结算数量 = 总数量 - 空差
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].settle_weight = Number(settle_weight.toFixed(2))
    computedKeys()
  },
  //   删除
  handleRowDel: async ({ id }: any) => {
    const res = await deleteToast('是否确认删除该成品')
    if (!res)
      return
    const index = finishProductionOptions.datalist.findIndex((item: any) => item.id === id)
    finishProductionOptions.datalist.splice(index, 1)
  },
  //   批量操作
  handEdit,
})

// 处理字段不一致
function isMainUnitFormat(item: any) {
  return isMainUnit(item, 'unit_id')
}
/**
 * 根据结算单位是否为主单位显示单价
 * @param item 成品数据
 * @param isInit 是否需要初始化单位
 */
function conductUnitPrice(item: any, isInit = false) {
  // 初始化结算单位
  if (isInit && !item.auxiliary_unit_id) {
    item.auxiliary_unit_id = item.unit_id
    item.auxiliary_unit_name = item.unit_name
  }
  if (isMainUnitFormat(item))
    item.length_unit_price = 0 // 主单位-把辅助单价置0

  else
    item.unit_price = 0 // 辅助单位-把单价置0
  computedKeys()
  return item
}

function handEdit() {
  if (finishProductionOptions.multipleSelection.length < 1)
    return ElMessage.error('请先勾选需要修改的数据')

  bulkShow.value = true
}
function handAllSelect({ records }: any) {
  finishProductionOptions.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  finishProductionOptions.multipleSelection = records
}
// 批量操作
const bulkSetting = ref<any>({})
const bulkList = reactive<any>([
  {
    field: 'out_roll',
    title: '出仓匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'out_length',
    title: '辅助数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'other_price',
    title: '其他金额',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])
async function bulkSubmit({ row, value }: any) {
  const ids = finishProductionOptions.multipleSelection.map((item: any) => item.uuid)

  finishProductionOptions.datalist.map((item: any) => {
    if (ids.includes(item.uuid))
      item[row.field] = value[row.field]
  })
  ElMessage.success('设置成功')
  handBulkClose()
  computedKeys()
}

function handBulkClose() {
  bulkShow.value = false
}

function getSFRoll(row: any) {
  return row.item_fc_data.reduce((pre: any, val: any) => pre + Number(val.roll), 0)
}

function getUnInRoll(row: any) {
  if (row.out_roll === '' || row.out_roll === null || row.out_roll === undefined)
    return false

  else
    return true
}

function showLibDialog() {
  AccordingLibAddRef.value.state.showModal = true
}

function computedKeys() {
  // 自动计算退货金额
  finishProductionOptions.datalist.forEach((item: any) => {
    const { unit_price = 0, settle_weight = 0, length_unit_price = 0, out_length = 0, other_price = 0 } = item
    item.total_price = Number(unit_price) * Number(settle_weight) + Number(length_unit_price) * Number(out_length) + Number(other_price)
    if (Number.isNaN(item.total_price))
      item.total_price = 0
    else
      item.total_price = Number(item.total_price.toFixed(2))
  })
}
const tablesRef = ref()
watch(
  () => finishProductionOptions.datalist,
  () => {
    // computedKeys()
    nextTick(() => {
      tablesRef.value.tableRef.updateFooter()
    })
  },
  { deep: true },
)

function showFineSizeDialog(row: any, rowIndex: number) {
  finishProductionOptions.rowIndex = rowIndex
  FineSizeAddRef.value.showDialog(row)
}
const formRef = ref()
// 提交所有数据
const { fetchData: addFetch, data: successData, success: addSuccess, msg: addMsg, loading } = addFpmPrtOutOrder()
function submitAddAllData() {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      const query = {
        ...state.formInline,
        store_keeper_id: state.formInline.store_keeper_id || 0,
        warehouse_out_time: formatDate(state.formInline.warehouse_out_time),
        item_data: [],
      }
      // 校验成品信息
      if (!finishProductionOptions.datalist.length)
        return ElMessage.error('请添加成品信息')

      // let FSRollFlag = true
      // let FSLengthFlag = true
      for (let i = 0; i < finishProductionOptions.datalist.length; i++) {
        const item = finishProductionOptions.datalist[i]
        if (!finishProductionOptions.datalist[i].product_code)
          return ElMessage.error('成品编号为必填项')

        if (!finishProductionOptions.datalist[i].product_name)
          return ElMessage.error('成品名称为必填项')

        // if (!Number(finishProductionOptions.datalist[i].out_roll))
        if (finishProductionOptions.datalist[i].out_roll === '' || finishProductionOptions.datalist[i].out_roll === undefined || finishProductionOptions.datalist[i].out_roll === null)
          return ElMessage.error('出仓匹数为必填项')

        if (!Number(finishProductionOptions.datalist[i].total_weight))
          return ElMessage.error('出仓数量为必填项')

        if (!item.auxiliary_unit_id)
          return ElMessage.error(`成品编号为${item.product_code}的数据,结算单位不能为空`)

        if (item.unit_price === '' && isMainUnitFormat(item))
          return ElMessage.error(`成品编号为${item.product_code}的数据,基本单位单价不能为空`)

        if (!item.out_length && !isMainUnitFormat(item))
          return ElMessage.error(`成品编号为${item.product_code}的数据,进仓辅助数量不能为空`)

        if (item.length_unit_price === '' && !isMainUnitFormat(item))
          return ElMessage.error(`成品编号为${item.product_code}的数据,辅助数量单价不能为空`)

        let FSRoll = 0 // 录入的细码匹数
        let FSLen = 0 // 录入的细码辅助数量
        finishProductionOptions.datalist[i].item_fc_data.forEach((item: any) => { // 遍历录入的细码
          FSRoll += Number(item.roll)
          FSLen += Number(item.length)
        })
        FSRoll = Number(FSRoll.toFixed(2))
        FSLen = Number(FSLen.toFixed(2))
        if (Number(finishProductionOptions.datalist[i].out_roll) !== Number(FSRoll)) {
          // const FSRoll = finishProductionOptions.datalist[i].item_fc_data.reduce((pre: any, val: any) => pre + Number(val.roll), 0)
          // if (FSRoll !== Number(finishProductionOptions.datalist[i].out_roll))
          return ElMessage.error('出仓匹数和录入细码匹数不匹配')
        }
        // const FSLen = finishProductionOptions.datalist[i].item_fc_data.reduce((pre: any, val: any) => pre + Number(val.length), 0)
        if (FSLen !== Number(finishProductionOptions.datalist[i].out_length))
          return ElMessage.error('细码总辅助数量与分录行的辅助数量不匹配')
      }
      // if (!FSRollFlag) {
      //   return ElMessage.error('出仓匹数和录入细码匹数不匹配')
      // }
      // if (!FSLengthFlag) {
      //   return ElMessage.error('细码总辅助数量与分录行的辅助数量不匹配')
      // }
      query.item_data = processDataIn(finishProductionOptions.datalist)

      await addFetch(query)
      if (addSuccess.value) {
        ElMessage.success('提交成功')
        routerList.push({
          name: 'FpPurchaseReturnDeliverGodownDetail',
          query: {
            id: successData.value.id,
          },
        })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

function changeSaleSystemId(item: any) {
  state.formInline.warehouse_id = item?.default_physical_warehouse || ''
}

onMounted(() => {
  // 获取用户上的默认营销体系
  const res = getDefaultSaleSystem()
  state.formInline.sale_system_id = res?.default_sale_system_id
})
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" :disabled="!finishProductionOptions.datalist.length" :loading="loading" @click="submitAddAllData">
        提交
      </el-button>
    </template>
    <el-form ref="formRef" :model="state.formInline" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '84px' }">
        <DescriptionsFormItem :required="true" label="营销体系:">
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents
                v-model="state.formInline.sale_system_id"
                api="GetSaleSystemDropdownListApi"
                label-field="name"
                value-field="id"
                default-status
                clearable
                @change-value="changeSaleSystemId"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="供应商名称:">
          <template #content>
            <el-form-item prop="biz_unit_id">
              <SelectBusinessDialog
                v-model="state.formInline.biz_unit_id"
                :query="{
                  unit_type_id: BusinessUnitIdEnum.finishedProduct,
                }"
                api="BusinessUnitSupplierEnumlist"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="出仓日期:">
          <template #content>
            <el-form-item prop="warehouse_out_time">
              <el-date-picker v-model="state.formInline.warehouse_out_time" type="date" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="仓库名称:">
          <template #content>
            <el-form-item prop="warehouse_id">
              <SelectComponents
                v-model="state.formInline.warehouse_id"
                warehouse_type_id="finishProduction"
                api="GetPhysicalWarehouseDropdownList" label-field="name" value-field="id" clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓管员:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.formInline.store_keeper_id"
                :query="{
                  duty: EmployeeType.warehouseManager,
                }"
                api="GetEmployeeListEnum"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证号:">
          <template #content>
            <el-form-item prop="voucher_number">
              <el-input v-model="state.formInline.voucher_number" placeholder="凭证号" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单类型:">
          <template #content>
            <SelectSaleMode v-model="state.formInline.sale_mode" :show-customer-book="false" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :copies="2" label="备注:">
          <template #content>
            <el-form-item prop="remark">
              <vxe-textarea v-model="state.formInline.remark" style="width: 100%" maxlength="500" show-word-count />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]">
    <template #right-top>
      <el-button type="primary" @click="handEdit">
        批量操作
      </el-button>
      <el-button type="primary" @click="showLibDialog">
        根据库存添加
      </el-button>
    </template>
    <Table
      v-if="finishProductionOptions.datalist.length"
      ref="tablesRef"
      :config="finishProductionOptions.tableConfig"
      :table-list="finishProductionOptions.datalist"
      :column-list="finishProductionOptions.columnList"
    >
      <!-- 出仓匹数 -->
      <template #out_roll="{ row }">
        <vxe-input v-model="row.out_roll" :disabled="state.formInline.arrange_order_no.length" type="float" />
      </template>
      <!-- 出仓辅助数量 -->
      <template #out_length="{ row }">
        <vxe-input v-model="row.out_length" type="float" @change="computedKeys" />
      </template>
      <!-- 其他金额 -->
      <template #other_price="{ row }">
        <vxe-input v-model="row.other_price" type="float" @change="computedKeys" />
      </template>
      <!-- 结算金额 -->
      <template #total_price="{ row }">
        ￥{{ row.total_price }}
      </template>
      <!-- 结算单位 -->
      <template #auxiliary_unit_id="{ row }">
        <SelectComponents v-model="row.auxiliary_unit_id" size="small" style="width: 100%;" api="getInfoBaseMeasurementUnitList" label-field="name" value-field="id" @select="conductUnitPrice(row)" />
      </template>
      <!-- 单价 -->
      <template #unit_price="{ row }">
        <vxe-input v-show="isMainUnitFormat(row)" v-model="row.unit_price" type="float" @change="computedKeys" />
        <span v-show="!isMainUnitFormat(row)">{{ row.unit_price }}</span>
      </template>
      <!-- 辅助数量单价 -->
      <template #length_unit_price="{ row }">
        <vxe-input v-show="!isMainUnitFormat(row)" v-model="row.length_unit_price" type="float" @change="computedKeys" />
        <span v-show="isMainUnitFormat(row)">{{ row.length_unit_price }}</span>
      </template>
      <!-- 备注 -->
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" max-length="200" type="text" />
      </template>
      <!-- 细码 -->
      <template #xima="{ row, rowIndex }">
        <!-- 进仓数量 -->
        <!-- <el-button v-if="row.out_roll" type="text" @click="showFineSizeDialog(row, rowIndex)">
          录入
          <span v-if="row.out_roll - getSFRoll(row) > 0" style="color: red">({{ row.out_roll - getSFRoll(row) }}条未录)</span>
          <span v-if="Number(row.out_roll) === getSFRoll(row)" style="color: #ccc">(已录入)</span>
        </el-button>
        <span v-else style="color: #ccc">请先输入出仓匹数</span> -->
        <el-button v-if="getUnInRoll(row) && Number(row.out_roll) === 0" type="primary" text link @click="showFineSizeDialog(row, rowIndex)">
          录入
          <el-text v-if="row.item_fc_data?.length === 0" style="color: red">
            (未录入)
          </el-text>
          <el-text v-else style="color: #ccc">
            (已录入)
          </el-text>
        </el-button>
        <!-- 如果进仓匹数大于0 -->
        <el-button v-else-if="getUnInRoll(row) && Number(row.out_roll) > 0" type="primary" text link @click="showFineSizeDialog(row, rowIndex)">
          录入
          <span v-if="row.out_roll - getSFRoll(row) > 0" style="color: red">({{ row.out_roll - getSFRoll(row) }}条未录)</span>
          <span v-if="Number(row.out_roll) === Number(getSFRoll(row))" style="color: #ccc">(已录入)</span>
        </el-button>
        <!-- 如果进仓匹数为空，那么右侧提示字样为未录入 -->
        <el-text v-else style="color: red">
          未录入
        </el-text>
      </template>
      <!-- 操作 -->
      <template #operate="{ row }">
        <el-button type="text" @click="finishProductionOptions.handleRowDel(row)">
          删除
        </el-button>
      </template>
    </Table>
    <div v-else class="no_data" style="color: #999">
      <el-icon :size="80">
        <MessageBox />
      </el-icon>
      <div class="text">
        请选择仓库
      </div>
    </div>
  </FildCard>
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose">
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
  </BulkSetting>
  <AccordingLibAdd ref="AccordingLibAddRef" :warehouse_id="state.formInline.warehouse_id" @handle-sure="finishProductionOptions.handleSure" />
  <FineSizeAdd ref="FineSizeAddRef" @handle-sure="finishProductionOptions.handleSureFineSize" />
  <AssociatedPurchasingSalesReturn />
</template>

<style lang="scss" scoped>
// .el-form-item {
//   width: 100%;
// }
.no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  user-select: none;
}

.flex_button {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}
</style>
