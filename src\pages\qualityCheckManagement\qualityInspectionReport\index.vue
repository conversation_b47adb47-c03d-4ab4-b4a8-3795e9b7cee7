<script lang="ts" setup name="QualityInspectionReport">
import { Plus } from '@element-plus/icons-vue'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'
import currency from 'currency.js'
import { IndexColumnList } from './pullData'
import FildCard from '@/components/FildCard.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import Table from '@/components/Table.vue'
import { debounce, getFilterData, orderStatusConfirmBox } from '@/common/util'
import { FpmQualityCheckoutReportPass, FpmQualityCheckoutReportWait, GetqualityInspectionReportList } from '@/api/qualityInspectionReport'
import { processDataOut } from '@/common/handBinary'
import { usePrintTemplate } from '@/components/PrintPopoverBtn'
import SelectMergeComponent from '@/components/SelectMergeComponent/index.vue'
import { formatHashTag } from '@/common/format'

const { options: printOptions } = usePrintTemplate({
  printType: PrintType.PrintTemplateQuarantine,
  dataType: PrintDataType.Product,
})

const router = useRouter() // 实例化路由
const route = useRoute() // 实例化路由
// 请求的参数
const query = ref<any>({
  arrange_time: [], // 质检日期
  quality_checker_id: '',
  supplier_id: '', // 供应商ID
  product_id: '', // 成品编号id
  product_color_id: '', // 色号ID
  dyelot_number: '',
  volume_number: '',
  order_no: '', // 单据编号
  creator_id: '', // 制单人id
  auditor_id: '', // 审核人id
  warehouse_id: '',
  audit_status: undefined, // 单据状态
})

const { fetchData: fetchDataList, data: mainListData, total: totalList, page: pageList, size: sizeList, loading: loadingList, handleSizeChange, handleCurrentChange } = GetqualityInspectionReportList()

// table配置
const tableConfig = ref({
  fieldApiKey: 'qualityInspectionReportIndex',
  height: '100%',
  loading: loadingList.value,
  showPagition: true,
  page: pageList,
  size: sizeList,
  total: totalList,
  showOperate: true,
  operateWidth: '10%',
  showSort: false,
  handleSizeChange,
  handleCurrentChange,
})

// table的主体数据
// const list = ref([])
const list: any = computed(() => {
  if (mainListData.value?.list) {
    const newArr = mainListData.value?.list?.map((item: any) => {
      return {
        ...processDataOut(item),
        shrinkage_rate: item.shrinkage_rate || '',
      }
    })
    return newArr
  }
  else {
    return []
  }
})

async function getData() {
  if (query.value.arrange_time?.length) {
    query.value.quality_check_date_begin = dayjs(query.value.arrange_time[0]).format('YYYY-MM-DD')
    query.value.quality_check_date_end = dayjs(query.value.arrange_time[1]).format('YYYY-MM-DD')
  }
  else {
    query.value.quality_check_date_begin = ''
    query.value.quality_check_date_end = ''
  }
  await fetchDataList(getFilterData(query.value))
}

onMounted(async () => {
  await getData()
})
// 监听页面
watch(() => route.path, async (newPath, _) => {
  if (newPath === '/qualityCheckManagement/qualityInspectionReport')
    await getData()
}, { deep: true })

watch(query, debounce(async () => {
  //
  await getData()
}, 400), { deep: true })
// #region 跳转到新增页面
function handAdd() {
  router.push({
    name: 'QualityInspectionReportAdd',
  })
}
// 查看
function handDetail(row: any) {
  router.push({
    name: 'QualityInspectionReportDetail',
    query: { id: row.id },
  })
}

function handEdit(row: any) {
  router.push({
    name: 'QualityInspectionReportEdit',
    query: { id: row.id },
  })
}
// 审核
async function handAudit(row: any) {
  await orderStatusConfirmBox({ id: row.id, audit_status: row.audit_status, message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' }, api: FpmQualityCheckoutReportPass })
  await getData()
}

// 取消审核
async function handApproved(row: any) {
  await orderStatusConfirmBox({ id: row.id, audit_status: row.audit_status, message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' }, api: FpmQualityCheckoutReportWait })
  await getData()
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="" class="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="质检日期:" width="310">
          <template #content>
            <SelectDate v-model="query.arrange_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称:" width="310">
          <template #content>
            <SelectMergeComponent
              v-model="query.product_id"
              :custom-label="(row:any) => `${formatHashTag(row.finish_product_code, row.finish_product_name)}`"
              :multiple="false"
              api-name="GetFinishProductDropdownList"
              remote
              remote-key="finish_product_code_or_name"
              remote-show-suffix
              placeholder="成品编号、成品名称"
              value-field="id"
              @change="query.product_color_id = ''"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="颜色编号:" width="310">
          <template #content>
            <SelectMergeComponent
              v-model="query.product_color_id"
              :custom-label="(row:any) => `${formatHashTag(row.product_color_code, row.product_color_name)}`"
              :multiple="false"
              :query="{
                finish_product_id: query.product_id,
              }"
              :disabled="!query.product_id"
              api-name="GetFinishProductColorDropdownList"
              remote
              remote-key="product_color_code_or_name"
              remote-show-suffix
              placeholder="成品颜色、色号"
              value-field="id"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓库名称:" width="310">
          <template #content>
            <SelectComponents v-model="query.warehouse_id" api="GetPhysicalWarehouseDropdownList" warehouse_type_id="finishProduction" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="缸号:" width="310">
          <template #content>
            <vxe-input
              v-model="query.dyelot_number"
              placeholder="缸号"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="制单人:" width="310">
          <template #content>
            <SelectComponents
              v-model="query.creator_id"
              style="width: 310px"
              api="GetUserDropdownList"
              label-field="name" value-field="id" clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="审核人:" width="310">
          <template #content>
            <SelectComponents
              v-model="query.auditor_id"
              api="GetUserDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据编号:" width="310">
          <template #content>
            <el-input v-model="query.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:" width="310">
          <template #content>
            <el-select
              v-model="query.audit_status"
              placeholder=""
              clearable
            >
              <el-option
                v-for="item in [{ value: 4, label: '已作废' }, { value: 3, label: '已驳回' }, { value: 1, label: '待审核' }, { value: 2, label: '已审核' }]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard class="table-card-full" :tool-bar="true">
      <template #right-top>
        <!-- <BottonExcel :loading="mainOptions.exportOptions.loadingExport" @onClickExcel="mainOptions.exportOptions.handleExport" title="导出文件"></BottonExcel> -->
        <el-button
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handAdd"
        >
          新建
        </el-button>
      </template>
      <Table
        :config="tableConfig"
        :table-list="list"
        :column-list="IndexColumnList"
      >
        <template #product_name="{ row }">
          {{ formatHashTag(row.product_code, row.product_name) }}
        </template>
        <template #product_color_code="{ row }">
          {{ formatHashTag(row.product_color_code, row.product_color_name) }}
        </template>
        <template #order_no="{ row }">
          <el-link type="primary" :underline="false" @click="handDetail(row)">
            {{ row?.order_no }}
          </el-link>
        </template>
        <template #inspect_ratio="{ row }">
          <span>{{ row.inspect_ratio ? `${currency(row.inspect_ratio).multiply(100).value}%` : '' }}</span>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'QualityInspectionReportDetail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'QualityInspectionReportEdit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>

            <el-link
              v-if="row.audit_status === 1"
              v-has="'QualityInspectionReportPass'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'QualityInspectionReportWait'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
            <PrintPopoverBtn
              v-if="row.audit_status === 1 || row.audit_status === 2"
              :id="row.id"
              :key="row.id"
              print-btn-type="text"
              style="width: auto"
              api="FpmQualityCheckoutReportPrint"
              :options="printOptions"
              :print-type="PrintType.PrintTemplateQuarantine"
              :data-type="PrintDataType.Product"
            />
          </el-space>
        </template>
      </Table>
    <!-- <GridTable :columns="columnList" :column-list="list" show-pagition :config="tableConfig" /> -->
    </FildCard>
  </div>
</template>

<style scoped lang="scss">

</style>
