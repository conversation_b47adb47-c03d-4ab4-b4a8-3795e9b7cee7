<script setup lang="ts" name="FpSaleAllotReturnDeliverGodownOrderEdit">
import { ElMessage } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import AccordingLibAdd from '../components/AccordingRepertoryAdd.vue'
import AssociatedPurchasingSalesReturn from '../components/AssociatedPurchasingSalesReturn.vue'
import FineSizeDeliveryOrderDetail from '../components/FineSizeDeliveryOrderDetail.vue'
import { getFpmSaleAllocateOutOrder, updateFpmSaleAllocateOutOrder } from '@/api/fpSaleAllotReturnDeliverGodownOrder'
import { EmployeeType } from '@/common/enum'
import { formatDate } from '@/common/format'
import { deleteToast } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import useRouterList from '@/use/useRouterList'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { processDataIn, processDataOut } from '@/common/handBinary'
// 备注: 配布单号
// 带出 采购退货单号
// 出仓匹数不可修改
// form 营销体系 供应商 仓库名称 不可修改 仓管员可修改
const routerList = useRouterList()
let uuid = 0
const AccordingLibAddRef = ref()
const FineSizeDeliveryOrderDetailRef = ref()
const state = reactive<any>({
  formInline: {
    arrange_order_no: '',
    sale_system_id: '',
    warehouse_id: '',
    warehouse_in_id: '',
    store_keeper_id: '',
    remark: '',
    driver_id: [],
    warehouse_out_time: new Date(),
  },
  formRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    warehouse_out_time: [{ required: true, message: '请选择出仓日期', trigger: 'change' }],
    warehouse_id: [{ required: true, message: '请选择仓库名称', trigger: 'change' }],
    warehouse_in_id: [{ required: true, message: '请选择调入仓库名称', trigger: 'change' }],
  },
  multipleSelection: [],
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
})

const bulkShow = ref(false)
const finishProductionOptions = reactive<any>({
  multipleSelection: [],
  rowIndex: 0,
  tableConfig: {
    showSlotNums: false,
    showSpanHeader: true,
    showCheckBox: true,
    handAllSelect: (val: any) => handAllSelect(val),
    handleSelectionChange: (val: any) => handleSelectionChange(val),
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 总数量 纸筒总重（kg） 结算数量 进仓辅助数量 采购辅助数量 进仓金额
        if (['total_weight'].includes(column.field))
          return sumNum(data, 'total_weight', '', 'float')

        if (['actually_weight'].includes(column.field))
          return sumNum(data, 'actually_weight', '', 'float')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '', 'float')

        if (['out_length'].includes(column.field))
          return sumNum(data, 'out_length', '', 'float')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      childrenList: [
        {
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
          required: true,
        },
        {
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
          required: true,
        },
      ],
    },
    {
      title: '成品信息',
      childrenList: [
        {
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },
        {
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          field: 'out_roll',
          soltName: 'out_roll',
          title: '出仓匹数',
          minWidth: 100,
          required: true,
        },
      ],
    },
    {
      title: '库存信息',
      childrenList: [
        {
          field: 'sum_stock_roll',
          title: '可用匹数',
          minWidth: 100,
        },
        {
          field: 'sum_stock_weight',
          title: '可用数量',
          minWidth: 100,
        },
        {
          field: 'sum_stock_length',
          title: '库存辅助数量',
          minWidth: 100,
        },
      ],
    },
    {
      title: '出仓数量信息',
      childrenList: [
        {
          field: 'total_weight',
          title: '出仓数量',
          minWidth: 100,
          required: true,
        },
        {
          field: 'weight_error',
          title: '码单空差',
          minWidth: 100,
        },
        {
          field: 'actually_weight',
          title: '码单数量',
          minWidth: 100,
        },
        {
          field: 'settle_error_weight',
          title: '结算空差',
          minWidth: 100,
        },
        {
          field: 'settle_weight',
          title: '结算数量',
          minWidth: 100,
        },
        {
          field: 'unit_name',
          title: '单位',
          minWidth: 100,
        },
      ],
    },
    {
      title: '出仓辅助数量信息',
      childrenList: [
        {
          field: 'out_length',
          title: '辅助数量',
          minWidth: 100,
        },
      ],
    },

    {
      title: '单据备注信息',
      childrenList: [
        {
          field: 'remark',
          soltName: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: '',
          soltName: 'xima',
          title: '细码',
          width: 100,
        },
      ],
    },
    {
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: '',
          soltName: 'operate',
          title: '操作',
          width: 100,
        },
      ],
    },
  ],
  handleSure: (list: any) => {
    // 成品成分
    // 取配布单不可修改
    list = list.map((item: any) => {
      return {
        // ...item,
        uuid: ++uuid,
        quote_order_no: '',
        product_code: item.product_code,
        product_name: item.product_name,
        customer_name: item.customer_name,
        customer_id: item.customer_id,
        product_color_code: item.product_color_code,
        product_color_id: item.product_color_id,
        product_color_name: item.product_color_name,
        dye_factory_dyelot_number: item.dyelot_number,
        product_level_name: item.product_level_name,
        product_level_id: item.product_level_id,
        product_remark: item.product_remark,
        product_craft: item.finish_product_craft,
        sum_stock_roll: item.roll,
        sum_stock_weight: item.weight,
        sum_stock_length: item.length,
        unit_name: item.measurement_unit_name,
        unit_id: item.measurement_unit_id,
        product_ingredient: item.finish_product_ingredient,
        id: item?.id,
        item_fc_data: [],
        remark: '',
      }
    })
    finishProductionOptions.datalist = [...finishProductionOptions.datalist, ...list]
  },
  handleSureFineSize: (list: any) => {
    let total_weight = 0
    let weight_error = 0
    let settle_weight = 0
    let out_length = 0
    list.forEach((item: any) => {
      total_weight += Number(item.base_unit_weight)
      weight_error += Number(item.weight_error)
      settle_weight += Number(item.settle_weight)
      out_length += Number(item.length)
    })
    // const total_weight = list.reduce((pre: any, val: any) => pre + Number(val.base_unit_weight), 0)
    // const weight_error = list.reduce((pre: any, val: any) => pre + Number(val.weight_error), 0)
    // const settle_weight = list.reduce((pre: any, val: any) => pre + Number(val.settle_weight), 0)
    // const out_length = list.reduce((pre: any, val: any) => pre + Number(val.length), 0)
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].item_fc_data = list
    // 出仓数量 = 细码数量之和
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].total_weight = Number(total_weight.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].out_length = Number(out_length.toFixed(2))
    // 空差 = 细码空差之和
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].weight_error = Number(weight_error.toFixed(2))
    // 结算数量 = 总数量 - 空差
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].settle_weight = Number(settle_weight.toFixed(2))
    computedKeys()
  },
  //   删除
  handleRowDel: async ({ id }: any) => {
    const res = await deleteToast('是否确认删除该成品')
    if (!res)
      return
    const index = finishProductionOptions.datalist.findIndex((item: any) => item.id === id)
    finishProductionOptions.datalist.splice(index, 1)
  },
  //   批量操作
  handEdit,
})
function handEdit() {
  if (finishProductionOptions.multipleSelection.length < 1)
    return ElMessage.error('请先勾选需要修改的数据')

  bulkShow.value = true
}
function handAllSelect({ records }: any) {
  finishProductionOptions.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  finishProductionOptions.multipleSelection = records
}
// 批量操作
const bulkSetting = ref<any>({})
const bulkList = reactive<any>([
  {
    field: 'out_roll',
    title: '出仓匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])
async function bulkSubmit({ row, value }: any) {
  const ids = finishProductionOptions.multipleSelection.map((item: any) => item.uuid)

  finishProductionOptions.datalist.map((item: any) => {
    if (ids.includes(item.uuid))
      item[row.field] = value[row.field]
  })
  ElMessage.success('设置成功')
  handBulkClose()
  computedKeys()
}

function handBulkClose() {
  bulkShow.value = false
}

// const getSFRoll = (row: any) => {
//   return row.item_fc_data.reduce((pre: any, val: any) => pre + Number(val.roll), 0)
// }

// const showLibDialog = () => {
//   AccordingLibAddRef.value.state.showModal = true
// }

function computedKeys() {
  // 自动计算退货金额
  // finishProductionOptions.datalist.forEach((item: any) => {
  //   const { unit_price, settle_weight, length_unit_price, out_length, other_price } = item
  //   item.total_price = Number(unit_price) * Number(settle_weight) + Number(length_unit_price) * Number(out_length) + Number(other_price)
  //   if (Number.isNaN(item.total_price)) {
  //     item.total_price = 0
  //   } else {
  //     item.total_price = item.total_price.toFixed(2)
  //   }
  // })
}
const tablesRef = ref()
watch(
  () => finishProductionOptions.datalist,
  () => {
    // computedKeys()
    nextTick(() => {
      tablesRef.value.tableRef.updateFooter()
    })
  },
  { deep: true },
)

function showFineSizeDialog(row: any, rowIndex: number) {
  finishProductionOptions.rowIndex = rowIndex
  FineSizeDeliveryOrderDetailRef.value.showDialog(row, true)
}
const formRef = ref()
const route = useRoute()
// 提交所有数据
const { fetchData: updateFetch, data: successData, success: updateSuccess, msg: updateMsg } = updateFpmSaleAllocateOutOrder()
function submitAddAllData() {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      const query = {
        ...state.formInline,
        customer_id: state.formInline.customer_id || 0,
        store_keeper_id: state.formInline.store_keeper_id || 0,
        sale_user_id: state.formInline.sale_user_id || 0,
        sale_follower_id: state.formInline.sale_follower_id || 0,
        logistics_company_id: state.formInline.logistics_company_id || 0,
        warehouse_out_time: formatDate(state.formInline.warehouse_out_time),
        driver_id: state.formInline.driver_id.join(','),
        item_data: [],
      }
      // 校验成品信息
      if (!finishProductionOptions.datalist.length)
        return ElMessage.error('请添加成品信息')

      // let FSRollFlag = true
      // let FSLengthFlag = true
      for (let i = 0; i < finishProductionOptions.datalist.length; i++) {
        if (!finishProductionOptions.datalist[i].product_code)
          return ElMessage.error('成品编号为必填项')

        if (!finishProductionOptions.datalist[i].product_name)
          return ElMessage.error('成品名称为必填项')

        if (!Number(finishProductionOptions.datalist[i].out_roll))
          return ElMessage.error('出仓匹数为必填项')

        if (!Number(finishProductionOptions.datalist[i].total_weight))
          return ElMessage.error('出仓数量为必填项')

        let FSRoll = 0
        let FSLen = 0
        finishProductionOptions.datalist[i].item_fc_data.forEach((item: any) => {
          FSRoll += Number(item.roll)
          FSLen += Number(item.length)
        })
        FSRoll = Number(FSRoll.toFixed(2))
        FSLen = Number(FSLen.toFixed(2))
        if (Number(finishProductionOptions.datalist[i].out_roll)) {
          // const FSRoll = finishProductionOptions.datalist[i].item_fc_data.reduce((pre: any, val: any) => pre + Number(val.roll), 0)

          if (FSRoll !== Number(finishProductionOptions.datalist[i].out_roll))
            return ElMessage.error('出仓匹数和录入细码匹数不匹配')
        }
        // const FSLen = finishProductionOptions.datalist[i].item_fc_data.reduce((pre: any, val: any) => pre + Number(val.length), 0)
        if (FSLen !== Number(finishProductionOptions.datalist[i].out_length))
          return ElMessage.error('细码总辅助数量与分录行的辅助数量不匹配')
      }

      // if (!FSRollFlag) {
      //   return ElMessage.error('出仓匹数和录入细码匹数不匹配')
      // }
      // if (!FSLengthFlag) {
      //   return ElMessage.error('细码总辅助数量与分录行的辅助数量不匹配')
      // }
      query.item_data = processDataIn(finishProductionOptions.datalist)

      await updateFetch({
        ...query,
        id: Number(route.query.id),
      })
      if (updateSuccess.value) {
        ElMessage.success('提交成功')
        getData()
        routerList.push({
          name: 'FpSaleAllotReturnDeliverGodownOrderDetail',
          query: {
            id: successData.value.id,
          },
        })
      }
      else {
        ElMessage.error(updateMsg.value)
      }
    }
  })
}
const customerRef = ref()
const { fetchData, data: detailData } = getFpmSaleAllocateOutOrder()
async function getData() {
  await fetchData({ id: route.query.id })
  const driver_id: any = []
  detailData.value.driver_id.split(',').forEach((item: any) => {
    if (item)
      driver_id.push(Number(item))
  })
  state.formInline = {
    ...detailData.value,
    customer_id: detailData.value.customer_id || '',
    store_keeper_id: detailData.value.store_keeper_id || '',
    sale_user_id: detailData.value.sale_user_id || '',
    sale_follower_id: detailData.value.sale_follower_id || '',
    logistics_company_id: detailData.value.logistics_company_id || '',
    driver_id,
  }
  customerRef.value.inputLabel = detailData.value.customer_name
  const list = processDataOut(detailData.value.item_data)
  list.forEach((item: any) => {
    item.uuid = ++uuid
  })
  finishProductionOptions.datalist = list
}
onMounted(() => {
  getData()
})
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" :disabled="!finishProductionOptions.datalist.length" @click="submitAddAllData">
        提交
      </el-button>
    </template>
    <el-form ref="formRef" :model="state.formInline" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="配布单号:">
          <template #content>
            {{ state.formInline.arrange_order_no }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="营销体系名称:">
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents v-model="state.formInline.sale_system_id" disabled api="GetSaleSystemDropdownListApi" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="仓库名称:">
          <template #content>
            <el-form-item prop="warehouse_id">
              <SelectComponents v-model="state.formInline.warehouse_id" disabled api="GetPhysicalWarehouseDropdownList" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="调入仓库名称:">
          <template #content>
            <el-form-item prop="warehouse_in_id">
              <SelectComponents v-model="state.formInline.warehouse_in_id" disabled api="GetDropdownListWithoutDS" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="出仓日期:">
          <template #content>
            <el-form-item prop="warehouse_out_time">
              <el-date-picker v-model="state.formInline.warehouse_out_time" type="date" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:">
          <template #content>
            <el-form-item>
              <SelectDialog
                ref="customerRef"
                v-model="state.formInline.customer_id"
                :label-name="detailData.customer_name"
                :query="{ name: componentRemoteSearch.customer_name, sale_system_id: state.formInline.sale_system_id }"
                api="GetCustomerEnumList"
                :column-list="[
                  {
                    title: '客户编号',
                    minWidth: 100,
                    required: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '客户编号',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    title: '客户名称',
                    minWidth: 100,
                    colGroupHeader: true,
                    required: true,
                    childrenList: [
                      {
                        isEdit: true,
                        field: 'name',
                        title: '客户名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    title: '电话',
                    colGroupHeader: true,
                    minWidth: 100,
                    childrenList: [
                      {
                        field: 'phone',
                        isEdit: true,
                        title: '电话',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    title: '销售员',
                    minWidth: 100,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'seller_name',
                        title: '销售员',
                        soltName: 'seller_name',
                        isEdit: true,
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-input="val => (componentRemoteSearch.customer_name = val)"
              />
              <!-- <SelectComponents api="GetCustomerEnumList" label-field="name" value-field="id" v-model="state.formInline.customer_id" clearable /> -->
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓管员:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.formInline.store_keeper_id"
                disabled
                :query="{
                  duty: EmployeeType.warehouseManager,
                }"
                api="GetEmployeeListEnum"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售员:">
          <template #content>
            <el-form-item>
              <SelectComponents v-model="state.formInline.sale_user_id" disabled :query="{ duty: EmployeeType.salesman }" api="GetEmployeeListEnum" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售跟单员:">
          <template #content>
            <el-form-item>
              <SelectComponents v-model="state.formInline.sale_follower_id" disabled :query="{ duty: EmployeeType.follower }" api="GetEmployeeListEnum" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="司机名称:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.formInline.driver_id"
                :query="{
                  duty: EmployeeType.driver,
                }"
                api="GetEmployeeListEnum"
                multiple
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="物流公司:">
          <template #content>
            <el-form-item>
              <vxe-input v-model="state.formInline.logistics_company_name" disabled style="width: 100%" />
              <!-- <SelectComponents v-model="state.formInline.logistics_company_id" disabled api="GetInfoSaleLogisticsCompanyEnumList" label-field="name" value-field="id" clearable /> -->
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="物流区域:">
          <template #content>
            <el-form-item>
              <vxe-input v-model="state.formInline.logistics_company_area" disabled style="width: 100%" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="备注:" copies="2">
          <template #content>
            <el-form-item prop="remark">
              <vxe-textarea v-model="state.formInline.remark" style="width: 100%" maxlength="500" show-word-count />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
    <!-- <el-button type="pirmary" @click="submitData">提交</el-button> -->
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]" :tool-bar="false">
    <template #right-top>
      <el-button v-if="!state.formInline.arrange_order_no.length" type="primary" @click="handEdit">
        批量操作
      </el-button>
      <!-- <el-button type="primary" @click="showLibDialog">根据库存添加</el-button> -->
    </template>
    <Table
      v-if="finishProductionOptions.datalist.length"
      ref="tablesRef"
      :config="finishProductionOptions.tableConfig"
      :table-list="finishProductionOptions.datalist"
      :column-list="finishProductionOptions.columnList"
    >
      <!-- 出仓匹数 -->
      <template #out_roll="{ row }">
        <vxe-input v-model="row.out_roll" :disabled="state.formInline.arrange_order_no.length" type="float" />
      </template>
      <!-- 出仓辅助数量 -->
      <template #out_length="{ row }">
        <vxe-input v-model="row.out_length" :disabled="state.formInline.arrange_order_no.length" type="float" @change="computedKeys" />
      </template>
      <!-- 其他金额 -->
      <template #other_price="{ row }">
        <vxe-input v-model="row.other_price" :disabled="state.formInline.arrange_order_no.length" type="float" @change="computedKeys" />
      </template>
      <!-- 结算金额 -->
      <template #total_price="{ row }">
        ￥{{ row.total_price }}
      </template>
      <!-- 单价 -->
      <template #unit_price="{ row }">
        <vxe-input v-model="row.unit_price" :disabled="state.formInline.arrange_order_no.length" type="float" @change="computedKeys" />
      </template>
      <!-- 辅助数量单价 -->
      <template #length_unit_price="{ row }">
        <vxe-input v-model="row.length_unit_price" :disabled="state.formInline.arrange_order_no.length" type="float" @change="computedKeys" />
      </template>
      <!-- 备注 -->
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" :disabled="state.formInline.arrange_order_no.length" maxlength="200" type="text" />
      </template>
      <!-- 细码 -->
      <template #xima="{ row, rowIndex }">
        <!-- 进仓数量 -->
        <el-button type="text" @click="showFineSizeDialog(row, rowIndex)">
          查看
        </el-button>
      </template>
      <!-- 操作 -->
      <template #operate="{ row }">
        <el-button v-if="!state.formInline.arrange_order_no.length" type="text" @click="finishProductionOptions.handleRowDel(row)">
          删除
        </el-button>
      </template>
    </Table>
    <div v-else class="no_data" style="color: #999">
      <el-icon :size="80">
        <MessageBox />
      </el-icon>
      <div class="text">
        请选择仓库
      </div>
    </div>
  </FildCard>
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose">
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
  </BulkSetting>
  <AccordingLibAdd ref="AccordingLibAddRef" @handle-sure="finishProductionOptions.handleSure" />
  <FineSizeDeliveryOrderDetail ref="FineSizeDeliveryOrderDetailRef" @handle-sure="finishProductionOptions.handleSureFineSize" />
  <AssociatedPurchasingSalesReturn />
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}

.no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  user-select: none;
}

.flex_button {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}
</style>
