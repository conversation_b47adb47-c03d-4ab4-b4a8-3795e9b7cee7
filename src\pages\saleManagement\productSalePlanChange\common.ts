import { SaleTypeEnum } from '@/enum'

export const detailConfig: any = {
  [SaleTypeEnum.FinishProduct]: {
    titleOne: '成品信息',
    fieldApiKeyOne: 'ProductSalePlanChangeDetail_A',
    showSpanHeader: false,
    tableOneColumn: [
      {
        field: 'detail_order_no',
        title: '订单编号',
        minWidth: 140,
        fixed: 'left',
      },
      {
        field: 'product_code',
        title: '成品编号',
        minWidth: 100,
        fixed: 'left',
      },
      {
        field: 'product_name',
        title: '成品名称',
        minWidth: 100,
        fixed: 'left',
      },
      {
        field: 'product_color_code',
        title: '色号',
        minWidth: 100,
        fixed: 'left',
      },
      {
        field: 'product_color_name',
        title: '颜色',
        minWidth: 100,
        fixed: 'left',
      },
      {
        field: 'roll',
        title: '匹数',
        minWidth: 80,
      },
      {
        field: 'change_roll',
        title: '变更匹数',
        minWidth: 120,
        soltName: 'change_roll',
      },
      {
        field: 'dye_roll',
        title: '已排染匹数',
        minWidth: 100,
      },
      {
        field: 'unit_price',
        title: '单价',
        minWidth: 100,
      },
      {
        field: 'change_unit_price',
        title: '变更单价',
        minWidth: 120,
        soltName: 'change_unit_price',
      },
      {
        field: 'weight',
        title: '数量',
        minWidth: 100,
      },
      {
        field: 'change_weight',
        title: '变更数量',
        minWidth: 120,
        soltName: 'change_weight',
      },
      {
        field: 'measurement_unit_name',
        title: '单位',
        minWidth: 70,
      },
      {
        field: 'upper_limit',
        title: '上限',
        minWidth: 70,
      },
      {
        field: 'lower_limit',
        title: '下限',
        minWidth: 70,
      },
      {
        field: 'other_price',
        title: '其他金额',
        minWidth: 100,
      },
      {
        field: 'change_other_price',
        title: '变更其他金额',
        minWidth: 120,
        soltName: 'change_other_price',
      },
      {
        field: 'remark',
        title: '备注',
        minWidth: 100,
      },
      {
        field: 'change_remark',
        title: '变更备注',
        minWidth: 120,
        soltName: 'change_remark',
      },
      {
        field: 'grey_fabric_code',
        title: '坯布编号',
        minWidth: 100,
      },
      {
        field: 'grey_fabric_name',
        title: '坯布名称',
        minWidth: 100,
      },
      {
        field: 'product_level_name',
        title: '成品等级',
        minWidth: 100,
      },
      {
        field: 'product_color_kind_name',
        title: '颜色类别',
        minWidth: 100,
      },
      {
        field: 'customer_account_num',
        title: '款号',
        minWidth: 100,
      },
      {
        field: 'finish_product_width_and_unit_name',
        title: '成品幅宽',
        minWidth: 80,
      },
      {
        field: 'finish_product_gram_weight_and_unit_name',
        title: '成品克重',
        minWidth: 80,
      },
      {
        field: 'paper_tube_weight',
        title: '纸筒',
        minWidth: 70,
      },
      {
        field: 'weight_error',
        title: '空差',
        minWidth: 70,
      },
      {
        field: 'total_price',
        title: '总金额',
        minWidth: 70,
        fixed: 'right',
      },
      {
        field: 'change_total_price',
        title: '变更总金额',
        minWidth: 100,
        fixed: 'right',
      },
    ],
    titleTwo: '坯布信息',
    fieldApiKeyTwo: 'ProductSalePlanChangeDetail_B',
    tableTwoColumn: [
      {
        field: 'grey_fabric_code',
        title: '坯布编号',
        minWidth: 100,
      },
      {
        field: 'grey_fabric_name',
        title: '坯布名称',
        minWidth: 150,
      },
      {
        field: 'roll',
        title: '匹数',
        minWidth: 100,
      },
      {
        field: 'change_roll',
        title: '变更匹数',
        minWidth: 100,
      },
      {
        field: 'weight',
        title: '数量',
        minWidth: 100,
      },
      {
        field: 'change_weight',
        title: '变更数量',
        minWidth: 100,
      },
      {
        field: 'planed_roll',
        title: '已计划匹数',
        minWidth: 100,
      },
      {
        field: 'planed_weight',
        title: '已计划数量',
        minWidth: 100,
      },
      {
        field: 'scheduling_roll',
        title: '排产匹数',
        minWidth: 100,
        isPrice: true,
      },
      {
        field: 'scheduling_weight',
        title: '排产数量',
        minWidth: 100,
      },
      {
        field: 'produced_roll',
        title: '已产匹数',
        minWidth: 100,
      },
      {
        field: 'produced_weight',
        title: '已产数量',
        minWidth: 100,
      },
      {
        field: 'use_stock_roll',
        title: '调库存匹数',
        minWidth: 100,
      },
    ],
  },

  [SaleTypeEnum.GreyFabric]: {
    titleOne: '坯布信息',
    showSpanHeader: true,
    fieldApiKeyOne: 'GreySalePlanChangeDetail_A',
    tableOneColumn: [
      {
        title: '',
        childrenList: [
          {
            field: 'detail_order_no',
            title: '订单编号',
            minWidth: 150,
          },
          {
            field: 'grey_fabric_code',
            title: '坯布编号',
            minWidth: 100,
          },
          {
            field: 'grey_fabric_name',
            title: '坯布名称',
            minWidth: 100,
          },
          {
            field: 'grey_fabric_width_and_unit_name',
            title: '坯布幅宽',
            minWidth: 250,
          },
          {
            field: 'grey_fabric_gram_weight_and_unit_name',
            title: '坯布克重',
            minWidth: 250,
          },
          {
            field: 'needle_size',
            title: '针寸数',
            minWidth: 150,
          },
          {
            field: 'total_needle_size',
            title: '总针数',
            minWidth: 150,
          },
          {
            field: 'gray_fabric_color_name',
            title: '织坯颜色',
            minWidth: 100,
          },
          {
            field: 'gray_fabric_level_name',
            title: '坯布等级',
            minWidth: 150,
          },
          {
            field: 'measurement_unit_name',
            title: '单位',
            minWidth: 150,
          },
          {
            field: 'weight_of_fabric',
            title: '坯布定重',
            minWidth: 150,
          },
          {
            field: 'roll',
            title: '匹数',
            minWidth: 100,
          },
          {
            field: 'change_roll',
            title: '变更匹数',
            minWidth: 120,
            soltName: 'change_roll',
          },
          {
            field: 'weight',
            title: '数量',
            minWidth: 100,
          },
          {
            field: 'change_weight',
            title: '变更数量',
            minWidth: 120,
            soltName: 'change_weight',
          },
          {
            field: 'upper_limit',
            title: '上限',
            minWidth: 100,
          },
          {
            field: 'lower_limit',
            title: '下限',
            minWidth: 100,
          },
          {
            field: 'unit_price',
            title: '单价',
            minWidth: 100,
          },
          {
            field: 'change_unit_price',
            title: '变更单价',
            minWidth: 120,
            soltName: 'change_unit_price',
          },
          {
            field: 'other_price',
            title: '其他金额',
            minWidth: 100,
          },
          {
            field: 'change_other_price',
            title: '变更其他金额',
            minWidth: 120,
            soltName: 'change_other_price',
          },
          {
            field: 'total_price',
            title: '总金额',
            minWidth: 100,
          },
          {
            field: 'change_total_price',
            title: '变更总金额',
            minWidth: 100,
          },
          {
            field: 'remark',
            title: '备注',
            minWidth: 100,
          },
          {
            field: 'change_remark',
            title: '变更备注',
            minWidth: 120,
            soltName: 'change_remark',
          },
        ],
      },

    ],
    titleTwo: '用纱信息',
    fieldApiKeyTwo: 'GreySalePlanChangeDetail_B',
    tableTwoColumn: [
      {
        field: 'raw_material_code',
        title: '原料编号',
        minWidth: 100,
      },
      {
        field: 'raw_material_name',
        title: '原料名称',
        minWidth: 100,
      },
      {
        field: 'raw_material_color_name',
        title: '原料颜色',
        minWidth: 100,
      },
      {
        field: 'measurement_unit_name',
        title: '单位',
        minWidth: 100,
      },
      {
        field: 'yarn_loss',
        title: '用纱损耗',
        minWidth: 100,
        soltName: 'yarn_loss',
      },
      {
        field: 'yarn_ratio',
        title: '用纱比例',
        minWidth: 100,
        soltName: 'yarn_ratio',
      },
      {
        field: 'use_yarn_quantity',
        title: '用纱量',
        minWidth: 100,
      },
      {
        field: 'change_use_yarn_quantity',
        title: '变更用纱量',
        minWidth: 100,
        soltName: 'change_use_yarn_quantity',
      },
    ],
  },
  [SaleTypeEnum.RawMaterial]: {
    titleOne: '原料信息',
    showSpanHeader: true,
    fieldApiKeyOne: 'GreySalePlanChangeDetail_A',
    tableOneColumn: [
      {
        title: '',
        childrenList: [
          {
            field: 'detail_order_no',
            title: '订单编号',
            minWidth: 150,
          },
          {
            field: 'raw_material_code',
            title: '原料编号',
            minWidth: 100,
          },
          {
            field: 'raw_material_name',
            title: '原料名称',
            minWidth: 100,
          },
          {
            field: 'type_name',
            title: '原料类型',
            minWidth: 100,
          },
          {
            field: 'ingredient',
            title: '原料成分',
            minWidth: 100,
          },
          {
            field: 'craft',
            title: '原料工艺',
            minWidth: 100,
          },
          {
            field: 'count',
            title: '原料支数',
            minWidth: 100,
          },
          {
            field: 'measurement_unit_name',
            title: '单位',
            minWidth: 100,
          },
          {
            field: 'raw_material_color_code',
            title: '颜色编号',
            minWidth: 100,
          },
          {
            field: 'raw_material_color_name',
            title: '颜色名称',
            minWidth: 100,
          },
          {
            field: 'weight',
            title: '数量',
            minWidth: 100,
          },
          {
            field: 'change_weight',
            title: '变更数量',
            minWidth: 120,
            soltName: 'change_weight',
          },
          {
            field: 'piece_count',
            title: '件数',
            minWidth: 100,
          },
          {
            field: 'change_piece_count',
            title: '变更件数',
            minWidth: 120,
            soltName: 'change_piece_count',
          },
          {
            field: 'upper_limit',
            title: '上限',
            minWidth: 100,
          },
          {
            field: 'lower_limit',
            title: '下限',
            minWidth: 100,
          },
          {
            field: 'unit_price',
            title: '单价',
            minWidth: 100,
          },
          {
            field: 'change_unit_price',
            title: '变更单价',
            minWidth: 120,
            soltName: 'change_unit_price',
          },
          {
            field: 'other_price',
            title: '其他金额',
            minWidth: 100,
          },
          {
            field: 'change_other_price',
            title: '变更其他金额',
            minWidth: 120,
            soltName: 'change_other_price',
          },
          {
            field: 'total_price',
            title: '总金额',
            minWidth: 100,
          },
          {
            field: 'change_total_price',
            title: '变更总金额',
            minWidth: 100,
          },
          {
            field: 'remark',
            title: '备注',
            minWidth: 100,
          },
          {
            field: 'change_remark',
            title: '变更备注',
            minWidth: 120,
            soltName: 'change_remark',
          },
        ],
      },

    ],
    titleTwo: '用纱信息',
    fieldApiKeyTwo: 'GreySalePlanChangeDetail_B',
    tableTwoColumn: [
      {
        field: 'raw_material_code',
        title: '原料编号',
        minWidth: 100,
      },
      {
        field: 'raw_material_name',
        title: '原料名称',
        minWidth: 100,
      },
      {
        field: 'type_name',
        title: '原料类型',
        minWidth: 100,
      },
      {
        field: 'ingredient',
        title: '原料成分',
        minWidth: 100,
      },
      {
        field: 'craft',
        title: '原料工艺',
        minWidth: 100,
      },
      {
        field: 'count',
        title: '原料支数',
        minWidth: 100,
      },
      {
        field: 'measurement_unit_name',
        title: '单位',
        minWidth: 100,
      },
      {
        field: 'yarn_loss',
        title: '用纱损耗',
        minWidth: 100,
        soltName: 'yarn_loss',
      },
      {
        field: 'yarn_ratio',
        title: '用纱比例',
        minWidth: 100,
        soltName: 'yarn_ratio',
      },
      {
        field: 'use_yarn_quantity',
        title: '用纱量',
        minWidth: 100,
      },
      {
        field: 'change_use_yarn_quantity',
        title: '变更用纱量',
        minWidth: 100,
        soltName: 'change_use_yarn_quantity',
      },
    ],
  },
}
