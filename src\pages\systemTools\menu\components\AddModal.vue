<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import Icon from './Icon.vue'
import { AddMenuListApi, UpdateMenuApi } from '@/api/menu'
import { GetResourceTreeListApi } from '@/api/resourceTree'
import { ResourceType } from '@/common/enum'
import { getFilterData } from '@/common/util'

export interface Row { parent_id: number, parent_name: string, name: string, avatar_url: string, resource_id: number, sort: number, is_hide: boolean, id: number }
export interface Props {
  title: string
  modelValue: boolean
  type: string
  row: Row
  menuList: Row[]
}
const props = withDefaults(defineProps<Props>(), {
  title: '添加菜单',
  modelValue: false,
  type: 'add',
})
const emit = defineEmits(['update:modelValue', 'addSuccess', 'editSuccess'])
const cascaderProps = {
  value: 'id',
  label: 'name',
  checkStrictly: true,
  children: 'sub_menu',
  emitPath: false,
}

const ruleFormRef = ref()
const menuList = ref<any>()
const showModal = ref<boolean>(false)
const state = reactive({
  form: {
    name: '',
    avatar_url: '',
    resource_id: 0,
    sort: 0,
    is_hide: false,
    parent_id: 0,
    id: 0,
  },
  fromRules: {
    name: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
  },
})
watch(
  () => props.modelValue,
  () => {
    showModal.value = props.modelValue
    state.form.parent_id = props.row.parent_id || 0
    menuList.value = [{ id: 0, name: '根菜单' }, ...(props.menuList || [])]
    if (props.type === 'edit' && props.modelValue) {
      state.form.name = props.row.name || ''
      state.form.avatar_url = props.row.avatar_url || ''
      state.form.resource_id = props.row.resource_id || 0
      state.form.sort = props.row.sort || 0
      state.form.is_hide = props.row.is_hide || false
      state.form.id = props.row.id || 0
      menuList.value = (() => {
        const list = filterMenu(JSON.parse(JSON.stringify(menuList.value)))
        return [...(list || [])]
      })()
    }
    if (!props.modelValue) {
      state.form.name = ''
      state.form.avatar_url = ''
      state.form.sort = 0
      state.form.is_hide = false
      state.form.parent_id = 0
      state.form.resource_id = 0
      menuList.value = []
    }
    else {
      getData()
    }
  },
)

// 添加菜单
const { fetchData: fetchDataAdd, success: successAdd, msg: msgAdd } = AddMenuListApi()
async function addData() {
  getEndId()
  await fetchDataAdd(getFilterData(state.form))
  if (successAdd.value) {
    ElMessage.success('添加成功')
    emit('addSuccess')
  }
  else {
    ElMessage.error(msgAdd.value)
  }
}

// 更新菜单
const { fetchData: fetchDataUpdate, success: successUpdate, msg: msgUpdate } = UpdateMenuApi()
async function updateData() {
  let parent_id = state.form.parent_id
  if (typeof state.form.parent_id === 'object')
    parent_id = state.form.parent_id[0]

  getEndId()
  await fetchDataUpdate(getFilterData({ ...state.form, parent_id }))
  if (successUpdate.value) {
    ElMessage.success('编辑成功成功')
    emit('editSuccess')
  }
  else {
    ElMessage.error(msgUpdate.value)
  }
}

// 获取最后一个resource_id
function getEndId() {
  if (state.form.resource_id && typeof state.form.resource_id === 'object') {
    const list: number[] = state.form.resource_id
    state.form.resource_id = list[list.length - 1]
  }
}

function onClose() {
  emit('update:modelValue', false)
}

function handCancel() {
  emit('update:modelValue', false)
}

function handleSure() {
  ruleFormRef.value?.validate(async (valid: any) => {
    if (valid)
      props.type === 'edit' ? updateData() : addData()
  })
}

// const menuList = computed(() => {
//   const list = filterMenu([...props.menuList])
//   return [{ id: 0, name: '根菜单' }, ...(list || [])]
// })

const options = ref([
  { label: '隐藏', value: true },
  { label: '显示', value: false },
])

const resourceList = ref()
const { fetchData: fetchDataList, data: dataList } = GetResourceTreeListApi()
async function getData() {
  await fetchDataList({ show_children: true })
  resourceList.value = dataList.value.list
  disabledResource(resourceList.value)
}

function disabledResource(list: any) {
  list?.map((item: any) => {
    if (item.type === 1 || item.type === 3)
      item.disabled = true
    if (item?.sub_resource_tree && item.sub_resource_tree.length > 0)
      disabledResource(item.sub_resource_tree)
  })
}

const cascaderPropsResource = {
  value: 'id',
  label: 'resource_name',
  checkStrictly: true,
  children: 'sub_resource_tree',
  // lazy: true,
  // lazyLoad(node, resolve) {
  //   const { data } = node
  //   const sub_resource_tree: any = data?.sub_resource_tree || []
  //   const children: any = sub_resource_tree?.length > 0 ? data?.sub_resource_tree : null
  //   if (children && children[0].type !== 3) {
  //     resolve(children)
  //   } else {
  //     resolve()
  //   }
  // },
}

let filterMenuStatus = false
function filterMenu(list: any[]) {
  list?.map((item) => {
    if (item.id === props.row.id || filterMenuStatus === true) {
      item.disabled = true
      filterMenuStatus = true
    }
    if (item.sub_menu && item.sub_menu.length > 0)
      filterMenu(item.sub_menu)
    else
      filterMenuStatus = false
  })
  return list
}
</script>

<template>
  <vxe-modal v-model="showModal" :title="props.title" show-footer width="700" height="420" :mask="false" :lock-view="false" :esc-closable="true" resize @close="onClose">
    <el-form ref="ruleFormRef" :model="state.form" label-width="140px" label-position="left" :rules="state.fromRules">
      <el-form-item label="父级菜单" prop="parent_id">
        <el-cascader v-model="state.form.parent_id" :props="cascaderProps" :options="menuList" :show-all-levels="false" />
      </el-form-item>
      <el-form-item label="菜单名称" prop="name">
        <el-input v-model="state.form.name" clearable placeholder="请输入菜单名称" />
      </el-form-item>
      <el-form-item label="菜单图标" prop="avatar_url">
        <Icon v-model="state.form.avatar_url" :default="props.row.avatar_url" style="width: 200px" />
      </el-form-item>
      <el-form-item label="资源名称" prop="resource_id">
        <el-cascader v-model="state.form.resource_id" clearable style="width: 300px" :props="cascaderPropsResource" :options="resourceList">
          <template #default="{ data }">
            <span>{{ `${data.resource_name}(${ResourceType[data.type]})` }}</span>
          </template>
        </el-cascader>
      </el-form-item>
      <el-form-item label="序号" prop="sort">
        <el-input v-model.number="state.form.sort" clearable placeholder="请输入序号" />
      </el-form-item>
      <el-form-item label="是否隐藏" prop="is_hide">
        <el-select v-model="state.form.is_hide" placeholder="Select" clearable>
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>
