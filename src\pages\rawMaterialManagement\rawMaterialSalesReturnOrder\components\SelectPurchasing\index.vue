<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import Big from 'big.js'
import { SaleOrderItemList } from '@/api/rawMaterIalSaleReturnOrder'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatDate, formatWeightDiv } from '@/common/format'
import { debounce, getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import SelectBusinessDialog from '@/components/SelectBusinessDialog/index.vue'

export interface Props {
  modelValue: boolean
  customer_id: number | string
  sale_system_id: number | string
  customer_name: string | undefined
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  customer_id: '',
  sale_system_id: '',
  customer_name: '',
})
const emit = defineEmits(['update:modelValue', 'submit'])
const state = reactive({
  filterData: {
    order_no: '',
    customer_id: '',
    sale_date: '',
    sale_start_date: '',
    sale_end_date: '',
    sale_unit_id: '',
  },
  tableList: [],
})
const tablesRef = ref()
const showModal = ref<boolean>(false)
const { fetchData: fetchDataList, data: dataList, total, page, size, loading, handleSizeChange, handleCurrentChange } = SaleOrderItemList()
async function getData() {
  if (!showModal.value)
    return
  await fetchDataList(getFilterData({ ...state.filterData, sale_system_id: props.sale_system_id, rtn_status: '1,2' }, ['sale_date']))
}

watch(
  () => [props.customer_id, props.modelValue],
  () => {
    if (!props.modelValue) {
      showModal.value = false
      state.filterData = {
        order_no: '',
        customer_id: '',
        sale_date: '',
        sale_start_date: '',
        sale_end_date: '',
        sale_unit_id: '',
      }
    }
    else {
      showModal.value = true
      state.filterData.customer_id = props.customer_id as string
      getData()
    }
  },
)

watch(
  () => state.filterData.sale_date,
  (value: any) => {
    state.filterData.sale_start_date = formatDate(value?.[0]) || ''
    state.filterData.sale_end_date = formatDate(value?.[1]) || ''
  },
)

const columnList = ref([
  {
    title: '销售单号',
    field: 'order_no',
    width: '8%',
    fixed: 'left',
  },
  {
    title: '销售日期',
    field: 'sale_date',
    is_date: true,
    width: 100,
  },
  {
    title: '客户名称',
    field: 'customer_name',
    width: 100,
  },
  {
    title: '出货单位名称',
    field: 'sale_unit_name',
    width: 100,
  },
  {
    title: '销售数量',
    field: 'total_weight',
    isWeight: true,
    width: 100,
  },
  // {
  //   title: '可退整件数',
  //   field: 'whole_piece_unrtn',
  //   width: 130,
  // },
  // {
  //   title: '未退整件数',
  //   field: 'whole_piece_count_unrtn',
  //   isWeight: true,
  //   width: 130,
  // },
  // {
  //   title: '可退散件数',
  //   field: 'bulk_piece_count_unrtn',
  //   width: 130,
  // },
  // {
  //   title: '未退散件数',
  //   field: 'bulk_piece_count_unrtn',
  //   isWeight: true,
  //   width: 130,
  // },
  {
    title: '已退数量',
    isWeight: true,
    width: 130,
    soltName: 'count_rtn',
  },
  {
    title: '原料名称',
    field: 'raw_material_name',
    width: 100,
  },
  {
    title: '采购金额',
    field: 'total_price',
    isPrice: true,
    width: 100,
  },
])

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  { deep: true },
)

const selectList = ref<any[]>()
function submit() {
  emit('submit', selectList.value)
}

function onClose() {
  emit('update:modelValue', false)
}

function handAllSelect({ records }: any) {
  selectList.value = records
}

function handleSelectionChange({ records }: any) {
  selectList.value = records
}

const tableConfig = ref({
  showCheckBox: true,
  showPagition: true,
  loading,
  size,
  page,
  total,
  checkRowKeys: [] as number[],
  handleSizeChange,
  handleCurrentChange,
  handAllSelect,
  handleSelectionChange,
})
</script>

<template>
  <vxe-modal v-model="showModal" show-footer title="根据销售单添加" width="1000" height="550" :mask="false" :lock-view="false" :esc-closable="true" resize @close="onClose">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="销售单号:">
        <template #content>
          <el-input v-model="state.filterData.order_no" size="small" placeholder="销售单号" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="客户名称:">
        <template #content>
          <SelectCustomerDialog v-model="state.filterData.customer_id" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="出货单位名称:">
        <template #content>
          <SelectBusinessDialog
            v-model="state.filterData.sale_unit_id"
            api="GetBusinessUnitListApi"
            :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory }"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="销售日期:">
        <template #content>
          <SelectDate v-model="state.filterData.sale_date" />
        </template>
      </DescriptionsFormItem>
    </div>
    <Table ref="tablesRef" :config="tableConfig" :table-list="dataList?.list" :column-list="columnList">
      <template #creatTime />
      <template #count_rtn="{ row }">
        {{ Big(formatWeightDiv(row.bulk_piece_count_rtn)).plus(formatWeightDiv(row.whole_piece_rtn)) }}
      </template>
    </Table>
    <template #footer>
      <el-button type="primary" size="small" @click="submit">
        提交
      </el-button>
    </template>
  </vxe-modal>
</template>
