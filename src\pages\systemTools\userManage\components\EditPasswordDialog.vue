<script lang="ts" setup>
import { reactive, ref } from 'vue'

const emits = defineEmits(['handleSure'])

function checkPassword(rule: any, value: any, callback: any) {
  const reg
    = /[\u4E00-\u9FA5|\u3002|\uFF1F|\uFF01|\uFF0C|\u3001|\uFF1B|\uFF1A|\u201C|\u201D|\u2018|\u2019|\uFF08|\uFF09|\u300A|\u300B|\u3008|\u3009|\u3010|\u3011|\u300E|\u300F|\u300C|\u300D|\uFE43|\uFE44|\u3014|\u3015|\u2026|\u2014|\uFF5E|\uFE4F|\uFFE5]/
  if (!reg.test(value))
    callback()
  else
    callback(new Error('不允许夹杂中文符号'))
}

const state = reactive({
  showModal: false,
  modalName: '修改密码',
  form: {
    userPassword: '',
    id: '',
  },
  fromRules: {
    userPassword: [
      { min: 6, max: 16, trigger: 'blur', message: '密码范围为6~16位', required: true },
      { validator: checkPassword, trigger: 'blur', required: true },
    ],
  },
})
const ruleFormRef = ref()

function handCancel() {
  state.showModal = false
}

async function handleSure() {
  if (!ruleFormRef.value)
    return
  await ruleFormRef.value.validate((valid: any) => {
    if (valid)
      emits('handleSure', state.form)
  })
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="500" height="200" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <el-form ref="ruleFormRef" :model="state.form" label-width="100px" label-position="top" :rules="state.fromRules">
      <el-form-item label="用户密码" prop="userPassword">
        <el-input v-model="state.form.userPassword" placeholder="请输入" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
