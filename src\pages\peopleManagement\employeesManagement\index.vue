<script setup lang="ts" name="EmployeesManagement">
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, ref, watch } from 'vue'
import type { TableColumn } from '@/components/Table/type'
import { DeleteEmployeeApi, DisableEmployeeA<PERSON>, GetEmployeeListApi, GetEmployeeListApiExport } from '@/api/employees'
import { DictionaryType } from '@/common/enum'
import { formatTime } from '@/common/format'
import { debounce, deleteToastWithRiskWarning, disabledConfirmBox, getFilterData, resetData } from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DepartmentTree from '@/components/DepartmentTree/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import StatusTag from '@/components/StatusTag/index.vue'
import Table from '@/components/Table.vue'
import router from '@/router'
import Accordion from '@/components/Accordion/index.vue'

const columnList = ref<TableColumn[]>([
  {
    sortable: true,
    field: 'code',
    title: '编号',
    width: 100,
  },
  {
    sortable: true,
    field: 'name',
    title: '员工名称',
    width: 100,
    // fixed: true,
  },
  {
    sortable: true,
    field: 'department_name',
    title: '员工部门',
    width: 120,
  },
  {
    sortable: true,
    field: 'sale_system_name_list',
    title: '所属营销体系',
    soltName: 'sale_system_name_list',
  },
  {
    sortable: true,
    field: 'qywx_user_name',
    title: '企微员工',
  },
  {
    sortable: true,
    field: 'duty_str',
    title: '员工职责',
    soltName: 'duty_str',
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    width: 100,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '修改时间',
    soltName: 'update_time',
    width: 140,
  },
  {
    sortable: true,
    field: 'status_name',
    title: '状态',
    align: 'center',
    // fixed: true,
    soltName: 'status_name',
    width: '5%',
  },
])

onMounted(() => {
  getList()
})

const searchData = ref({
  name: '',
  code: '',
  department_id: [],
  status: '',
  sale_system_id: '',
  duty: '',
})

// 获取列表
const { fetchData: fetchDataList, data: listData, total, page, size, handleSizeChange, handleCurrentChange } = GetEmployeeListApi()
async function getList() {
  await fetchDataList(getQuery())
}

// 获取请求参数
function getQuery() {
  return getFilterData({ ...searchData.value, department_id: searchData.value.department_id.join(',') })
}

// 查看
function handleDetail(row: any) {
  router.push({ name: 'EmployeesManagementDetail', params: { id: row.id } })
}

// 新建
function handleAdd() {
  router.push({ name: 'EmployeesManagementAdd' })
}

// 编辑
function handleEdit(row: any) {
  router.push({ name: 'EmployeesManagementEdit', params: { id: row.id } })
}

const selectRow = ref<any[]>([])
// 删除
const { fetchData: fetchDataDel, success: successDel, msg: msgDel } = DeleteEmployeeApi()
async function handDelete(row: any, status = 'one') {
  const res = await deleteToastWithRiskWarning(row.name)
  if (res) {
    const ids = status === 'one' ? [row.id] : row.map((item: any) => item.id)

    await fetchDataDel({ id: ids.join(',') })
    if (successDel.value) {
      ElMessage({
        type: 'success',
        message: `删除成功`,
      })
      getList()
      selectRow.value = []
    }
    else {
      ElMessage({
        type: 'error',
        message: msgDel.value,
      })
    }
  }
}

function hadleRecycle() {
  router.push({
    name: 'ContactUnitRecycle',
  })
}

async function updateStatus(row: any) {
  disabledConfirmBox({ row, api: DisableEmployeeApi }).then(() => {
    getList()
  })
}

function onBlukStatus(status: 1 | 2 = 1) {
  if (selectRow.value.length <= 0)
    return ElMessage.error('请先勾选数据')
  disabledConfirmBox({ row: selectRow.value, api: DisableEmployeeApi, allStatus: status }).then(() => {
    getList()
    selectRow.value = []
  })
}

watch(
  () => searchData.value,
  debounce(() => getList(), 300),
  {
    deep: true,
  },
)

function handAllSelect({ records }: any) {
  selectRow.value = records
}

function handleSelectionChange({ records }: any) {
  selectRow.value = records
}

function onDelBulk() {
  if (selectRow.value.length <= 0)
    return ElMessage.error('请先勾选数据')
  handDelete(selectRow.value, 'all')
}

function handleResetFormData() {
  searchData.value = resetData(searchData.value)
}

// const onStopBulk = () => {
//   if (selectRow.value.length <= 0) return ElMessage.error('请先勾选数据')
//   updateStatus(selectRow.value, 'all')
// }

// 导出
const loadingExcel = ref(false)
async function handleExport() {
  if (!listData?.value.list || listData?.value.list.length <= 0)
    return ElMessage.warning('当前无数据可导出')
  const name_str = '员工管理'
  const { fetchData: getFetch, success: getSuccess, msg: getMsg } = GetEmployeeListApiExport({ nameFile: name_str })
  loadingExcel.value = true
  await getFetch({
    ...getFilterData(getQuery()),
    download: 1,
  })
  if (getSuccess.value) {
    ElMessage({
      type: 'success',
      message: '成功',
    })
  }
  else {
    ElMessage({
      type: 'error',
      message: getMsg.value,
    })
  }
  loadingExcel.value = false
}

const tableConfig = ref({
  fieldApiKey: 'EmployeesManagement',
  loading: false,
  showPagition: true,
  page,
  size,
  total,
  showCheckBox: true,
  height: '100%',
  showOperate: true,
  operateWidth: '10%',
  showSort: false,
  handleSizeChange,
  handleCurrentChange,
  handAllSelect,
  handleSelectionChange,
})
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="编号:">
          <template #content>
            <el-input v-model="searchData.code" placeholder="请输入编号" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="员工名称:">
          <template #content>
            <el-input v-model="searchData.name" placeholder="请输入员工名称" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="所属营销体系:">
          <template #content>
            <SelectComponents v-model="searchData.sale_system_id" api="GetSaleSystemDropdownListApi" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="员工职责:">
          <template #content>
            <SelectComponents v-model="searchData.duty" api="GetEmployeeDutyListApi" :query="{ dictionary_id: DictionaryType.duty }" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="状态:">
          <template #content>
            <SelectComponents v-model="searchData.status" api="StatusListApi" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handleResetFormData" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <div class="flex flex-1 overflow-y-hidden">
      <Accordion :open-status="true" class="!mr-[5px]">
        <DepartmentTree v-model="searchData.department_id" type="checkbox" check-strictly highlight-current default-expand-all />
      </Accordion>

      <FildCard title="" class="table-card-full" @hadle-recycle="hadleRecycle">
        <template #right-top>
          <el-button v-has="'EmployeesManagement_add'" style="margin-right: 10px" type="primary" :icon="Plus" @click="handleAdd">
            新建
          </el-button>
          <el-dropdown>
            <span class="el-dropdown-link">
              <el-button>批量操作</el-button>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="onDelBulk">
                  批量删除
                </el-dropdown-item>
                <el-dropdown-item @click="onBlukStatus(1)">
                  批量启用
                </el-dropdown-item>
                <el-dropdown-item @click="onBlukStatus(2)">
                  批量禁用
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <BottonExcel v-has="'EmployeesManagement_export'" :loading="loadingExcel" title="导出文件" @on-click-excel="handleExport" />
        </template>
        <Table :config="tableConfig" :table-list="listData?.list" :column-list="columnList">
          <template #status_name="{ row }">
            <StatusTag :status="row.status" :name="row.status_name" />
          </template>
          <template #sale_system_name_list="{ row }">
            <span v-for="(item, index) in row.sale_system_name_list" :key="index">{{ item }}</span>
          </template>
          <template #duty_str="{ row }">
            <span v-for="(item, index) in row.duty_str" :key="index" class="mr-2">{{ item }}</span>
          </template>
          <template #update_time="{ row }">
            {{ formatTime(row.update_time) }}
          </template>
          <template #operate="{ row }">
            <el-space :size="10">
              <el-link v-has="'EmployeesManagement_detail'" :underline="false" type="primary" @click="handleDetail(row)">
                查看
              </el-link>
              <el-link v-has="'EmployeesManagement_edit'" :underline="false" type="primary" @click="handleEdit(row)">
                编辑
              </el-link>
              <el-link v-has="'EmployeesManagement_del'" :underline="false" type="primary" @click.stop="handDelete(row)">
                删除
              </el-link>
              <el-link v-has="'EmployeesManagement_status'" :underline="false" type="primary" @click="updateStatus(row)">
                {{ row.status === 1 ? '禁用' : '启用' }}
              </el-link>
            </el-space>
          </template>
        </Table>
      </FildCard>
    </div>
  </div>
</template>

<style lang="scss" scoped>
::v-deep(.el-form-item) {
  margin-bottom: 0;
}

::v-deep(.el-descriptions) {
  margin-bottom: 10px;
}
</style>
