import blankFabricManagement from './blankFabricManagement'
import codeLabels from './codeLabels'
import dyeingNoticeDetail from './dyeingNoticeDetail'
import finishedProductProcurement from './finishedProductProcurement'
import greyFabricinformation from './greyFabricinformation'
import productionColor from './productionColor'
import productionXiMa from './productionXiMa'
import productionOrder from './productionOrder'
import rawInfomation from './rawInfomation'
import rawMaterialSourcing from './rawMaterialSourcing'
import saleDeliver from './saleDeliver'
import saleDeliverGrey from './saleDeliverGrey'
import saleDeliverRaw from './saleDeliverRaw'
import salePlanOrder from './salePlanOrder'
import transferSales from './transferSales'
import productionXimaLabelCode from './productionXimaLabelCode'
import wareHouseSpace from './wareHouseSpace'
import wareHouse4Space from './wareHouse4Space'
import finishedProductDeliveryNote from './finishedProductDeliveryNote'
import returnSales from './returnSales'
import rawMaterialDyeingAndFinishingNotificationForm
  from './rawMaterialDyeingAndFinishingNotificationForm'
import qualityAssuranceManagement from './qualityAssuranceManagement'
import qualityAssuranceZSManagement from './qualityAssuranceZSManagement'
import RawMaterialInformationA from './RawMaterialInformationA'
import greyFabricInventoryLabel from './greyFabricInventoryLabel'
import finishedProductDeliveryNote2 from '@/components/PrintBtn/formatData/finishedProductDeliveryNote2'
import productNotice from '@/components/PrintBtn/formatData/productNotice'
import { PrintType } from '@/components/PrintPopoverBtn/types'
import finishedProductDeliveryNote3 from '@/components/PrintBtn/formatData/finishedProductDeliveryNote3'
import customerReconciliationStatement1 from '@/components/PrintBtn/formatData/CustomerReconciliationStatement1'
import SupplierReconciliationStatement1 from '@/components/PrintBtn/formatData/SupplierReconciliationStatement1'
import finishedProductDeliveryNote4 from '@/components/PrintBtn/formatData/finishedProductDeliveryNote4'

const dataHandler = {
  codeLabels, // 成品标签-成品库存打印
  productionOrder, // 织加工合同-生产通知单详情打印
  productionColor, // 样板标签-成品颜色标签
  productionXiMa, // 样板标签-成品颜色标签
  productionXimaLabelCode, // 成品布细码标签（二维码条形码）
  saleDeliver, // 销售单-销售送货单详情
  saleDeliverGrey, // 坯布出货单
  saleDeliverRaw, // 销售出货单
  salePlanOrder, // 产品合同 - 成品销售计划单详情
  rawInfomation, // 原料资料标签打印
  greyFabricinformation, // 坯布资料标签打印
  rawMaterialSourcing,
  blankFabricManagement,
  finishedProductProcurement,
  dyeingNoticeDetail,
  transferSales,
  wareHouseSpace,
  wareHouse4Space,
  finishedProductDeliveryNote, // 成品送货单
  returnSales, // 退货销售单
  finishedProductDeliveryNote2, // 成品送货单模板2
  productNotice,
  finishedProductDeliveryNote3, // 成品送货单模板3
  finishedProductDeliveryNote4, // 成品送货单模板4
  rawMaterialDyeingAndFinishingNotificationForm, // 原料染整通知单
  qualityAssuranceManagement, // 质检管理打印
  qualityAssuranceZSManagement, // 质检管理打印--致盛-2024/08/23
  RawMaterialInformationA, // 原料资料标签打印
  greyFabricInventoryLabel, // 坯布库存标签打印
  customerReconciliationStatement1, // 客户对账表
  SupplierReconciliationStatement1, // 供方对账表
}

const templateType: any = {
  [PrintType.PrintTemplateTypeStock]: [
    {
      id: 1,
      name: '库存--模板1',
      code: 'codeLabels',
    },
    {
      id: 2,
      name: '库存--模板2',
      code: 'productionXiMa',
    },
    {
      id: 25,
      name: '库存--模板3',
      code: 'productionXimaLabelCode',
    },
    {
      id: 26,
      name: '库存--模板4',
      code: 'productionColor',
    },
    {
      id: 50,
      name: '坯布库存标签打印',
      code: 'greyFabricInventoryLabel',
    },
  ], // 成品标签-成品库存打印
  [PrintType.PrintTemplateTypeSaleOrder]: [
    {
      id: 4,
      name: '成品销售单-模板1',
      code: 'salePlanOrder',
    },
    {
      id: 3,
      name: '原料销售单-模板1',
      code: 'saleDeliverRaw',
    },
  ],
  [PrintType.PrintTemplateTypeColorLabel]: [
    {
      id: 5,
      name: '成品资料标签打印-模板1',
      code: 'greyFabricinformation',
    },
    {
      id: 6,
      name: '成品资料标签打印-模板2',
      code: 'rawInfomation',
    },
    {
      id: 7,
      name: '成品资料标签打印-模板3',
      code: 'productionXimaLabelCode',
    },
    {
      id: 8,
      name: '成品资料标签打印-模板4',
      code: 'productionColor',
    },
    {
      id: 21,
      name: '成品资料标签打印-模板5',
      code: 'productionXiMa',
    },
    {
      id: 28,
      name: '原料资料标签打印-模板1',
      code: 'RawMaterialInformationA',
    },
  ],
  [PrintType.PrintTemplateTypeLabel]: [
    {
      id: 5,
      name: '成品资料标签打印-模板1',
      code: 'greyFabricinformation',
    },
    {
      id: 6,
      name: '成品资料标签打印-模板2',
      code: 'rawInfomation',
    },
    {
      id: 7,
      name: '成品资料标签打印-模板3',
      code: 'productionXimaLabelCode',
    },
    {
      id: 8,
      name: '成品资料标签打印-模板4',
      code: 'productionColor',
    },
    {
      id: 21,
      name: '成品资料标签打印-模板5',
      code: 'productionXiMa',
    },
  ],
  [PrintType.PrintTemplateTypePurOrder]: [
    {
      id: 27,
      name: '成品采购单-模板1',
      code: 'finishedProductProcurement',
    },
    {
      id: 9,
      name: '原料采购单-模板1',
      code: 'rawMaterialSourcing',
    },
    {
      id: 10,
      name: '坯布采购单-模板1',
      code: 'blankFabricManagement',
    },
  ],
  [PrintType.PrintTemplateTypeProduceNotify]: [
    {
      id: 12,
      name: '成品采购单-模板2',
      code: 'saleDeliverGrey',
    },
    {
      id: 20,
      name: '生产通知单-模板1',
      code: 'productNotice',
    },
  ],
  [PrintType.PrintTemplateTypeDNFNotify]: [
    {
      id: 13,
      name: '染整通知单-模板1',
      code: 'dyeingNoticeDetail',
    },
    {
      id: 22,
      name: '染整通知单-模板2',
      code: 'productNotice',
    },
    {
      id: 24,
      name: '原料染整通知单-模板一',
      code: 'rawMaterialDyeingAndFinishingNotificationForm',
    },
  ],
  [PrintType.PrintTemplateTypeSaleTransferOrder]: [
    {
      id: 14,
      name: '调货销售单-模板1',
      code: 'transferSales',
    },
  ],
  [PrintType.PrintTemplateTypeWarehouseBin]: [
    {
      id: 15,
      name: '仓位打印-模板1',
      code: 'wareHouseSpace',
    },
    {
      id: 16,
      name: '仓位打印-模板2',
      code: 'wareHouse4Space',
    },
  ],
  [PrintType.PrintTemplateTypeSaleTransferOrderRtn]: [
    {
      id: 17,
      name: '调货退货单-模板1',
      code: 'returnSales',
    },
  ],
  [PrintType.PrintTemplateTypeShouldCollectOrder]: [
    {
      id: 18,
      name: '销售送货单-模板1',
      code: 'finishedProductDeliveryNote',
    },
    {
      id: 19,
      name: '销售送货单-模板2',
      code: 'finishedProductDeliveryNote2',
    },
    {
      id: 23,
      name: '销售送货单-模板3',
      code: 'finishedProductDeliveryNote3',
    },
    {
      id: 30,
      name: '销售送货单-模板4',
      code: 'finishedProductDeliveryNote4',
    },
  ],
  [PrintType.PrintTemplateQuarantine]: [
    {
      id: 24,
      name: '质检管理打印-模板1',
      code: 'qualityAssuranceManagement',
    },
    {
      id: 29,
      name: '质检管理打印-模板2',
      code: 'qualityAssuranceZSManagement',
    },
  ],
  [PrintType.PrintTemplateTypePurchaserReconciliation]: [
    {
      id: 31,
      name: '客户对账表-模板1',
      code: 'customerReconciliationStatement1',
    },
  ],
  [PrintType.PrintTemplateTypeSupplierReconciliation]: [
    {
      id: 32,
      name: '供方对账表-模板1',
      code: 'SupplierReconciliationStatement1',
    },
  ],
}

export type templateTypeParam = keyof typeof dataHandler

export default ({
  type,
  data = {},
}: {
  type: templateTypeParam
  data: any
}) => {
  return dataHandler[type](JSON.parse(JSON.stringify(data)))
}

export function getDataHandler(type: PrintType) {
  return templateType[type] || null
}
