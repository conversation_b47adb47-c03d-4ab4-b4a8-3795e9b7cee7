declare namespace Api {
  namespace QuickScheduleProduction {
    /**
     * 列表请求参数
     */
    export interface ListRequest {
      /** 生产通知单号 */
      production_notice_no?: string
      /** 排产单号 */
      schedule_no?: string
      /** 坯布名称 */
      grey_fabric_name?: string
      /** 机台ID */
      machine_id?: number
      /** 织工ID */
      weaver_id?: number
      /** 状态 */
      status?: number[]
      /** 开始时间 */
      start_date?: string
      /** 结束时间 */
      end_date?: string
      /** 创建时间开始 */
      create_start_date?: string
      /** 创建时间结束 */
      create_end_date?: string
    }

    /**
     * 列表响应数据
     */
    export interface ListResponse {
      /** ID */
      id: number
      /** 排产单号 */
      schedule_no: string
      /** 生产通知单号 */
      production_notice_no: string
      /** 生产通知单ID */
      production_notice_id: number
      /** 坯布名称 */
      grey_fabric_name: string
      /** 坯布编码 */
      grey_fabric_code: string
      /** 机台ID */
      machine_id?: number
      /** 机台名称 */
      machine_name?: string
      /** 织工ID */
      weaver_id?: number
      /** 织工名称 */
      weaver_name?: string
      /** 计划开始时间 */
      plan_start_time?: string
      /** 计划结束时间 */
      plan_end_time?: string
      /** 实际开始时间 */
      actual_start_time?: string
      /** 实际结束时间 */
      actual_end_time?: string
      /** 计划产量 */
      plan_quantity: number
      /** 实际产量 */
      actual_quantity?: number
      /** 状态 1:待排产 2:已排产 3:生产中 4:已完成 5:已取消 */
      status: number
      /** 状态名称 */
      status_name: string
      /** 优先级 1:低 2:中 3:高 4:紧急 */
      priority: number
      /** 优先级名称 */
      priority_name: string
      /** 备注 */
      remark?: string
      /** 创建时间 */
      create_time: string
      /** 更新时间 */
      update_time: string
      /** 创建人 */
      create_user_name: string
    }

    /**
     * 详情请求参数
     */
    export interface DetailRequest {
      /** 快速排产单ID */
      id: number
    }

    /**
     * 详情响应数据
     */
    export interface DetailResponse {
      /** ID */
      id: number
      /** 排产单号 */
      schedule_no: string
      /** 生产通知单信息 */
      production_notice: {
        id: number
        order_no: string
        grey_fabric_name: string
        grey_fabric_code: string
        customer_name: string
        plan_quantity: number
        unit_name: string
      }
      /** 机台信息 */
      machine?: {
        id: number
        name: string
        code: string
        type_name: string
      }
      /** 织工信息 */
      weaver?: {
        id: number
        name: string
        code: string
        skill_level: string
      }
      /** 计划开始时间 */
      plan_start_time?: string
      /** 计划结束时间 */
      plan_end_time?: string
      /** 实际开始时间 */
      actual_start_time?: string
      /** 实际结束时间 */
      actual_end_time?: string
      /** 计划产量 */
      plan_quantity: number
      /** 实际产量 */
      actual_quantity?: number
      /** 状态 */
      status: number
      /** 优先级 */
      priority: number
      /** 备注 */
      remark?: string
      /** 创建时间 */
      create_time: string
      /** 更新时间 */
      update_time: string
    }

    /**
     * 新增请求参数
     */
    export interface AddRequest {
      /** 生产通知单ID */
      production_notice_id: number
      /** 机台ID */
      machine_id?: number
      /** 织工ID */
      weaver_id?: number
      /** 计划开始时间 */
      plan_start_time?: string
      /** 计划结束时间 */
      plan_end_time?: string
      /** 计划产量 */
      plan_quantity: number
      /** 优先级 */
      priority: number
      /** 备注 */
      remark?: string
    }

    /**
     * 编辑请求参数
     */
    export interface EditRequest {
      /** ID */
      id: number
      /** 机台ID */
      machine_id?: number
      /** 织工ID */
      weaver_id?: number
      /** 计划开始时间 */
      plan_start_time?: string
      /** 计划结束时间 */
      plan_end_time?: string
      /** 计划产量 */
      plan_quantity: number
      /** 优先级 */
      priority: number
      /** 备注 */
      remark?: string
    }

    /**
     * 删除请求参数
     */
    export interface DeleteRequest {
      /** ID */
      id: number
    }

    /**
     * 批量删除请求参数
     */
    export interface BatchDeleteRequest {
      /** ID列表 */
      ids: number[]
    }

    /**
     * 审核请求参数
     */
    export interface AuditRequest {
      /** ID */
      id: number
    }

    /**
     * 取消审核请求参数
     */
    export interface CancelAuditRequest {
      /** ID */
      id: number
    }

    /**
     * 生产通知单选项
     */
    export interface ProductionNoticeOption {
      /** ID */
      id: number
      /** 生产通知单号 */
      order_no: string
      /** 坯布名称 */
      grey_fabric_name: string
      /** 客户名称 */
      customer_name: string
      /** 计划产量 */
      plan_quantity: number
      /** 单位名称 */
      unit_name: string
    }

    /**
     * 机台选项
     */
    export interface MachineOption {
      /** ID */
      id: number
      /** 机台名称 */
      name: string
      /** 机台编码 */
      code: string
      /** 机台类型 */
      type_name: string
      /** 状态 */
      status: number
    }

    /**
     * 织工选项
     */
    export interface WeaverOption {
      /** ID */
      id: number
      /** 织工姓名 */
      name: string
      /** 织工编码 */
      code: string
      /** 技能等级 */
      skill_level: string
      /** 状态 */
      status: number
    }

    /**
     * 批量分配机台请求参数
     */
    export interface BatchAssignMachineRequest {
      /** ID列表 */
      ids: number[]
      /** 机台ID */
      machine_id: number
    }

    /**
     * 批量分配织工请求参数
     */
    export interface BatchAssignWeaverRequest {
      /** ID列表 */
      ids: number[]
      /** 织工ID */
      weaver_id: number
    }

    /**
     * 批量设置开始时间请求参数
     */
    export interface BatchSetStartTimeRequest {
      /** ID列表 */
      ids: number[]
      /** 开始时间 */
      start_time: string
    }

    /**
     * 统计请求参数
     */
    export interface StatisticsRequest {
      /** 开始日期 */
      start_date?: string
      /** 结束日期 */
      end_date?: string
    }

    /**
     * 统计响应数据
     */
    export interface StatisticsResponse {
      /** 总排产数 */
      total_count: number
      /** 待排产数 */
      pending_count: number
      /** 生产中数量 */
      producing_count: number
      /** 已完成数量 */
      completed_count: number
      /** 已取消数量 */
      cancelled_count: number
      /** 平均完成时间(小时) */
      avg_completion_time: number
      /** 机台利用率 */
      machine_utilization: number
    }
  }
}
