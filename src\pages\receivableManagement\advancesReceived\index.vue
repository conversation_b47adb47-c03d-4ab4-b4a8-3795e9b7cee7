<script setup lang="ts" name="AdvancesReceived">
import { onActivated, onMounted, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { Plus } from '@element-plus/icons-vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import { debounce, deleteToast, getFilterData } from '@/common/util'
import { formatDate, formatTwoDecimalsDiv } from '@/common/format'
import { getAdvanceCollectOrderList, updateAdvanceCollectOrderStatusPass, updateAdvanceCollectOrderStatusWait } from '@/api/advancesReceived'
import FildCard from '@/components/FildCard.vue'

// import { BusinessUnitIdEnum } from '@/common/enum'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectSettleAccountDialog from '@/components/SelectSettleAccountDialog/index.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'

const router = useRouter()

const { fetchData, data, total, page, size, loading, handleSizeChange, handleCurrentChange } = getAdvanceCollectOrderList()
const mainOptions = reactive<any>({
  multipleSelection: [],
  tableConfig: {
    showSlotNums: true,
    loading,
    showPagition: true,
    page,
    size,
    total,
    showCheckBox: true,
    showOperate: true,
    operateWidth: '8%',
    height: '100%',
    showSort: false,
    handleSizeChange,
    handleCurrentChange,
    handAllSelect: (val: any) => mainOptions.handAllSelect(val),
    handleSelectionChange: (val: any) => mainOptions.handleSelectionChange(val),
  },
  mainList: [],
  columnList: [
    {
      sortable: true,
      field: 'order_no',
      title: '单据编号',
      fixed: 'left',
      width: '8%',
      soltName: 'order_no',
    },
    {
      sortable: true,
      field: 'customer_name',
      title: '客户名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'sale_system_name',
      title: '营销体系名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'operator_name',
      title: '经手员',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_advance_price',
      title: '预收金额',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_used_price',
      title: '已核销金额',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_remain_price',
      title: '未核销金额',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'settle_type_name',
      title: '收款账户',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'receive_collect_date',
      title: '收款日期',
      minWidth: 100,
      is_date: true,
    },
    {
      sortable: true,
      field: 'remark',
      title: '备注',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'creator_name',
      title: '创建人',
      width: 100,
    },
    {
      sortable: true,
      field: 'create_time',
      title: '创建时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'update_user_name',
      title: '修改人',
      width: 100,
    },
    {
      sortable: true,
      field: 'update_time',
      title: '修改时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'auditor_name',
      title: '审核人',
      width: 100,
    },
    {
      sortable: true,
      field: 'audit_time',
      title: '审核时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'audit_status',
      title: '单据状态',
      showOrder_status: true,
      soltName: 'audit_status',
      fixed: 'right',
      width: '5%',
    },
  ],
  // 表格选中事件
  handAllSelect: ({ records }: any) => {
    mainOptions.multipleSelection = records
  },
  handleSelectionChange: ({ records }: any) => {
    mainOptions.multipleSelection = records
  },
})
// 审核
const { fetchData: auditFetch, success: auditSuccess, msg: auditMsg } = updateAdvanceCollectOrderStatusPass()
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id.toString() })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
const { fetchData: cancelFetch, success: cancelSuccess, msg: cancelMsg } = updateAdvanceCollectOrderStatusWait()
async function handUnApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
// 详情
function handDetail(row: any) {
  router.push({
    name: 'AdvancesReceivedDetail',
    query: {
      id: row.id,
    },
  })
}
// 编辑
function handEdit(row: any) {
  router.push({
    name: 'AdvancesReceivedEdit',
    query: {
      id: row.id,
    },
  })
}
// 新建
function handAdd() {
  router.push({
    name: 'AdvancesReceivedAdd',
  })
}
// 侦听参数获取数据
const filterData = reactive<any>({
  order_no: '', // 单据编号
  customer_id: '', // 客户id
  settle_type_id: '', // 收款账户id
  receive_collect_date: null, // 收款日期
  audit_status: [], // 单据状态
})
// 获取列表数据
async function getData() {
  const query = {
    order_no: filterData.order_no || '',
    customer_id: filterData.customer_id || '',
    settle_type_id: filterData.settle_type_id || '',
    begin_receive_collect_date: filterData.receive_collect_date ? formatDate(filterData.receive_collect_date[0]) : '',
    end_receive_collect_date: filterData.receive_collect_date ? formatDate(filterData.receive_collect_date[1]) : '',
    audit_status: filterData.audit_status.length ? filterData.audit_status.join(',') : '',
  }
  await fetchData(getFilterData({ ...query }))
}

watch(
  () => data.value,
  () => {
    mainOptions.mainList
      = data.value?.list?.map((item: any) => {
        return {
          ...item,
          total_advance_price: formatTwoDecimalsDiv(item.total_advance_price),
          total_used_price: formatTwoDecimalsDiv(item.total_used_price),
          total_remain_price: formatTwoDecimalsDiv(item.total_remain_price),
        }
      }) || []
  },
  { deep: true },
)
onActivated(getData)
// 初始化数据
onMounted(() => {
  getData()
})
watch(
  filterData,
  debounce(() => {
    getData()
  }, 400),
)
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="" class="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <vxe-input v-model="filterData.order_no" style="width: 100%" clearable placeholder="单据编号" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:">
          <template #content>
            <SelectCustomerDialog v-model="filterData.customer_id" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收款账户:">
          <template #content>
            <SelectSettleAccountDialog
              v-model="filterData.settle_type_id"
              field="name"
            />
            <!--            <SelectComponents v-model="filterData.settle_type_id" style="width: 100%" api="GetTypeSettleAccountsEnumList" label-field="name" value-field="id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收款日期:" width="310">
          <template #content>
            <SelectDate v-model="filterData.receive_collect_date" style="width: 100%" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents v-model="filterData.audit_status" multiple api="GetAuditStatusEnum" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <!-- 表格信息 -->
    <FildCard class="table-card-full" :tool-bar="true">
      <template #right-top>
        <!-- <BottonExcel :loading="mainOptions.exportOptions.loadingExport" @onClickExcel="mainOptions.exportOptions.handleExport" title="导出文件"></BottonExcel> -->
        <el-button v-has="'AdvancesReceived_add'" style="margin-left: 10px" type="primary" :icon="Plus" @click="handAdd">
          新建
        </el-button>
      </template>
      <Table :config="mainOptions.tableConfig" :table-list="mainOptions.mainList" :column-list="mainOptions.columnList">
        <template #order_no="{ row }">
          <el-link type="primary" :underline="false" @click="handDetail(row)">
            {{ row?.order_no }}
          </el-link>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link v-has="'AdvancesReceived_detail'" type="primary" :underline="false" @click="handDetail(row)">
              查看
            </el-link>
            <el-link v-if="row.audit_status === 1 || row.audit_status === 3" v-has="'AdvancesReceived_edit'" type="primary" :underline="false" @click="handEdit(row)">
              编辑
            </el-link>
            <el-link v-if="row.audit_status === 1" v-has="'AdvancesReceived_pass'" type="primary" :underline="false" @click="handAudit(row)">
              审核
            </el-link>
            <el-link v-if="row.audit_status === 2" v-has="'AdvancesReceived_wait'" type="primary" :underline="false" @click="handUnApproved(row)">
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
  </div>
</template>

<style></style>
