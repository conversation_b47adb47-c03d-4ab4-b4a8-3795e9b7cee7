<script setup lang="ts">
import Big from 'big.js'
import { ElMessage } from 'element-plus'
import { nextTick, reactive, ref, watch } from 'vue'
import SelectRawMaterial from './SelectRawMaterial/index.vue'
import { getProductStockDetail } from '@/api/materialPlan'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatDate, formatPriceDiv, formatTwoDecimalsDiv, formatWeightDiv, sumNum, sumTotal } from '@/common/format'
import { deleteToast } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import SeeGreyStockFabric from '@/pages/saleManagement/components/SeeGreyStockFabric.vue'
import SelectStockFabric from '@/pages/saleManagement/components/SelectGreyStockFabric.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { typeDisabledStrategy } from '@/pages/saleManagement/components/shared'

const props = withDefaults(defineProps<{ isEdit: boolean }>(), {
  isEdit: true,
})

const emits = defineEmits(['handleSure'])

const componentRemoteSearch = reactive({
  name: '',
  code: '',
  customer_name: '',
  unit_name: '',
  raw_unit_name: '',
  grey_unit_name: '',
  raw_code: '',
  raw_name: '',
  color_code: '',
  color_name: '',
  weave_factory_name: '',
  dye_factory_name: '',
  shipping_unit_name: '',
})

const tableConfig = ref({
  footerMethod: (val: any) => FooterMethod(val),
  showSlotNums: true,
  showOperate: true,
  operateWidth: '200',
  fieldApiKey: 'GreyMaterialPlanEditDrog',
})

const state = reactive<any>({
  showModal: false,
  modalName: '计划分配',
  code: '',
  name: '',
  horsepower: 0,
  weight: 0,
  tableData: [],
  canEnter: 0, // 可以录入的匹数
  total_roll: 0, // 录入的全部匹数
  row_id: -1,
  isDisabled: false,
  rowIndex: -1,
  plan_roll: 0,
  plan_weight: 0,
  color_name: '',
  plan_type_name: '',
  finish_product_gram_weight: '',
  finish_product_width: '',
  need_weight: '',
  remark: '',
  rowInfo: {},
  customer_id: '',
  product_color_code: '',
  product_id: '',
  product_color_id: '',
  product_level_id: '',
  grey_fabric_id: '',
  plan_type: '', // 销售类型
})

watch(
  () => state.showModal,
  () => {
    if (state.showModal && !state.tableData.length) {
      nextTick(() => {
        state.tableData.push({
          grey_fabric_id: state.rowInfo?.grey_fabric_id,
          gray_fabric_color_id: state.rowInfo?.gray_fabric_color_id,
          product_supplier_id: '',
          roll: state.horsepower,
          shipping_unit_id: '',
          weave_factory_id: '',
          push_type: '',
          weight: state.horsepower,
          raw_material_supplier_id: '',
          delivery_time: formatDate(new Date()),
        })
      })
    }
  },
)

const columnList = ref([
  {
    field: 'push_type',
    title: '计划类型',
    minWidth: 140,
    soltName: 'push_type',
    required: true,
  },
  {
    field: 'roll',
    title: '坯布匹数',
    minWidth: 140,
    soltName: 'roll',
  },
  {
    field: 'weight',
    title: '坯布数量',
    minWidth: 140,
    soltName: 'weight',
    required: true,
  },
  {
    field: 'shipping_unit_id',
    title: '出货单位',
    minWidth: 140,
    soltName: 'shipping_unit_id',
  },
  {
    field: 'grey_fabric_supplier_id',
    title: '坯布供应商',
    minWidth: 140,
    soltName: 'grey_fabric_supplier_id',
  },
  {
    field: 'weave_factory_id',
    title: '织厂',
    minWidth: 140,
    soltName: 'weave_factory_id',
  },
  {
    field: 'dye_factory_id',
    title: '染厂',
    minWidth: 140,
    soltName: 'dye_factory_id',
  },
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 140,
    soltName: 'raw_material_code',
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 140,
  },
  {
    field: 'raw_material_color_code',
    title: '原料色号',
    minWidth: 140,
    soltName: 'raw_material_color_code',
  },
  {
    field: 'raw_material_color_name',
    title: '原料颜色',
    minWidth: 140,
    soltName: 'raw_material_color_name',
  },
  {
    field: 'unit_name',
    title: '原料单位',
    minWidth: 140,
  },
  {
    field: 'raw_material_weight',
    title: '原料数量',
    minWidth: 140,
    soltName: 'raw_material_weight',
  },
  {
    field: 'raw_material_supplier_id',
    title: '原料供应商',
    minWidth: 140,
    soltName: 'raw_material_supplier_id',
  },
  {
    field: 'dye_raw_material_factory_id',
    title: '染纱厂',
    minWidth: 140,
    soltName: 'dye_raw_material_factory_id',
  },
  {
    field: 'delivery_time',
    title: '交期',
    minWidth: 140,
    soltName: 'delivery_time',
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 140,
    soltName: 'remark',
  },
])

function handCancel() {
  state.showModal = false
}

async function handleSure() {
  const list = JSON.parse(JSON.stringify(state.tableData))
  for (let i = 0; i < list.length; i++) {
    if (!list[i].push_type)
      return ElMessage.error('计划类型必填内容')
    if (list[i].weight === '')
      return ElMessage.error('坯布数量是必填内容')
    if ((list[i].push_type === 6 || list[i].push_type === 7) && list[i].raw_material_weight === '')
      return ElMessage.error('原料数量不能为空')
    if ((list[i].push_type === 6 || list[i].push_type === 7) && !list[i].raw_material_code)
      return ElMessage.error('原料编号不能为空')
    if (list[i].push_type === 1 && (!list[i].stock_message_list || list[i].stock_message_list.length <= 0))
      return ElMessage.error('计划类型为现货出货必须选择库存')
    list[i].shipping_unit_id = list[i].shipping_unit_id || 0
    list[i].grey_fabric_supplier_id = list[i].grey_fabric_supplier_id || 0
    list[i].weave_factory_id = list[i].weave_factory_id || 0
    list[i].raw_material_supplier_id = list[i].raw_material_supplier_id || 0
    list[i].dye_raw_material_factory_id = list[i].dye_raw_material_factory_id || 0
    list[i].raw_material_weight = list[i].raw_material_weight || 0
    list[i].raw_material_color_id = list[i].raw_material_color_id || 0
  }
  emits('handleSure', { ...state, tableData: list })
}

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property)) {
        state.total_roll = sumTotal(data, 'roll')
        state.plan_roll = state.total_roll
        return `${sumNum(data, 'roll')}`
      }
      if (['weight'].includes(column.property)) {
        state.plan_weight = sumTotal(data, 'weight')
        return `${sumNum(data, 'weight')}`
      }
      return null
    }),
  ]
}

const tableRef = ref()

watch(
  () => state.tableData,
  () => {
    if (state.tableData.length > 0) {
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

function handAdd() {
  state.tableData.push({
    grey_fabric_id: '',
    gray_fabric_color_id: '',
    product_supplier_id: '',
    roll: state.horsepower - formatTwoDecimalsDiv(state.rowInfo.planed_total_roll) < 0 ? '' : state.horsepower - formatTwoDecimalsDiv(state.rowInfo.planed_total_roll),
    weight: state.weight - formatWeightDiv(state.rowInfo.planed_total_weight) < 0 ? '' : state.weight - formatWeightDiv(state.rowInfo.planed_total_weight),
    shipping_unit_id: '',
    weave_factory_id: '',
    push_type: '',
    raw_material_supplier_id: '',
    delivery_time: formatDate(new Date()),
    count: '',
    is_new: true,
  })
  state.tableData = [...state.tableData]
}

const SelectStockFabricRef = ref()

const { fetchData, data } = getProductStockDetail()

async function handSelect(row: any, rowIndex: number) {
  SelectStockFabricRef.value.state.showModal = true
  SelectStockFabricRef.value.state.rowIndex = rowIndex
  SelectStockFabricRef.value.state.stock_roll = row.roll
  SelectStockFabricRef.value.state.weight = row.weight

  if (row.count === 0)
    await fetchData({ id: row.id })

  const arr = row.count > 0 ? row?.stock_message_list : data?.value?.list
  const newArr = arr.map((item: any) => {
    // TODO:因为组件封的时候就是需要id来渲染勾选回去，所以新增个字段来代替后端返回的字段
    // TODO:stock_id也是后端需要的字段所以不能沿用这个字段去反查数据
    item.is_edit_id = !!row?.is_new
    item.edit_id = row?.is_new ? item.id : null
    // item.stock_product_id = item.id
    item.use_roll = row.count === 0 ? formatPriceDiv(item.pmc_roll) : item.use_roll
    item.use_weight = row.count === 0 ? formatWeightDiv(item.pmc_weight) : item.use_weight
    return item
  })

  state.tableData.map((item: any, index: number) => {
    if (index === rowIndex) {
      item.stock_message_list = newArr
      item.count += 1
      return item
    }
  })
  SelectStockFabricRef.value.state.multipleSelection = newArr
}

const SeeGreyStockFabricRef = ref()

async function handSeeStock(row: any) {
  await fetchData({ id: row.id })
  SeeGreyStockFabricRef.value.state.showModal = true
  SeeGreyStockFabricRef.value.state.multipleSelection = data?.value?.list
}

const SelectComponentsRef = ref()
async function changType(val: any, rowIndex: number, row: any) {
  if (row.push_type === 6 || row.push_type === 7)
    computedData(row)

  if (row.old_push_type) {
    const res = await deleteToast('确认更换计划类型？,所填信息将被清空')
    if (res) {
      row.roll = ''
      row.weight = ''
      row.shipping_unit_id = ''
      row.grey_fabric_supplier_id = ''
      row.weave_factory_id = ''
      row.raw_material_code = ''
      row.raw_material_color_name = ''
      row.raw_material_color_code = ''
      row.unit_name = ''
      row.raw_material_weight = ''
      row.raw_material_supplier_id = ''
      row.dye_raw_material_factory_id = ''
      // row.delivery_time = ''
      row.remark = ''
      row.stock_message_list = []
      SelectComponentsRef.value.SelectOptionsRef.selectRef.blur()
    }
    else {
      // 取消-重置
      row.push_type = row.old_push_type
      SelectComponentsRef.value.SelectOptionsRef.selectRef.blur()
      return
    }
  }
  row.old_push_type = row.push_type
}

function handSureStock(list: any, rowIndex: number) {
  const arr = list.map((item: any) => {
    item.pmc_roll = item.use_roll
    item.pmc_weight = item.use_weight
    return item
  })
  state.tableData.map((item: any, index: number) => {
    if (index === rowIndex) {
      item.stock_message_list = arr
      return item
    }
  })
}

function handDelete(rowIndex: number) {
  state.tableData.splice(rowIndex, 1)
}

const selectRow = ref()
const SelectRawMaterialRef = ref()
function openRaw(row: any, status: boolean) {
  if (status)
    return false
  selectRow.value = row
  SelectRawMaterialRef.value.state.showModal = true
  SelectRawMaterialRef.value.state.grey_fabric_info_id = state.rowInfo.grey_fabric_id
}

function onSubmit(row: any) {
  selectRow.value.raw_material_name = row.name
  selectRow.value.raw_material_supplier_id = row.supplier_id?.[0] || ''
  selectRow.value.raw_material_code = row.code
  selectRow.value.unit_name = row.unit_name
  selectRow.value.material_loss = row.material_loss || 0
  selectRow.value.material_ratio = row.material_ratio || 0
  selectRow.value.raw_material_id = row.id
  SelectRawMaterialRef.value.state.showModal = false
  selectRow.value.raw_material_id = row.radioValue === '1' ? row.raw_material_id : row.id
  computedData(selectRow.value)
}

const changeRow = ref<any>()
function changeData(row: any) {
  changeRow.value = row
  computedData(row)
}
// watch(
//   () => changeRow.value,
//   () => {
//     computedData(changeRow.value)
//   },
//   { deep: true }
// )
// 计算原料数量
function computedData(row: any) {
  if (row.push_type === 6 || row.push_type === 7) {
    row.raw_material_weight = Number.parseFloat(
      Big(row.weight || 0)
        .times(Big(row.material_ratio || 0).div(10000))
        .times(Big(Big(row.material_loss || 0).div(10000)).plus(1))
        .toFixed(4),
    )
  }
  else {
    row.raw_material_weight = ''
  }
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="1500" height="600" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="坯布编号:">
        <template #content>
          {{ state.code }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="坯布名称:">
        <template #content>
          {{ state.name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="坯布克重:">
        <template #content>
          {{ state?.grey_fabric_gram_weight }}
        </template>
      </DescriptionsFormItem>

      <DescriptionsFormItem label="坯布幅宽:">
        <template #content>
          {{ state?.grey_fabric_width }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="针寸数:">
        <template #content>
          {{ state?.needle_size }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="织坯颜色:">
        <template #content>
          {{ state?.gray_fabric_color_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="需求匹数:">
        <template #content>
          {{ state?.horsepower }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="单位:">
        <template #content>
          {{ state?.measurement_unit_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="需求数量:">
        <template #content>
          {{ state.weight }}
        </template>
      </DescriptionsFormItem>

      <DescriptionsFormItem label="备注:">
        <template #content>
          {{ state?.remark }}
        </template>
      </DescriptionsFormItem>
    </div>
    <FildCard title="" class="mt-[5px]">
      <template #right-top>
        <div v-if="props.isEdit" class="buttom-oper" style="margin-bottom: 20px">
          <el-button type="primary" @click="handAdd()">
            新增
          </el-button>
        </div>
      </template>
      <Table ref="tableRef" :config="tableConfig" :column-list="columnList" :table-list="state.tableData">
        <template #push_type="{ row, rowIndex }">
          <SelectComponents
            ref="SelectComponentsRef"
            v-model="row.push_type"
            :disabled="!props.isEdit"
            :query="{ plan_type: 2 }"
            api="PushType"
            label-field="name"
            value-field="id"
            clearable
            @change-value="val => changType(val, rowIndex, row)"
          />
        </template>
        <template #roll="{ row }">
          <vxe-input v-model="row.roll" :disabled="!props.isEdit" type="float" placeholder="必填" />
        </template>
        <template #grey_fabric_supplier_id="{ row }">
          <SelectDialog
            v-model="row.grey_fabric_supplier_id"
            :label-name="row.grey_fabric_supplier_name"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'grey_fabric_supplier_id')"
            :query="{ unit_type_id: BusinessUnitIdEnum.blankFabric, name: componentRemoteSearch.grey_unit_name }"
            api="BusinessUnitSupplierEnumlist"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.grey_unit_name = val)"
          />
        </template>
        <template #weight="{ row }">
          <vxe-input v-model="row.weight" :disabled="!props.isEdit" type="float" placeholder="必填" @blur="changeData(row)" />
        </template>
        <template #shipping_unit_id="{ row }">
          <SelectDialog
            v-model="row.shipping_unit_id"
            :label-name="row.shipping_unit_name"
            :disabled="!props.isEdit || row.push_type !== 1"
            :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory, name: componentRemoteSearch.shipping_unit_name }"
            api="BusinessUnitSupplierEnumlist"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.shipping_unit_name = val)"
          />
        </template>
        <template #weave_factory_id="{ row }">
          <SelectDialog
            v-model="row.weave_factory_id"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'weave_factory_id')"
            :label-name="row.weave_factory_name"
            :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory, name: componentRemoteSearch.weave_factory_name }"
            api="GetBusinessUnitListApi"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.weave_factory_name = val)"
          />
        </template>
        <template #dye_factory_id="{ row }">
          <SelectDialog
            v-model="row.dye_factory_id"
            :label-name="row.dye_factory_name"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'dye_factory_id')"
            :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory, name: row.dye_factory_name }"
            api="BusinessUnitSupplierEnumlist"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (row.dye_factory_name = val)"
          />
        </template>
        <template #raw_material_code="{ row }">
          <el-input
            v-model="row.raw_material_code"
            placeholder="请选择内容"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'raw_material_code')"
            clearable
            @click="openRaw(row, !props.isEdit || (row.push_type !== 5 && row.push_type !== 6 && row.push_type !== 7))"
          />
        </template>
        <!--        原料色号 -->
        <template #raw_material_color_code="{ row }">
          <!-- 这个颜色和色号好像什么时候都不能编辑，暂定写0，就是什么情况都是disable -->
          <!--          <vxe-input :disabled="!props.isEdit || row.push_type !== 0" v-model="row.raw_material_color_code" placeholder=""></vxe-input> -->
          <SelectDialog
            v-model="row.raw_material_color_id"
            api="GetRawMaterialColor"
            label-field="code"
            :query="{ raw_matl_id: row.raw_material_id, code: row.color_code }"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'raw_material_color_code', { plan_type: state.plan_type })"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :table-column="[
              {
                field: 'code',
                title: '原料色号',
                minWidth: 100,
              },
            ]"
            @on-input="val => (row.color_code = val)"
          />
        </template>
        <template #raw_material_color_name="{ row }">
          <!--          <vxe-input :disabled="!props.isEdit || row.push_type !== 0" v-model="row.raw_material_color_name" placeholder=""></vxe-input> -->
          <SelectDialog
            v-model="row.raw_material_color_id"
            api="GetRawMaterialColor"
            label-field="name"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'raw_material_color_name', { plan_type: state.plan_type })"
            :query="{ raw_matl_id: row.raw_material_id, code: row.color_name }"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (row.color_name = val)"
          />
        </template>
        <!--        原料数量 -->
        <template #raw_material_weight="{ row }">
          <vxe-input v-model="row.raw_material_weight" :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'raw_material_weight')" placeholder="" />
        </template>
        <template #raw_material_supplier_id="{ row }">
          <SelectDialog
            v-model="row.raw_material_supplier_id"
            :label-name="row.raw_material_supplier_name"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'raw_material_supplier_id')"
            :query="{ unit_type_id: BusinessUnitIdEnum.rawMaterial, name: componentRemoteSearch.raw_unit_name }"
            api="BusinessUnitSupplierEnumlist"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.raw_unit_name = val)"
          />
        </template>
        <template #dye_raw_material_factory_id="{ row }">
          <SelectDialog
            v-model="row.dye_raw_material_factory_id"
            :label-name="row.dye_raw_material_factory_name"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'dye_raw_material_factory_id')"
            :query="{ unit_type_id: BusinessUnitIdEnum.dyeingMill, name: componentRemoteSearch.unit_name }"
            api="BusinessUnitSupplierEnumlist"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.unit_name = val)"
          />
        </template>
        <template #delivery_time="{ row }">
          <vxe-input v-model="row.delivery_time" :disabled="!props.isEdit" type="date" placeholder="" transfer />
        </template>
        <template #remark="{ row }">
          <vxe-input v-model="row.remark" :disabled="!props.isEdit" placeholder="" />
        </template>
        <template #operate="{ row, rowIndex }">
          <el-space :size="10">
            <el-link v-if="props.isEdit && row.push_type === 1" :underline="false" type="primary" @click="handSelect(row, rowIndex)">
              选取库存
            </el-link>
            <el-link v-if="!props.isEdit && row.push_type === 1" :underline="false" type="primary" @click="handSeeStock(row)">
              查看库存
            </el-link>
            <el-link v-if="props.isEdit" :underline="false" type="danger" @click="handDelete(rowIndex)">
              删除
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <template #footer>
      <div v-if="props.isEdit" class="buttom-oper" style="margin-top: 20px">
        <el-button @click="handCancel">
          取消
        </el-button>
        <el-button type="primary" @click="handleSure">
          确认
        </el-button>
      </div>
    </template>
  </vxe-modal>
  <SelectRawMaterial ref="SelectRawMaterialRef" @submit="onSubmit" />
  <SelectStockFabric ref="SelectStockFabricRef" @handle-sure="handSureStock" />
  <SeeGreyStockFabric ref="SeeGreyStockFabricRef" />
</template>

<style></style>
