<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  getSaleProductPlanOrder,
  updateSaleProductPlanOrderAuditStatusCancel,
  updateSaleProductPlanOrderAuditStatusPass,
  updateSaleProductPlanOrderAuditStatusReject,
  updateSaleProductPlanOrderAuditStatusWait,
} from '@/api/productSalePlan'
import {
  formatDate,
  formatPriceDiv,
  sumNum,
} from '@/common/format'
import { orderStatusConfirmBox } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import { processDataOut } from '@/common/handBinary'
import ImageFileCard from '@/components/UploadFile/FileCard/index.vue'
import TextureMapWall from '@/components/TextureMapWall/index.vue'
import { OrderAuditStatusEnum } from '@/enum/orderEnum'
import { UpdateSaleProductPlanOrderVocherUrl } from '@/api/sale/saleProductPlanOrder'
import AddressCard from '@/components/AddressCard/index.vue'

const route = useRoute()

const { fetchData, data: originData } = getSaleProductPlanOrder()
const data: any = computed(() => {
  const fileList = originData.value?.texture_url ? originData.value.texture_url.split(',') : []
  return {
    ...processDataOut(originData.value),
    fileList,
  }
})
// 是否编辑凭证
const editCertificate = ref(false)

onMounted(() => {
  fetchData({ id: route.query.id })
})
// 计算属性处理地址数据
const addressDataProps = computed(() => {
  if (!data.value && !data.value.id)
    return null
  return {
    id: 0,
    location: data.value.location ? data.value.location.split(',') : [], // 省市区地址
    address: data.value.receipt_address, // 详细地址
    biz_uint_id: data.value.customer_id, // 客户id
    is_default: false, // 是否默认地址 (没有默认选择)
    logistics_company: data.value.logistics_company, // 物流公司
    logistics_area: data.value.logistics_area, // 物流区域
    contact_name: data.value.contact_name, // 联系人名称
    phone: data.value.customer_phone, // 手机号
    print_tag: data.value.print_tag, // 打印打印标签
    name: data.value.process_factory, // 加工厂名称
  }
})

const tableConfig = ref({
  fieldApiKey: 'ProductSalePlanDetail_A',
  showSlotNums: true,
  filterStatus: false,
  footerMethod: (val: any) => FooterMethod(val),
})
const tableConfig2 = ref({
  fieldApiKey: 'ProductSalePlanDetail_B',
  showSlotNums: true,
  filterStatus: false,
  footerMethod: (val: any) => FooterMethod(val),
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${(sumNum(data, 'roll') as any)}`

      if (['dye_roll'].includes(column.property))
        return `${(sumNum(data, 'dye_roll') as any)}`

      if (['weight'].includes(column.property))
        return `${(sumNum(data, 'weight') as any)}`

      // if (['unit_price'].includes(column.property)) {
      //   return `${formatUnitPriceDiv(sumNum(data, 'unit_price') as any)}`
      // }
      if (['total_price'].includes(column.property))
        return `￥${(sumNum(data, 'total_price') as any)}`

      if (['other_price'].includes(column.property))
        return `￥${(sumNum(data, 'other_price') as any)}`

      return null
    }),
  ]
}

async function updateStatus(audit_status: number) {
  const id: any = route.query.id?.toString()
  if (audit_status === 4) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' },
      api: updateSaleProductPlanOrderAuditStatusCancel,
    })
  }
  if (audit_status === 3) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' },
      api: updateSaleProductPlanOrderAuditStatusReject,
    })
  }
  if (audit_status === 2) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' },
      api: updateSaleProductPlanOrderAuditStatusPass,
    })
  }
  if (audit_status === 1) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: {
        desc: '点击消审后订单将变为待审核状态✌',
        title: '是否消审该订单？',
      },
      api: updateSaleProductPlanOrderAuditStatusWait,
    })
  }
  fetchData({ id })
}
// 变更
const router = useRouter()
function changeOrder() {
  // 有待审核的变更单
  if (data.value?.sale_product_change_order_id) {
    router.push({
      name: 'SalePlanChangeEdit',
      query: { id: data.value.sale_product_change_order_id, planType: data.value.plan_type },
    })
    return
  }

  router.push({
    name: 'SalePlanChangeAdd',
    query: { id: data.value.id, planType: data.value.plan_type },
  })
}

// 更新凭证信息
const { fetchData: updateData, success: editSuccess, msg: editMsg } = UpdateSaleProductPlanOrderVocherUrl()
async function updateImageList() {
  const query = {
    id: data.value?.id,
    texture_url: data.value.fileList ? data.value.fileList.join(',') : '',
  }
  await updateData(query)
  if (!editSuccess.value)
    ElMessage.error(editMsg.value)
}

const columnList = ref([
  {
    field: 'detail_order_no',
    title: '订单编号',
    minWidth: 150,
    fixed: 'left',
  },
  {
    field: 'product_code',
    title: '成品编号',
    minWidth: 100,
    fixed: 'left',
  },
  {
    field: 'product_name',
    title: '成品名称',
    minWidth: 100,
    fixed: 'left',
  },
  {
    field: 'product_color_code',
    title: '色号',
    minWidth: 100,
    fixed: 'left',
  },
  {
    field: 'product_color_name',
    title: '颜色',
    minWidth: 100,
    fixed: 'left',
  },
  {
    field: 'roll',
    title: '匹数',
    minWidth: 70,
    soltName: 'roll',
    fixed: 'left',
  },
  {
    field: 'unit_price',
    title: '单价',
    minWidth: 70,
    soltName: 'unit_price',
    fixed: 'left',
  },
  {
    field: 'weight',
    title: '数量',
    minWidth: 70,
    soltName: 'weight',
    fixed: 'left',
  },
  {
    field: 'measurement_unit_name',
    title: '单位',
    minWidth: 70,
  },
  {
    field: 'upper_limit',
    title: '上限',
    minWidth: 70,
  },
  {
    field: 'lower_limit',
    title: '下限',
    minWidth: 70,
  },
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    field: 'product_level_name',
    title: '成品等级',
    minWidth: 100,
  },
  {
    field: 'product_color_kind_name',
    title: '颜色类别',
    minWidth: 100,
  },

  {
    field: 'customer_account_num',
    title: '款号',
    minWidth: 100,
  },

  {
    field: 'finish_product_width_and_unit_name',
    title: '成品幅宽',
    minWidth: 100,
  },
  {
    field: 'finish_product_gram_weight_and_unit_name',
    title: '成品克重',
    minWidth: 100,
  },
  {
    field: 'paper_tube_weight',
    title: '纸筒',
    minWidth: 100,
  },
  {
    field: 'weight_error',
    title: '空差',
    minWidth: 100,
  },
  {
    field: 'dye_roll',
    title: '已排染匹数',
    minWidth: 100,
  },
  {
    field: 'other_price',
    title: '其他金额',
    minWidth: 100,
    soltName: 'other_price',
  },
  {
    field: 'total_price',
    title: '总金额',
    minWidth: 100,
    soltName: 'total_price',
    fixed: 'right',
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
    fixed: 'right',
  },
])

const columnList_fabric = ref([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 150,
  },
  {
    field: 'roll',
    title: '用坯匹数',
    minWidth: 150,
    soltName: 'roll',
  },
  {
    field: 'weight',
    title: '用坯数量',
    minWidth: 150,
    soltName: 'weight',
  },
  {
    field: 'planed_roll',
    title: '已计划匹数',
    minWidth: 150,
  },
  {
    field: 'planed_weight',
    title: '已计划数量',
    minWidth: 150,
  },
  {
    field: 'scheduling_roll',
    title: '排产匹数',
    minWidth: 150,
  },
  {
    field: 'scheduling_weight',
    title: '排产数量',
    minWidth: 150,
  },
  {
    field: 'produced_roll',
    title: '已产匹数',
    minWidth: 150,
  },
  {
    field: 'produced_weight',
    title: '已产数量',
    minWidth: 150,
  },
  {
    field: 'use_stock_roll',
    title: '调库存匹数',
    minWidth: 150,
  },
])
</script>

<template>
  <StatusColumn
    show-change-btn
    :order_no="data.order_no"
    :order_id="data.id"
    :status="data.audit_status"
    :status_name="data.audit_status_name"
    permission_wait_key="ProductSalePlanWait"
    permission_reject_key="ProductSalePlanReject"
    permission_pass_key="ProductSalePlanPass"
    permission_cancel_key="ProductSalePlanCancel"
    permission_change_key="ProductSalePlanChange"
    permission_edit_key="ProductSalePlanEdit"
    edit_router_name="ProductSalePlanEdit"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
    @on-modification="changeOrder"
  >
    <template #custom>
      <div
        class="mr-[90px] ml-[40px] justify-center flex flex-col items-center"
      >
        <div class="mb-[10px] font-medium">
          销售类型
        </div>
        <div class="text-[14px] text-[#0077ff]">
          {{ data.plan_type_name }}
        </div>
      </div>
    </template>
    <template #print>
      <PrintPopoverBtn
        :id="route.query.id"
        print-btn-text="打印"
        :print-type="PrintType.PrintTemplateTypeSaleOrder"
        :data-type="PrintDataType.Product"
        api="getSaleProductPlanOrder"
      />
      <!--      <el-popover placement="left" title="选择打印" :width="160" trigger="hover"> -->
      <!--        <template #reference> -->
      <!--          <el-button>打印</el-button> -->
      <!--        </template> -->
      <!--        <PrintBtn btnText="产品销售合同" type="salePlanOrder" :tid="1656313773261056" :id="route.query.id" api="getSaleProductPlanOrder" /> -->
      <!--        <PrintBtn style="margin: 10px 0 0 0" btnText="产品销售清单" type="salePlanOrder" :tid="1655275977494784" :id="route.query.id" api="getSaleProductPlanOrder" /> -->
      <!--      </el-popover> -->
    </template>
  </StatusColumn>
  <FildCard class="mt-[5px]" title="基础信息" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="客户编号:">
        <template #content>
          {{ data?.customer_code }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="客户名称:">
        <template #content>
          {{ data?.customer_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="营销体系:">
        <template #content>
          {{ data?.sale_system_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="订单日期:">
        <template #content>
          {{ formatDate(data?.order_time) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="交货日期:">
        <template #content>
          {{ formatDate(data?.receipt_time) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="销售员:">
        <template #content>
          {{ data?.sale_user_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="合同编号:">
        <template #content>
          {{ data?.voucher_number }}
        </template>
      </DescriptionsFormItem>
      <!-- <DescriptionsFormItem label="客户电话:">
        <template #content>
          {{ data?.customer_phone }}
        </template>
      </DescriptionsFormItem> -->
      <DescriptionsFormItem label="合同定金:">
        <template #content>
          {{ data?.deposit }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="结算方式:">
        <template #content>
          {{ data?.settle_method_name }}
        </template>
      </DescriptionsFormItem>
      <!-- <DescriptionsFormItem label="交货地址:">
        <template #content>
          {{ data?.receipt_address }}
        </template>
      </DescriptionsFormItem> -->
      <DescriptionsFormItem label="是否含税:">
        <template #content>
          {{ data?.is_with_tax_rate ? "是" : "否" }}
          <span v-if="data.is_with_tax_rate">，税率{{ data?.sale_tax_rate }}%</span>
        </template>
      </DescriptionsFormItem>
      <!-- <DescriptionsFormItem label="发货方式:">
        <template #content>
          {{ data?.delivery_type_name }}
        </template>
      </DescriptionsFormItem> -->
      <DescriptionsFormItem label="单据备注:">
        <template #content>
          <div class="break-words">
            {{ data?.internal_remark }}
          </div>
        </template>
      </DescriptionsFormItem>
    </div>
    <div class="m-[10px]">
      <AddressCard
        v-if="data.id"
        type="Detail"
        :sale-shipment-type-code="data.sale_shipment_type_code"
        :sale-system-ids="data.sale_system_id"
        :customer-ids="data.customer_id"
        :customer-name="data.customer_name"
        :address-data="addressDataProps"
      />
    </div>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]">
    <Table
      :config="tableConfig"
      :table-list="data.item_data"
      :column-list="columnList"
    >
      <template #roll="{ row }">
        {{ row.roll }}
        <span v-if="row.change_roll > 0" class="text-red-600">(↑{{ row.change_roll }})</span>
        <span v-if="row.change_roll < 0" class="text-green-600">(↓{{ row.change_roll }})</span>
      </template>

      <template #weight="{ row }">
        {{ row.weight }}
        <span v-if="row.change_weight > 0" class="text-red-600">(↑{{ row.change_weight }})</span>
        <span v-if="row.change_weight < 0" class="text-green-600">(↓{{ row.change_weight }})</span>
      </template>

      <template #total_price="{ row }">
        {{ row.total_price }}
        <span v-if="row.change_total_price > 0" class="text-red-600">(↑{{ row.change_total_price }})</span>
        <span v-if="row.change_total_price < 0" class="text-green-600">(↓{{ row.change_total_price }})</span>
      </template>

      <template #unit_price="{ row }">
        {{ row.unit_price }}
        <span v-if="row.change_unit_price > 0" class="text-red-600">(↑{{ row.change_unit_price }})</span>
        <span v-if="row.change_unit_price < 0" class="text-green-600">(↓{{ row.change_unit_price }})</span>
      </template>

      <template #other_price="{ row }">
        {{ row.other_price }}
        <span v-if="row.change_other_price > 0" class="text-red-600">(↑{{ row.change_other_price }})</span>
        <span v-if="row.change_other_price < 0" class="text-green-600">(↓{{ row.change_other_price }})</span>
      </template>
    </Table>
  </FildCard>
  <FildCard title="坯布信息" class="mt-[5px]">
    <Table
      :config="tableConfig2"
      :table-list="data.gf_data"
      :column-list="columnList_fabric"
    >
      <template #roll="{ row }">
        {{ row.roll }}
        <span v-if="row.change_roll > 0" class="text-red-600">(↑{{ row.change_roll }})</span>
        <span v-if="row.change_roll < 0" class="text-green-600">(↓{{ row.change_roll }})</span>
      </template>

      <template #weight="{ row }">
        {{ row.weight }}
        <span v-if="row.change_weight > 0" class="text-red-600">(↑{{ row.change_weight }})</span>
        <span v-if="row.change_weight < 0" class="text-green-600">(↓{{ row.change_weight }})</span>
      </template>
    </Table>
  </FildCard>
  <FildCard title="凭证信息" class="mt-[5px]" :tool-bar="false">
    <template #right-top>
      <el-button v-if="data?.audit_status && data?.audit_status !== OrderAuditStatusEnum.Pending && data?.audit_status !== OrderAuditStatusEnum.TurnDown" v-has="'ProductSalePlanTextureUrlEdit'" :type="editCertificate ? 'warning' : 'primary'" @click="editCertificate = !editCertificate">
        {{ editCertificate ? '取消编辑' : '编辑凭证' }}
      </el-button>
    </template>
    <template v-if="editCertificate">
      <TextureMapWall v-model:image-list="data.fileList" text="" @update:image-list="updateImageList" />
    </template>
    <template v-else>
      <ImageFileCard v-for="(item, index) in data?.fileList" :key="index" :file-url="item" :all-urls="data?.fileList" clear-disabled />
    </template>
  </FildCard>
</template>

<style lang="scss" scoped>
.oreder_code {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
</style>
