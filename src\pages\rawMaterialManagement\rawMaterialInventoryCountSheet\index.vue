<script setup lang="ts" name="RawMaterialInventoryCountSheet">
import { Plus } from '@element-plus/icons-vue'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import {
  StockCheckOrderCancel,
  StockCheckOrderDetail,
  StockCheckOrderList,
  StockCheckOrderListDownLoad,
  StockCheckOrderPass,
} from '@/api/rawMaterialInventoryCountSheet'
import { BusinessUnitIdEnum } from '@/common/enum'
import {
  formatDate,
  sumNum,
} from '@/common/format'
import {
  debounce,
  exportList,
  getFilterData,
  orderStatusConfirmBox,
} from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DataChange from '@/components/DataChange/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import router from '@/router'
import { processDataOut } from '@/common/handBinary'

const filterData = reactive({
  order_no: '',
  check_unit_id: '',
  sale_system_id: '',
  check_date: '',
  status: '',
  check_start_date: '',
  check_end_date: '',
})

onMounted(() => {
  getData()
})

const {
  fetchData: fetchDataList,
  data: dataList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
} = StockCheckOrderList()
async function getData() {
  const status = ((filterData.status as unknown as []) || []).join(',')
  await fetchDataList(getFilterData({ ...filterData, status }, ['check_date']))
  if (dataList.value?.list)
    showDetail(dataList.value.list[0])
}
onActivated(getData)

const selectRow = ref()
const detailShow = ref()
const {
  fetchData: fetchDataDetail,
  data: dataDetail,
  loading: loadingDetail,
} = StockCheckOrderDetail()
async function getDataDetail() {
  await fetchDataDetail({ id: selectRow.value.id })
  dataDetail.value.items = processDataOut(dataDetail.value.items || [])
}

watch(
  () => filterData.check_date,
  (value: any) => {
    filterData.check_start_date = formatDate(value?.[0]) || ''
    filterData.check_end_date = formatDate(value?.[1]) || ''
  },
)

watch(
  () => filterData,
  debounce(() => {
    getData()
  }, 400),
  { deep: true },
)

function showDetail(row: any) {
  selectRow.value = row
  detailShow.value = true
  getDataDetail()
}

function handDetail(row: any) {
  router.push({
    name: 'RawMaterialInventoryCountSheetDetail',
    params: { id: row?.id },
  })
}
async function handPass(id: number) {
  const msg = { title: '是否审核该订单', desc: '点击审核后订单将审核通过' }
  await orderStatusConfirmBox({ id, message: msg, api: StockCheckOrderPass })
  getData()
}
async function handCancel(id: number) {
  const msg = {
    title: '是否消审该订单',
    desc: '点击消审后订单将变回待审核状态',
  }
  await orderStatusConfirmBox({ id, message: msg, api: StockCheckOrderCancel })
  getData()
}

function handEdit(row: any) {
  router.push({
    name: 'RawMaterialInventoryCountSheetEdit',
    params: { id: row?.id },
  })
}

function handAdd() {
  router.push({
    name: 'RawMaterialInventoryCountSheetAdd',
  })
}

const columnList = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '订单编号',
    soltName: 'order_no',
    width: '8%',
  },
  {
    sortable: true,
    field: 'check_unit_name',
    title: '盘点单位名称',
    width: 150,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系名称',
    width: 150,
  },
  {
    sortable: true,
    field: 'check_date',
    title: '盘点日期',
    width: 110,
    is_date: true,
    formatTime: 'YYYY-MM-DD',
  },
  {
    sortable: true,
    field: 'warehouse_manager_name',
    title: '仓管员',
    width: 110,
  },

  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    width: 110,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    isDate: true,
    width: 150,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    width: 110,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '修改时间',
    isDate: true,
    width: 150,
  },
  {
    sortable: true,
    field: 'auditor_name',
    title: '审核人',
    width: 100,
  },
  {
    sortable: true,
    field: 'audit_time',
    title: '审核时间',
    isDate: true,
    width: 150,
  },
  {
    sortable: true,
    field: 'status',
    title: '单据状态',
    showOrder_status: true,
    soltName: 'status',
    fixed: 'right',
    width: '5%',
  },
])

const cColumnList = ref([
  {
    field: 'A',
    title: '原料信息',
    childrenList: [
      {
        sortable: true,
        field: 'raw_material_code',
        title: '原料编号',
        fixed: 'left',
        width: 110,
      },
      {
        sortable: true,
        field: 'raw_material_name',
        title: '原料名称',
        fixed: 'left',
        width: 110,
      },
      {
        sortable: true,
        field: 'customer_name',
        title: '所属客户',
        width: 100,
      },
      {
        sortable: true,
        field: 'brand',
        title: '原料品牌',
        width: 100,
      },
      {
        sortable: true,
        field: 'craft',
        title: '原料工艺',
        width: 100,
      },
      {
        sortable: true,
        field: 'color_scheme',
        title: '原料色系',
        width: 100,
      },
      {
        sortable: true,
        field: 'supplier_name',
        title: '供应商',
        width: 100,
      },
      {
        sortable: true,
        field: 'batch_num',
        title: '原料批号',
        width: 100,
      },
      {
        sortable: true,
        field: 'level_name',
        title: '原料等级',
        width: 100,
      },
      {
        sortable: true,
        field: 'raw_material_remark',
        title: '原料备注',
        width: 100,
      },
      {
        sortable: true,
        field: 'production_date',
        title: '生产日期',
        width: 100,
        isDate: true,
        formatTime: 'YYYY-MM-DD',
      },
      {
        sortable: true,
        field: 'spinning_type',
        title: '纺纱类型',
        width: 100,
      },
      {
        sortable: true,
        field: 'cotton_origin',
        title: '棉花产地',
        width: 100,
      },
      {
        sortable: true,
        field: 'yarn_origin',
        title: '棉纱产地',
        width: 100,
      },
      {
        sortable: true,
        field: 'carton_num',
        title: '装箱单号',
        width: 100,
      },
      {
        sortable: true,
        field: 'fapiao_num',
        title: '发票号',
        width: 100,
      },
    ],
  },
  {
    field: 'B',
    title: '库存信息',
    childrenList: [
      {
        sortable: true,
        field: 'whole_piece_count_stock',
        title: '整件件数',
        width: 130,
      },
      {
        sortable: true,
        field: 'bulk_piece_count_stock',
        title: '散件件数',
        width: '5%',
      },
      {
        sortable: true,
        field: 'total_weight_stock',
        title: '数量总计(kg)',
        width: 130,
      },
    ],
  },
  {
    field: 'C',
    title: '盘点信息',
    childrenList: [
      {
        sortable: true,
        field: 'whole_piece_count',
        title: '盘点整件件数',
        width: 130,
      },
      {
        sortable: true,
        field: 'bulk_piece_count',
        title: '盘点散件件数',
        width: 130,
      },
      {
        sortable: true,
        field: 'total_weight',
        title: '盘点数量总计(kg)',
        width: 130,
      },
    ],
  },
  {
    field: 'D',
    title: '盈亏信息',
    childrenList: [
      {
        sortable: true,
        field: 'whole_piece_count_diff',
        title: '盈亏整件件数',
        width: 130,
        soltName: 'whole_piece_count_diff',
      },
      {
        sortable: true,
        field: 'bulk_piece_count_diff',
        title: '盈亏散件件数',
        width: 130,
        soltName: 'bulk_piece_count_diff',
      },
      {
        sortable: true,
        field: 'total_weight_diff',
        title: '盈亏数量总计(kg)',
        width: 130,
        soltName: 'total_weight_diff',
      },
      {
        sortable: true,
        field: 'unit_price',
        title: '单价',
        width: 90,
      },
      {
        sortable: true,
        field: 'total_price_diff',
        title: '盈亏金额',
        width: 110,
        soltName: 'total_price_diff',
      },
    ],
  },
])

const loadingExport = ref(false)
async function handleExport() {
  loadingExport.value = true
  await exportList({
    api: StockCheckOrderListDownLoad,
    title: '原料库存盘点单',
    filterData,
  })
  loadingExport.value = false
}

// const footerMethod = ({ columns, data }: { columns: any; data: any }) => {
//   const footerData = [
//     columns.map((column: any, _columnIndex: number) => {
//       if (['raw_material_code'].includes(column.field)) {
//         return '合计'
//       }
//       if (
//         [
//           'whole_piece_count_stock',
//           'total_weight_stock',
//           'whole_piece_count',
//           'bulk_piece_count',
//           'total_weight',
//           'whole_piece_count_diff',
//           'bulk_piece_count_diff',
//           'total_weight_diff',
//           'total_price',
//         ].includes(column.field)
//       ) {
//         return sumNum(data, column.field)
//       }
//     }),
//   ]
//   return footerData
// }

function footerMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '总计'

      if (
        [
          'whole_piece_count_stock',
          'bulk_piece_count_stock',
          'whole_piece_count',
          'bulk_piece_count',
          'whole_piece_count_diff',
          'bulk_piece_count_diff',
        ].includes(column.property)
      )
        return `${sumNum(data, column.property)}`

      if (
        [
          'whole_piece_weight',
          'bulk_weight',
          'total_weight',
          'total_weight_stock',
          'total_weight_diff',
        ].includes(column.property)
      )
        return `${sumNum(data, column.property) as unknown as number}`

      if (['total_price_diff'].includes(column.property))
        return `¥${sumNum(data, column.property) as unknown as number}`

      return null
    }),
  ]
}

const tableConfig = ref({
  fieldApiKey: 'RawMaterialInventoryCountSheet',
  showSlotNums: true,
  loading: loadingList.value,
  showPagition: true,
  page: pageList,
  size: sizeList,
  total: totalList,
  showCheckBox: true,
  height: '100%',
  showOperate: true,
  operateWidth: '9%',
  showSort: false,
  handleSizeChange,
  handleCurrentChange,
})

const CTableConfig = ref({
  fieldApiKey: 'RawMaterialInventoryCountSheet_B',
  height: '100%',
  operateWidth: '150',
  showSort: false,
  showSpanHeader: true,
  loading: loadingDetail.value,
  footerMethod,
})
</script>

<template>
  <div class="list-page">
    <FildCard title="" :tool-bar="false" class="">
      <slot>
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem label="单据编号:">
            <template #content>
              <el-input
                v-model="filterData.order_no"
                placeholder="单据编号"
              />
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="盘点单位:">
            <template #content>
              <SelectComponents
                v-model="filterData.check_unit_id"
                :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory }"
                api="GetBusinessUnitListApi"
                label-field="name"
                value-field="id"
                clearable
              />
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="营销体系名称:">
            <template #content>
              <SelectComponents
                v-model="filterData.sale_system_id"
                api="GetSaleSystemDropdownListApi"
                label-field="name"
                value-field="id"
              />
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="盘点日期:" width="330">
            <template #content>
              <SelectDate v-model="filterData.check_date" />
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="单据状态:">
            <template #content>
              <SelectComponents
                v-model="filterData.status"
                :multiple="true"
                api="GetAuditStatusEnum"
                label-field="name"
                value-field="id"
                clearable
              />
            </template>
          </DescriptionsFormItem>
        </div>
      </slot>
    </FildCard>
    <FildCard class="flex flex-col h-full overflow-hidden">
      <template #right-top>
        <el-button
          v-has="`RawMaterialInventoryCountSheetAdd`"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handAdd"
        >
          新建
        </el-button>
        <BottonExcel
          v-has="'RawMaterialInventoryCountSheet_export'"
          :loading="loadingExport"
          title="导出文件"
          @on-click-excel="handleExport"
        />
      </template>
      <Table
        :config="tableConfig"
        :table-list="dataList.list"
        :column-list="columnList"
      >
        <template #order_no="{ row }">
          <el-link type="primary" @click="showDetail(row)">
            {{
              row.order_no
            }}
          </el-link>
        </template>
        <template #status="{ row }">
          <StatusTag :status="row.status" />
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link v-has="'RawMaterialInventoryCountSheetDetail'" type="primary" :underline="false" @click="handDetail(row)">
              查看
            </el-link>
            <el-link
              v-if="row.status === 1 || row.status === 3"
              v-has="'RawMaterialInventoryCountSheetEdit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link v-if="row.status === 1" v-has="'RawMaterialInventoryCountSheet_PASS'" type="primary" :underline="false" @click="handPass(row.id)">
              审核
            </el-link>
            <el-link
              v-if="row.status === 2"
              v-has="'RawMaterialInventoryCountSheet_cancel'"
              type="primary"
              :underline="false"
              @click="handCancel(row.id)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard title="" class="table-card-bottom">
      <Table
        :config="CTableConfig"
        :table-list="dataDetail.items"
        :column-list="cColumnList"
      >
        <template #whole_piece_count_diff="{ row }">
          <DataChange :val="row.whole_piece_count_diff" />
        </template>
        <template #bulk_piece_count_diff="{ row }">
          <DataChange :val="row.bulk_piece_count_diff" />
        </template>
        <template #total_weight_diff="{ row }">
          <DataChange :val="row.total_weight_diff" />
        </template>
        <template #total_price_diff="{ row }">
          <DataChange :val="row.total_price_diff" />
        </template>
      </Table>
    </FildCard>
  </div>
</template>

<style lang="scss" scoped>
.up_red {
  color: red;
}

.lower_green {
  color: green;
}
</style>
