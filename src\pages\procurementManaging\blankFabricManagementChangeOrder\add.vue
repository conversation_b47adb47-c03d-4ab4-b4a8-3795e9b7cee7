<script lang="ts" setup>
import { Plus } from '@element-plus/icons-vue'
import Big from 'big.js'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { computed, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import AddressAdd from './components/AddressAdd/index.vue'
import SelectProductionNotice from './components/SelectProductionNotice/index.vue'
import SelectRawMaterial from '@/components/SelectRawMaterial/index.vue'
import { AddPurchaseGreyFarbric } from '@/api/blanketManagement'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatDate, formatTwoDecimalsMul, formatUnitPriceMul, formatWeightMul, sumNum } from '@/common/format'
import { getDefaultSaleSystem, getFilterData } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import { isConfigured } from '@/pages/procurementManaging/blankFabricManagementChangeOrder/utils'

const state = reactive({
  form: {
    sale_system_id: getDefaultSaleSystem()?.default_sale_system_id,
    sale_system_name: '',
    supplier_id: '',
    recipien_entity_id: '',
    purchase_time: dayjs().format('YYYY-MM-DD'),
    receiving_time: '',
    invoice_header_id: '',
    remark: '',
    item_params: [] as any[],
  },
  tableData: [] as any[],
  fromRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    supplier_id: [{ required: true, message: '请选择供应商', trigger: 'change' }],
    recipien_entity_id: [{ required: true, message: '请选择收货单位', trigger: 'change' }],
    purchase_time: [{ required: true, message: '请选择采购日期', trigger: 'change' }],
  },
})

const showAdd = ref(false)
const showAddress = ref(false)
const showProduct = ref(false)

function handDel(row: any, rowIndex: number) {
  state.tableData.splice(rowIndex, 1)
}
function handAdd() {
  showAdd.value = true
}

const columnList = ref([
  {
    field: 'code',
    title: '坯布编号',
    fixed: 'left',
    width: 150,
  },
  {
    field: 'name',
    title: '坯布名称',
    fixed: 'left',
    width: 100,
  },
  {
    field: 'customer_id',
    title: '所属客户',
    width: 130,
    soltName: 'customer_id',
    bulkStatus: true,
  },
  {
    field: 'grey_fabric_width',
    title: '幅宽',
    width: 100,
  },
  {
    field: 'grey_fabric_gram_weight',
    title: '克重',
    width: 100,
  },
  {
    field: 'needle_size',
    title: '针寸数',
    width: 100,
  },
  {
    field: 'total_needle_size',
    title: '总针数',
    width: 100,
  },
  {
    field: 'gray_fabric_color_name',
    title: '织坯颜色',
    width: 100,
    soltName: 'gray_fabric_color_name',
  },
  {
    field: 'yarn_batch',
    title: '纱批',
    width: 100,
    soltName: 'yarn_batch',
  },
  {
    field: 'machine_combination_number',
    title: '机台号',
    width: 100,
    soltName: 'machine_combination_number',
  },
  {
    field: 'grey_fabric_level_id',
    title: '坯布等级',
    width: 100,
    soltName: 'grey_fabric_level_id',
  },
  {
    field: 'weave_factory_name_id',
    title: '织厂名称',
    width: 130,
    soltName: 'weave_factory_name_id',
  },
  {
    field: 'remark',
    title: '备注',
    width: 100,
    soltName: 'remark',
  },
  {
    field: 'address',
    title: '收货地址',
    fixed: 'right',
    width: 130,
    soltName: 'address',
  },
  {
    field: 'number',
    title: '匹数',
    fixed: 'right',
    width: 100,
    soltName: 'number',
  },
  {
    field: 'average_weight',
    title: '均重(kg)',
    fixed: 'right',
    width: 100,
    soltName: 'average_weight',
  },
  {
    field: 'count_weight',
    title: '数量总计',
    fixed: 'right',
    width: 100,
    soltName: 'count_weight',
  },
  {
    field: 'single_price',
    title: '单价',
    fixed: 'right',
    width: 100,
    soltName: 'single_price',
  },
  {
    field: 'count_price',
    title: '金额',
    fixed: 'right',
    width: 100,
    soltName: 'count_price',
  },
])

const changeRow = ref<any>()
function changeData(row: any) {
  changeRow.value = row
}

const tablesRef = ref()
watch(
  () => [changeRow.value?.number, changeRow.value?.average_weight, changeRow.value?.single_price],
  async () => {
    computedData(changeRow.value)
  },
  {
    deep: true,
  },
)

const saleSaleSystemInfo = ref()
function getSaleSystem(row: any) {
  state.form.sale_system_name = row?.name
  saleSaleSystemInfo.value = row
  clearData(row)
}

function computedData(row: any) {
  if (row?.number && row?.average_weight)
    row.count_weight = Number.parseFloat(Big(row.number).times(row?.average_weight).toFixed(2))

  // 计算金额
  if (row?.single_price && row?.count_weight)
    row.count_price = Number.parseFloat(Big(row.single_price).times(row?.count_weight).toFixed(2))

  tablesRef.value.tableRef.updateFooter()
}

const bulkShow = ref(false)
const bulkSetting = ref<any>({
  address: {},
})

const multipleSelection = ref<any[]>([])
const bulkFormRef = ref()
async function bulkSubmit({ row, value }: any) {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  let tf = false
  if (row.field !== 'address') {
    if (!value[row.field]) {
      ElMessage.error('请输入参数')
      tf = true
    }
  }
  else {
    await bulkFormRef.value.validate((valid: any) => {
      if (!valid)
        tf = true
    })
  }
  if (tf)
    return
  multipleSelection.value?.map((item: any) => {
    if (row.field === 'address') {
      if (item.number && item.average_weight) {
        item.item_param_addrs = [{ ...value[row.field], number: item.number, total_weight: Number.parseFloat(item.average_weight || 0) * Number.parseFloat(item.number || 0) }]
        ElMessage.success('设置成功')
      }
      else {
        ElMessage.warning('填写匹数和均重')
      }
    }
    else {
      item[row.field] = value[row.field]
      ElMessage.success('设置成功')
    }
    computedData(item)
  })
}

function bulkHand() {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请选择批量修改的数据')
  bulkShow.value = true
}

function onSubmit(row: any) {
  showAdd.value = false
  const val = row?.map((item: any) => {
    return { ...item, customer_id: saleSaleSystemInfo.value?.default_customer_id || '', customer_name: saleSaleSystemInfo.value?.default_customer_name || '', selected: false }
  })
  state.tableData = [...state.tableData, ...val]
}

const router = useRouter()
const { fetchData: fetchDataAdd, success: successAdd, msg: msgAdd } = AddPurchaseGreyFarbric()
const ruleFormRef = ref()
async function handSubmit() {
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      if (validateList())
        return false
      const res = fomatData()
      await fetchDataAdd(getFilterData({ ...state.form, purchase_time: formatDate(state.form.purchase_time), receiving_time: formatDate(state.form.receiving_time), item_params: res }))
      if (successAdd.value) {
        ElMessage.success('添加成功')
        router.push({
          name: 'BlanketManagement',
        })
      }
      else {
        ElMessage.error(msgAdd.value)
      }
    }
  })
}

// 验证坯布信息字段
function validateList() {
  let msg = ''
  if (!state.tableData || state.tableData.length === 0) {
    msg = '坯布信息不能为空'
  }
  else {
    state.tableData?.some((item: any) => {
      if (!item.number || !item.average_weight || !item.single_price || !item.customer_id) {
        msg = `编号为${item.code}的数据所属客户、条数、均重、单价不能为空`
        return true
      }
      else {
        return false
      }
    })
  }
  msg && ElMessage.error(msg)
  return msg
}

const selectRow = ref()
function openAddress(row: any) {
  if (!row.number || !row.average_weight)
    return ElMessage.warning('条数、均重不能为空')

  selectRow.value = row
  showAddress.value = true
}

// 整理坯布信息
function fomatData() {
  return state.tableData?.map((item: any) => {
    const addr = item.item_param_addrs?.map((citem: any) => {
      return {
        ...citem,
        total_weight: formatWeightMul(citem.total_weight || 0),
        number: formatTwoDecimalsMul(citem.number),
      }
    })
    const {
      average_weight = 0,
      customer_id = 0,
      grey_fabric_level_id = 0,
      grey_fabric_price_unit_id = 0,
      machine_combination_number = '',
      number = 0,
      purchase_grey_fabric_id = 0,
      remark = '',
      single_price = 0,
      weave_back_color = '',
      weave_factory_name_id = 0,
      yarn_batch = '',
    } = item
    return {
      ...item,
      grey_fabric_id: item.id,
      average_weight: formatWeightMul(average_weight || 0),
      customer_id,
      grey_fabric_info_id: item?.id,
      grey_fabric_level_id,
      grey_fabric_price_unit_id,
      item_param_addrs: addr,
      machine_combination_number,
      number: formatTwoDecimalsMul(number),
      purchase_grey_fabric_id,
      remark,
      single_price: formatUnitPriceMul(single_price || 0),
      weave_back_color,
      weave_factory_name_id,
      yarn_batch,
    }
  })
}

const addressRow = computed(() => {
  return {
    code: selectRow.value?.code,
    name: selectRow.value?.name,
    number: selectRow.value?.number,
    total_weight: selectRow.value?.count_weight,
    average_weight: selectRow.value?.average_weight,
  }
})

function onAddress(row: any) {
  selectRow.value.item_param_addrs = row?.map((item: any) => {
    return {
      addr: item.addr || '',
      contact_phone: item.contact_phone || '',
      contacts: item.contacts || '',
      number: Number.parseFloat(item.number) || 0,
      recipient_entity_id: item.recipient_entity_id,
      recipient_entity_type: item.recipient_entity_type,
      total_weight: Number.parseFloat(item.total_weight) || 0,
    }
  })
  showAddress.value = false
}

function getAddress(val: any, row: any) {
  row.receipt_unit_name = val.name
  row.recipient_entity_name = val.name
  row.contacts = val.contact_name
  row.contact_phone = val.phone
  row.addr = val.address
}

const bulkList = reactive<any>([
  {
    field: 'customer_id',
    field_name: 'customer_name',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
    query: { sale_system_id: state.form.sale_system_id },
  },
  {
    field: 'grey_fabric_level_id',
    title: '坯布等级',
    component: 'select',
    api: 'GetInfoBaseGreyFabricLevelListUseByOther',
  },
  {
    field: 'yarn_batch',
    title: '纱批',
    component: 'input',
    type: 'text',
  },
  {
    field: 'machine_combination_number',
    title: '机台号',
    component: 'input',
    type: 'text',
  },

  {
    field: 'weave_factory_name_id',
    title: '织厂名称',
    component: 'select',
    api: 'GetBusinessUnitListApi',
    query: { unit_type_id: BusinessUnitIdEnum.knittingFactory },
  },
  {
    field: 'number',
    title: '匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'average_weight',
    title: '均重',
    component: 'input',
    type: 'float',
  },
  {
    field: 'single_price',
    title: '单价',
    component: 'input',
    type: 'float',
  },
  {
    field: 'address',
    title: '收货地址',
    hideTitle: true,
    rule: {
      recipient_entity_id: [{ required: true, message: '请选择收货单位', trigger: 'change' }],
      contacts: [{ required: true, message: '请填写收货联系人', trigger: 'change' }],
      contact_phone: [{ required: true, message: '请填写收货电话', trigger: 'change' }],
      addr: [{ required: true, message: '请填写收货地址', trigger: 'change' }],
    },
  },
])

function handAllSelect({ records }: any) {
  multipleSelection.value = records
}

function handleSelectionChange({ records }: any) {
  multipleSelection.value = records
}

function footerMethod({ columns, data }: { columns: any, data: any }) {
  const footerData = [
    columns.map((column: any, _columnIndex: number) => {
      if (['code'].includes(column.field))
        return '合计'

      if (['number', 'count_weight', 'count_price'].includes(column.field))
        return sumNum(data, column.field)
    }),
  ]
  return footerData
}
function handBulkClose() {
  bulkShow.value = false
}
function clearData(row: any) {
  state.tableData?.map((item) => {
    item.customer_id = saleSaleSystemInfo.value?.default_customer_id || ''
    item.customer_name = saleSaleSystemInfo.value?.default_customer_name || ''
  })
  bulkList[0].query = { sale_system_id: row.id }
  bulkSetting.value.customer_id = ''
}
const tableConfig = ref({
  showOperate: true,
  operateWidth: 100,
  bulkSubmit,
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  footerMethod,
})
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <div class="line" />
    <template #right-top>
      <el-button v-btnAntiShake="handSubmit" type="primary">
        提交
      </el-button>
    </template>
    <slot>
      <el-form ref="ruleFormRef" :model="state.form" label-width="60px" label-position="top" :rules="state.fromRules">
        <el-descriptions :column="3" border size="small">
          <el-descriptions-item label="营销体系名称">
            <template #label>
              营销体系名称
              <span style="color: red">*</span>
            </template>
            <el-form-item prop="sale_system_id">
              <SelectComponents
                v-model="state.form.sale_system_id"
                :default-status="true"
                api="GetSaleSystemDropdownListApi"
                label-field="name"
                value-field="id"
                @select="getSaleSystem"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="供应商名称">
            <template #label>
              供应商名称
              <span style="color: red">*</span>
            </template>
            <el-form-item prop="supplier_id">
              <SelectComponents
                v-model="state.form.supplier_id"
                :query="{ unit_type_id: BusinessUnitIdEnum.blankFabric }"
                api="BusinessUnitSupplierEnumAll"
                label-field="name"
                value-field="id"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="收货单位名称">
            <template #label>
              收货单位名称
              <span style="color: red">*</span>
            </template>
            <el-form-item prop="recipien_entity_id">
              <SelectComponents
                v-model="state.form.recipien_entity_id"
                :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory }"
                api="GetBusinessUnitListApi"
                label-field="name"
                value-field="id"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="采购日期">
            <template #label>
              采购日期
              <span style="color: red">*</span>
            </template>
            <el-form-item prop="purchase_time">
              <SelectDate v-model="state.form.purchase_time" type="date" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="收货日期">
            <el-form-item prop="receiving_time">
              <SelectDate v-model="state.form.receiving_time" type="date" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="发票抬头">
            <el-form-item prop="invoice_header_id">
              <SelectComponents v-model="state.form.invoice_header_id" api="GetInfoPurchaseInvoiceHeaderListUseByOther" label-field="name" value-field="id" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="备注">
            <el-form-item prop="userName">
              <vxe-textarea v-model="state.form.remark" maxlength="500" show-word-count />
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
    </slot>
  </FildCard>
  <FildCard title="坯布信息" :tool-bar="false" class="mt-[10px]">
    <template #right-top>
      <el-button style="margin-left: 10px" type="primary" @click="bulkHand">
        批量操作
      </el-button>
      <el-button style="margin-left: 10px" type="primary" :icon="Plus" @click="handAdd">
        根据资料添加
      </el-button>
    </template>
    <Table ref="tablesRef" :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
      <template #customer_id="{ row }">
        <SelectComponents
          v-model="row.customer_id"
          :query="{ sale_system_id: state.form.sale_system_id }"
          size="small"
          api="GetCustomerEnumList"
          label-field="name"
          value-field="id"
        />
      </template>
      <template #gray_fabric_color_name="{ row }">
        {{ row.gray_fabric_color_name }}
      </template>
      <template #yarn_batch="{ row }">
        <vxe-input v-model="row.yarn_batch" size="mini" maxlength="200" />
      </template>
      <template #machine_combination_number="{ row }">
        <vxe-input v-model="row.machine_combination_number" size="mini" />
      </template>
      <template #grey_fabric_level_id="{ row }">
        <SelectComponents v-model="row.grey_fabric_level_id" size="small" api="GetInfoBaseGreyFabricLevelListUseByOther" label-field="name" value-field="id" />
      </template>
      <template #weave_factory_name_id="{ row }">
        <SelectComponents
          v-model="row.weave_factory_name_id"
          size="small"
          :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory }"
          api="GetBusinessUnitListApi"
          label-field="name"
          value-field="id"
        />
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" size="mini" maxlength="200" />
      </template>
      <template #number="{ row }">
        <vxe-input v-model="row.number" size="mini" type="float" :min="0" @input="changeData(row)" />
      </template>
      <template #average_weight="{ row }">
        <vxe-input v-model="row.average_weight" size="mini" type="float" :min="0" @input="changeData(row)" />
      </template>
      <template #count_weight="{ row }">
        {{ row.count_weight }}
      </template>
      <template #single_price="{ row }">
        <vxe-input v-model="row.single_price" size="mini" type="float" :min="0" @input="changeData(row)" />
      </template>
      <template #count_price="{ row }">
        {{ row.count_price }}
      </template>
      <template #address="{ row }">
        <el-link type="primary" @click="openAddress(row)">
          编辑
        </el-link>
        <span class="text-black-40" :style="`color:${!isConfigured(row) && 'red'}`">
          {{ isConfigured(row) ? '（已配置）' : '（未配置）' }}
        </span>
      </template>
      <template #operate="{ row, rowIndex }">
        <el-button type="text" @click="handDel(row, rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <SelectRawMaterial v-model="showAdd" @submit="onSubmit" />
  <AddressAdd v-model="showAddress" :row="addressRow" :default-list="[...(selectRow?.item_param_addrs || [])]" @submit="onAddress" />
  <SelectProductionNotice v-model="showProduct" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose">
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
    <template #address="{ row }">
      <el-form ref="bulkFormRef" :model="bulkSetting[row.field]" :rules="row.rule" label-width="120px" label-position="left">
        <el-form-item prop="receipt_unit_id" label="收货单位">
          <SelectComponents
            v-model="bulkSetting[row.field].recipient_entity_id"
            api="GetBusinessUnitListApi"
            label-field="name"
            value-field="id"
            @select="val => getAddress(val, bulkSetting[row.field])"
          />
        </el-form-item>
        <el-form-item prop="contacts" label="收货联系人">
          <el-input v-model="bulkSetting[row.field].contacts" />
        </el-form-item>
        <el-form-item prop="contact_phone" label="收货电话">
          <el-input v-model="bulkSetting[row.field].contact_phone" />
        </el-form-item>
        <el-form-item prop="addr" label="收货地址">
          <el-input v-model="bulkSetting[row.field].addr" />
        </el-form-item>
      </el-form>
    </template>
  </BulkSetting>
</template>

<style lang="scss" scoped></style>
