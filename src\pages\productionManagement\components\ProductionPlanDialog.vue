<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import Table from '@/components/Table.vue'
import { debounce, getFilterData, resetData } from '@/common/util'
import { getProductionPlanOrder } from '@/api/productionNotice'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { formatDate, formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'
import { BusinessUnitIdEnum } from '@/common/enum'

export interface Props {
  filterDataDefault: any
  multiple: boolean
}

const props = withDefaults(defineProps<Props>(), {
  filterDataDefault: {},
  multiple: true,
})
const emits = defineEmits(['handleSure'])

const create_date = ref([new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000), new Date()])

const state = reactive<any>({
  filterData: {
    order_no: '',
    grey_fabric_code: '',
    grey_fabric_id: '',
    weaving_mill_id: '',
    customer_id: '',
    create_date: [],
  },
  showModal: false,
  modalName: '生产计划单',
  multipleSelection: [],
  tableData: [],
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  grey_fabric_code: '',
  grey_fabric_name: '',
  product_name: '',
  color_name: '',
})

const {
  fetchData: fetchData1,
  data: tableData,
  total: total1,
  loading: loading1,
  page: page1,
  size: size1,
  handleSizeChange: handleSizeChange1,
  handleCurrentChange: handleCurrentChange1,
} = getProductionPlanOrder()

watch(
  () => state.showModal,
  () => {
    if (state.showModal) {
      // state.filterData = {
      //   ...state.filterData,
      //   ...props.filterDataDefault,
      // }
      getData()
    }
  },
)

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  {
    deep: true,
  },
)

const tableConfig = reactive<any>({
  loading: loading1,
  showPagition: true,
  showSlotNums: false,
  page: page1,
  size: size1,
  total: total1,
  showCheckBox: true,
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange1(val),
  handleCurrentChange: (val: number) => handleCurrentChange1(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

async function getData() {
  const form = {
    ...state.filterData,
    order_no: props.filterDataDefault.order_no,
  }
  if (create_date.value.length) {
    form.start_create_date = formatDate(create_date.value[0])
    form.end_create_date = formatDate(create_date.value[1])
  }
  await fetchData1(getFilterData(form))

  tableConfig.total = total1
  tableConfig.page = page1
  tableConfig.size = size1
}

watch(
  () => tableData.value,
  () => {
    state.tableData
      = tableData.value?.list?.map((item: any) => {
        return {
          ...item,
          plan_roll: formatTwoDecimalsDiv(item.plan_roll),
          plan_weight: formatWeightDiv(item.plan_weight),
          weight_of_fabric: formatWeightDiv(Number(item.weight_of_fabric)),
          process_price: formatUnitPriceDiv(Number(item.process_price)),
        }
      }) || []
  },
  { deep: true },
)

const columnList = ref([
  {
    field: 'order_no',
    width: 150,
    title: '生产计划单号',
  },
  {
    field: 'weaving_mill_name',
    width: 100,
    title: '织厂名称',
  },
  {
    field: 'grey_fabric_code',
    width: 100,
    title: '坯布编号',
  },
  {
    field: 'grey_fabric_name',
    width: 100,
    title: '坯布名称',
  },
  {
    field: 'customer_name',
    width: 100,
    title: '所属客户',
  },
  {
    field: 'grey_fabric_width',
    width: 100,
    title: '坯布幅宽',
  },
  {
    field: 'grey_fabric_gram_weight',
    width: 100,
    title: '坯布克重',
  },
  {
    field: 'grey_fabric_color_name',
    width: 100,
    title: '织坯颜色',
  },
  {
    field: 'plan_roll',
    width: 100,
    title: '计划匹数',
  },
  {
    field: 'plan_weight',
    width: 100,
    title: '计划数量',
  },
  {
    field: 'unit_name',
    width: 100,
    title: '单位',
  },
  {
    field: 'remark',
    width: 100,
    title: '备注',
  },
  {
    field: 'create_time',
    width: 150,
    title: '创建时间',
    isDate: true,
  },
])

const selectList = ref<any[]>()
function handAllSelect({ records }: any) {
  // 单选
  if (records.length > 1) {
    records.forEach((item: any, index: number) => {
      item.selected = index === 0
    })
    selectList.value = records.filter((v: any) => v.selected)
    return ElMessage.error('只能选中一种')
  }
  selectList.value = records
}

function handleSelectionChange({ records, row }: any) {
  // 单选
  if (records.length > 1) {
    const index = records.findIndex((item: any) => item.id === row.id)
    records.forEach((item: any, i: number) => {
      item.selected = index === i
    })
    selectList.value = records.filter((v: any) => v.selected)
    return ElMessage.error('只能选中一种')
  }
  selectList.value = records
}

function handCancel() {
  state.showModal = false
}

function handleSure() {
  if (!selectList.value?.length) {
    return ElMessage.error('请选择一条数据')
  }
  else {
    // return

    emits('handleSure', selectList.value)
  }
}

// 重置
function handReset() {
  state.filterData = resetData(state.filterData)
  create_date.value = []
}

function changeDate() {
  // create_date.value = [row.date_min, row.date_max]
  getData()
}

const greyRef2 = ref()
// const greyRef1 = ref()
// function changeGrey(stringRef: string) {
//   if (stringRef === 'greyRef1')
//     greyRef2.value.inputLabel = greyRef1.value.item?.code
//   else
//     greyRef1.value.inputLabel = greyRef2.value.item?.name
// }
defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="1200" height="700" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="生产计划单号:">
        <template #content>
          <el-input :disabled="props.filterDataDefault.order_no" :value="props.filterDataDefault.order_no" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="坯布编号:">
        <template #content>
          <SelectDialog
            ref="greyRef2"
            v-model="state.filterData.grey_fabric_id"
            :query="{ code: componentRemoteSearch.grey_fabric_code }"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :table-column="[
              {
                field: 'code',
                title: '坯布编号',
                minWidth: 100,
              },
            ]"
            api="GetGreyFabricInfoListUseByOthersMenu"
            label-field="code"
            @change-input="val => (componentRemoteSearch.grey_fabric_code = val)"
          />
          <!-- <el-input v-model="state.filterData.grey_fabric_code" clearable></el-input> -->
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="坯布名称:">
        <template #content>
          <SelectDialog
            v-model="state.filterData.grey_fabric_id"
            :query="{ code: componentRemoteSearch.grey_fabric_name }"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :table-column="[
              {
                field: 'name',
                title: '坯布名称',
                minWidth: 100,
              },
            ]"
            api="GetGreyFabricInfoListUseByOthersMenu"
            @change-input="val => (componentRemoteSearch.grey_fabric_name = val)"
          />
          <!-- <SelectComponents api="GetGreyFabricInfoListUseByOthersMenu" label-field="name" value-field="id" v-model="state.filterData.grey_fabric_id" clearable /> -->
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="织厂名称:">
        <template #content>
          <SelectDialog
            v-model="state.filterData.weaving_mill_id"
            api="business_unitlist"
            :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory, name: componentRemoteSearch.name }"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.name = val)"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="客户名称:">
        <template #content>
          <SelectDialog
            v-model="state.filterData.customer_id"
            api="GetCustomerEnumList"
            :query="{ name: componentRemoteSearch.customer_name }"
            :column-list="[
              {
                title: '客户编号',
                minWidth: 100,
                required: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '客户编号',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '客户名称',
                minWidth: 100,
                colGroupHeader: true,
                required: true,
                childrenList: [
                  {
                    isEdit: true,
                    field: 'name',
                    title: '客户名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '电话',
                colGroupHeader: true,
                minWidth: 100,
                childrenList: [
                  {
                    field: 'phone',
                    isEdit: true,
                    title: '电话',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '销售员',
                minWidth: 100,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'seller_name',
                    title: '销售员',
                    soltName: 'seller_name',
                    isEdit: true,
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.customer_name = val)"
          />
          <!-- <SelectComponents api="GetCustomerEnumList" label-field="name" value-field="id" v-model="state.filterData.customer_id" clearable /> -->
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="创建日期:" width="330">
        <template #content>
          <SelectDate v-model="create_date" @change-date="changeDate" />
        </template>
      </DescriptionsFormItem>
    </div>
    <div class="flex-end mb-[20px]">
      <el-button type="primary" @click="handReset()">
        重置
      </el-button>
    </div>
    <Table :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
      <template #grey_fabric_gram_weight="{ row }">
        <div>{{ row.grey_fabric_gram_weight }}g</div>
      </template>
    </Table>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style>
.flex-end {
  display: flex;
  justify-content: flex-end;
}
</style>
