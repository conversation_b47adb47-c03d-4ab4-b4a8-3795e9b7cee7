<!-- eslint-disable ts/no-use-before-define -->
<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import dayjs from 'dayjs'
import FildCard from '@/components/FildCard.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import Table from '@/components/Table.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { sumNum } from '@/util/tableFooterCount'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import { processDataOut } from '@/common/handBinary'
import { BusinessUnitIdEnum, EmployeeType } from '@/common/enum'
import { debounce, getFilterData } from '@/common/util'
import { getFpmQualityCheckDefectList, getFpmQualityCheckList } from '@/api/fpQualityCheck'
import { usePrintTemplate } from '@/components/PrintPopoverBtn/index'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'

const filterData = reactive<any>({
  arrange_time: [dayjs().subtract(7, 'days').toString(), dayjs().toString()],
  quality_checker_id: '',
  supplier_id: '',
  product_id: '',
  product_color_id: '',
  dyelot_number: '',
  volume_number: '',
})

// 远程筛选值
const componentRemoteSearch = reactive({
  finish_product_code: '',
  finish_product_name: '',
  color_name: '',
  color_code: '',
})
const tablesRef1 = ref()

// #region  主表数据
// 获取列表
const { fetchData: fetchDataList, data: mainListData, total: totalList, page: pageList, size: sizeList, loading: loadingList, handleSizeChange, handleCurrentChange } = getFpmQualityCheckList()
async function getData() {
  const query = {
    ...filterData,
  }
  if (filterData.arrange_time?.length) {
    query.quality_check_date_begin = dayjs(filterData.arrange_time[0]).format('YYYY-MM-DD')
    query.quality_check_date_end = dayjs(filterData.arrange_time[1]).format('YYYY-MM-DD')
  }
  delete query.arrange_time
  mainTableOptions.currenClickId = ''
  await fetchDataList(getFilterData({ ...query }))
  if (mainListData.value?.list)
    showDefectDetail(mainListData.value.list[0])
}

const mainList: any = computed(() => processDataOut(mainListData.value?.list || []))

// 质检列表配置
const mainTableOptions = reactive({
  tableConfig: {
    showSlotNums: true,
    loading: loadingList.value,
    showPagition: true,
    page: pageList,
    size: sizeList,
    total: totalList,
    showCheckBox: false,
    showOperate: true,
    operateWidth: '5%',
    height: '100%',
    showSort: false,
    fieldApiKey: 'fpQualityCheckReportMain',
    handleSizeChange,
    handleCurrentChange,
    footerMethod: (val: any) => mainTableOptions.FooterMethod(val),
  },
  currenClickId: '', // 当前点击的id
  columnList: [
    {
      sortable: true,
      field: 'order_no',
      title: '单号',
      soltName: 'order_no',
      width: '8%',
    },
    {
      sortable: true,
      field: 'order_type_name',
      title: '单据类型',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'supplier_name',
      title: '来源供应商',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'quality_check_date',
      title: '质检日期',
      minWidth: 100,
      is_date: true,
    },
    {
      sortable: true,
      field: 'bar_code',
      title: '条形码',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'product_code',
      title: '成品编号',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'product_name',
      title: '成品名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'product_color_code',
      title: '色号',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'product_color_name',
      title: '颜色',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'dyelot_number',
      title: '缸号',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'roll',
      title: '条数',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'volume_number',
      title: '卷号',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'weight',
      title: '数量',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'edge_width',
      title: '包边门幅',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'useful_width',
      title: '有效门幅',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'ingredient',
      title: '成分',
      minWidth: 120,
    },
    {
      sortable: true,
      field: 'yarn_count',
      title: '纱支',
      minWidth: 120,
    },
    {
      sortable: true,
      field: 'density',
      title: '密度',
      minWidth: 120,
    },
    {
      sortable: true,
      field: 'weaving_organization_name',
      title: '组织',
      minWidth: 120,
    },
    {
      sortable: true,
      field: 'qc_gram_weight',
      title: '克重',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'defect_weight',
      title: '次布数量',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'actually_weight',
      title: '实际数量',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'defect_merge_str',
      title: '疵点信息',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'remark',
      title: '质检备注',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'quality_checker_name',
      title: '质检员',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'creator_name',
      title: '创建人',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'create_time',
      title: '创建时间',
      minWidth: 150,
      isDate: true,
    },
    {
      sortable: true,
      field: 'update_user_name',
      title: '修改人',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'update_time',
      title: '修改时间',
      minWidth: 150,
      isDate: true,
    },
  ],
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '合计'

        // 条数 数量 次布数量
        if (['roll'].includes(column.field))
          return sumNum(data, 'roll', '', 'float')

        if (['weight'].includes(column.field))
          return sumNum(data, 'weight', '', 'float')

        if (['defect_weight'].includes(column.field))
          return sumNum(data, 'defect_weight', '', 'float')

        return null
      }),
    ]
  },
})

// #endregion

// #region 从表数据 - 疵点
// 获取疵点信息
const {
  fetchData: fetchDataList1,
  data: mainList1,
  loading: loadingList1,
} = getFpmQualityCheckDefectList()
async function getCiDianTable() {
  if (!mainTableOptions.currenClickId)
    return

  await fetchDataList1({ pid: mainTableOptions.currenClickId })
  defectTableOptions.datalist = processDataOut(mainList1.value.list)
}

// 疵点表格配置
const defectTableOptions = reactive({
  tableConfig: {
    showPagition: false,
    showSlotNums: true,
    showCheckBox: false,
    operateWidth: '90',
    height: '100%',
    showSort: false,
    loading: loadingList1.value,
    fieldApiKey: 'fpQualityCheckReportDefect',
  },
  datalist: [],
  columnList: [
    {
      sortable: true,
      field: 'bar_code',
      title: '条形码',
      minWidth: 150,
    },
    {
      sortable: true,
      field: 'defect_name',
      title: '疵点名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'measurement_unit_name',
      title: '单位名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'defect_position',
      title: '疵点位置',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'defect_count',
      title: '疵点数量',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'score',
      title: '分数',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'fabric_inspector_name',
      title: '验布员',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'creator_name',
      title: '创建人',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'create_time',
      title: '创建时间',
      minWidth: 150,
      isDate: true,
    },
    {
      sortable: true,
      field: 'update_user_name',
      title: '修改人',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'update_time',
      title: '修改时间',
      minWidth: 150,
      isDate: true,
    },
  ],
})
// #endregion

const showDefectModal = ref(false)// 显示疵点表格
// 点击单据编号
async function showDefectDetail(row: any) {
  showDefectModal.value = true
  mainTableOptions.currenClickId = row.id// 点击单号的id
  getCiDianTable()
}

onMounted(() => {
  getData()
})
watch(
  filterData,
  debounce(() => {
    getData()
  }, 400),
)
const { options: printOptions } = usePrintTemplate({
  printType: PrintType.PrintTemplateQuarantine,
  dataType: PrintDataType.Product,
})

// 选择成品
function changeProductSelect(val: any) {
  filterData.product_id = val?.id || ''
  componentRemoteSearch.finish_product_code = val?.finish_product_code || ''
  componentRemoteSearch.finish_product_name = val?.finish_product_name || ''
  filterData.product_color_id = ''
  componentRemoteSearch.color_code = ''
  componentRemoteSearch.color_name = ''
}
// 选择颜色
function changeColor(val: any) {
  filterData.product_color_id = val?.id || ''
  componentRemoteSearch.color_code = val?.product_color_code || ''
  componentRemoteSearch.color_name = val?.product_color_name || ''
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="" class="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="质检日期:" width="350">
          <template #content>
            <SelectDate v-model="filterData.arrange_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="质检员:">
          <template #content>
            <SelectComponents v-model="filterData.quality_checker_id" style="width: 300px" api="GetUserDropdownList" :query="{ duty: EmployeeType.quarantine }" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="来源供应商:">
          <template #content>
            <SelectComponents
              v-model="filterData.supplier_id"
              :query="{ unit_type_id: `${BusinessUnitIdEnum.dyeFactory},${BusinessUnitIdEnum.otherSupplier},${BusinessUnitIdEnum.colorCardSupplier},${BusinessUnitIdEnum.logisticsSupplier},${BusinessUnitIdEnum.finishedProduct}` }"
              api="BusinessUnitSupplierEnumAll"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品编号:">
          <template #content>
            <SelectProductDialog
              v-model="filterData.product_id"
              :label-name="componentRemoteSearch.finish_product_code"
              field="finish_product_code"
              :query="{
                finish_product_code: componentRemoteSearch.finish_product_code,
              }"
              @on-input="
                (val) => (componentRemoteSearch.finish_product_code = val)
              "
              @change-value="changeProductSelect"
            />
            <!--            <SelectDialog -->
            <!--              v-model="filterData.product_id" -->
            <!--              label-field="finish_product_code" -->
            <!--              :label-name="componentRemoteSearch.finish_product_code" -->
            <!--              :query="{ -->
            <!--                finish_product_code: componentRemoteSearch.finish_product_code, -->
            <!--              }" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品名称', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      title: '成品名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品编号', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      title: '成品编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '成品编号', -->
            <!--                }, -->
            <!--              ]" -->
            <!--              @on-input=" -->
            <!--                (val) => (componentRemoteSearch.finish_product_code = val) -->
            <!--              " -->
            <!--              @change-value="changeProductSelect" -->
            <!--            /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称:">
          <template #content>
            <SelectProductDialog
              v-model="filterData.product_id"
              :label-name="componentRemoteSearch.finish_product_name"
              field="finish_product_name"
              :query="{
                finish_product_name: componentRemoteSearch.finish_product_name,
              }"
              @on-input="
                (val) => (componentRemoteSearch.finish_product_name = val)
              "
              @change-value="changeProductSelect"
            />
            <!--            <SelectDialog -->
            <!--              v-model="filterData.product_id" -->
            <!--              label-field="finish_product_name" -->
            <!--              :label-name="componentRemoteSearch.finish_product_name" -->
            <!--              :query="{ -->
            <!--                finish_product_name: componentRemoteSearch.finish_product_name, -->
            <!--              }" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品名称', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      title: '成品名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品编号', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      title: '成品编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  title: '成品名称', -->
            <!--                }, -->
            <!--              ]" -->
            <!--              @change-value="changeProductSelect" -->
            <!--              @on-input=" -->
            <!--                (val) => (componentRemoteSearch.finish_product_name = val) -->
            <!--              " -->
            <!--            /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="色号:">
          <template #content>
            <SelectDialog
              key="color2"
              v-model="filterData.product_color_id"
              :disabled="!filterData.product_id"
              :label-name="componentRemoteSearch.color_code"
              :query="{
                finish_product_id: filterData.product_id,
                product_color_code: componentRemoteSearch.color_code,
              }"
              :column-list="[
                {
                  field: 'product_color_code',
                  title: '色号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_code',
                      isEdit: true,
                      title: '色号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'product_color_name',
                  title: '颜色',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_name',
                      isEdit: true,
                      title: '颜色',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'product_color_code',
                  title: '色号',
                },
              ]"
              api="GetFinishProductColorDropdownList"
              label-field="product_color_code"
              @on-input="(val) => (componentRemoteSearch.color_code = val)"
              @change-value="changeColor"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="颜色:">
          <template #content>
            <SelectDialog
              key="color1"
              v-model="filterData.product_color_id"
              :disabled="!filterData.product_id"
              :label-name="componentRemoteSearch.color_name"
              :query="{
                finish_product_id: filterData.product_id,
                product_color_name: componentRemoteSearch.color_name,
              }"
              :column-list="[
                {
                  field: 'product_color_code',
                  title: '色号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_code',
                      isEdit: true,
                      title: '色号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'product_color_name',
                  title: '颜色',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_name',
                      isEdit: true,
                      title: '颜色',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'product_color_name',
                  title: '色号',
                },
              ]"
              api="GetFinishProductColorDropdownList"
              label-field="product_color_name"
              @on-input="(val) => (componentRemoteSearch.color_name = val)"
              @change-value="changeColor"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="缸号:" width="220">
          <template #content>
            <vxe-input
              v-model="filterData.dyelot_number"
              placeholder="缸号"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="卷号:" width="220">
          <template #content>
            <vxe-input
              v-model="filterData.volume_number"
              placeholder="卷号"
              clearable
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>

    <FildCard class="table-card-full">
      <Table ref="tablesRef1" :config="mainTableOptions.tableConfig" :table-list="mainList" :column-list="mainTableOptions.columnList">
        <template #order_no="{ row }">
          <el-link
            type="primary"
            :underline="false"
            @click="showDefectDetail(row)"
          >
            {{ row?.order_no }}
          </el-link>
        </template>
        <template #operate="{ row }">
          <PrintPopoverBtn
            :id="row.id"
            :key="row.id"
            print-btn-type="text"
            style="width: auto"
            api="GetQualityCheckDetail"
            :options="printOptions"
            :print-type="PrintType.PrintTemplateQuarantine"
            :data-type="PrintDataType.Product"
          />
        </template>
      </Table>
    </FildCard>
    <FildCard title="" class="table-card-bottom">
      <div class="flex flex-col h-full">
        <div class="file_card flex-auto">
          <Table class="h-full" :config="defectTableOptions.tableConfig" :table-list="defectTableOptions.datalist" :column-list="defectTableOptions.columnList" />
        </div>
      </div>
    </FildCard>
  </div>
</template>

<style lang="scss" scoped>
.items {
  display: flex;
  flex-wrap: wrap;

  .item {
    padding: 6px 14px;
    text-align: center;
    background: #fff;
    color: #0e7eff;
    border: 1px solid #0e7eff;
    cursor: pointer;
    user-select: none;
    font-size: 14px;
    min-width: 100px;

    &.active {
      background: #0e7eff;
      color: #fff;
    }
  }
}

::v-deep(.el-button) {
  margin-left: 0 !important;
  margin-right: 10px;
}
</style>
