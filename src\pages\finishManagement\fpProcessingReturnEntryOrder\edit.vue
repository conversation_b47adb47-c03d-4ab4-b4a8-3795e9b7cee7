<script setup lang="ts" name="FpProcessingReturnEntryOrderEdit">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import currency from 'currency.js'
import AccordingProcessingAdd from '../components/AccordingProcessingAdd.vue'
import FineSizeAdd from '../components/FineSizeProcessingOrderAdd.vue'
import { getFpmProcessReturnInOrder, updateFpmProcessReturnInOrder } from '@/api/fpProcessingReturnEntryOrder'
import { BusinessUnitIdEnum, EmployeeType, WarehouseTypeIdEnum } from '@/common/enum'
import { formatDate, formatLengthDiv, formatLengthMul, formatTwoDecimalsDiv, formatTwoDecimalsMul, formatWeightDiv, formatWeightMul } from '@/common/format'
import { deleteToast } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import useRouterList from '@/use/useRouterList'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { fieldApiKeyList } from '@/common/fieldApiKeyList'
import SelectBusinessDialog from '@/components/SelectBusinessDialog/index.vue'

const routerList = useRouterList()
const AccordingProcessingAddRef = ref()
const FineSizeAddRef = ref()
const formRef = ref()
const state = reactive<any>({
  formInline: {
    sale_system_id: '',
    biz_unit_id: '',
    warehouse_id: '',
    store_keeper_id: '',
    warehouse_in_time: new Date(),
    remark: '',
  },
  formRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    biz_unit_id: [{ required: true, message: '请选择加工单位', trigger: 'change' }],
    warehouse_id: [{ required: true, message: '请选择仓库', trigger: 'change' }],
    warehouse_in_time: [{ required: true, message: '请选择进仓日期', trigger: 'change' }],
  },
})

let uuid = 0
const finishProductionOptions = reactive<any>({
  multipleSelection: [],
  rowIndex: 0,
  tableConfig: {
    fieldApiKey: fieldApiKeyList.FpProcessingReturnEntryOrderEdit,
    showSlotNums: false,
    showSpanHeader: true,
    showCheckBox: true,
    filterStatus: false,
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
    handAllSelect: (val: any) => handAllSelect(val),
    handleSelectionChange: (val: any) => handleSelectionChange(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 采购匹数 采购总数量 进仓数量 空差 结算数量 采购辅助数量 进仓辅助数量 进仓金额
        if (['out_length'].includes(column.field))
          return sumNum(data, 'out_length', '')

        if (['total_weight'].includes(column.field))
          return sumNum(data, 'total_weight', '')

        if (['weight_error'].includes(column.field))
          return sumNum(data, 'weight_error', '')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '')

        if (['in_length', 'in_roll', 'out_roll', 'out_weight'].includes(column.field))
          return sumNum(data, column.field, '')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      field: 'base',
      fixed: 'left',
      childrenList: [
        {
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
          required: true,
        },
        {
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
          required: true,
        },
      ],
    },
    {
      title: '成品信息',
      field: 'pruduct_info',
      childrenList: [
        {
          field: 'parent_order_no',
          title: '加工出仓单单号',
          width: 150,
        },
        {
          field: 'customer_name',
          title: '所属客户',
          width: 100,
        },
        {
          field: '',
          soltName: 'product_color_code',
          title: '色号',
          width: 100,
          required: true,
        },
        {
          field: '',
          soltName: 'product_color_id',
          title: '颜色',
          width: 100,
          required: true,
        },
        // {
        //   field: 'dye_factory_color_code',
        //   soltName: 'dye_factory_color_code',
        //   title: '染厂色号',
        //   minWidth: 100,
        // },
        {
          field: 'dye_factory_dyelot_number',
          soltName: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        // {
        //   field: 'product_width',
        //   soltName: 'product_width',
        //   title: '成品幅宽',
        //   minWidth: 100,
        // },
        // {
        //   field: 'product_gram_weight',
        //   soltName: 'product_gram_weight',
        //   title: '成品克重',
        //   minWidth: 100,
        // },
        {
          field: 'product_level_id',
          soltName: 'product_level_id',
          title: '成品等级',
          minWidth: 100,
        },
        {
          field: 'product_remark',
          soltName: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          field: 'in_roll',
          soltName: 'in_roll',
          title: '进仓匹数',
          minWidth: 100,
          required: true,
        },
      ],
    },
    {
      title: '出仓信息',
      field: 'out_info',
      childrenList: [
        {
          field: 'out_roll',
          title: '出仓匹数',
          minWidth: 100,
        },
        {
          field: 'out_weight',
          title: '出仓数量',
          minWidth: 100,
        },
        {
          field: 'out_length',
          title: '出仓辅助数量',
          minWidth: 100,
        },
      ],
    },
    {
      title: '进仓数量信息',
      field: 'out_count_info',
      childrenList: [
        {
          field: 'total_weight',
          title: '进仓数量',
          minWidth: 100,
          required: true,
        },
        {
          field: 'weight_error',
          title: '空差',
          minWidth: 100,
        },
        {
          field: 'settle_weight',
          title: '结算数量',
          minWidth: 100,
        },
        {
          field: 'unit_name',
          title: '单位',
          minWidth: 100,
        },
      ],
    },
    {
      title: '进仓辅助数量信息',
      field: 'out_length_info',
      childrenList: [
        {
          field: 'in_length',
          soltName: 'in_length',
          title: '进仓辅助数量',
          minWidth: 100,
        },
      ],
    },
    {
      title: '单据备注信息',
      field: 'doc_remark',
      childrenList: [
        {
          field: 'remark',
          soltName: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      title: '',
      fixed: 'right',
      field: 'xima_info',
      childrenList: [
        {
          field: '',
          soltName: 'xima',
          title: '细码',
          width: 120,
        },
      ],
    },
    {
      title: '',
      fixed: 'right',
      field: 'operate_info',
      childrenList: [
        {
          field: '',
          soltName: 'operate',
          title: '操作',
          width: 100,
        },
      ],
    },
  ],
  // 确认添加带出数据
  handleSure: (list: any) => {
    // 自动带出字段
    //   所属客户 成品加工发料出仓单号 成品编号 成品名称 色号 颜色
    //   染厂色号 染厂缸号 成品幅宽 成品克重 成品等级 成品备注
    //   成品工艺 成品成分 出仓匹数 出仓数量 出仓辅助数量 单位
    list = list.map((item: any) => {
      return {
        ...item,
        uuid: ++uuid,
        quote_order_item_id: item.id,
        customer_id: item.customer_id, // 所属客户
        parent_order_no: item.parent_order_no, // 成品加工发料出仓单号
        product_code: item.product_code, // 成品编号
        product_name: item.product_name, // 成品名称
        product_id: item.product_id,
        product_color_code: item.product_color_code, // 色号
        product_color_id: item.product_color_id, // 颜色
        finishProductionColorId: item.product_color_id,
        product_color_name: item.product_color_name, // 颜色
        dye_factory_color_code: item.dye_factory_color_code, // 染厂色号
        dye_factory_dyelot_number: item.dye_factory_dyelot_number, // 染厂缸号
        product_width: item.product_width, // 成品幅宽
        product_gram_weight: item.product_gram_weight, // 成品克重
        product_level_id: item.product_level_id, // 成品等级
        product_remark: item.product_remark, // 成品备注
        product_craft: item.product_craft, // 成品工艺
        product_ingredient: item.product_ingredient, // 成品成分
        out_roll: item.out_roll, // 出仓匹数
        out_weight: item.total_weight, // 出仓数量
        out_length: item.out_length, // 出仓辅助数量
        unit_name: item.unit_name, // 单位
        unit_id: item.unit_id, // 单位
        in_roll: item.out_roll, // 进仓匹数
        item_fc_data: [],
        total_weight: 0,
        weight_error: 0,
        settle_weight: 0,
        in_length: 0,
      }
    })
    finishProductionOptions.datalist = [...finishProductionOptions.datalist, ...list]
  },
  // 确认录入，需要统计部分字段
  handleSureFineSize: (list: any) => {
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].item_fc_data = list
    // 自动计算
    // 进仓数量 = 所有细码数量之和
    // 空差 = 所有细码空差之和
    // 进仓辅助数量 = 所有细码辅助数量之和
    // 结算数量 =  进仓数量-空差
    let total_weight = 0
    let weight_error = 0
    let in_length = 0
    list.forEach((item: any) => {
      total_weight = currency(total_weight).add(item.base_unit_weight).value
      weight_error = currency(weight_error).add(item.weight_error).value
      in_length = currency(in_length).add(item.length).value
    })
    // const total_weight = list.reduce((pre: any, val: any) => pre + Number(val.base_unit_weight), 0)
    // const weight_error = list.reduce((pre: any, val: any) => pre + Number(val.weight_error), 0)

    const settle_weight = currency(total_weight).subtract(weight_error).value
    // const in_length = list.reduce((pre: any, val: any) => pre + Number(val.length), 0)
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].total_weight = total_weight
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].weight_error = weight_error
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].in_length = in_length
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].settle_weight = settle_weight
    // 进仓金额 = (数量单价*结算数量)+(辅助数量单价*进仓辅助数量)+其他金额
    computedTotalPrice()
    // const {unit_price,length_unit_price,other_price} = finishProductionOptions.datalist[finishProductionOptions.rowIndex]
    // finishProductionOptions.datalist[finishProductionOptions.rowIndex].total_price = (Number(unit_price)*settle_weight)+(Number(length_unit_price)*in_length)+Number(other_price)
  },
  //   删除
  handleRowDel: async ({ uuid }: any) => {
    const res = await deleteToast('是否确认删除该成品')
    if (!res)
      return
    const index = finishProductionOptions.datalist.findIndex((item: any) => item.uuid === uuid)
    finishProductionOptions.datalist.splice(index, 1)
  },
  //   批量操作
  handEdit: () => handEdit,
})
// 计算进仓金额
function computedTotalPrice() {
  // 进仓金额 = (数量单价*结算数量)+(辅助数量单价*进仓辅助数量)+其他金额
  // finishProductionOptions.datalist.forEach((item: any) => {
  //   const { unit_price, settle_weight, in_length, length_unit_price, other_price } = item
  //   const total_price = Number(unit_price) * Number(settle_weight) + Number(in_length) * Number(length_unit_price) + Number(other_price)
  //   item.total_price = Number.isNaN(total_price) ? 0 : total_price
  // })
}
// 色号和颜色联动
function setFinishProductColor(item: any, row: any) {
  row.finishProductionColorId = item?.id
  row.product_color_id = item?.id
  row.product_color_name = item?.product_color_name
  row.product_color_code = item?.product_color_code
}

// 表格选中事件
function handAllSelect({ records }: any) {
  finishProductionOptions.multipleSelection = records
}
function handleSelectionChange({ records }: any) {
  finishProductionOptions.multipleSelection = records
}

// 批量操作
const bulkShow = ref(false)
function handEdit() {
  if (finishProductionOptions.multipleSelection.length < 1)
    return ElMessage.error('请选择数据')

  bulkShow.value = true
}
const bulkSetting = ref<any>({})
const bulkList = reactive<any>([
  // {
  //   field: 'customer_id',
  //   field_name: 'customer_id',
  //   title: '所属客户',
  //   component: 'select',
  //   api: 'GetCustomerEnumList',
  // },
  // {
  //   field: 'finishProductionColorId',
  //   valueField: 'id',
  //   labelField: 'product_color_code',
  //   title: '色号',
  //   component: 'select',
  //   api: 'GetFinishProductColorDropdownList',
  // },
  // {
  //   field: 'finishProductionColorId',
  //   valueField: 'id',
  //   labelField: 'product_color_name',
  //   title: '颜色',
  //   component: 'select',
  //   api: 'GetFinishProductColorDropdownList',
  // },
  // {
  //   field: 'dye_factory_color_code',
  //   title: '染厂色号',
  //   component: 'input',
  // },
  // {
  //   field: 'dye_factory_dyelot_number',
  //   title: '染厂缸号',
  //   component: 'input',
  //   type: 'text',
  // },
  // {
  //   field: 'product_width',
  //   title: '成品幅宽',
  //   component: 'input',
  // },
  // {
  //   field: 'product_gram_weight',
  //   title: '成品克重(g)',
  //   component: 'input',
  //   type: 'integer',
  // },
  {
    field: 'product_level_id',
    field_name: 'product_level_id',
    title: '成品等级',
    component: 'select',
    api: 'GetInfoBaseFinishedProductLevelEnumList',
  },
  {
    field: 'product_remark',
    title: '成品备注',
    component: 'input',
    type: 'text',
  },
  {
    field: 'in_roll',
    title: '进仓匹数',
    component: 'input',
    type: 'float',
  },
  // {
  //   field: 'unit_price',
  //   title: '进仓数量单价',
  //   component: 'input',
  //   type: 'float',
  // },
  {
    field: 'in_length',
    title: '进仓辅助数量',
    component: 'input',
    type: 'float',
  },
  // {
  //   field: 'length_unit_price',
  //   title: '进仓辅助数量单价',
  //   component: 'input',
  //   type: 'float',
  // },
  // {
  //   field: 'other_price',
  //   title: '其他金额',
  //   component: 'input',
  //   type: 'float',
  // },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])
async function bulkSubmit({ row, value }: any, val: any) {
  const ids = finishProductionOptions.multipleSelection.map((item: any) => item.uuid)
  finishProductionOptions.datalist.map((item: any) => {
    if (ids.includes(item.uuid)) {
      item[row.field] = value[row.field]
      if (row.field === 'finishProductionColorId') {
        item.product_color_name = val?.product_color_name
        item.product_color_code = val?.product_color_code
        item.product_color_id = val?.id
      }
    }
  })
  if (row.field)
    ElMessage.success('设置成功')
  handBulkClose()
}
function handBulkClose() {
  bulkShow.value = false
}
// 根据资料添加
function showLibDialog() {
  AccordingProcessingAddRef.value.state.showModal = true
}

// 默认生成出仓条数数据
function showFineSizeDialog(row: any, rowIndex: number) {
  // FineSizeAddRef.value.state.showModal = true
  finishProductionOptions.rowIndex = rowIndex
  FineSizeAddRef.value.showDialog(
    {
      ...row,
      out_roll: row.in_roll,
    },
    state.formInline,
  )
}

function getSFRoll(row: any) {
  return row.item_fc_data.reduce((pre: any, val: any) => pre + Number(val.roll), 0)
}

// 新增提交
const { fetchData: addFetch, data: successData, success: addSuccess, msg: addMsg } = updateFpmProcessReturnInOrder()
// 提交所有数据
function submitAddAllData() {
  // 表单验证
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 成品信息必填项
      // 判断戏码匹数和还有辅助数量和
      // let ximaLengthFlag = true // xima必须和进仓匹数一致
      // let lengthFlag = true // 辅助数量和与分录行辅助数量不一致
      for (let i = 0; i < finishProductionOptions.datalist.length; i++) {
        if (!finishProductionOptions.datalist[i].product_code)
          return ElMessage.error('成品编号为必填项')

        if (!finishProductionOptions.datalist[i].product_name)
          return ElMessage.error('成品名称为必填项')

        if (!finishProductionOptions.datalist[i].product_color_name)
          return ElMessage.error('颜色为必填项')

        if (!finishProductionOptions.datalist[i].product_color_code)
          return ElMessage.error('色号为必填项')

        // if (!finishProductionOptions.datalist[i].dye_factory_dyelot_number)
        //   return ElMessage.error('染厂缸号为必填项')

        if (!Number(finishProductionOptions.datalist[i].in_roll))
          return ElMessage.error('进仓匹数为必填项')

        if (!Number(finishProductionOptions.datalist[i].total_weight))
          return ElMessage.error('进仓数量为必填项')

        if (finishProductionOptions.datalist[i]?.item_fc_data?.length) {
          let roll = 0
          let length = 0
          finishProductionOptions.datalist[i].item_fc_data.forEach((item: any) => {
            roll += Number(item.roll)
            length += Number(item.length)
          })
          roll = Number(roll.toFixed(2))
          length = Number(length.toFixed(2))
          // const roll = finishProductionOptions.datalist[i].item_fc_data.reduce((pre: any, val: any) => pre + Number(val.roll), 0)
          if (Number(finishProductionOptions.datalist[i].in_roll) !== roll)
            return ElMessage.error('进仓匹数与细码匹数总量不一致')

          // const length = finishProductionOptions.datalist[i].item_fc_data.reduce((pre: any, val: any) => pre + Number(val.length), 0)
          if (Number(finishProductionOptions.datalist[i].in_length) !== length)
            return ElMessage.error('进仓辅助数量与细码辅助数量总量不一致')
        }
        else {
          return ElMessage.error('进仓匹数与细码匹数总量不一致')
        }
      }
      // if (!ximaLengthFlag) {
      //   return ElMessage.error('进仓匹数与细码匹数总量不一致')
      // }
      // if (!lengthFlag) {
      //   return ElMessage.error('进仓辅助数量与细码辅助数量总量不一致')
      // }
      // 整理参数
      const query = {
        ...state.formInline,
        store_keeper_id: state.formInline.store_keeper_id || 0,
        warehouse_in_time: formatDate(state.formInline.warehouse_in_time),
        item_data: finishProductionOptions.datalist.map((item: any) => {
          const item_fc_data = item.item_fc_data.map((v: any) => {
            return {
              ...v,
              roll: formatTwoDecimalsMul(v.roll),
              length: formatLengthMul(v.length),
              weight_error: formatWeightMul(v.weight_error),
              paper_tube_weight: formatWeightMul(v.paper_tube_weight),
              base_unit_weight: formatWeightMul(v.base_unit_weight),
              settle_weight: formatWeightMul(v.settle_weight),
            }
          })
          return {
            ...item,
            in_roll: formatTwoDecimalsMul(Number(item.in_roll)),
            out_roll: formatTwoDecimalsMul(Number(item.out_roll)),
            out_weight: formatWeightMul(Number(item.out_weight)),
            total_weight: formatWeightMul(Number(item.total_weight)),
            weight_error: formatWeightMul(Number(item.weight_error)),
            settle_weight: formatWeightMul(Number(item.settle_weight)),
            out_length: formatLengthMul(Number(item.out_length)),
            in_length: formatLengthMul(Number(item.in_length)),
            item_fc_data,
          }
        }),
      }
      await addFetch(query)
      if (addSuccess.value) {
        ElMessage.success('提交成功')
        // 跳转到列表页
        getData()
        routerList.push({
          name: 'FpProcessingReturnEntryOrderDetail',
          query: {
            id: successData.value.id,
          },
        })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}
const tablesRef = ref()
watch(
  () => finishProductionOptions.datalist,
  () => {
    tablesRef.value.tableRef.updateFooter()
  },
  { deep: true },
)

const route = useRoute()
const { fetchData: DetailFetch, data: detailData } = getFpmProcessReturnInOrder()
onMounted(() => {
  getData()
})
async function getData() {
  await DetailFetch({ id: route.query.id })
  state.formInline = {
    ...detailData.value,
    store_keeper_id: detailData.value.store_keeper_id || '',
    warehouse_in_time: formatDate(detailData.value.warehouse_in_time),
  }
  finishProductionOptions.datalist = detailData.value?.item_data?.map((item: any) => {
    const item_fc_data = item.item_fc_data.map((v: any) => {
      return {
        uuid: ++uuid,
        ...v,
        roll: formatTwoDecimalsDiv(v.roll),
        length: formatLengthDiv(v.length),
        weight_error: formatWeightDiv(v.weight_error),
        paper_tube_weight: formatWeightDiv(v.paper_tube_weight),
        base_unit_weight: formatWeightDiv(v.base_unit_weight),
        settle_weight: formatWeightDiv(v.settle_weight),
      }
    })
    return {
      ...item,
      finishProductionColorId: item.product_color_id,
      in_roll: formatTwoDecimalsDiv(Number(item.in_roll)),
      out_roll: formatTwoDecimalsDiv(Number(item.out_roll)),
      out_weight: formatWeightDiv(Number(item.out_weight)),
      total_weight: formatWeightDiv(Number(item.total_weight)),
      weight_error: formatWeightDiv(Number(item.weight_error)),
      settle_weight: formatWeightDiv(Number(item.settle_weight)),
      out_length: formatLengthDiv(Number(item.out_length)),
      in_length: formatLengthDiv(Number(item.in_length)),
      item_fc_data,
    }
  })

  computedTotalPrice()
}

function changeSaleSystemId(item: any) {
  state.formInline.warehouse_id = item?.default_physical_warehouse
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" :disabled="finishProductionOptions.datalist.length ? false : true" @click="submitAddAllData">
        提交
      </el-button>
    </template>
    <el-form ref="formRef" :model="state.formInline" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem :required="true" label="营销体系名称:">
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents v-model="state.formInline.sale_system_id" api="GetSaleSystemDropdownListApi" label-field="name" value-field="id" clearable @change-value="changeSaleSystemId" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="加工单位名称:">
          <template #content>
            <el-form-item prop="biz_unit_id">
              <SelectBusinessDialog
                v-model="state.formInline.biz_unit_id"
                api="BusinessUnitSupplierEnumlist"
                :query="{
                  unit_type_id: BusinessUnitIdEnum.dyeFactory,
                }"
                :default-value="{
                  id: detailData.biz_unit_id,
                  name: detailData.biz_unit_name,
                }"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="仓库名称:">
          <template #content>
            <el-form-item prop="warehouse_id">
              <SelectComponents
                v-model="state.formInline.warehouse_id"
                api="GetPhysicalWarehouseDropdownList"
                :query="{ warehouse_type_id: WarehouseTypeIdEnum.finishProduction }"
                label-field="name" value-field="id" clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="进仓日期:">
          <template #content>
            <el-form-item prop="warehouse_in_time">
              <el-date-picker v-model="state.formInline.warehouse_in_time" type="date" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓管员:">
          <template #content>
            <el-form-item>
              <SelectComponents v-model="state.formInline.store_keeper_id" :query="{ duty: EmployeeType.warehouseManager }" api="GetEmployeeListEnum" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="备注:" copies="2">
          <template #content>
            <el-form-item prop="remark">
              <vxe-textarea v-model="state.formInline.remark" style="width: 100%" maxlength="500" show-word-count />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]" :tool-bar="true">
    <template #right-top>
      <el-button type="primary" @click="handEdit">
        批量操作
      </el-button>
      <el-button :disabled="!state.formInline.biz_unit_id" type="primary" @click="showLibDialog">
        根据加工出仓单添加
      </el-button>
    </template>
    <div v-show="finishProductionOptions.datalist.length">
      <Table ref="tablesRef" :config="finishProductionOptions.tableConfig" :table-list="finishProductionOptions.datalist" :column-list="finishProductionOptions.columnList">
        <!-- 所属客户 -->
        <!-- <template #customer_id="{ row }">
          <SelectComponents
            v-model="row.customer_name" disabled api="GetCustomerEnumList" label-field="name" value-field="name" clearable @change-value="val => {
              row.customer_id = val.id
              row.customer_name = val.name
            }"
          />
        </template> -->
        <!-- 色号 -->
        <template #product_color_code="{ row }">
          <SelectDialog
            v-model="row.finishProductionColorId"
            disabled
            :column-list="[
              {
                field: 'product_color_code',
                title: '色号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'product_color_code',
                    isEdit: true,
                    title: '色号',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'product_color_name',
                title: '颜色',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'product_color_name',
                    isEdit: true,
                    title: '颜色',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :table-column="[
              {
                field: 'product_color_code',
                title: '色号',
                defaultData: {
                  id: row.finishProductionColorId,
                  product_color_code: row.product_color_code,
                  product_color_name: row.product_color_name,
                },
              },
            ]"
            api="GetFinishProductColorDropdownList"
            label-field="product_color_code"
            value-field="id"
            @change-value="
              item => {
                setFinishProductColor(item, row)
              }
            "
          />
        </template>
        <!-- 颜色 -->
        <template #product_color_id="{ row }">
          <SelectDialog
            v-model="row.finishProductionColorId"
            disabled
            :column-list="[
              {
                field: 'product_color_code',
                title: '色号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'product_color_code',
                    isEdit: true,
                    title: '色号',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'product_color_name',
                title: '颜色',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'product_color_name',
                    isEdit: true,
                    title: '颜色',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :table-column="[
              {
                field: 'product_color_name',
                title: '颜色',
                defaultData: {
                  id: row.finishProductionColorId,
                  product_color_code: row.product_color_code,
                  product_color_name: row.product_color_name,
                },
              },
            ]"
            api="GetFinishProductColorDropdownList"
            label-field="product_color_name"
            value-field="id"
            @change-value="
              item => {
                setFinishProductColor(item, row)
              }
            "
          />
        </template>
        <!-- 染厂色号 -->
        <template #dye_factory_color_code="{ row }">
          <vxe-input v-model="row.dye_factory_color_code" disabled />
        </template>
        <!-- 染厂缸号 -->
        <template #dye_factory_dyelot_number="{ row }">
          <vxe-input v-model="row.dye_factory_dyelot_number" disabled />
        </template>
        <!-- 成品幅宽 -->
        <template #product_width="{ row }">
          <vxe-input v-model="row.product_width" disabled />
        </template>
        <!-- 成品克重 -->
        <template #product_gram_weight="{ row }">
          <vxe-input v-model="row.product_gram_weight" disabled type="text" />
        </template>
        <!-- 成品等级 -->
        <template #product_level_id="{ row }">
          <SelectComponents v-model="row.product_level_id" api="GetInfoBaseFinishedProductLevelEnumList" label-field="name" value-field="id" clearable />
        </template>
        <!-- 成品备注 -->
        <template #product_remark="{ row }">
          <vxe-input v-model="row.product_remark" maxlength="200" />
        </template>
        <!-- 进仓匹数 -->
        <template #in_roll="{ row }">
          <vxe-input v-model="row.in_roll" :max="row.out_roll" type="float" />
        </template>
        <!-- 单价 -->
        <template #unit_price="{ row }">
          <vxe-input v-model="row.unit_price" type="float" @change="computedTotalPrice" />
        </template>
        <!-- 进仓辅助数量 -->
        <template #in_length="{ row }">
          <vxe-input v-model="row.in_length" type="float" @change="computedTotalPrice" />
        </template>
        <!-- 辅助数量单价 -->
        <template #length_unit_price="{ row }">
          <vxe-input v-model="row.length_unit_price" type="float" @change="computedTotalPrice" />
        </template>
        <!-- 其他金额 -->
        <template #other_price="{ row }">
          <vxe-input v-model="row.other_price" type="float" @change="computedTotalPrice" />
        </template>
        <!-- 进仓金额 -->
        <template #total_price="{ row }">
          ￥{{ row.total_price }}
        </template>
        <!-- 备注 -->
        <template #remark="{ row }">
          <vxe-input v-model="row.remark" maxlength="200" type="text" />
        </template>
        <!-- 细码 -->
        <template #xima="{ row, rowIndex }">
          <!-- 进仓数量 -->
          <el-button v-if="row.in_roll" type="primary" link @click="showFineSizeDialog(row, rowIndex)">
            录入
            <span v-if="row.in_roll - getSFRoll(row) > 0" style="color: red">({{ row.in_roll - getSFRoll(row) }}条未录)</span>
            <span v-if="row.in_roll === getSFRoll(row)" style="color: #ccc">(已录入)</span>
          </el-button>
          <span v-else style="color: #ccc">请先输入退货匹数</span>
        </template>
        <!-- 操作 -->
        <template #operate="{ row }">
          <el-button type="primary" link @click="finishProductionOptions.handleRowDel(row)">
            删除
          </el-button>
        </template>
      </Table>
    </div>
    <div v-if="finishProductionOptions.datalist.length === 0" class="no_data">
      <el-icon :size="80">
        <MessageBox />
      </el-icon>
      <div class="text">
        请选择仓库
      </div>
    </div>
  </FildCard>
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose">
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
  </BulkSetting>
  <AccordingProcessingAdd ref="AccordingProcessingAddRef" :process_unit_id="state.formInline.biz_unit_id" @handle-sure="finishProductionOptions.handleSure" />
  <FineSizeAdd ref="FineSizeAddRef" @handle-sure="finishProductionOptions.handleSureFineSize" />
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}

.no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  user-select: none;
  color: #999;
}

.flex_button {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}
</style>
