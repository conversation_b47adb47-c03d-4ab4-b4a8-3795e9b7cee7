<script lang="ts" setup name="GreyClothProductionReturnEdit">
import { ElMessage } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import currency from 'currency.js'
import AddInventoryDialog from '../components/AddInventoryDialog.vue'
import YarnInformationDialog from '../components/AddYarnInformationDialog.vue'
import InventoryXima from '../components/InventoryXima.vue'
import SelectProductNo from '../components/SelectProductNo.vue'
import { getGfmProduceReturnOrder, updateGfmProduceReturnOrder } from '@/api/greyClothProductionReturn'
import { formatDate, formatPriceDiv, formatPriceMul, formatUnitPriceDiv, formatUnitPriceMul, formatWeightDiv, formatWeightMul, sumNum, sumTotal } from '@/common/format'
import { deepClone, isXimaAlreadyEntered } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import useRouterList from '@/use/useRouterList'
import { BusinessUnitIdEnum } from '@/common/enum'

const routerList = useRouterList()

const state = reactive<any>({
  form: {
    marketingSystem: '', // 营销体系
    consignee: '', // 货源单位
    destprovidername: '', // 接收单位
    date: '', // 退货日期
    remark: '',
  },
  formRules: {
    marketingSystem: [{ required: true, message: '请选择营销体系', trigger: 'blur' }],
    consignee: [{ required: true, message: '请选择货源单位', trigger: 'change' }],
    destprovidername: [{ required: true, message: '请选择接收单位', trigger: 'change' }],
    date: [{ required: true, message: '请选择退货日期', trigger: 'blur' }],
  },
  tableList: [],
  isInformation: true,
  multipleSelection: [],
  otherValue: '',
  yarnRatioList: [],
  yarnRatioList_total: [],
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
  supplier_name: '',
})

const tableConfig = ref({
  showSlotNums: true,
  showOperate: true,
  operateWidth: '80',
  footerMethod: (val: any) => FooterMethod(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  cellDBLClickEvent: (val: any) => cellDBLClickEvent(val, 1),
})

const tableConfig_other = ref({
  showSlotNums: true,
  height: 600,
  footerMethod: (val: any) => FooterMethodOnce(val),
  cellDBLClickEvent: (val: any) => cellDBLClickEvent(val, 2),
})

const tableConfig_other_two = ref({
  showSlotNums: true,
  showOperate: true,
  operateWidth: '80',
  height: 600,
  footerMethod: (val: any) => FooterMethodOther(val),
})

const tableRef = ref()

const tableRefes = ref()

const tableRefs = ref()

watch(
  () => state.tableList,
  () => {
    if (state.tableList?.length > 0) {
      state.tableList?.map((item: any) => {
        item.total_price = (Number(item?.process_single_unit_price) * Number(item?.total_weight) + Number(item?.other_price)).toFixed(2)
        item.average_weight = Number(item.roll) ? currency(item.total_weight).divide(item.roll).value : 0
        return item
      })
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
        tableRefs.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

watch(
  () => state.yarnRatioList_total,
  () => {
    if (state.yarnRatioList_total.length > 0) {
      state.yarnRatioList.map((item: any) => {
        item.actually_use_yarn_quantity = sumTotal(item.use_yarn_item_data, 'use_yarn_quantity')
        return item
      })
      nextTick(() => {
        tableRefes.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

const { fetchData: getFetch, data: fabricList } = getGfmProduceReturnOrder()

const route = useRoute()

onMounted(() => {
  getInfomation()
})

// 获取坯布信息
async function getInfomation() {
  await getFetch({ id: route.query.id })
}
const destprovidernameRef = ref()

watch(
  () => fabricList.value,
  () => {
    if (fabricList.value) {
      state.form.marketingSystem = fabricList.value.sale_system_id
      state.form.consignee = fabricList.value.return_unit_id
      state.form.destprovidername = fabricList.value.supplier_id
      state.form.date = fabricList.value.return_time
      state.form.remark = fabricList.value.remark
      state.tableList = fabricList.value?.item_data
      if (destprovidernameRef.value)
        destprovidernameRef.value.inputLabel = fabricList.value.supplier_name

      // 数据清洗，讲金额和数量格式化单位
      for (let i = 0; i < state.tableList?.length; i++) {
        state.tableList[i].process_single_unit_price = Number(formatUnitPriceDiv(state.tableList[i].process_single_unit_price))
        state.tableList[i].other_price = Number(formatPriceDiv(state.tableList[i].other_price))
        state.tableList[i].total_weight = formatWeightDiv(state.tableList[i]?.total_weight)
        state.tableList[i].roll = Number(formatPriceDiv(state.tableList[i].roll))
        state.tableList[i].avg_weight = formatWeightDiv(state.tableList[i]?.avg_weight)

        state.tableList[i].grey_fabric_gram_weight = state.tableList[i]?.grey_fabric_gram_weight.toString()
        for (let q = 0; q < state.tableList[i].item_fc_data?.length; q++) {
          state.tableList[i].item_fc_data[q].weight = Number(formatWeightDiv(state.tableList[i].item_fc_data[q].weight))
          state.tableList[i].item_fc_data[q].roll = Number(formatPriceDiv(state.tableList[i].item_fc_data[q].roll))
          state.tableList[i].item_fc_data[q].grey_fabric_stock_id = Number(state.tableList[i].item_fc_data[q].grey_fabric_stock_id)
          state.tableList[i].item_fc_data[q].id = state.tableList[i].item_fc_data[q].grey_fabric_stock_id
        }
        for (let j = 0; j < state.tableList[i].use_yarn_data?.length; j++) {
          state.tableList[i].use_yarn_data[j].actually_use_yarn_quantity = formatWeightDiv(state.tableList[i].use_yarn_data[j]?.actually_use_yarn_quantity)

          for (let c = 0; c < state.tableList[i].use_yarn_data[j].use_yarn_item_data?.length; c++) {
            if (state.tableList[i]?.use_yarn_data[j]?.use_yarn_item_data[c]?.use_yarn_quantity) {
              state.tableList[i].use_yarn_data[j].use_yarn_item_data[c].use_yarn_quantity = formatWeightDiv(state.tableList[i]?.use_yarn_data[j]?.use_yarn_item_data[c]?.use_yarn_quantity)
              state.tableList[i].use_yarn_data[j].use_yarn_item_data[c].grayId = state.tableList[i].id
              state.tableList[i].use_yarn_data[j].use_yarn_item_data[c].parent_id = state.tableList[i].use_yarn_data[j]?.id
              state.tableList[i].use_yarn_data[j].use_yarn_item_data[c].production_date = formatDate(state.tableList[i].use_yarn_data[j].use_yarn_item_data[c].production_date)

              // 自己造个id用来编辑方便判断数据
              if (state.tableList[i].use_yarn_data[j].use_yarn_item_data[c].rml_stock_id === 0) {
                const idNum = Math.floor(Math.random() * 10000)
                state.tableList[i].use_yarn_data[j].use_yarn_item_data[c].item_id = idNum
              }
            }
          }
        }
      }
    }
  },
)

const columnList = ref([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    fixed: 'left',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    fixed: 'left',
    minWidth: 150,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    minWidth: 150,
  },
  {
    field: 'grey_fabric_width',
    soltName: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 150,
  },
  {
    field: 'grey_fabric_gram_weight',
    soltName: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 150,
    // isWeight: true,
  },
  {
    field: 'raw_material_yarn_name',
    title: '原料纱名',
    minWidth: 150,
  },
  {
    field: 'raw_material_batch_num',
    title: '原料批号',
    minWidth: 150,
  },
  {
    field: 'raw_material_batch_brand',
    title: '原料品牌',
    minWidth: 150,
  },
  {
    field: 'unit_name',
    title: '单位',
    minWidth: 150,
  },
  {
    field: 'needle_size',
    title: '针寸数',
    minWidth: 150,
  },
  {
    field: 'yarn_batch',
    title: '纱批',
    minWidth: 150,
  },
  {
    field: 'gray_fabric_color_name',
    title: '织坯颜色',
    minWidth: 150,
  },
  {
    field: 'grey_fabric_level_name',
    title: '坯布等级',
    minWidth: 150,
  },
  {
    field: 'machine_number',
    title: '机台号',
    minWidth: 150,
  },
  {
    field: 'grey_fabric_remark',
    title: '坯布备注',
    minWidth: 150,
    // soltName: 'fabircRemark',
  },
  {
    field: 'source_code',
    title: '出坯单号',
    minWidth: 150,
  },
  {
    field: 'source_time',
    title: '出坯日期',
    minWidth: 150,
    is_date: true,
  },
  {
    field: 'produce_notice_order_no',
    title: '生产通知单号',
    minWidth: 150,
    soltName: 'orderCode',
  },
  {
    field: 'roll',
    title: '匹数',
    minWidth: 150,
    soltName: 'horsepower',
    required: true,
  },
  {
    field: 'remark',
    title: '细码',
    minWidth: 150,
    soltName: 'xima',
    required: true,
  },
  {
    field: 'total_weight',
    title: '总数量',
    minWidth: 150,
  },
  {
    field: 'average_weight',
    title: '平均数量',
    minWidth: 150,
  },
  {
    field: 'process_single_unit_price',
    title: '加工单价',
    minWidth: 150,
    soltName: 'unitPrice',
    required: true,
  },
  {
    field: 'other_price',
    title: '其他金额',
    minWidth: 150,
    soltName: 'otherPrice',
  },
  {
    field: 'total_price',
    title: '总金额',
    minWidth: 150,
  },

  {
    field: 'remark',
    title: '备注',
    minWidth: 150,
    soltName: 'remark',
  },
])

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${sumNum(data, 'roll')}`

      if (['total_weight'].includes(column.property))
        return `${sumNum(data, 'total_weight')}`

      if (['other_price'].includes(column.property))
        return `￥${sumNum(data, 'other_price')}`

      if (['total_price'].includes(column.property))
        return `￥${sumNum(data, 'total_price')}`

      return null
    }),
  ]
}

function FooterMethodOnce({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['use_yarn_quantity'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'use_yarn_quantity') as any)}`

      if (['actually_use_yarn_quantity'].includes(column.property))
        return `${sumNum(data, 'actually_use_yarn_quantity') as any}`

      return null
    }),
  ]
}

function FooterMethodOther({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['use_yarn_quantity'].includes(column.property))
        return `${sumNum(data, 'use_yarn_quantity') as any}`

      return null
    }),
  ]
}

const AddInventoryDialogRef = ref()

function handAdd() {
  AddInventoryDialogRef.value.state.filterData.warehouse_id = state.form.consignee

  AddInventoryDialogRef.value.state.showModal = true
}

// 添加坯布信息
function handleSureFabric(list: any) {
  list.forEach((item: any) => {
    const idNum = Math.floor(Math.random() * 10000)
    state.tableList.push({
      ...item,
      grey_fabric_code: item.grey_fabric_code,
      grey_fabric_name: item.grey_fabric_name,
      customer_id: item.customer_id,
      customer_name: item.customer_name,
      grey_fabric_width: item.grey_fabric_width,
      grey_fabric_gram_weight: item.grey_fabric_gram_weight,
      needle_size: item.needle_size,
      yarn_batch: item.yarn_batch,
      gray_fabric_color_id: item.gray_fabric_color_id,
      gray_fabric_color_name: item.gray_fabric_color_name,
      grey_fabric_level_id: item.grey_fabric_level_id,
      grey_fabric_level_name: item.grey_fabric_level_name,
      machine_number: item.machine_number,
      produce_notice_order_no: item?.produce_order_no,
      produce_notice_order_item_id: item?.produce_order_id,
      total_weight: 0,
      average_weight: 0,
      total_price: 0,
      //   order_code: '',
      source_code: item.source_code,
      source_time: item.source_time,
      roll: 0,
      process_single_unit_price: 0,
      other_price: 0,
      raw_material_yarn_name: item?.raw_material_yarn_name,
      raw_material_batch_num: item?.raw_material_batch_num,
      raw_material_batch_brand: item?.raw_material_batch_brand,
      supplier_id: item.supplier_id,
      remark: '',
      item_fc_data: [],
      grey_fabric_remark: item.source_remark,
      grey_fabric_id: item?.grey_fabric_id || 0,
      warehouse_sum_id: item?.id || 0,
      source_id: item.source_id,
      id: idNum,
      use_yarn_data: item?.production_notify_material_ratio || [],
    })
  })
  AddInventoryDialogRef.value.state.showModal = false
}

// 录入细码
const InventoryXimaRef = ref()

function handWrite(row: any, index: number) {
  if (row.roll === '')
    return ElMessage.error('请先输入匹数')

  const info = {
    grey_fabric_code: row.grey_fabric_code,
    grey_fabric_name: row.grey_fabric_name,
    customer_id: row.customer_id,
    customer_name: row.customer_name,
    yarn_batch: row.yarn_batch,
    supplier_id: row.supplier_id,
    gray_fabric_color_id: row.gray_fabric_color_id,
    grey_fabric_level_id: row.grey_fabric_level_id,
    raw_material_batch_brand: row.raw_material_batch_brand,
    raw_material_batch_num: row.raw_material_batch_num,
    raw_material_yarn_name: row.raw_material_yarn_name,
    source_remark: row.source_remark,
    warehouse_id: state.form.consignee,
    warehouse_sum_id: row?.warehouse_sum_id || 0,
  }
  InventoryXimaRef.value.state.info = info

  InventoryXimaRef.value.state.showModal = true
  InventoryXimaRef.value.state.ximaList = row?.item_fc_data || []
  InventoryXimaRef.value.state.canEnter = row.roll
  InventoryXimaRef.value.state.id = row.id
  InventoryXimaRef.value.state.rowIndex = index
}

function handleSureXima(val: any) {
  state.tableList?.map((item: any, index: number) => {
    if (index === val.rowIndex) {
      item.item_fc_data = val.ximaList
      item.total_weight = sumNum(val.ximaList, 'weight')
      item.average_weight = Number(item.roll) ? currency(item.total_weight).divide(item.roll).value : 0
      return item
    }
  })
  InventoryXimaRef.value.state.showModal = false
}

const ruleFormRef = ref()

const { fetchData: addPost, data: addData, success: addSuccess, msg: addMsg } = updateGfmProduceReturnOrder()

// 提交数据
async function handleSure() {
  if (!state.tableList.length)
    return ElMessage.error('至少添加一条坯布信息')

  const list = deepClone(state.tableList)

  for (let i = 0; i < list.length; i++) {
    if (list[i].roll === '')
      return ElMessage.error('匹数不可为空')

    if (Number(list[i].roll) - Number(sumNum(list[i]?.item_fc_data, 'roll')) !== 0 && Number(list[i].roll) !== 0)
      return ElMessage.error(`第${i + 1}行的细码必须录完`)

    if (list[i].process_single_unit_price === '')
      return ElMessage.error('加工单价不可为空')

    list[i].total_weight = Number(formatWeightMul(list[i]?.total_weight))
    list[i].total_price = Number(formatPriceMul(list[i]?.total_price))
    list[i].roll = Number(formatPriceMul(list[i]?.roll))

    list[i].process_single_unit_price = Number(formatUnitPriceMul(list[i].process_single_unit_price))
    list[i].other_price = Number(formatPriceMul(list[i].other_price))
    // 将坯布信息的数量乘以一千给后端
    for (let q = 0; q < list[i].item_fc_data?.length; q++) {
      if (list[i].item_fc_data[q].weight === '')
        return ElMessage.error('细码数量不可为空')

      if (list[i].item_fc_data[q].roll === '')
        return ElMessage.error('细码匹数不可为空')

      list[i].item_fc_data[q].roll = Number(formatPriceMul(list[i].item_fc_data[q].roll))
      list[i].item_fc_data[q].grey_fabric_stock_id = Number(list[i].item_fc_data[q].id)
      list[i].item_fc_data[q].weight = Number(formatWeightMul(list[i].item_fc_data[q].weight))
    }
    for (let j = 0; j < list[i].use_yarn_data?.length; j++) {
      list[i].use_yarn_data[j].actually_use_yarn_quantity = formatWeightMul(list[i].use_yarn_data[j]?.actually_use_yarn_quantity)

      if (!list[i].use_yarn_data[j].produce_order_use_yarn_id)
        list[i].use_yarn_data[j].produce_order_use_yarn_id = list[i].use_yarn_data[j].id

      for (let c = 0; c < list[i].use_yarn_data[j].use_yarn_item_data?.length; c++) {
        // list[i].use_yarn_data[j].use_yarn_item_data.produce_order_use_yarn_id = list[i].use_yarn_data[j].use_yarn_item_data.id
        if (list[i]?.use_yarn_data[j]?.use_yarn_item_data[c]?.use_yarn_quantity === '')
          return ElMessage.error('用纱数量不可为空')

        if (list[i]?.use_yarn_data[j]?.use_yarn_item_data[c]?.use_yarn_quantity) {
          list[i].use_yarn_data[j].use_yarn_item_data[c].use_yarn_quantity = formatWeightMul(list[i]?.use_yarn_data[j]?.use_yarn_item_data[c]?.use_yarn_quantity)
          list[i].use_yarn_data[j].use_yarn_item_data[c].production_date = formatDate(list[i].use_yarn_data[j].use_yarn_item_data[c].production_date)
        }
      }
    }
  }
  const query = {
    item_data: list,
    remark: state.form.remark,
    return_unit_id: state.form.consignee,
    return_time: formatDate(state.form.date),
    sale_system_id: state.form.marketingSystem,
    supplier_id: state.form.destprovidername,
    id: Number(route.query.id),
  }

  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(query)

      if (addSuccess.value) {
        ElMessage.success('成功')
        routerList.push({ name: 'GreyClothProductionReturnDetail', query: { id: addData.value.id } })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

function handDelete(index: number) {
  state.tableList.splice(index, 1)
}

function changeValue(val: any) {
  if (val === '') {
    state.yarnRatioList = []
    state.yarnRatioList_total = []
  }
}

const SelectProductNoRef = ref()

function focus(row: any, rowIndex: number) {
  SelectProductNoRef.value.state.showModal = true
  SelectProductNoRef.value.state.rowIndex = rowIndex
}

function handSelectProductNo(val: any, rowIndex: number) {
  state.tableList?.map((item: any, index: number) => {
    if (rowIndex === index) {
      item.produce_notice_order_no = val[0].order_no
      item.produce_notice_order_item_id = val[0].id
      item.use_yarn_data = val[0].production_notify_material_ratio
      item.use_yarn_data?.map((it: any) => {
        it.use_yarn_item_data = []
        return item
      })
      return item
    }
  })
  SelectProductNoRef.value.state.showModal = false
}

const bulkShow = ref(false)

const bulkSetting = ref<any>({})

function bulkHand() {
  bulkShow.value = true
}

function handBulkClose() {
  bulkShow.value = false
}

async function bulkSubmit({ row, value }: any) {
  if (state.multipleSelection?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  state.tableList?.map((item: any) => {
    if (item?.selected) {
      item[row.field] = value[row.field]
      if (row.field === 'produce_notice_order_no')
        item.produce_notice_order_item_id = Number(state.otherValue)

      return item
    }
  })
  bulkShow.value = false
  ElMessage.success('设置成功')
}

const bulkList = reactive<any>([
  {
    field: 'produce_notice_order_no',
    title: '生产通知单号',
    component: 'select',
    labelField: 'order_no',
    valueField: 'order_no',
    api: 'getProductionNotifyOrderList',
  },
  {
    field: 'roll',
    title: '匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'process_single_unit_price',
    title: '单价',
    component: 'input',
    type: 'float',
  },
  {
    field: 'other_price',
    title: '其他金额',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

const YarnInformationDialogRef = ref()

function cellDBLClickEvent(val: any, nums: number) {
  if (nums === 1) {
    if (!val.row.use_yarn_data?.length)
      return ElMessage.error('请先选择生产通知单号')

    state.yarnRatioList = val.row?.use_yarn_data
    state.yarnRatioList_total = flattenUseYarnItemData(val.row.use_yarn_data)
    YarnInformationDialogRef.value.state.grayIndex = val.rowIndex
    YarnInformationDialogRef.value.state.grayId = val.row.id
  }
  else {
    const arr = deepClone(val?.row.use_yarn_item_data || [])
    YarnInformationDialogRef.value.state.showModal = true
    YarnInformationDialogRef.value.state.filterData.source_id = state.tableList[YarnInformationDialogRef.value.state.grayIndex].source_id
    YarnInformationDialogRef.value.state.list = arr
    YarnInformationDialogRef.value.state.parent_id = val.row.id
    YarnInformationDialogRef.value.state.rowIndex = val.rowIndex

    YarnInformationDialogRef.value.state.info = {
      yarn_ratio: formatPriceDiv(val.row.yarn_ratio),
      yarn_loss: formatPriceDiv(val.row.yarn_loss),
      total_weight: Number(state.tableList[YarnInformationDialogRef.value.state.grayIndex].total_weight),
      source_code: state.tableList[YarnInformationDialogRef.value.state.grayIndex].source_code,
      source_time: state.tableList[YarnInformationDialogRef.value.state.grayIndex].source_time,
      grey_fabric_code: state.tableList[YarnInformationDialogRef.value.state.grayIndex].grey_fabric_code,
      grey_fabric_name: state.tableList[YarnInformationDialogRef.value.state.grayIndex].grey_fabric_name,
      raw_material_code: state.yarnRatioList[YarnInformationDialogRef.value.state.rowIndex].raw_material_code,
      raw_material_name: state.yarnRatioList[YarnInformationDialogRef.value.state.rowIndex].raw_material_name,
    }
  }
}

function handAddYarn(info: any) {
  state.tableList.forEach((item: any) => {
    if (item.id === info.grayId) {
      item.use_yarn_data.map((it: any, index: number) => {
        if (index === info.rowIndex) {
          it.use_yarn_item_data = deepClone(info.list || [])
          return it
        }
        return item
      })
    }
  })
  YarnInformationDialogRef.value.state.showModal = false
  state.yarnRatioList_total = flattenUseYarnItemData(state.tableList[info.grayIndex].use_yarn_data)
}

// 用纱信息汇总删除
function handDelteToatalItems(row: any, rowIndex: number) {
  // TODO：删除数组里的这里信息
  state.yarnRatioList_total.splice(rowIndex, 1)
  if (row.rml_stock_id !== 0)
    state.tableList = removeDuplicateRmlStockIds(state.tableList, row.grayId, row.parent_id, row.rml_stock_id)
  else
    state.tableList = removeHandItems(state.tableList, row.grayId, row.parent_id, row.item_id)
}

// TODO:先用坯布信息的id进行匹配，再用用纱比例的id进行匹配，再用里面的汇总id进行匹配删除
function removeDuplicateRmlStockIds(arr: any, grayId: number, produce_order_use_yarn_id: number, rml_stock_id: number) {
  arr.forEach((item: any) => {
    if (item.id === grayId) {
      item.use_yarn_data.forEach((useYarn: any) => {
        if (useYarn.id === produce_order_use_yarn_id) {
          useYarn.use_yarn_item_data.forEach((itemData: any, index: number) => {
            if (itemData.rml_stock_id === rml_stock_id)
              useYarn.use_yarn_item_data.splice(index, 1)
          })
        }
      })
    }
  })
  return arr
}

// TODO:原理同上，只不过这个方法是应对手动添加的汇总信息
function removeHandItems(arr: any, grayId: number, produce_order_use_yarn_id: number, rml_stock_id: number) {
  arr?.forEach((item: any) => {
    if (item.id === grayId) {
      item.use_yarn_data.forEach((useYarn: any) => {
        if (useYarn.id === produce_order_use_yarn_id) {
          useYarn.use_yarn_item_data.forEach((itemData: any, index: number) => {
            if (itemData.item_id === rml_stock_id)
              useYarn.use_yarn_item_data.splice(index, 1)
          })
        }
      })
    }
  })
  return arr
}

// TODO:将数据源摊平
function flattenUseYarnItemData(arr: any) {
  const result: any = []

  arr?.forEach((item: any) => {
    const useYarnItemData = item.use_yarn_item_data?.map((itemData: any) => Object.entries(itemData).reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {}))
    result.push(...useYarnItemData)
  })

  return result
}

// 用纱信息修改则tableList数据源跟着改动
function handChange(val: any, row: any, type: number) {
  if (row.rml_stock_id !== 0)
    state.tableList = updateUseYarnItemData(state.tableList, row.grayId, row.parent_id, row.rml_stock_id, val.value, type)
  else
    state.tableList = updateHandItemData(state.tableList, row.grayId, row.parent_id, row.item_id, val.value, type)
}

// 汇总信息编辑
function updateUseYarnItemData(arr: any, uuid: number, produce_order_use_yarn_id: number, rml_stock_id: number, nums: string, type: number) {
  const index = arr.findIndex((item: any) => item.id === uuid)
  if (index !== -1) {
    const useYarnIndex = arr[index].use_yarn_data.findIndex((useYarn: any) => useYarn.id === produce_order_use_yarn_id)
    if (useYarnIndex !== -1) {
      const itemDataIndex = arr[index].use_yarn_data[useYarnIndex].use_yarn_item_data.findIndex((itemData: any) => itemData.rml_stock_id === rml_stock_id)
      if (itemDataIndex !== -1) {
        if (type === 1)
          arr[index].use_yarn_data[useYarnIndex].use_yarn_item_data[itemDataIndex].use_yarn_quantity = nums
        else
          arr[index].use_yarn_data[useYarnIndex].use_yarn_item_data[itemDataIndex].remark = nums
      }
    }
  }
  return arr
}

// TODO:原理同上只不过也是应对汇总信息收到添加的数据
function updateHandItemData(arr: any, uuid: number, produce_order_use_yarn_id: number, rml_stock_id: number, nums: string, type: number) {
  const index = arr.findIndex((item: any) => item.id === uuid)
  if (index !== -1) {
    const useYarnIndex = arr[index].use_yarn_data.findIndex((useYarn: any) => useYarn.id === produce_order_use_yarn_id)
    if (useYarnIndex !== -1) {
      const itemDataIndex = arr[index].use_yarn_data[useYarnIndex].use_yarn_item_data.findIndex((itemData: any) => itemData.item_id === rml_stock_id)
      if (itemDataIndex !== -1) {
        if (type === 1)
          arr[index].use_yarn_data[useYarnIndex].use_yarn_item_data[itemDataIndex].use_yarn_quantity = nums
        else
          arr[index].use_yarn_data[useYarnIndex].use_yarn_item_data[itemDataIndex].remark = nums
      }
    }
  }
  return arr
}

const yarnRatio_columnList = ref([
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 150,
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 150,
  },
  {
    field: 'raw_material_brand',
    title: '原料品牌',
    minWidth: 150,
  },
  {
    field: 'raw_material_batch_number',
    title: '原料批号',
    minWidth: 150,
  },
  {
    field: 'color_scheme',
    title: '原料颜色',
    minWidth: 150,
  },
  {
    field: 'yarn_ratio',
    title: '用纱比例(%)',
    minWidth: 150,
    isPrice: true,
  },
  {
    field: 'yarn_loss',
    title: '用纱损耗(%)',
    minWidth: 150,
    isPrice: true,
  },
  {
    field: 'use_yarn_quantity',
    title: '用纱量',
    minWidth: 150,
    isWeight: true,
  },
  {
    field: 'actually_use_yarn_quantity',
    title: '实际用纱量',
    minWidth: 150,
    soltName: 'actually_use_yarn_quantity',
  },
  {
    field: 'unit_name',
    title: '单位',
    minWidth: 150,
  },
])

const yarnRatio_total = ref([
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 150,
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 150,
  },
  {
    field: 'unit_name',
    title: '织厂名称',
    minWidth: 150,
  },
  {
    field: 'supplier_name',
    title: '供应商',
    minWidth: 150,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    minWidth: 150,
  },
  {
    field: 'brand',
    title: '原料品牌',
    minWidth: 150,
  },
  {
    field: 'batch_num',
    title: '原料批号',
    minWidth: 150,
  },
  {
    field: 'color_scheme',
    title: '原料颜色',
    minWidth: 150,
  },
  {
    field: 'level_name',
    title: '原料等级',
    minWidth: 150,
  },
  {
    field: 'rml_remark',
    title: '原料备注',
    minWidth: 150,
  },
  {
    field: 'measurement_unit_name',
    title: '单位',
    minWidth: 100,
  },
  {
    field: 'production_date',
    title: '生产日期',
    minWidth: 150,
  },
  {
    field: 'spinning_type',
    title: '纺纱类型',
    minWidth: 150,
  },
  {
    field: 'cotton_origin',
    title: '棉花产地',
    minWidth: 150,
  },
  // {
  //   field: '',
  //   title: '棉纱产地',
  //   minWidth: 150,
  // },
  {
    field: 'carton_num',
    title: '装箱单号',
    minWidth: 150,
  },
  {
    field: 'fapiao_num',
    title: '发票号',
    minWidth: 150,
  },
  // {
  //   field: '',
  //   title: '进仓日期',
  //   minWidth: 150,
  // },
  {
    field: 'use_yarn_quantity',
    title: '用纱数量',
    minWidth: 100,
    soltName: 'use_yarn_quantity',
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
    soltName: 'remark',
  },
])
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem required label="营销体系:">
          <template #content>
            <el-form-item prop="marketingSystem">
              <SelectComponents v-model="state.form.marketingSystem" api="AdminsaleSystemgetSaleSystemDropdownList" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="货源单位:">
          <template #content>
            <el-form-item prop="consignee">
              <SelectDialog
                v-model="state.form.consignee"
                :label-name="fabricList.return_unit_name"
                api="BusinessUnitSupplierEnumlist"
                :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory, name: componentRemoteSearch.name }"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-input="val => (componentRemoteSearch.name = val)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="接收单位:">
          <template #content>
            <el-form-item prop="destprovidername">
              <SelectDialog
                v-model="state.form.destprovidername"
                :label-name="fabricList.supplier_name"
                :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory, name: componentRemoteSearch.supplier_name }"
                api="BusinessUnitSupplierEnumlist"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-input="val => (componentRemoteSearch.supplier_name = val)"
              />
              <!-- <SelectComponents
                :query="{ unit_type_id: 11 }"
                style="width: 300px"
                api="BusinessUnitSupplierEnumlist"
                label-field="name"
                value-field="id"
                v-model="state.form.destprovidername"
                clearable
              /> -->
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="退货日期:">
          <template #content>
            <el-form-item prop="date">
              <el-date-picker v-model="state.form.date" type="date" placeholder="退货日期" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据备注:" copies="2">
          <template #content>
            <el-form-item prop="remark">
              <el-input v-model="state.form.remark" placeholder="单据备注" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="坯布信息" :tool-bar="false" class="mt-[5px]">
    <template #right-top>
      <el-button type="primary" @click="bulkHand">
        批量操作
      </el-button>
      <el-button :disabled="state.form.marketingSystem === '' || state.form.consignee === ''" type="primary" @click="handAdd">
        从库存中添加
      </el-button>
    </template>
    <Table ref="tableRef" :config="tableConfig" :table-list="state.tableList" :column-list="columnList">
      <template #orderCode="{ row, rowIndex }">
        <SelectComponents
          v-model="row.produce_notice_order_no"
          api="getProductionNotifyOrderList"
          label-field="order_no"
          value-field="order_no"
          clearable
          visible-change-close
          @change-value="val => changeValue(val)"
          @focus="focus(row, rowIndex)"
        />
      </template>
      <template #horsepower="{ row }">
        <vxe-input v-model="row.roll" :min="0" type="float" placeholder="必填" />
      </template>
      <template #xima="{ row, rowIndex }">
        <div class="flex items-center">
          <el-button type="primary" text link @click="handWrite(row, rowIndex)">
            录入
          </el-button>
          <div v-if="isXimaAlreadyEntered(row, { rollKey: 'roll', weightKey: 'total_weight' })" class="text-[#b5b39f] ml-[5px]">
            (已录完)
          </div>
          <div v-else class="text-[#efa6ae] ml-[5px]">
            ({{ (row?.roll - Number(sumNum(row?.item_fc_data, 'roll'))).toFixed(2) }}匹未录)
          </div>
        </div>
      </template>
      <template #unitPrice="{ row }">
        <vxe-input v-model="row.process_single_unit_price" type="float" placeholder="必填">
          <template #prefix>
            ￥
          </template>
        </vxe-input>
      </template>
      <template #otherPrice="{ row }">
        <vxe-input v-model="row.other_price" type="float" placeholder="请输入">
          <template #prefix>
            ￥
          </template>
        </vxe-input>
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" placeholder="请输入" />
      </template>
      <template #operate="{ rowIndex }">
        <el-button text type="danger" @click="handDelete(rowIndex)">
          删除
        </el-button>
      </template>
      <template #grey_fabric_width="{ row }">
        {{ row?.grey_fabric_width }} {{ row?.grey_fabric_width_unit_name }}
      </template>
      <template #grey_fabric_gram_weight="{ row }">
        {{ row?.grey_fabric_gram_weight }} {{ row?.grey_fabric_gram_weight_unit_name }}
      </template>
    </Table>
  </FildCard>
  <FildCard title="用纱比例" :tool-bar="false" class="mt-[5px]">
    <div class="mb-[20px]">
      提示：（双击行从库存中选择用纱）
    </div>
    <Table ref="tableRefs" :config="tableConfig_other" :table-list="state.yarnRatioList" :column-list="yarnRatio_columnList">
      <template #actually_use_yarn_quantity="{ row }">
        {{ sumNum(row?.use_yarn_item_data, 'use_yarn_quantity') }}
      </template>
    </Table>
  </FildCard>
  <FildCard title="用纱信息汇总" :tool-bar="false" class="mt-[5px]">
    <Table ref="tableRefes" :config="tableConfig_other_two" :table-list="state.yarnRatioList_total" :column-list="yarnRatio_total">
      <template #use_yarn_quantity="{ row }">
        <vxe-input v-model="row.use_yarn_quantity" type="float" min="0" placeholder="请输入" @blur="val => handChange(val, row, 1)" />
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" placeholder="请输入" @blur="val => handChange(val, row, 2)" />
      </template>
      <template #operate="{ row, rowIndex }">
        <el-button text type="danger" @click="handDelteToatalItems(row, rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <AddInventoryDialog ref="AddInventoryDialogRef" @handle-sure="handleSureFabric" />
  <InventoryXima ref="InventoryXimaRef" @handle-sure="handleSureXima" />
  <BulkSetting v-model:otherValue="state.otherValue" v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
  <YarnInformationDialog ref="YarnInformationDialogRef" @handle-sure="handAddYarn" />
  <SelectProductNo ref="SelectProductNoRef" @handle-sure="handSelectProductNo" />
</template>

<style></style>
