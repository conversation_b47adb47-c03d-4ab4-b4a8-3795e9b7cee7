<script setup lang="ts" name="OtherReceivableOrder">
import { onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { Plus } from '@element-plus/icons-vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import { debounce, deleteToast, getFilterData } from '@/common/util'
import { formatDate, formatTwoDecimalsDiv, formatWeightDiv } from '@/common/format'
import { shouldCollectOrderList, updateAuditStatusPass, updateAuditStatusWait } from '@/api/otherReceivableOrder'

// import { BusinessUnitIdEnum } from '@/common/enum'
import FildCard from '@/components/FildCard.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
})

const router = useRouter()
const { fetchData, data, total, page, size, loading, handleSizeChange, handleCurrentChange } = shouldCollectOrderList()
const mainOptions = reactive<any>({
  multipleSelection: [],
  tableConfig: {
    showSlotNums: true,
    loading,
    showPagition: true,
    page,
    size,
    total,
    showCheckBox: true,
    showOperate: true,
    operateWidth: '8%',
    height: '100%',
    showSort: false,
    fieldApiKey: 'OtherReceivableOrderIndex',
    handleSizeChange,
    handleCurrentChange,
    handAllSelect: (val: any) => mainOptions.handAllSelect(val),
    handleSelectionChange: (val: any) => mainOptions.handleSelectionChange(val),
  },
  mainList: [],
  columnList: [
    {
      sortable: true,
      field: 'order_no',
      soltName: 'order_no',
      title: '单据编号',
      fixed: 'left',
      width: '8%',
    },
    {
      sortable: true,
      field: 'customer_name',
      title: '客户名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'sale_system_name',
      title: '营销体系名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'sale_user_name',
      title: '销售员',
      minWidth: 100,
    },
    // {
    //   sortable: true,
    //   field: 'total_settle_money',
    //   title: '销售金额',
    //   minWidth: 100,
    // },
    {
      sortable: true,
      field: 'total_should_collect_money',
      title: '应收金额',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_collected_money',
      title: '实收金额',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_uncollect_money',
      title: '未收金额',
      minWidth: 100,
    },
    // {
    //   sortable: true,
    //   field: 'roll',
    //   title: '匹数',
    //   minWidth: 100,
    // },
    {
      sortable: true,
      field: 'weight',
      title: '数量',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'order_remark',
      title: '备注',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'creator_name',
      title: '创建人',
      width: 100,
    },
    {
      sortable: true,
      field: 'create_time',
      title: '创建时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'update_user_name',
      title: '修改人',
      width: 100,
    },
    {
      sortable: true,
      field: 'update_time',
      title: '修改时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'auditor_name',
      title: '审核人',
      width: 100,
    },
    {
      sortable: true,
      field: 'audit_date',
      title: '审核时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'audit_status',
      title: '单据状态',
      showOrder_status: true,
      soltName: 'audit_status',
      fixed: 'right',
      width: '5%',
    },
  ],
  // 表格选中事件
  handAllSelect: ({ records }: any) => {
    mainOptions.multipleSelection = records
  },
  handleSelectionChange: ({ records }: any) => {
    mainOptions.multipleSelection = records
  },
})
// 审核
const { fetchData: auditFetch, success: auditSuccess, msg: auditMsg } = updateAuditStatusPass()
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id.toString() })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
const { fetchData: cancelFetch, success: cancelSuccess, msg: cancelMsg } = updateAuditStatusWait()
async function handUnApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
// 详情
function handDetail(row: any) {
  router.push({
    name: 'OtherReceivableOrderDetail',
    query: {
      id: row.id,
    },
  })
}
// 编辑
function handEdit(row: any) {
  router.push({
    name: 'OtherReceivableOrderEdit',
    query: {
      id: row.id,
    },
  })
}
// 侦听参数获取数据
const filterData = reactive<any>({
  audit_status: [],
})
// 日期回调
const order_time = ref()
// 获取列表数据
async function getData() {
  const query = {
    ...filterData,
  }
  if (query.audit_status.length)
    query.audit_status = query.audit_status.join(',')

  if (order_time?.value?.length) {
    query.start_order_time = formatDate(order_time.value[0])
    query.end_order_time = formatDate(order_time.value[1])
  }
  await fetchData(getFilterData({ ...query }))
}

watch(
  () => data.value,
  () => {
    mainOptions.mainList
      = data.value?.list?.map((item: any) => {
        return {
          ...item,
          origin_price: formatTwoDecimalsDiv(item.origin_price),
          total_settle_money: formatTwoDecimalsDiv(item.total_settle_money),
          total_collected_money: formatTwoDecimalsDiv(item.total_collected_money),
          total_should_collect_money: formatTwoDecimalsDiv(item.total_should_collect_money),
          total_uncollect_money: formatTwoDecimalsDiv(item.total_uncollect_money),
          roll: formatTwoDecimalsDiv(item.roll),
          weight: formatWeightDiv(item.weight),
        }
      }) || []
  },
  { deep: true },
)

function changeDate() {
  // order_time.value = [row.date_min, row.date_max]
  getData()
}
// 初始化数据
onMounted(() => {
  getData()
})
watch(
  filterData,
  debounce(() => {
    getData()
  }, 400),
)

function handAdd() {
  router.push({
    name: 'OtherReceivableOrderAdd',
  })
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="" class="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <vxe-input v-model="filterData.order_no" style="width: 100%" placeholder="单据编号" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:">
          <template #content>
            <SelectDialog
              v-model="filterData.customer_id"
              api="GetCustomerEnumList"
              :editable="true"
              :query="{ name: componentRemoteSearch.customer_name }"
              :column-list="[
                {
                  title: '客户编号',
                  minWidth: 100,
                  required: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '客户编号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '客户名称',
                  minWidth: 100,
                  colGroupHeader: true,
                  required: true,
                  childrenList: [
                    {
                      isEdit: true,
                      field: 'name',
                      title: '客户名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '电话',
                  colGroupHeader: true,
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'phone',
                      isEdit: true,
                      title: '电话',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '销售员',
                  minWidth: 100,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'seller_name',
                      title: '销售员',
                      soltName: 'seller_name',
                      isEdit: true,
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="val => (componentRemoteSearch.customer_name = val)"
            />
          <!-- <SelectComponents style="width: 100%" api="GetCustomerEnumList" label-field="name" value-field="id" v-model="filterData.customer_id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据时间:" width="310">
          <template #content>
            <SelectDate v-model="order_time" style="width: 100%" @change-date="changeDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:" width="310">
          <template #content>
            <SelectComponents v-model="filterData.audit_status" multiple api="GetAuditStatusEnum" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <!-- 表格信息 -->
    <FildCard class="table-card-full" :tool-bar="true">
      <template #right-top>
        <!-- <BottonExcel :loading="mainOptions.exportOptions.loadingExport" @onClickExcel="mainOptions.exportOptions.handleExport" title="导出文件"></BottonExcel> -->
        <el-button v-has="'OtherReceivableOrder_add'" style="margin-left: 10px" type="primary" :icon="Plus" @click="handAdd">
          新建
        </el-button>
      </template>
      <Table :config="mainOptions.tableConfig" :table-list="mainOptions.mainList" :column-list="mainOptions.columnList">
        <template #order_no="{ row }">
          <el-link type="primary" :underline="false" @click="handDetail(row)">
            {{
              row.order_no
            }}
          </el-link>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link v-has="'OtherReceivableOrder_detail'" type="primary" :underline="false" @click="handDetail(row)">
              查看
            </el-link>
            <el-link v-if="row.audit_status === 1 || row.audit_status === 3" v-has="'OtherReceivableOrder_edit'" :underline="false" type="primary" @click="handEdit(row)">
              编辑
            </el-link>
            <el-link v-if="row.audit_status === 1" v-has="'OtherReceivableOrder_pass'" :underline="false" type="primary" @click="handAudit(row)">
              审核
            </el-link>
            <el-link v-if="row.audit_status === 2" v-has="'OtherReceivableOrder_wait'" :underline="false" type="primary" @click="handUnApproved(row)">
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
  </div>
</template>

<style></style>
