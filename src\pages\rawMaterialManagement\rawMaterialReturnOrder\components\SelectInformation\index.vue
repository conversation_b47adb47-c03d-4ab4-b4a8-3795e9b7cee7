<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { GetGawMaterialListMenu } from '@/api/rawMatableterialInventory'
import { formatDate } from '@/common/format'
import { debounce, getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import FildCard from '@/components/FildCard.vue'
import { fieldApiKeyList } from '@/common/fieldApiKeyList'

export interface Props {
  // eslint-disable-next-line vue/prop-name-casing
  unit_id: number | string
}

const props = withDefaults(defineProps<Props>(), {
  unit_id: 0,
})

const emit = defineEmits(['submit'])

const modelValue = defineModel({
  default: false,
  required: true,
})

const state = reactive({
  filterData: {
    raw_material_name: '',
    raw_material_code: '',
    brand: '',
    craft: '',
    color_scheme: '',
    enter_date: '',
    production_date: '',
    batch_num: '',
    supplier_id: '',
    customer_id: '',
    enter_start_date: '',
    enter_end_date: '',
    production_start_date: '',
    production_end_date: '',
  },
  tableList: [],
})

const {
  fetchData: fetchDataList,
  data: dataList,
  total,
  page,
  size,
  loading,
  handleSizeChange,
  handleCurrentChange,
} = GetGawMaterialListMenu()
async function getData() {
  const filterData = {
    ...state.filterData,
    unit_id: Number.parseInt(props.unit_id as string),
  }
  await fetchDataList(getFilterData(filterData))
}

watch(
  () => [modelValue.value, props.unit_id],
  () => {
    if (modelValue.value) {
      getData()
    }
    else {
      state.filterData = {
        raw_material_name: '',
        raw_material_code: '',
        brand: '',
        craft: '',
        color_scheme: '',
        enter_date: '',
        production_date: '',
        batch_num: '',
        supplier_id: '',
        customer_id: '',
        enter_start_date: '',
        enter_end_date: '',
        production_start_date: '',
        production_end_date: '',
      }
    }
  },
)

watch(
  () => state.filterData.enter_date,
  (value: any) => {
    state.filterData.enter_start_date = formatDate(value?.[0]) || ''
    state.filterData.enter_end_date = formatDate(value?.[1]) || ''
  },
)
watch(
  () => state.filterData.production_date,
  (value: any) => {
    state.filterData.production_start_date = formatDate(value?.[0]) || ''
    state.filterData.production_end_date = formatDate(value?.[1]) || ''
  },
)

const columnList = ref([
  {
    field: 'raw_material_code',
    title: '原料编号',
    width: 150,
    fixed: 'left',
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    width: 100,
    fixed: 'left',
  },
  {
    field: 'customer_name',
    title: '所属客户',
    width: 100,
  },
  {
    field: 'brand',
    title: '原料品牌',
    width: 100,
  },
  {
    field: 'craft',
    title: '原料工艺',
    width: 100,
  },
  {
    field: 'color_scheme',
    title: '原料色系',
    width: 100,
  },
  {
    field: 'supplier_name',
    title: '供应商',
    width: 100,
  },
  {
    field: 'batch_num',
    title: '原料批号',
    width: 100,
  },
  {
    field: 'remark',
    title: '原料备注',
    width: 100,
  },
  {
    field: 'level_name',
    title: '原料等级',
    width: 100,
  },
  {
    field: 'measurement_unit_name',
    title: '单位',
    width: 100,
  },
  {
    field: 'production_date',
    title: '生产日期',
    width: 100,
    isDate: true,
    formatTime: 'YYYY-MM-DD',
  },
  {
    field: 'spinning_type',
    title: '纺纱类型',
    width: 100,
  },
  {
    field: 'cotton_origin',
    title: '棉花产地',
    width: 100,
  },
  {
    field: 'yarn_origin',
    title: '棉纱产地',
    width: 100,
  },
  {
    field: 'carton_num',
    title: '装箱单号',
    width: 100,
  },
  {
    field: 'fapiao_num',
    title: '发票号',
    width: 100,
  },
  // {
  //   field: 'enter_date',
  //   title: '入库日期',
  //   width: 100,
  // },
  {
    field: 'whole_piece_count',
    title: '整件库存件数',
    width: 100,
    isPrice: true,
  },
  // {
  //   field: 'whole_piece_weight',
  //   title: '件重(kg)',
  //   width: 70,
  //   fixed: 'right',
  //   isWeight: true,
  // },
  {
    field: 'bulk_piece_count',
    title: '散件库存件数',
    isPrice: true,
    width: 100,
  },
  // {
  //   field: 'bulk_weight',
  //   title: '数量',
  //   width: 70,
  //   fixed: 'right',
  //   isWeight: true,
  // },
  {
    field: 'total_weight',
    title: '数量总计',
    width: 100,
    isWeight: true,
  },
  {
    field: 'unit_price',
    title: '单价',
    width: 70,
    isUnitPrice: true,
  },
  {
    field: 'total_price',
    title: '金额',
    width: 70,
    isPrice: true,
  },
])

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  { deep: true },
)

const selectList = ref<any[]>([])
function submit() {
  if (selectList.value.length === 0)
    return ElMessage.warning('请选择一项')
  emit('submit', selectList.value)
}

function onClose() {
  modelValue.value = false
}

function handAllSelect({ records }: any) {
  selectList.value = records
}

function handleSelectionChange({ records }: any) {
  selectList.value = records
}
const componentRemoteSearch = ref({
  name: '',
})
const tableConfig = ref({
  showCheckBox: true,
  handAllSelect,
  handleSelectionChange,
  showPagition: true,
  loading,
  fieldApiKey: fieldApiKeyList.SelectInformationDialog,
  height: '500px',
  size,
  page,
  total,
  checkRowKeys: [] as number[],
  handleSizeChange,
  handleCurrentChange,
})
</script>

<template>
  <vxe-modal
    v-model="modelValue"
    show-footer
    title="根据库存添加"
    width="80vw"
    height="80vh"
    :mask="false"
    :lock-view="false"
    :esc-closable="true"
    resize
    @close="onClose"
  >
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="原料编号:">
        <template #content>
          <el-input
            v-model="state.filterData.raw_material_code"
            size="small"
            placeholder="原料编号"
            clearable
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="原料名称:">
        <template #content>
          <el-input
            v-model="state.filterData.raw_material_name"
            size="small"
            placeholder="原料名称"
            clearable
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="原料品牌:">
        <template #content>
          <el-input
            v-model="state.filterData.brand"
            size="small"
            placeholder="原料成分"
            clearable
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="原料工艺:">
        <template #content>
          <el-input
            v-model="state.filterData.craft"
            size="small"
            placeholder="原料工艺"
            clearable
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="原料色系:">
        <template #content>
          <el-input
            v-model="state.filterData.color_scheme"
            size="small"
            placeholder="原料色系"
            clearable
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="原料批号:">
        <template #content>
          <el-input
            v-model="state.filterData.batch_num"
            size="small"
            placeholder="原料批号"
            clearable
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="供应商:">
        <template #content>
          <!--          <SelectDialog -->
          <!--            v-model="state.filterData.supplier_id" -->
          <!--            api="AdminuenumsupplierType" -->
          <!--            :column-list="[ -->
          <!--              { -->
          <!--                field: 'name', -->
          <!--                title: '供应商名称', -->
          <!--                minWidth: 100, -->
          <!--                isEdit: true, -->
          <!--              }, -->
          <!--              { -->
          <!--                field: 'code', -->
          <!--                title: '供应商编号', -->
          <!--                minWidth: 100, -->
          <!--                isEdit: true, -->
          <!--              }, -->
          <!--              { -->
          <!--                field: 'address', -->
          <!--                title: '地址', -->
          <!--                minWidth: 100, -->
          <!--                isEdit: true, -->
          <!--              }, -->
          <!--            ]" -->
          <!--            :valid-config="{ -->
          <!--              name: [ -->
          <!--                { required: true, message: '请输入名称' }, -->
          <!--                { -->
          <!--                  validator({ cellValue }) { -->
          <!--                    if (cellValue === '') { -->
          <!--                      new Error('供应商名称'); -->
          <!--                    } -->
          <!--                  }, -->
          <!--                }, -->
          <!--              ], -->
          <!--            }" -->
          <!--            :editable="true" -->
          <!--          /> -->

          <SelectDialog
            v-model="state.filterData.supplier_id"
            :query="{ name: componentRemoteSearch.name }"
            api="BusinessUnitSupplierEnumlist"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '供应商编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '供应商编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.name = val)"
          />
          <!-- <SelectComponents api="AdminuenumsupplierType" label-field="name" value-field="id" v-model="state.filterData.supplier_id" clearable /> -->
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="所属客户:">
        <template #content>
          <SelectCustomerDialog v-model="state.filterData.customer_id" />
          <!-- <SelectComponents api="GetCustomerEnumList" label-field="name" value-field="id" v-model="state.filterData.supplier_id" clearable /> -->
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="生产日期:" width="330">
        <template #content>
          <SelectDate v-model="state.filterData.production_date" size="small" />
        </template>
      </DescriptionsFormItem>
    </div>
    <FildCard title="" no-shadow :tool-bar="true">
      <Table
        :config="tableConfig"
        :table-list="dataList?.list"
        :column-list="columnList"
      />
    </FildCard>
    <template #footer>
      <el-button type="primary" size="small" @click="submit">
        提交
      </el-button>
    </template>
  </vxe-modal>
</template>
