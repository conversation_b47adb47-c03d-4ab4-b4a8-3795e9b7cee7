<!-- 细码录入 坯布盘点单  -->
<script setup lang="ts">
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import currency from 'currency.js'
import Table from '@/components/Table.vue'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import { getGfmWarehouseList } from '@/api/greyFabricPurchaseReturn'
import { deepClone, deleteToast, getFilterData } from '@/common/util'
import { formatPriceDiv, formatWeightDiv, sumNum, sumTotal } from '@/common/format'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { WarehouseTypeIdEnum } from '@/common/enum'
import SelectComponents from '@/components/SelectComponents/index.vue'
import type { TableConfig } from '@/components/Table/type'

const props = withDefaults(defineProps<{
  isEdit: boolean
}>(), {
  isEdit: true,
})
const emits = defineEmits(['handleSure'])
const state = reactive<any>({
  filterData: {},
  info: {},
  showModal: false,
  modalName: '细码录入',
  multipleSelection: [],
  ximaList: [],
  canEnter: 0,
  rowIndex: -1,
  total_roll: 0,
  isCheck: false,
})

const { fetchData, data, loading, total: totalList, page: pageList, size: sizeList, handleSizeChange, handleCurrentChange } = getGfmWarehouseList()

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

// 表格配置
const tableConfig = computed<Partial<TableConfig>>(() => ({
  showSlotNums: true,
  loading: loading.value,
  page: pageList.value,
  size: sizeList.value,
  total: totalList.value,
  showCheckBox: props.isEdit,
  showSort: false,
  showPagition: true,
  showOperate: false,
  height: 300,
  backSelection: true,
  handleSizeChange,
  handleCurrentChange,
  footerMethod: (val: any) => FooterMethod1(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
}))

const ximaPage = ref(1)
const ximaSize = ref(50)

const tableConfig_xima = computed(() => ({
  showSlotNums: true,
  showOperate: props.isEdit,
  height: 300,
  showPagition: true,
  page: ximaPage.value,
  size: ximaSize.value,
  total: state.ximaList.length,
  pageSizes: [50, 100, 500, 1000],
  footerMethod: (val: any) => FooterMethod(val),
  handleSizeChange: (value: number) => {
    ximaSize.value = value
  },
  handleCurrentChange: (value: number) => {
    ximaPage.value = value
  },
}))

const paginatedXimaList = computed(() => {
  const start = (ximaPage.value - 1) * ximaSize.value
  const end = start + ximaSize.value
  return state.ximaList.slice(start, end)
})

function FooterMethod1({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['num'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'num') as any)}`

      if (['weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'weight') as any)}`

      return null
    }),
  ]
}

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['before_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'before_roll') as any)}`

      if (['actually_roll'].includes(column.property)) {
        state.total_roll = sumTotal(data, 'actually_roll')

        return `${sumNum(data, 'actually_roll')}`
      }
      if (['before_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'before_weight') as any)}`

      if (['actually_weight'].includes(column.property))
        return `${sumNum(data, 'actually_weight')}`

      if (['previa_roll'].includes(column.property))
        return `${sumNum(data, 'previa_roll') as any}`

      if (['previa_weight'].includes(column.property))
        return `${sumNum(data, 'previa_weight')}`

      return null
    }),
  ]
}

async function getData() {
  await fetchData(getFilterData(state.filterData))
  data.value.list?.map((item: any) => {
    item.selected = state.ximaList?.some((citem: { id: any }) => citem.id === item.id) ?? false
  })
}

// 面料表主体数据
const columnList = ref([
  {
    field: 'volume_number',
    title: '卷号',
  },
  {
    field: 'warehouse_bin_Name',
    title: '仓位',
  },
  {
    field: 'num',
    title: '匹数',
    isPrice: true,
  },
  {
    field: 'weight',
    title: '数量',
    isWeight: true,
  },
  {
    field: 'source_code',
    title: '生产通知单号',
  },
  {
    field: 'grey_fabric_width',
    title: '坯布幅宽',
    soltName: 'grey_fabric_width',
  },
  {
    field: 'grey_fabric_gram_weight',
    title: '坯布克重',
    soltName: 'grey_fabric_gram_weight',
  },
  {
    field: 'fabric_piece_code',
    title: '条码',
  },
])

// 细码表主体数据
const columnList_xima = reactive([
  {
    field: 'volume_number',
    title: '卷号',
    soltName: 'volume_number',
  },
  {
    field: 'position',
    title: '仓位',
    minWidth: 100,
    soltName: 'position',
  },
  {
    field: 'before_roll',
    title: '盘前匹数',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'before_weight',
    title: '盘前数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'actually_roll',
    title: '实盘匹数',
    minWidth: 100,
    soltName: 'actually_roll',
    required: true,
  },
  {
    field: 'actually_weight',
    title: '实盘数量',
    minWidth: 100,
    soltName: 'actually_weight',
    required: true,
  },
  {
    field: 'previa_roll',
    title: '盈/亏匹数',
    minWidth: 100,
    soltName: 'previa_roll',
  },
  {
    field: 'previa_weight',
    title: '盈/亏数量',
    minWidth: 100,
    soltName: 'previa_weight',
  },
  {
    field: 'fabric_piece_code',
    title: '条码',
    minWidth: 100,
  },
])

// 批量操作
const bulkShow = ref(false)
function handEdit() {
  bulkShow.value = true
}

const bulkSetting = ref<any>({})
const bulkList = reactive<any>([
  {
    field: 'actually_roll',
    title: '实盘匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'actually_weight',
    title: '实盘数量',
    component: 'input',
    type: 'float',
  },
])
// 批量设置
function bulkSubmit({ row, value, quickInputResult }: any) {
  if (row.field === 'roll') {
    return
  }
  else {
    state?.ximaList.map((item: any, index: number) => {
      if (row.quickInput && quickInputResult?.[index]) {
        item[row.field] = quickInputResult[index]
        return item
      }

      item[row.field] = value[row.field]
    })
  }

  ElMessage.success('设置成功')
  handBulkClose()
}
function handBulkClose() {
  bulkShow.value = false
}

function handAllSelect({ checked, selectCheck }: any) {
  data.value.list?.map((item: any) => {
    if (!checked) {
      item.selected = false
      return item
    }
    else {
      item.selected = true
      return item
    }
  })
  state.ximaList = deepClone(selectCheck)
  for (let i = 0; i < state.ximaList.length; i++) {
    state.ximaList[i].before_weight = Number(state.ximaList[i].weight)
    state.ximaList[i].before_roll = Number(state.ximaList[i].num)
    state.ximaList[i].previa_weight = 0
    state.ximaList[i].previa_roll = 0
    state.ximaList[i].actually_roll = formatPriceDiv(state.ximaList[i].num)
    state.ximaList[i].actually_weight = formatWeightDiv(state.ximaList[i].weight)
    state.ximaList[i].position = state.ximaList[i].warehouse_bin_Name
    state.ximaList[i].is_stock_source = true
  }
}

// 点击选择上部数据
function handleSelectionChange({ checked, row }: any) {
  data.value.list?.map((item: any) => {
    if (item.id === row.id) {
      if (!checked)
        item.selected = false
      else
        item.selected = true

      return item
    }
  })

  let filterList = []
  filterList = state.ximaList.filter((item: any) => {
    return item.id === row.id
  })

  if (!filterList.length) {
    state.ximaList.push({
      id: row.id,
      position: row?.warehouse_bin_Name,
      warehouse_bin_id: row?.warehouse_bin_id,
      volume_number: row?.volume_number,
      before_roll: row.num,
      before_weight: row?.weight,
      is_stock_source: true,
      previa_weight: 0,
      previa_roll: 0,
      actually_roll: formatPriceDiv(row.num),
      actually_weight: formatWeightDiv(row?.weight),
      fabric_piece_code: row.fabric_piece_code,
    })
  }
  else {
    state.ximaList = state.ximaList.filter((item: any) => {
      return item.id !== row.id
    })
  }
}

function handCancel() {
  state.showModal = false
}

function handleSure() {
  let canEmit = true
  if (state.ximaList.length === 0) {
    canEmit = false
    return ElMessage.error('请添加数据')
  }
  state.ximaList.forEach((item: any, index: number) => {
    if (item.actually_roll === '') {
      canEmit = false
      return ElMessage.error(`第${index + 1}行的实盘匹数不能为空`)
    }
    if (item.actually_weight === '') {
      canEmit = false
      return ElMessage.error(`第${index + 1}行的实盘数量不能为空`)
    }
  })
  if (canEmit)
    emits('handleSure', state)
}

const tableRef = ref()

watch(
  () => state.ximaList,
  () => {
    if (state.ximaList.length > 0) {
      nextTick(() => {
        state.ximaList?.map((item: any) => {
          if (formatPriceDiv(item.before_roll) > Number(item.actually_roll))
            // currency(formatPriceDiv(item.before_roll)).subtract(Number(item.actually_roll)).value
            // item.previa_roll = formatPriceDiv(item.before_roll) - Number(item.actually_roll)
            item.previa_roll = currency(formatPriceDiv(item.before_roll)).subtract(Number(item.actually_roll)).value

          if (formatPriceDiv(item.before_roll) < Number(item.actually_roll))
            // item.previa_roll = Number(item.actually_roll) - formatPriceDiv(item.before_roll)
            item.previa_roll = currency(Number(item.actually_roll)).subtract(formatPriceDiv(item.before_roll)).value

          if (formatPriceDiv(item.before_roll) - Number(item.actually_roll) === 0)
            item.previa_roll = 0

          if (formatWeightDiv(item.before_weight) > Number(item.actually_weight))
            item.previa_weight = Number(formatWeightDiv(item.before_weight) - Number(item.actually_weight)).toFixed(2)

          if (formatWeightDiv(item.before_weight) < Number(item.actually_weight))
            item.previa_weight = Number(Number(item.actually_weight) - formatWeightDiv(item.before_weight)).toFixed(2)

          if (formatWeightDiv(item.before_weight) - Number(item.actually_weight) === 0)
            item.previa_weight = 0
        })
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

function handDelete(row: any, rowIndex: number) {
  state.ximaList.splice(rowIndex, 1)
}

function handAdd() {
  state.ximaList.push({
    volume_number: '',
    position: '',
    before_roll: 0,
    before_weight: 0,
    actually_roll: 0,
    actually_weight: 0,
    previa_roll: 0,
    previa_weight: 0,
  })
}

watch(
  () => state.ximaList,
  () => {
    data.value.list?.map((item: any) => {
      item.selected = state.ximaList?.some((citem: { id: any }) => citem.id === item.id) ?? false
      return item
    })
  },
  {
    deep: true,
  },
)

watch(
  () => state.isCheck,
  async (newVal) => {
    if (newVal) {
      if (!state.ximaList.length) {
        state.isCheck = false
        return ElMessage.error('请先选择或者添加细码数据')
      }
      const res = await deleteToast('是否确定实盘数据默认等于盘前？')
      if (res)
        changeData()
      else
        state.isCheck = false
    }
  },
)

function changeData() {
  state.ximaList.map((item: any) => {
    item.actually_roll = formatPriceDiv(item.before_roll)
    item.actually_weight = formatWeightDiv(item.before_weight)
    return item
  })
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" :show-footer="props.isEdit" :title="state.modalName" width="1500" height="800" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <!-- 顶部面料主体信息 -->
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="坯布编号:">
        <template #content>
          {{ state.info.grey_fabric_code }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="坯布名称:">
        <template #content>
          {{ state.info.grey_fabric_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="供方名称:">
        <template #content>
          {{ state.info.supplier_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="机台号:">
        <template #content>
          {{ state.info.machine_number }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="所属客户:">
        <template #content>
          {{ state.info.customer_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="纱批:">
        <template #content>
          {{ state.info.yarn_batch }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="织坯颜色:">
        <template #content>
          {{ state.info.gray_fabric_color_name }}
        </template>
      </DescriptionsFormItem>
    </div>
    <!-- 最外层表格 -->
    <div>
      <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList">
        <template #grey_fabric_width="{ row }">
          {{ row.grey_fabric_width }} {{ row.grey_fabric_width_unit_name }}
        </template>
        <template #grey_fabric_gram_weight="{ row }">
          {{ row.grey_fabric_gram_weight }} {{ row.grey_fabric_gram_weight_unit_name }}
        </template>
      </Table>
    </div>

    <!-- <div class="mt-[20px]">
      已选
    </div> -->
    <div v-if="props.isEdit" class="buttom-oper-box">
      <div class="flex items-center mt-[15px]">
        <span class="mr-[10px]">实盘等于盘前数量</span>
        <el-checkbox v-model="state.isCheck" />
      </div>
      <div>
        <el-button type="primary" :disabled="!state.ximaList.length" @click="handEdit">
          批量操作
        </el-button>
        <el-button type="primary" @click="handAdd">
          盘盈增加
        </el-button>
      </div>
    </div>
    <div class="mt-[15px]">
      <Table ref="tableRef" :config="tableConfig_xima" :table-list="paginatedXimaList" :column-list="columnList_xima">
        <template #volume_number="{ row }">
          <template v-if="props.isEdit && !row.is_stock_source">
            <vxe-input v-model="row.volume_number" />
          </template>
          <template v-else>
            {{ row.volume_number }}
          </template>
        </template>

        <template #position="{ row }">
          <template v-if="props.isEdit && !row.is_stock_source">
            <SelectComponents
              v-model="row.warehouse_bin_id"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              :query="{ dictionary_id: WarehouseTypeIdEnum.commonWarehouseBin }"
              clearable
            />
          </template>
          <template v-else>
            {{ row.position }}
          </template>
        </template>

        <template #actually_roll="{ row }">
          <template v-if="props.isEdit">
            <vxe-input v-model="row.actually_roll" :min="0" type="float" placeholder="必填" />
          </template>
          <template v-else>
            {{ row.actually_roll }}
          </template>
        </template>

        <template #actually_weight="{ row }">
          <template v-if="props.isEdit">
            <vxe-input v-model="row.actually_weight" :min="0" type="float" placeholder="必填" />
          </template>
          <template v-else>
            {{ row.actually_weight }}
          </template>
        </template>

        <template #previa_roll="{ row }">
          <div v-if="row.is_stock_source">
            <div v-if="formatPriceDiv(row.before_roll) > Number(row.actually_roll)" class="text-[#04b865]">
              {{ `-${currency(formatPriceDiv(row.before_roll)).subtract(Number(row.actually_roll)).value}` }}
            </div>
            <div v-else-if="formatPriceDiv(row.before_roll) < Number(row.actually_roll)" class="text-[#ed8a94]">
              {{ `+${currency(Number(row.actually_roll)).subtract(formatPriceDiv(row.before_roll)).value}` }}
            </div>
            <div v-else-if="currency(formatPriceDiv(row.before_roll)).subtract(Number(row.actually_roll)).value === 0">
              {{ 0 }}
            </div>
          </div>
        </template>
        <template #previa_weight="{ row }">
          <div v-if="row.is_stock_source">
            <div v-if="formatWeightDiv(row.before_weight) > Number(row.actually_weight)" class="text-[#04b865]">
              {{ `-${currency(Number(formatWeightDiv(row.before_weight))).subtract(Number(row.actually_weight)).value}` }}
            </div>
            <div v-else-if="formatWeightDiv(row.before_weight) < Number(row.actually_weight)" class="text-[#ed8a94]">
              {{ `+${currency(Number(row.actually_weight)).subtract(formatWeightDiv(row.before_weight)).value}` }}
            </div>
            <div v-else-if="currency(formatWeightDiv(row.before_weight)).subtract(Number(row.actually_weight)).value === 0">
              {{ 0 }}
            </div>
          </div>
        </template>
        <template #operate="{ row, rowIndex }">
          <template v-if="props.isEdit">
            <el-button text type="danger" @click="handDelete(row, rowIndex)">
              删除
            </el-button>
          </template>
        </template>
      </Table>
    </div>
    <!-- </div> -->
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
  <BulkSetting v-if="props.isEdit" v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose">
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
  </BulkSetting>
</template>

<style lang="scss" scoped>
.buttom-oper-box{
  display: flex;
  align-items: baseline;
  justify-content: space-between;
}
</style>
