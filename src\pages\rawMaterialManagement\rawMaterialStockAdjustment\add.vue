<script lang="ts" setup name="RawMaterialStockAdjustmentAdd">
import { Plus } from '@element-plus/icons-vue'
import Big from 'big.js'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import SelectInformation from './components/SelectInformation/index.vue'
import SelectPurchasing from './components/SelectPurchasing/index.vue'
import { AdjustOrderAdd } from '@/api/rawMaterialStockAdjustment'
import { BusinessUnitIdEnum, EmployeeType } from '@/common/enum'
import { formatDate, formatPriceDiv, formatPriceMul, formatUnitPriceDiv, formatUnitPriceMul, formatWeightDiv, formatWeightMul, sumNum } from '@/common/format'
import { getDefaultSaleSystem, getFilterData } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'
import SelectDialog from '@/components/SelectDialog/index.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'

const routerList = useRouterList()

const state = reactive({
  form: {
    sale_system_id: getDefaultSaleSystem()?.default_sale_system_id,
    sale_system_name: '',
    remark: '',
    adjust_unit_id: '',
    adjust_unit_name: '',
    adjust_date: dayjs().format('YYYY-MM-DD'),
    warehouse_manager_name: '',
    warehouse_manager_id: '',
  },
  tableData: [] as any[],
  fromRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    adjust_unit_id: [{ required: true, message: '请选调整单位', trigger: 'change' }],
    adjust_date: [{ required: true, message: '请选择调整日期', trigger: 'change' }],
  },
})

const showAdd = ref(false)
const showPurchasing = ref(false)

function handDel(row: any, rowIndex: number) {
  ElMessageBox.confirm('是否删除该数据?', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      state.tableData.splice(rowIndex, 1)
    })
    .catch(() => {})
}

function handAdd() {
  if (!state.form.adjust_unit_id)
    return ElMessage.error('请选择调整单位')
  showAdd.value = true
}

const columnList = ref([
  {
    title: '',
    childrenList: [
      {
        field: 'raw_material_code',
        title: '原料编号',
        fixed: 'left',
        width: 150,
      },
      {
        field: 'raw_material_name',
        title: '原料名称',
        fixed: 'left',
        width: 100,
      },
    ],
  },
  {
    title: '调整前',
    childrenList: [
      {
        field: 'customer_name_stock',
        title: '所属客户',
        width: 100,
      },
      {
        field: 'brand_stock',
        title: '原料品牌',
        width: 100,
      },
      {
        field: 'craft_stock',
        title: '原料工艺',
        width: 100,
      },
      {
        field: 'supplier_name_stock',
        title: '供应商',
        width: 100,
      },
      {
        field: 'color_scheme_stock',
        title: '原料色系',
        width: 100,
      },
      {
        field: 'batch_num_stock',
        title: '原料批号',
        width: 100,
      },
      {
        field: 'level_stock',
        title: '原料等级',
        width: 130,
      },
      {
        field: 'carton_num_stock',
        title: '装箱单号',
        width: 100,
      },
      {
        field: 'fapiao_num_stock',
        title: '发票号',
        width: 100,
      },
      {
        field: 'production_date_stock',
        title: '生产日期',
        width: 100,
        isDate: true,
      },
      {
        field: 'spinning_type_stock',
        title: '纺纱类型',
        width: 100,
      },

      {
        field: 'cotton_origin_stock',
        title: '棉花产地',
        width: 100,
      },
      {
        field: 'yarn_origin_stock',
        title: '棉纱产地',
        width: 100,
      },
      {
        field: 'whole_piece_count_stock',
        title: '整件库存件数',
        width: 150,
      },
      // {
      //   field: 'whole_piece_weight_stock',
      //   title: '整件件重',
      //   width: 150,
      //   isWeight: true,
      // },
      {
        field: 'bulk_piece_count_stock',
        title: '散件库存件数',
        width: 150,
      },
      // {
      //   field: 'bulk_weight_stock',
      //   title: '散件数量总计(kg)',
      //   width: 150,
      //   isWeight: true,
      // },
      {
        field: 'total_weight_stock',
        title: '数量总计(kg)',
        width: 150,
      },
      {
        field: 'unit_price_stock',
        title: '单价',
        width: 150,
      },
      {
        field: 'total_price_stock',
        title: '金额',
        width: 150,
      },
    ],
  },
  {
    title: '调整后',
    childrenList: [
      {
        field: 'customer_id',
        title: '所属客户',
        width: 130,
        soltName: 'customer_id',
      },
      {
        field: 'brand',
        title: '原料品牌',
        width: 100,
        soltName: 'brand',
      },
      {
        field: 'craft',
        title: '原料工艺',
        width: 100,
        soltName: 'craft',
      },
      {
        field: 'supplier_name',
        title: '供应商',
        width: 130,
        soltName: 'supplier_name',
      },
      {
        field: 'color_scheme',
        title: '原料颜色',
        width: 100,
        soltName: 'color_scheme',
      },
      {
        field: 'measurement_unit_id',
        title: '计量单位',
        width: 100,
        soltName: 'measurement_unit_id',
      },
      {
        field: 'dyelot_number',
        title: '缸号',
        width: 100,
        soltName: 'dyelot_number',
      },
      {
        field: 'batch_num',
        title: '原料批号',
        width: 100,
        soltName: 'batch_num',
      },
      {
        field: 'level',
        title: '原料等级',
        width: 130,
        soltName: 'level',
      },
      {
        field: 'carton_num',
        title: '装箱单号',
        width: 100,
        soltName: 'carton_num',
      },
      {
        field: 'fapiao_num',
        title: '发票号',
        width: 100,
        soltName: 'fapiao_num',
      },
      {
        field: 'production_date',
        title: '生产日期',
        width: 150,
        soltName: 'production_date',
      },
      {
        field: 'spinning_type',
        title: '纺纱类型',
        width: 100,
        soltName: 'spinning_type',
      },

      {
        field: 'cotton_origin',
        title: '棉花产地',
        width: 100,
        soltName: 'cotton_origin',
      },
      {
        field: 'yarn_origin',
        title: '棉纱产地',
        width: 100,
        soltName: 'yarn_origin',
      },
      {
        field: 'whole_piece_count',
        title: '整件调整件数',
        width: 150,
        soltName: 'whole_piece_count',
        required: true,
      },
      {
        field: 'bulk_piece_count',
        title: '散件调整件数',
        width: 150,
        soltName: 'bulk_piece_count',
      },
      {
        field: 'total_weight',
        title: '数量总计(kg)',
        width: 150,
        isWeight: true,
        soltName: 'total_weight',
      },
      {
        field: 'unit_price',
        title: '单价',
        width: 150,
        soltName: 'unit_price',
        required: true,
      },
      {
        field: 'total_price',
        title: '金额',
        width: 150,
        soltName: 'total_price',
      },
    ],
  },
])

const changeRow = ref<any>()
function changeData(row: any) {
  changeRow.value = row
}

const tablesRef = ref()
watch(
  () => changeRow.value,
  () => {
    computedData(changeRow.value)
  },
  {
    deep: true,
  },
)

const saleSaleSystemInfo = ref()
function getSaleSystem(row: any) {
  state.form.sale_system_name = row?.name
  saleSaleSystemInfo.value = row
  clearData()
}

function computedData(row: any) {
  row.total_price = Number.parseFloat(
    Big(row?.total_weight || 0)
      .times(row?.unit_price || 0)
      .toFixed(2),
  )
  tablesRef.value.tableRef.updateFooter()
}

const bulkShow = ref(false)
const bulkSetting = ref<any>({
  address: {},
})

const multipleSelection = ref<any[]>([])
async function bulkSubmit({ row, value }: any) {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  if (!value[row.field])
    return ElMessage.error('请输入参数')

  multipleSelection.value?.map((item: any) => {
    item[row.field] = value[row.field]
    computedData(item)
  })
  ElMessage.success('设置成功')
}

function bulkHand() {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')
  bulkShow.value = true
}

function onSubmit(row: any) {
  showAdd.value = false
  const data = row?.map((item: any) => {
    return {
      raw_material_code: item.raw_material_code,
      raw_material_name: item.raw_material_name,
      customer_name: item.customer_name,
      brand: item.brand,
      craft: item.craft,
      supplier_name: item.supplier_name,
      color_scheme: item.color_scheme,
      batch_num: item.batch_num,
      level: item.level,
      level_id: item.level_id,
      carton_num: item.carton_num,
      fapiao_num: item.fapiao_num,
      production_date: item.production_date,
      spinning_type: item.spinning_type,
      cotton_origin: item.cotton_origin,
      yarn_origin: item.yarn_origin,
      // whole_piece_count: item.whole_piece_count,
      whole_piece_weight: item.whole_piece_weight,
      // bulk_piece_count: item.bulk_piece_count,
      bulk_weight: item.bulk_weight,
      // total_weight: formatWeightDiv(item.total_weight),
      customer_id: item.customer_id,
      supplier_id: item.supplier_id,
      stock_item_id: item.id,
      raw_material_id: item.raw_material_id,

      customer_name_stock: item.customer_name,
      brand_stock: item.brand,
      craft_stock: item.craft,
      supplier_name_stock: item.supplier_name,
      color_scheme_stock: item.color_scheme,
      batch_num_stock: item.batch_num,
      level_stock: item.level_name,
      level_id_stock: item.level_id,
      carton_num_stock: item.carton_num,
      fapiao_num_stock: item.fapiao_num,
      production_date_stock: item.production_date,
      spinning_type_stock: item.spinning_type,
      cotton_origin_stock: item.cotton_origin,
      yarn_origin_stock: item.yarn_origin,
      whole_piece_count_stock: item.whole_piece_count,
      whole_piece_count: item.whole_piece_count,
      whole_piece_weight_stock: item.whole_piece_weight,
      bulk_piece_count_stock: item.bulk_piece_count,
      bulk_piece_count: item.bulk_piece_count,
      bulk_weight_stock: item.bulk_weight,
      total_weight_stock: formatWeightDiv(item.total_weight || 0),
      total_weight: formatWeightDiv(item.total_weight || 0),
      unit_price_stock: formatUnitPriceDiv(item.unit_price || 0),
      unit_price: formatUnitPriceDiv(item.unit_price || 0),
      customer_id_stock: item.customer_id,
      supplier_id_stock: item.supplier_id,
      total_price_stock: formatPriceDiv(item.total_price || 0),
      color_id: item.color_id,
      measurement_unit_id: item.measurement_unit_id,
      dyelot_number: '',
    }
  })
  state.tableData = [...state.tableData, ...data]
}

function onSubmitPurchasing(row: any) {
  showPurchasing.value = false
  const data = row?.map((item: any) => {
    return {
      raw_material_code: item.raw_material_code,
      raw_material_name: item.raw_material_name,
      pur_order_no: item.order_no,
      brand: item.brand,
      color_scheme: item.color_scheme,
      batch_num: item.batch_num,
      level: item.level,
      yarn_origin: item.yarn_origin,
      carton_num: item.carton_num,
      weight_shortage_loss_rate: item.weight_shortage_loss_rate,
      spinning_type: item.spinning_type,
      raw_material_remark: item.raw_material_remark,
      whole_piece_count: item.whole_piece_count,
      whole_piece_weight: item.whole_piece_weight,
      bulk_piece_count: item.bulk_piece_count,
      bulk_weight: item.bulk_weight,
      raw_material_id: item.raw_material_id,
      sale_order_item_id: item.id,
    }
  })
  state.tableData = [...state.tableData, ...data]
}

const { fetchData: fetchDataAdd, data: addData, success: successAdd, msg: msgAdd } = AdjustOrderAdd()
const ruleFormRef = ref()
async function handSubmit() {
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      if (validateList())
        return false
      const res = fomatData()

      await fetchDataAdd(getFilterData({ ...state.form, adjust_date: formatDate(state.form.adjust_date), items: res || [] }))
      if (successAdd.value) {
        ElMessage.success('添加成功')
        routerList.push({
          name: 'RawMaterialStockAdjustmentDetail',
          params: { id: addData.value.id },
        })
      }
      else {
        ElMessage.error(msgAdd.value)
      }
    }
  })
}

// 验证坯布信息字段
function validateList() {
  let msg = ''
  if (!state.tableData || state.tableData.length === 0) {
    msg = '原料信息不能为空'
  }
  else {
    state.tableData?.some((item: any) => {
      if (!item.raw_material_code) {
        msg = `编号为${item.raw_material_code}的数据,原料编号不能为空`
        return true
      }
      if (!item.raw_material_name) {
        msg = `编号为${item.raw_material_code}的数据,原料名称不能为空`
        return true
      }

      if (!Number(item.whole_piece_count) || item.whole_piece_count < 0) {
        msg = `编号为${item.raw_material_code}的数据,整件调整件数不合法`
        return true
      }
      if (!item.unit_price) {
        msg = `编号为${item.raw_material_code}的数据,单价不能为空`
        return true
      }
      if (item.color_id === '') {
        msg = `编号为${item.raw_material_code}的数据,颜色不能为空`
        return true
      }
    })
  }
  msg && ElMessage.error(msg)
  return msg
}

// // 整理提交信息
function fomatData() {
  return state.tableData?.map((item: any) => {
    const { bulk_weight, bulk_piece_count, total_weight, other_price, unit_price, whole_piece_count, whole_piece_weight, production_date } = item
    const res = getFilterData({
      ...item,
      production_date: formatDate(production_date),
      whole_piece_count: Number.parseInt(whole_piece_count),
      whole_piece_weight: formatWeightMul(whole_piece_weight),
      bulk_piece_count: Number.parseInt(bulk_piece_count),
      bulk_weight: formatWeightMul(bulk_weight),
      other_price: formatPriceMul(other_price),
      unit_price: formatUnitPriceMul(unit_price),
      total_weight: formatWeightMul(total_weight),
    })
    return res
  })
}

const bulkList = reactive<any>([
  {
    field: 'customer_id',
    field_name: 'customer_name',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
    query: { sale_system_id: state.form.sale_system_id },
  },
  {
    field: 'brand',
    title: '原料品牌',
    component: 'input',
    type: 'text',
  },
  {
    field: 'craft',
    title: '原料工艺',
    component: 'input',
    type: 'text',
  },
  {
    field: 'supplier_id',
    field_name: 'supplier_name',
    title: '供应商',
    component: 'select',
    api: 'BusinessUnitSupplierEnumlist',
  },
  {
    field: 'color_id',
    title: '原料颜色',
    component: 'select',
    api: 'GetRawMaterialColor',
  },
  {
    field: 'dyelot_number',
    title: '缸号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'batch_num',
    title: '原料批号',
    component: 'input',
    api: 'text',
  },
  {
    field: 'level',
    field_name: 'level_name',
    title: '原料等级',
    component: 'select',
    api: 'GetInfoBaseRawMaterialLevelEnumList',
  },
  {
    field: 'carton_num',
    title: '装箱单号',
    component: 'input',
    api: 'text',
  },
  {
    field: 'production_date',
    title: '生产日期',
    component: 'selectDate',
    type: 'date',
  },
  {
    field: 'cotton_origin',
    title: '棉花产地',
    component: 'input',
    type: 'text',
  },
  {
    field: 'yarn_origin',
    title: '棉纱产地',
    component: 'input',
    type: 'text',
  },
  {
    field: 'fapiao_num',
    title: '发票号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'spinning_type',
    title: '纺纱类型',
    component: 'input',
    type: 'text',
  },
  {
    field: 'whole_piece_count',
    title: '整件调整件数',
    component: 'input',
    type: 'integer',
  },
  {
    field: 'bulk_piece_count',
    title: '散件调整件数',
    component: 'input',
    type: 'integer',
  },
  {
    field: 'total_weight',
    title: '总数量总计(kg）',
    component: 'input',
    type: 'float',
    digits: 4,
  },
  {
    field: 'unit_price',
    title: '单价',
    component: 'input',
    type: 'float',
    digits: 4,
  },
])

function handAllSelect({ records }: any) {
  multipleSelection.value = records
}

function handleSelectionChange({ records }: any) {
  multipleSelection.value = records
}

function footerMethod({ columns, data }: { columns: any, data: any }) {
  const footerData = [
    columns.map((column: any, _columnIndex: number) => {
      if (['raw_material_code'].includes(column.field))
        return '合计'

      if (
        [
          'bulk_piece_count',
          'whole_piece_count',
          'total_weight',
          'other_price',
          'total_price',
          'whole_piece_weight',
          'bulk_weight',
          'total_weight_stock',
          'whole_piece_weight_stock',
          'bulk_weight_stock',
        ].includes(column.field)
      ) {
        if (column.field === 'total_weight_stock')
          return sumNum(data, column.field)
        else
          return sumNum(data, column.field)
      }
    }),
  ]
  return footerData
}
function handBulkClose() {
  bulkShow.value = false
}

function clearData() {
  state.tableData?.map((item) => {
    item.customer_id = saleSaleSystemInfo.value?.default_customer_id || ''
    item.customer_name = saleSaleSystemInfo.value?.default_customer_name || ''
  })
  bulkList[0].query = { sale_system_id: state.form.sale_system_id }
  bulkSetting.value.customer_id = ''
}
const tableConfig = ref({
  showOperate: true,
  operateWidth: 100,
  bulkSubmit,
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  footerMethod,
  showSpanHeader: true,
})
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <div class="line" />
    <template #right-top>
      <el-button v-btnAntiShake="handSubmit" type="primary">
        提交
      </el-button>
    </template>
    <slot>
      <el-form ref="ruleFormRef" :model="state.form" label-width="60px" label-position="top" :rules="state.fromRules">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem required label="营销体系名称:">
            <template #content>
              <el-form-item prop="sale_system_id">
                <SelectComponents
                  v-model="state.form.sale_system_id"
                  :default-status="true"
                  api="GetSaleSystemDropdownListApi"
                  label-field="name"
                  value-field="id"
                  @change-value="getSaleSystem"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="调整单位名称:">
            <template #content>
              <el-form-item prop="adjust_unit_id">
                <SelectComponents
                  v-model="state.form.adjust_unit_id"
                  api="GetBusinessUnitListApi"
                  label-field="name"
                  value-field="id"
                  :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory }"
                  @change-value="row => (state.form.adjust_unit_name = row?.name)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="调整日期:">
            <template #content>
              <el-form-item prop="adjust_date">
                <SelectDate v-model="state.form.adjust_date" type="date" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="仓管员:">
            <template #content>
              <el-form-item prop="warehouse_manager_id">
                <SelectComponents
                  v-model="state.form.warehouse_manager_id"
                  api="Adminemployeelist"
                  :query="{ duty: EmployeeType.warehouseManager }"
                  label-field="name"
                  value-field="id"
                  @change-value="row => (state.form.warehouse_manager_name = row?.name)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="备注:" copies="2">
            <template #content>
              <el-form-item prop="userName">
                <el-input v-model="state.form.remark" type="textarea" placeholder="请输入备注" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
        </div>
      </el-form>
    </slot>
  </FildCard>
  <FildCard title="原料信息" :tool-bar="false" class="mt-[5px]">
    <template #right-top>
      <el-button style="margin-left: 10px" type="primary" @click="bulkHand">
        批量操作
      </el-button>
      <el-button style="margin-left: 10px" type="primary" :icon="Plus" @click="handAdd">
        根据库存添加
      </el-button>
    </template>
    <Table ref="tablesRef" :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
      <template #customer_id="{ row }">
        <SelectCustomerDialog
          v-model="row.customer_id"
          :query="{ sale_system_id: state.form.sale_system_id }"
          :default-value="{
            id: row.customer_id,
            name: row.customer_name,
          }"
          @change-value="val => (row.customer_name = val.name)"
        />
      </template>
      <template #brand="{ row }">
        <vxe-input v-model.trim="row.brand" maxlength="200" :min="0" size="mini" :class="{ my_input: row.brand !== row.brand_stock }" />
      </template>
      <template #craft="{ row }">
        <vxe-input v-model.trim="row.craft" maxlength="200" :min="0" size="mini" :class="{ my_input: row.craft !== row.craft_stock }" />
      </template>
      <template #color_scheme="{ row }">
        <SelectDialog
          v-model="row.color_id"
          api="GetRawMaterialColor"
          :query="{
            name: row.serach_color_name,
          }"
          label-field="name"
          :label-name="row.color_scheme"
          :column-list="[
            {
              field: 'name',
              title: '原料颜色',
              minWidth: 100,
            },
            {
              field: 'code',
              title: '原料色号',
              minWidth: 100,
            },
          ]"
          @change-value="val => (row.color_scheme = val?.name || '')"
          @on-input="val => (row.serach_color_name = val)"
        />
      </template>
      <template #measurement_unit_id="{ row }">
        <SelectComponents v-model="row.measurement_unit_id" disabled :label-value="row.measurement_unit_name" api="getInfoBaseMeasurementUnitEnumList" label-field="name" value-field="id" clearable />
      </template>
      <template #dyelot_number="{ row }">
        <vxe-input v-model="row.dyelot_number" clearable />
      </template>
      <template #supplier_id="{ row }">
        <SelectComponents
          v-model="row.supplier_id"
          api="BusinessUnitSupplierEnumAll"
          label-field="name"
          value-field="id"
          :class="{ my_input: row.supplier_id !== row.supplier_id_stock }"
          @change-value="val => (row.supplier_name = val?.name || '')"
        />
      </template>
      <template #batch_num="{ row }">
        <vxe-input v-model="row.batch_num" maxlength="200" :min="0" size="mini" :class="{ my_input: row.batch_num !== row.batch_num_stock }" />
      </template>
      <template #level="{ row }">
        <SelectComponents
          v-model="row.level_id"
          size="small"
          api="GetInfoBaseRawMaterialLevelEnumList"
          :class="{ my_input: row.level_id !== row.level_id_stock }"
          label-field="name"
          value-field="id"
        />
      </template>
      <template #production_date="{ row }">
        <SelectDate v-model="row.production_date" type="date" size="small" style="width: 130px" :class="{ my_input: row.production_date !== row.production_date_stock }" />
      </template>
      <template #spinning_type="{ row }">
        <vxe-input v-model="row.spinning_type" maxlength="200" :min="0" size="mini" :class="{ my_input: row.spinning_type !== row.spinning_type_stock }" />
      </template>
      <template #cotton_origin="{ row }">
        <vxe-input v-model="row.cotton_origin" maxlength="200" :min="0" size="mini" :class="{ my_input: row.cotton_origin !== row.cotton_origin_stock }" />
      </template>
      <template #yarn_origin="{ row }">
        <vxe-input v-model="row.yarn_origin" maxlength="200" :min="0" size="mini" :class="{ my_input: row.yarn_origin !== row.yarn_origin_stock }" />
      </template>
      <template #carton_num="{ row }">
        <vxe-input v-model="row.carton_num" maxlength="200" :min="0" size="mini" :class="{ my_input: row.carton_num !== row.carton_num_stock }" />
      </template>
      <template #fapiao_num="{ row }">
        <vxe-input v-model="row.fapiao_num" class-name="changeClass" maxlength="200" :min="0" size="mini" :class="{ my_input: row.fapiao_num !== row.fapiao_num_stock }" />
      </template>
      <template #weight_shortage_loss_rate="{ row }">
        <vxe-input
          v-model="row.weight_shortage_loss_rate"
          maxlength="200"
          :min="0"
          type="float"
          size="mini"
          :class="{ my_input: row.weight_shortage_loss_rate !== row.weight_shortage_loss_rate_stock }"
        />
      </template>
      <template #raw_material_remark="{ row }">
        <vxe-input v-model="row.raw_material_remark" maxlength="200" size="mini" :class="{ my_input: row.raw_material_remark !== row.raw_material_remark_stock }" />
      </template>
      <template #whole_piece_count="{ row }">
        <vxe-input v-model="row.whole_piece_count" maxlength="200" type="integer" :min="0" size="mini" :class="{ my_input: row.whole_piece_count !== row.whole_piece_count_stock }" />
      </template>
      <template #whole_piece_weight="{ row }">
        <vxe-input v-model="row.whole_piece_weight" maxlength="200" type="float" :min="0" size="mini" :class="{ my_input: row.whole_piece_weight !== row.whole_piece_weight_stock }" />
      </template>
      <template #bulk_piece_count="{ row }">
        <vxe-input
          v-model="row.bulk_piece_count"
          maxlength="200"
          type="integer"
          :min="0"
          size="mini"
          :class="{ my_input: row.bulk_piece_count !== row.bulk_piece_count_stock }"
          @input="changeData(row)"
        />
      </template>
      <template #bulk_weight="{ row }">
        <vxe-input v-model="row.bulk_weight" maxlength="200" type="float" :min="0" size="mini" :class="{ my_input: row.bulk_weight !== row.bulk_weight_stock }" @input="changeData(row)" />
      </template>
      <template #total_weight="{ row }">
        <vxe-input
          v-model="row.total_weight"
          maxlength="200"
          type="float"
          :digits="4"
          :min="0"
          size="mini"
          :class="{ my_input: row.total_weight !== row.total_weight_stock }"
          @input="changeData(row)"
        />
      </template>
      <template #unit_price="{ row }">
        <vxe-input
          v-model="row.unit_price"
          maxlength="200"
          type="float"
          :digits="4"
          :min="0"
          size="mini"
          :class="{ my_input: row.unit_price !== row.unit_price_stock }"
          @input="changeData(row)"
        />
      </template>
      <template #total_price="{ row }">
        {{ row.total_price }}
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" maxlength="200" size="mini" />
      </template>
      <template #operate="{ row, rowIndex }">
        <el-button type="text" @click="handDel(row, rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <SelectInformation v-model="showAdd" :unit_id="state.form.adjust_unit_id" @submit="onSubmit" />
  <SelectPurchasing v-model="showPurchasing" :sale_system_id="state.form.sale_system_id" @submit="onSubmitPurchasing" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
</template>

<style lang="scss" scoped>
.changeClass {
  .vxe-input--inner {
    color: red !important;
  }
}

.my_input {
  :deep(.el-input__inner) {
    color: red;
  }

  :deep(.vxe-input--inner) {
    color: red;
  }
}
</style>
