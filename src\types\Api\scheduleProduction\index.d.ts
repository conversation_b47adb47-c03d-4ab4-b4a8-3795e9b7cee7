declare namespace Api.GetProductionScheduleOrderList {
  export interface Request {
    /**
     * 审核时间
     */
    audit_date?: string
    /**
     * 审核人ID
     */
    auditor_id?: number
    /**
     * 往来单位ID
     */
    biz_unit_id?: number
    /**
     * 织厂跟单用户ID
     */
    biz_unit_order_follower_id?: number
    /**
     * 跟单电话
     */
    biz_unit_order_follower_phone?: string
    /**
     * 布飞状态
     */
    bu_fei_status?: number
    /**
     * 公司ID
     */
    company_id?: number
    /**
     * 下单用户所属部门
     */
    department_id?: number
    /**
     * download
     */
    download?: number
    /**
     * limit
     */
    limit?: number
    /**
     * 机台号
     */
    machine_number?: string
    /**
     * 用料批号
     */
    material_batch_numbers?: string
    /**
     * offset
     */
    offset?: number
    /**
     * 生产排产单号
     */
    order_no?: string
    /**
     * page
     */
    page?: number
    /**
     * 收坯地址
     */
    receipt_grey_fabric_address?: string
    /**
     * 交坯日期
     */
    receipt_grey_fabric_date?: string
    /**
     * 销售计划单号
     */
    sale_plan_order_no?: number
    /**
     * 营销体系ID
     */
    sale_system_id?: number
    /**
     * 排产日期结束
     */
    schedule_date_end?: string
    /**
     * 排产日期开始
     */
    schedule_date_start?: string
    /**
     * size
     */
    size?: number
    /**
     * 订单状态
     */
    status?: string
    [property: string]: any
  }
  /**
   * produce.GetProductionScheduleOrderData
   */
  export interface Response {
  /**
   * 款号
   */
    account_num?: string
    /**
     * 审核时间
     */
    audit_date?: string
    /**
     * 订单状态 1待审核 2已审核 3已驳回 4已作废
     */
    audit_status?: number
    /**
     * 状态名称
     */
    audit_status_name?: string
    /**
     * 审核人ID
     */
    auditor_id?: number
    /**
     * 审核人名称
     */
    auditor_name?: string
    /**
     * 合同序号（默认01，当更新后纱名出现变更则加1）
     */
    contract_sequence?: string
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 客户id
     */
    customer_id?: number
    /**
     * 客户名称
     */
    customer_name?: string
    /**
     * 部门id
     */
    department_id?: number
    /**
     * 布种后整
     */
    fabric_finishing?: string
    /**
     * 坯布编号
     */
    grey_fabric_code?: string
    /**
     * 坯布颜色id
     */
    grey_fabric_color_id?: number
    /**
     * 坯布颜色名称
     */
    grey_fabric_color_name?: string
    /**
     * 坯布id
     */
    grey_fabric_id?: number
    /**
     * 坯布名称
     */
    grey_fabric_name?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 进仓条数
     */
    in_warehouse_roll?: number
    /**
     * 进仓数量
     */
    in_warehouse_weight?: number
    /**
     * 验布条数
     */
    inspection_roll?: number
    /**
     * 验布数量
     */
    inspection_weight?: number
    /**
     * 织机机型ID
     */
    loom_model_id?: number
    /**
     * 织机类型
     */
    loom_model_name?: string
    /**
     * 机台号
     */
    machine_number?: string
    /**
     * 已打布飞条数
     * 未打布飞条数
     * 最新的布飞打印人
     * 最新的布飞打印时间
     * 最新的布飞打印状态
     */
    machine_stop_date?: string
    /**
     * 停机备注
     */
    machine_stop_remark?: string
    /**
     * 停机状态
     */
    machine_stop_status?: boolean
    /**
     * 用料批号（子表所有的原料批号组合取来用+号拼接，不用去重）
     */
    material_batch_numbers?: string
    /**
     * 用料比例
     */
    material_ratios?: ProduceGetProductionScheduleOrderMaterialRatioData[]
    /**
     * 针寸数
     */
    needle_size?: string
    /**
     * 排产单号
     */
    order_no?: string
    /**
     * 出仓条数
     */
    out_warehouse_roll?: number
    /**
     * 出仓数量
     */
    out_warehouse_weight?: number
    /**
     * 生产通知单
     */
    produced_roll?: number
    /**
     * 生产通知单id
     */
    production_notify_order_id?: number
    /**
     * 生产通知单号
     */
    production_notify_order_no?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 营销体系id
     */
    sale_system_id?: number
    /**
     * 营销体系名称
     */
    sale_system_name?: string
    /**
     * 排产日期
     */
    schedule_date?: string
    /**
     * 排产条数
     */
    schedule_roll?: number
    /**
     * 库存条数
     */
    stock_roll?: number
    /**
     * 库存数量
     */
    stock_weight?: number
    /**
     * 总针数
     */
    total_needle_size?: string
    /**
     * 已排产匹数
     */
    total_scheduled_roll?: number
    /**
     * 未排产匹数(排产匹数-已排产匹数)
     */
    total_un_schedule_roll?: number
    /**
     * 未织条数(生产条数-称重条数)
     */
    un_produce_roll?: number
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    /**
     * 织厂id
     */
    weave_factory_id?: number
    /**
     * 织厂名称
     */
    weave_factory_name?: string
    /**
     * 称重条数
     */
    weighing_roll?: number
    /**
     * 称重数量
     */
    weighing_weight?: number
    /**
     * 纱批
     */
    yarn_batch?: string
    /**
     * 纱牌（子表所有的原料品牌组合取来用+号拼接，不用去重）
     */
    yarn_brand?: string
    /**
     * 纱名（子表的原料名称、原料品牌、原料批号、原料缸号、原料颜色组合起来，每个子表间数据用"+"分开）
     */
    yarn_name?: string
    [property: string]: any
  }

  /**
   * produce.GetProductionScheduleOrderMaterialRatioData
   */
  export interface ProduceGetProductionScheduleOrderMaterialRatioData {
    id?: number
    /**
     * 原料损耗
     */
    material_loss?: number
    /**
     * 原料比例
     */
    material_ratio: number
    /**
     * 生产通知单详情id
     */
    production_notify_order_detail_id: number
    /**
     * 排产单主表id
     */
    productionScheduleOrderId?: number
    /**
     * 原料批号
     */
    raw_material_batch_number?: string
    /**
     * 原料品牌
     */
    raw_material_brand?: string
    /**
     * 原料颜色id
     */
    raw_material_color_id: number
    /**
     * 原料颜色名称
     */
    raw_material_color_name: string
    /**
     * 原料缸号
     */
    raw_material_dyelot_number?: string
    /**
     * 原料id
     */
    raw_material_id: number
    /**
     * 原料名称
     */
    raw_material_name: string
    /**
     * 供应商id
     */
    supplier_id?: number
    /**
     * 织造类别id
     */
    weaving_category_id?: number
    [property: string]: any
  }
}
