<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import Table from '@/components/Table.vue'
import { getFinishProductColorDropdownList } from '@/api/productSalePlan'

import { debounce, getFilterData } from '@/common/util'
// import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'

const emits = defineEmits(['handleSure'])

const state = reactive<any>({
  filterData: {
    product_color_name: '',
    dye_factory_color_code: '',
    dye_factory_color_name: '',
  },
  info: {
    factory_name: '',
    finish_product_code: '',
    finish_product_name: '',
  },
  showModal: false,
  rowIndex: -1,
  multipleSelection: [],
  productId: '',
  dye_factory_id: '',
})

const { fetchData, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = getFinishProductColorDropdownList()

const getData = debounce(async () => {
  const query = {
    dye_factory_id: state.dye_factory_id,
    finish_product_id: state.productId,
    ...state.filterData,
  }
  await fetchData(getFilterData(query))
}, 400)
watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)
const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  height: 400,
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// const handReset = () => {
//   state.filterData = resetData(state.filterData)
// }

const columnList = ref([
  {
    field: 'finish_product_code',
    title: '成品编号',
    minWidth: 100,
  },
  {
    field: 'finish_product_name',
    title: '成品名称',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  // {
  //   field: 'dye_factory_color_name',
  //   title: '染厂颜色',
  //   minWidth: 100,
  // },
  {
    field: 'type_finished_product_kind_name',
    title: '颜色类别',
    minWidth: 100,
  },
  {
    field: 'product_color_code',
    title: '颜色编号',
    minWidth: 100,
  },
  {
    field: 'product_color_name',
    title: '颜色名称',
    minWidth: 100,
  },
  {
    field: 'type_grey_fabric_code',
    title: '布种类型编号',
    minWidth: 100,
  },
  {
    field: 'type_grey_fabric_name',
    title: '布种类型名称',
    minWidth: 100,
  },
  // {
  //   field: 'remark',
  //   title: '备注',
  //   minWidth: 100,
  // },
])

function handCancel() {
  state.showModal = false
}

async function handleSure() {
  if (state.multipleSelection.length > 1)
    return ElMessage.error('只允许选择一条数据')

  emits('handleSure', state.multipleSelection, state.rowIndex)
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer title="成品颜色资料" width="1200" height="600" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <!-- <div class="buttom-oper" style="margin-top: 20px">
      <el-button type="primary" @click="handReset">重置</el-button>
    </div>
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="成品编号:">
        <template v-slot:content>
          {{ state.info.finish_product_code }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="成品名称:">
        <template v-slot:content>
          {{ state.info.finish_product_name }}
        </template>
      </DescriptionsFormItem>
    </div> -->
    <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList" />
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
