<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import { GetGfmWarehouseSummaryListEnum } from '@/api/greyFabricPurchaseReturn'
import { getFilterData, resetData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'

export interface Props {
  obj: any
}

const emits = defineEmits(['handleSure'])

const state = reactive<any>({
  filterData: {},
  showModal: false,
  modalName: '根据库存添加',
  multipleSelection: [],
  list: [],
  sale_customer_id: '',
  cashList: [],
  info: {},
  rowIndex: -1,
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  unit_name: '',
  raw_code: '',
  raw_name: '',
  color_code: '',
  color_name: '',
})

const {
  fetchData: fetchData1,
  data: data1,
  total: total1,
  loading: loading1,
  page: page1,
  size: size1,
  handleSizeChange: handleSizeChange1,
  handleCurrentChange: handleCurrentChange1,
} = GetGfmWarehouseSummaryListEnum()

const tableConfig = reactive<any>({
  loading: loading1,
  showPagition: true,
  showSlotNums: true,
  page: page1,
  size: size1,
  total: total1,
  showCheckBox: true,
  height: 300,
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange1(val),
  handleCurrentChange: (val: number) => handleCurrentChange1(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

const tableConfig_second = ref({
  showCheckBox: true,
  showSlotNums: true,
  height: 400,
  showSort: false,
  handAllSelect: () => handAllSelect_twince(),
  handleSelectionChange: (val: any) => handleSelectionChange_twince(val),
  showOperate: true,
  operateWidth: '100',
})

// function handChange(val: any) {
//   state.filterData.radioValue = val.toString()
// }

async function getData() {
  const query = {
    sale_customer_id: state?.sale_customer_id,
    ...state.filterData,
  }
  await fetchData1(getFilterData(query))
  state.list = data1.value?.list
  tableConfig.total = total1
  tableConfig.page = page1
  tableConfig.size = size1
}
watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

watch(
  () => [data1.value.list, state.multipleSelection],
  () => {
    state.list = data1.value.list?.map((item: any) => {
      item.choose_sign = 1
      item.use_weight = ''
      item.selected = state.multipleSelection?.some((citem: any) => citem.stock_product_id === item.id && citem.choose_sign === item.choose_sign) ?? false
      return item
    })
    // TODO:缓存记住当前页的数据内容，以便取消的时候可以剔除
    state.cashList = data1?.value?.list
  },
)

watch(
  () => state.multipleSelection,
  () => {
    // state.multipleSelection = state.multipleSelection.map((item: any) => {
    state.multipleSelection.map((item: any) => {
      item.selected = true
      return item
    })
  },
)

function handAllSelect({ records, checked }: any) {
  if (checked) {
    const listId = state.multipleSelection?.map((item: any) => item.stock_product_id)
    records?.map((item: any, index: number) => {
      if (!listId.includes(item.id)) {
        item.stock_product_id = item.id
        if (state.multipleSelection.length <= 0 && index === 0 && item.use_roll === '' && item.use_weight === '') {
          item.use_roll = state.stock_roll
          item.use_weight = state.weight
        }
        state.multipleSelection = [...state.multipleSelection, item]
      }
    })
  }
  else {
    state.multipleSelection = state.multipleSelection.filter((item2: any) => !state.cashList.some((item1: any) => item1.id === item2.id && item1.choose_sign === item2.choose_sign))
  }
}

function handleSelectionChange({ checked, row }: any) {
  if (checked) {
    if (state.multipleSelection.some((item: any) => item.stock_product_id === row.id))
      return false
    row.stock_product_id = row.id
    state.multipleSelection.push(row)
    if (state.multipleSelection.length === 1) {
      row.use_roll = state.stock_roll
      row.use_weight = state.weight
    }
  }
  else {
    state.multipleSelection = removeObjectFromArray(row.id, row.choose_sign, state.multipleSelection)
  }
}

function handAllSelect_twince() {
  state.multipleSelection = []
  state.list?.map((item: any) => {
    item.selected = state.multipleSelection?.some((citem: { id: any, choose_sign: number }) => citem.id === item.id && citem.choose_sign === item.choose_sign) ?? false
    return item
  })
}

function handleSelectionChange_twince({ rowIndex }: any) {
  state.multipleSelection.splice(rowIndex, 1)
  state.list?.map((item: any) => {
    item.selected = state.multipleSelection?.some((citem: { id: any, choose_sign: number }) => citem.id === item.id && citem.choose_sign === item.choose_sign) ?? false
    return item
  })
}

// 合并清洗数据
// function margeArr(array1: any, array2: any) {
//   const mergedArray = array1.concat(array2)
//   const uniqueArray = []
//   const visitedIds = new Set()
//
//   for (const item of mergedArray) {
//     const key = `${item.id}-${item.choose_sign}`
//     if (!visitedIds.has(key)) {
//       visitedIds.add(key)
//       uniqueArray.push(item)
//     }
//   }
//
//   return uniqueArray
// }

// 查找两个数组的id和choose_sign的值是否相同
// function checkSameIdAndChooseSign(array1: any, array2: any) {
//   for (let i = 0; i < array1.length; i++) {
//     for (let j = 0; j < array2.length; j++) {
//       if (array1[i].id === array2[j].id && array1[i].choose_sign === array2[j].choose_sign)
//         return true
//     }
//   }
//   return false
// }

function removeObjectFromArray(id: number, choose_sign: number, array: any) {
  for (let i = 0; i < array.length; i++) {
    if (array[i].id === id && array[i].choose_sign === choose_sign) {
      array.splice(i, 1)
      break
    }
  }
  return array
}

function handDelete(index: number) {
  state.multipleSelection.splice(index, 1)
  state.list?.map((item: any) => {
    item.selected = state.multipleSelection?.some((citem: { id: any, choose_sign: number }) => citem.id === item.id && citem.choose_sign === item.choose_sign) ?? false
    return item
  })
}

function handCancel() {
  state.showModal = false
}

function handleSure() {
  if (!state.multipleSelection.length) {
    return ElMessage.error('请选择数据')
  }
  else {
    for (let i = 0; i < state.multipleSelection.length; i++) {
      if (state.multipleSelection[i].use_weight === '')
        return ElMessage.warning('请填写数量')
    }

    state.showModal = false
    emits('handleSure', state.multipleSelection, state.rowIndex)
  }
}

// 重置
function handReset() {
  state.filterData = resetData(state.filterData)
  state.filterData.radioValue = '2'
}

defineExpose({
  state,
})

const columnList_new = ref([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
  },
  {
    field: 'supplier_name',
    title: '供方名称',
  },
  {
    field: 'customer_name',
    title: '所属客户',
  },
  {
    field: 'yarn_batch',
    title: '纱批',
  },
  {
    field: 'machine_number',
    title: '机台号',
  },
  {
    field: 'gray_fabric_color_name',
    title: '织坯颜色',
  },
  {
    field: 'grey_fabric_level_name',
    title: '坯布等级',
  },
  {
    field: 'needle_size',
    title: '针寸数',
  },
  {
    field: 'source_remark',
    title: '坯布备注',
  },
  {
    field: 'stock_roll',
    title: '匹数',
    isPrice: true,
    soltName: 'stock_roll',
  },
  {
    field: 'stock_weight',
    title: '数量',
    isWeight: true,
    soltName: 'stock_weight',
    required: true,
  },
])

const columnList = ref([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
  },
  {
    field: 'supplier_name',
    title: '供方名称',
  },
  {
    field: 'customer_name',
    title: '所属客户',
  },
  {
    field: 'yarn_batch',
    title: '纱批',
  },
  {
    field: 'machine_number',
    title: '机台号',
  },
  {
    field: 'gray_fabric_color_name',
    title: '织坯颜色',
  },
  {
    field: 'grey_fabric_level_name',
    title: '坯布等级',
  },
  //   {
  //     field: 'warehouse_bin_Name',
  //     title: '仓位',
  //   },
  //   {
  //     field: 'finish_product_width',
  //     title: '成品幅宽',
  //   },
  //   {
  //     field: 'finish_product_gram_weight',
  //     title: '成品克重',
  //   },
  {
    field: 'needle_size',
    title: '针寸数',
  },
  {
    field: 'source_remark',
    title: '坯布备注',
  },
  {
    field: 'stock_roll',
    title: '匹数',
    isPrice: true,
  },
  {
    field: 'stock_weight',
    title: '数量',
    isWeight: true,
  },
])
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="1400" height="800" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="坯布编号:">
        <template #content>
          <el-input v-model="state.filterData.grey_fabric_code" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="坯布名称:">
        <template #content>
          <el-input v-model="state.filterData.grey_fabric_name" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="供方名称:">
        <template #content>
          <SelectDialog
            v-model="state.filterData.supplier_id"
            api="BusinessUnitSupplierEnumlist"
            :query="{ name: componentRemoteSearch.name }"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '供应商编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '供应商编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :valid-config="{
              name: [
                { required: true, message: '请输入名称' },
                {
                  validator({ cellValue }) {
                    if (cellValue === '') {
                      new Error('供应商名称')
                    }
                  },
                },
              ],
            }"
            :editable="true"
            @change-input="val => (componentRemoteSearch.name = val)"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="所属客户:">
        <template #content>
          <SelectDialog
            v-model="state.filterData.customer_id"
            :query="{ sale_system_id: state.sale_system_id, name: componentRemoteSearch.customer_name }"
            api="GetCustomerEnumList"
            :column-list="[
              {
                title: '客户编号',
                minWidth: 100,
                required: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '客户编号',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '客户名称',
                minWidth: 100,
                colGroupHeader: true,
                required: true,
                childrenList: [
                  {
                    isEdit: true,
                    field: 'name',
                    title: '客户名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '电话',
                colGroupHeader: true,
                minWidth: 100,
                childrenList: [
                  {
                    field: 'phone',
                    isEdit: true,
                    title: '电话',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '销售员',
                minWidth: 100,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'seller_name',
                    title: '销售员',
                    soltName: 'seller_name',
                    isEdit: true,
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.customer_name = val)"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="">
        <template #content>
          <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
            清除条件
          </el-button>
        </template>
      </DescriptionsFormItem>
    </div>

    <Table :config="tableConfig" :table-list="state.list" :column-list="columnList" />
    <div class="mt-[20px] mb-[20px]">
      已选：
    </div>
    <Table :config="tableConfig_second" :table-list="state?.multipleSelection" :column-list="columnList_new">
      <template #stock_roll="{ row }">
        <vxe-input v-model="row.use_roll" type="float" :min="0" />
      </template>
      <template #stock_weight="{ row }">
        <vxe-input v-model="row.use_weight" type="float" :min="0" />
      </template>
      <template #operate="{ rowIndex }">
        <el-button text type="danger" @click="handDelete(rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
    <template #footer>
      <el-button type="primary" @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style>
.flex_end {
  display: flex;
  justify-content: flex-end;
}
</style>
