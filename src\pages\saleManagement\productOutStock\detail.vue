<script lang="ts" setup>
import { useRoute } from 'vue-router'
import { onMounted, ref } from 'vue'
import {
  getShortageProductOrder,
  updateShortageProductOrderAuditStatusCancel,
  updateShortageProductOrderAuditStatusPass,
  updateShortageProductOrderAuditStatusReject,
  updateShortageProductOrderAuditStatusWait,
} from '@/api/productOutStock'
import FildCard from '@/components/FildCard.vue'
import { formatDate, formatHashTag, formatLengthDiv, formatPriceDiv, formatWeightDiv, sumNum } from '@/common/format'
import Table from '@/components/Table.vue'
import { orderStatusConfirmBox } from '@/common/util'
import StatusColumn from '@/components/StatusColumn/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'

const rourte = useRoute()

const { fetchData, data } = getShortageProductOrder()

onMounted(() => {
  fetchData({ id: rourte.query.id })
})

const tableConfig = ref({
  showSlotNums: true,
  operateWidth: '80',
  footerMethod: (val: any) => FooterMethod(val),
  showSpanHeader: true,
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['shortage_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'shortage_roll') as any)}`

      if (['shortage_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'shortage_weight') as any)}`

      if (['shortage_length'].includes(column.property))
        return `${formatLengthDiv(sumNum(data, 'shortage_length') as any)}`

      return null
    }),
  ]
}

const columnList = ref([
  {
    title: '',
    childrenList: [
      {
        field: 'product_name',
        title: '成品名称',
        minWidth: 100,
        soltName: 'product_name',
      },
      {
        field: 'customer_name',
        title: '所属客户',
        minWidth: 100,
      },
      {
        field: 'customer_account_num',
        title: '款号',
        minWidth: 100,
      },
      {
        field: 'product_color_code',
        title: '色号颜色',
        minWidth: 100,
        soltName: 'product_color_code',
      },
      {
        field: 'product_color_kind_name',
        title: '颜色类别',
        minWidth: 100,
      },
      // {
      //   field: 'dyelot_number',
      //   title: '缸号',
      //   minWidth: 100,
      // },
      {
        field: 'product_level_name',
        title: '成品等级',
        minWidth: 100,
      },
      {
        field: 'product_remark',
        title: '成品备注',
        minWidth: 100,
      },
      {
        field: 'measurement_unit_name',
        title: '单位',
        minWidth: 100,
      },
    ],
  },
  {
    title: '欠货信息',
    childrenList: [
      {
        field: 'warehouse_name',
        title: '出货仓库',
        minWidth: 100,
      },
      {
        field: 'shortage_roll',
        title: '匹数',
        minWidth: 100,
        isPrice: true,
      },
      {
        field: 'shortage_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
      {
        field: 'shortage_length',
        title: '辅助数量',
        minWidth: 100,
        isLength: true,
      },
    ],
  },
  {
    title: '其他信息',
    childrenList: [
      {
        field: 'remark',
        title: '备注',
        minWidth: 100,
      },
    ],
  },
])

async function updateStatus(audit_status: number) {
  const id: any = rourte.query.id?.toString()
  if (audit_status === 4)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' }, api: updateShortageProductOrderAuditStatusCancel })

  if (audit_status === 3)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' }, api: updateShortageProductOrderAuditStatusReject })

  if (audit_status === 2)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' }, api: updateShortageProductOrderAuditStatusPass })

  if (audit_status === 1)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' }, api: updateShortageProductOrderAuditStatusWait })

  fetchData({ id })
}
</script>

<template>
  <StatusColumn
    :order_no="data.order_no"
    :order_id="data.id"
    :status="data.audit_status"
    :status_name="data.audit_status_name"
    permission_wait_key="ProductOutStockWait"
    permission_reject_key="ProductOutStockReject"
    permission_pass_key="ProductOutStockPass"
    permission_cancel_key="ProductOutStockCancel"
    permission_edit_key="ProductOutStockEdit"
    edit_router_name="ProductOutStockEdit"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  />
  <FildCard class="mt-[5px]" title="基础信息" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="营销体系:">
        <template #content>
          {{ data?.sale_system_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="出货类型:">
        <template #content>
          {{ data?.send_product_type_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="调入仓库:">
        <template #content>
          {{ data?.warehouse_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="订单日期:">
        <template #content>
          {{ formatDate(data?.order_time) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="客户编号:">
        <template #content>
          {{ data?.customer_code }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="客户名称:">
        <template #content>
          {{ data?.customer_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="销售群体:">
        <template #content>
          {{ data?.sale_group_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="销售员:">
        <template #content>
          {{ data?.sale_user_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="销售跟单:">
        <template #content>
          {{ data?.sale_follower_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="结算类型:">
        <template #content>
          {{ data?.settle_type_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="加工厂名称:">
        <template #content>
          {{ data?.process_factory_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="联系人:">
        <template #content>
          {{ data?.contacts }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="联系电话:">
        <template #content>
          {{ data?.contact_phone }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="物流公司:">
        <template #content>
          {{ data?.logistics_company_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="物流区域:">
        <template #content>
          {{ data?.logistics_area }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="收货地址:">
        <template #content>
          {{ data?.receipt_address }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="含税项目:">
        <template #content>
          {{ data?.info_sale_taxable_item_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem v-if="data.is_with_tax_rate" label="税率:">
        <template #content>
          {{ formatPriceDiv(data?.sale_tax_rate) }}%
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="是否含税:">
        <template #content>
          {{ data?.is_with_tax_rate ? "是" : "否" }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="邮费项目:">
        <template #content>
          {{ data?.postage_items_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="出货标签:">
        <template #content>
          {{ data?.print_tag }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="内部备注:" copies="2">
        <template #content>
          {{ data?.internal_remark }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="出货备注:" copies="2">
        <template #content>
          {{ data?.send_product_remark }}
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]">
    <Table :config="tableConfig" :table-list="data.item_data" :column-list="columnList">
      <template #product_name="{ row }">
        {{ formatHashTag(row.product_code, row.product_name) }}
      </template>
      <template #product_color_code="{ row }">
        {{ formatHashTag(row.product_color_code, row.product_color_name) }}
      </template>
    </Table>
  </FildCard>
</template>

<style lang="scss" scoped>
.oreder_code {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
</style>
