<script setup lang="ts">
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Close, Edit, Plus, View } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import ShutdownRemarkDialog, { type ShutdownRemarkData } from './components/ShutdownRemarkDialog.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import MachineSchedulingDialog from '@/pages/productionManagement/productionNotice/components/MachineSchedulingDialog.vue'
import FabricFlyCancelDialog from '@/pages/grayFabricMange/greyClothTicketCancel/components/FabricFlyCancelDialog.vue'
import {
  auditQuickScheduleProduction,
  cancelAuditQuickScheduleProduction,
  getMachineDropdownList,
  getQuickScheduleProductionList,
  getQuickScheduleStatistics,
  getWeaverDropdownList,
} from '@/api/quickScheduleProduction'
import { debounce, deleteToast, getFilterData } from '@/common/util'
import { usePrintTemplate } from '@/components/PrintPopoverBtn/index'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import { getGreyFabricInfoDetail } from '@/api/greyFabricInformation'

const router = useRouter()

const { options } = usePrintTemplate({
  printType: PrintType.PrintTemplateTypeProduceNotify,
  dataType: PrintDataType.Product,
})

// 状态选项
const statusOptions = [
  { label: '待排产', value: 1 },
  { label: '已排产', value: 2 },
  { label: '生产中', value: 3 },
  { label: '已完成', value: 4 },
  { label: '已取消', value: 5 },
]

// 优先级选项
// const priorityOptions = [
//   { label: '低', value: 1 },
//   { label: '中', value: 2 },
//   { label: '高', value: 3 },
//   { label: '紧急', value: 4 },
// ]
//
// const componentRemoteSearch = reactive({
//   production_notice_no: '',
//   grey_fabric_name: '',
//   machine_name: '',
//   weaver_name: '',
// })

const state = reactive<any>({
  tableData: [],
  filterData: {
    production_notice_no: '',
    schedule_no: '',
    grey_fabric_name: '',
    machine_id: '',
    weaver_id: '',
    status: [],
    start_date: '',
    end_date: '',
    create_start_date: '',
    create_end_date: '',
  },
  multipleSelection: [],
  statistics: {
    total_count: 0,
    pending_count: 0,
    producing_count: 0,
    completed_count: 0,
    cancelled_count: 0,
    avg_completion_time: 0,
    machine_utilization: 0,
  },
})

// 机台排产对话框状态
const machineSchedulingDialogVisible = ref(false)
const currentProductionData = ref<any>({})

// 布飞取消对话框状态
const fabricFlyCancelDialogVisible = ref(false)

// 停机备注对话框状态
const shutdownRemarkDialogVisible = ref(false)

const {
  fetchData: ApiQuickScheduleList,
  data: datalist,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
}: any = getQuickScheduleProductionList()

// 获取统计数据
const { fetchData: ApiStatistics } = getQuickScheduleStatistics()

// 首次加载数据
onMounted(() => {
  getData()
  getStatisticsData()
})

onActivated(() => {
  getData()
  getStatisticsData()
})

// 快速排产单表格列配置
const columnList = ref([
  {
    sortable: true,
    field: 'schedule_no',
    title: '排产单号',
    fixed: 'left',
    width: '120px',
  },
  {
    sortable: true,
    field: 'production_notice_no',
    title: '生产通知单号',
    fixed: 'left',
    width: '140px',
  },
  {
    sortable: true,
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'machine_name',
    title: '机台',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'weaver_name',
    title: '织工',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'plan_start_time',
    title: '计划开始时间',
    minWidth: 140,
    isDate: true,
  },
  {
    sortable: true,
    field: 'plan_end_time',
    title: '计划结束时间',
    minWidth: 140,
    isDate: true,
  },
  {
    sortable: true,
    field: 'plan_quantity',
    title: '计划产量',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'actual_quantity',
    title: '实际产量',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'priority_name',
    title: '优先级',
    minWidth: 80,
  },
  {
    sortable: true,
    field: 'status_name',
    title: '状态',
    minWidth: 80,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    minWidth: 140,
    isDate: true,
  },
  {
    sortable: true,
    field: 'create_user_name',
    title: '创建人',
    minWidth: 100,
  },
  {
    field: 'operation',
    title: '操作',
    fixed: 'right',
    width: '300px',
    slot: 'operation',
  },
])

// 获取数据
async function getData() {
  await ApiQuickScheduleList(getFilterData(state.filterData))
  state.tableData = datalist.value
}

// 获取统计数据
async function getStatisticsData() {
  const res = await ApiStatistics({
    start_date: state.filterData.start_date,
    end_date: state.filterData.end_date,
  })
  if (res)
    state.statistics = res
}

// 搜索防抖
const searchDebounce = debounce(() => {
  getData()
}, 300)

// 监听搜索条件变化
watch(() => state.filterData, () => {
  searchDebounce()
}, { deep: true })

const config = reactive({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '140',
  height: 'auto',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  cellClick: (val: any) => handleCellClick(val),
})

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}
const { fetchData: getGreyFetch, data: detailGreyData }
  = getGreyFabricInfoDetail()
function handleCellClick({ row }: any) {
  getGreyFetch({ id: row.id })
}
function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// 处理机台排产对话框保存
function handleMachineSchedulingSave(data: any) {
  console.log('保存机台排产数据:', data)
  ElMessage.success('机台排产保存成功')
  machineSchedulingDialogVisible.value = false
  // 刷新列表数据
  getData()
}

// 打开布飞取消对话框
function handleFabricFlyCancel() {
  fabricFlyCancelDialogVisible.value = true
}

// 处理布飞取消成功
function handleFabricFlyCancelSuccess(data: any) {
  console.log('布飞取消成功:', data)
  ElMessage.success(`条码 ${data.barcode} 布飞取消成功`)
  // 刷新列表数据
  getData()
}
// 新建
function handleAdd() {
  currentProductionData.value = {}
  machineSchedulingDialogVisible.value = true
}

// 查看详情
function handleDetail(row: any) {
  router.push({
    name: 'QuickScheduleProductionDetail',
    query: { id: row.id },
  })
}

// 编辑
function handleEdit(row: any) {
  router.push({
    name: 'QuickScheduleProductionEdit',
    query: { id: row.id },
  })
}

// 停机备注
function handleShutdownRemark() {
  shutdownRemarkDialogVisible.value = true
}

// 停机备注确认处理
function handleShutdownRemarkConfirm(data: ShutdownRemarkData) {
  console.log('停机备注数据:', data)
  // 这里可以调用API保存停机备注数据
  // 例如：await saveShutdownRemark(data)
  ElMessage.success('停机备注保存成功')
}

// 审核
const { fetchData: auditFetch, success: auditSuccess, msg: auditMsg } = auditQuickScheduleProduction()

async function handleAudit(row: any) {
  const res = await deleteToast('确认审核该快速排产单吗？')
  if (res) {
    await auditFetch({ id: row.id })
    if (auditSuccess.value) {
      ElMessage.success('审核成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value || '审核失败')
    }
  }
}

// 取消审核
const { fetchData: cancelAuditFetch, success: cancelAuditSuccess, msg: cancelAuditMsg } = cancelAuditQuickScheduleProduction()

async function handleCancelAudit(row: any) {
  const res = await deleteToast('确认取消审核该快速排产单吗？')
  if (res) {
    await cancelAuditFetch({ id: row.id })
    if (cancelAuditSuccess.value) {
      ElMessage.success('取消审核成功')
      getData()
    }
    else {
      ElMessage.error(cancelAuditMsg.value || '取消审核失败')
    }
  }
}

const tableConfig2 = ref({
  loading,
  operateWidth: '230',
  showSlotNums: true,
  showSort: false,
  showCheckBox: true,
  height: '100%',
})
const columnList2 = ref([
  {
    field: 'machine_number',
    title: '机台号',
    width: 100,
  },
  {
    field: 'barcode',
    title: '条形码',
    width: 100,
  },
  {
    field: 'volume_number',
    title: '卷号',
    width: 100,
  },
  {
    field: 'grey_fabric_level_name',
    title: '坯布等级',
    width: 100,
  },
  {
    field: 'remark',
    title: '质量备注',
    width: 100,
  },
  {
    field: 'yarn_batch',
    title: '纱批',
    width: 100,
  },
  {
    field: 'batch_num',
    title: '原料批号',
    width: 100,
  },
  {
    field: 'brand',
    title: '纱牌',
    width: 100,
  },
  {
    field: 'in_weight',
    title: '进仓数量',
    width: 100,
  },
  {
    field: 'in_roll',
    title: '进仓条数',
    width: 100,
  },
  {
    field: 'weigh_weight',
    title: '称重数量',
    width: 100,
  },
  {
    field: 'weigh_roll',
    title: '称重条数',
    width: 100,
  },
  {
    field: 'check_weight',
    title: '验布数量',
    width: 100,
  },
  {
    field: 'check_roll',
    title: '验布条数',
    width: 100,
  },
  {
    field: 'out_weight',
    title: '出仓数量',
    width: 100,
  },
  {
    field: 'out_roll',
    title: '出仓条数',
    width: 100,
  },
  {
    field: 'stock_weight',
    title: '库存数量',
    width: 100,
  },
  {
    field: 'stock_roll',
    title: '库存条数',
    width: 100,
  },
  {
    field: 'check_time',
    title: '查布时间',
    width: 100,
  },
  {
    field: 'printor',
    title: '打印人',
    width: 100,
  },
  {
    field: 'print_date',
    title: '打印时间',
    width: 100,
  },
])
function handlePrintAll() {

}
const showUnprint = ref(false)
</script>

<template>
  <div class="list-page">
    <!-- 搜索表单 -->
    <FildCard title="" :tool-bar="false">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="生产通知单号">
          <el-input
            v-model="state.filterData.production_notice_no"
            placeholder="请输入生产通知单号"
            clearable
          />
        </DescriptionsFormItem>
        <DescriptionsFormItem label="排产单号">
          <el-input
            v-model="state.filterData.schedule_no"
            placeholder="请输入排产单号"
            clearable
          />
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布名称">
          <el-input
            v-model="state.filterData.grey_fabric_name"
            placeholder="请输入坯布名称"
            clearable
          />
        </DescriptionsFormItem>
        <DescriptionsFormItem label="状态">
          <el-select
            v-model="state.filterData.status"
            placeholder="请选择状态"
            multiple
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="机台">
          <SelectComponents
            v-model="state.filterData.machine_id"
            :api="getMachineDropdownList"
            placeholder="请选择机台"
            clearable
            label-key="name"
            value-key="id"
          />
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织工">
          <SelectComponents
            v-model="state.filterData.weaver_id"
            :api="getWeaverDropdownList"
            placeholder="请选择织工"
            clearable
            label-key="name"
            value-key="id"
          />
        </DescriptionsFormItem>
        <DescriptionsFormItem label="计划时间">
          <SelectDate
            v-model:start-date="state.filterData.start_date"
            v-model:end-date="state.filterData.end_date"
            placeholder="请选择计划时间范围"
          />
        </DescriptionsFormItem>
        <DescriptionsFormItem label="创建时间">
          <SelectDate
            v-model:start-date="state.filterData.create_start_date"
            v-model:end-date="state.filterData.create_end_date"
            placeholder="请选择创建时间范围"
          />
        </DescriptionsFormItem>
      </div>
    </FildCard>

    <!-- 操作按钮 -->
    <FildCard title="" class="table-card-full" :tool-bar="false">
      <template #right-top>
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          新增布飞排产
        </el-button>
        <el-button
          type="primary"
          @click="handleShutdownRemark"
        >
          停机备注
        </el-button>
        <el-button
          type="primary"
          @click="handleFabricFlyCancel"
        >
          布飞取消
        </el-button>
        <PrintPopoverBtn
          :options="options"
          :data="state.tableData"
          print-btn-text="排产布飞打印"
        />
      </template>
      <!-- 表格 -->
      <Table
        :table-list="state.tableData"
        :column-list="columnList"
        :config="config"
      >
        <template #operation="{ row }">
          <el-button
            type="primary"
            :icon="View"
            size="small"
            @click="handleDetail(row)"
          >
            查看
          </el-button>
          <el-button
            v-if="row.status === 1"
            type="warning"
            :icon="Edit"
            size="small"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="row.status === 1"
            type="success"
            :icon="Check"
            size="small"
            @click="handleAudit(row)"
          >
            审核
          </el-button>
          <el-button
            v-if="row.status === 2"
            type="info"
            :icon="Close"
            size="small"
            @click="handleCancelAudit(row)"
          >
            取消审核
          </el-button>
        </template>
      </Table>
    </FildCard>
    <FildCard :tool-bar="false" class="table-card-bottom">
      <div>
        <el-button type="primary" plain @click="handlePrintAll">
          打印布飞
        </el-button>
        <el-checkbox type="primary" class="ml-2" @click="showUnprint = !showUnprint">
          显示未打印条码
        </el-checkbox>
      </div>

      <Table
        :config="tableConfig2"
        :table-list="detailGreyData?.raw_material_info"
        :column-list="columnList2"
      />
    </FildCard>

    <!-- 机台排产对话框 -->
    <MachineSchedulingDialog
      v-model="machineSchedulingDialogVisible"
      :production-data="currentProductionData"
      mode="add"
      @save="handleMachineSchedulingSave"
    />

    <!-- 布飞取消对话框 -->
    <FabricFlyCancelDialog
      v-model="fabricFlyCancelDialogVisible"
      title="布飞取消"
      @success="handleFabricFlyCancelSuccess"
    />

    <!-- 停机备注对话框 -->
    <ShutdownRemarkDialog
      v-model="shutdownRemarkDialogVisible"
      title="停机备注"
      @confirm="handleShutdownRemarkConfirm"
    />
  </div>
</template>

<style lang="scss" scoped>
.quick-schedule-production {
  .statistics-cards {
    margin-bottom: 20px;

    .stat-card {
      border-radius: 8px;
      border: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      &.pending {
        border-left: 4px solid #e6a23c;
      }

      &.producing {
        border-left: 4px solid #409eff;
      }

      &.completed {
        border-left: 4px solid #67c23a;
      }

      &.cancelled {
        border-left: 4px solid #f56c6c;
      }

      &.utilization {
        border-left: 4px solid #909399;
      }

      .stat-content {
        text-align: center;

        .stat-number {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 8px;
        }

        .stat-label {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }
}
</style>
