<script setup lang="ts" name="ProductSalePlan">
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'

// import router from '@/router'
import {
  getSaleProductPlanOrderAndDetailList,
  getSaleProductPlanOrderList,
  getSaleProductPlanOrderListExport,
  updateSaleProductPlanOrderAuditStatusPass,
  updateSaleProductPlanOrderAuditStatusWait,
} from '@/api/productSalePlan'
import { formatDate } from '@/common/format'
import { debounce, deleteToast, getFilterData, resetData } from '@/common/util'
import { EmployeeType } from '@/common/enum'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import PushDown from '@/pages/saleManagement/productSalePlan/components/pushDown.vue'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import { usePrintTemplate } from '@/components/PrintPopoverBtn/index'
import { SaleTypeEnum } from '@/enum'
import CoverImage from '@/components/UploadFile/CoverImage/index.vue'

const state = reactive({
  tableData: [],
  filterData: {
    order_no: '',
    sale_system_id: '',
    sale_order_no: '',
    src_order_no: '',
    order_date: '',
    auditor_id: '',
    updater_id: '',
    create_time: '',
    audit_time: '',
    edit_time: '',
    audit_status: '',
    voucher_number: '',
    sale_user_id: '',
    customer_id: '',
    customer_code: '',
    plan_type: '',
    product_code_and_name: '',
    color_code_and_name: '',
    situ_status_arr: [],
    account_num: '', // 款号
    remark: '', // 备注
  },
  multipleSelection: [],
})

const { options } = usePrintTemplate({
  printType: PrintType.PrintTemplateTypeSaleOrder,
  dataType: PrintDataType.Product,
})

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = getSaleProductPlanOrderList()
const {
  fetchData: ApiCustomerList1,
  data: data1,
  total: total1,
  loading: loading1,
  page: page1,
  size: size1,
  handleSizeChange: handleSizeChange1,
  handleCurrentChange: handleCurrentChange1,
} = getSaleProductPlanOrderAndDetailList()
// 改为默认合并
const mergeFlag = ref<boolean>(true)

function formatDateData(dateArray: string | string[]) {
  if (dateArray && dateArray !== '' && dateArray.length)
    return [formatDate(dateArray[0]), formatDate(dateArray[1])]

  return ['', '']
}

const queryData = computed(() => {
  const query: any = {
    start_order_time: formatDateData(state.filterData.create_time)[0],
    end_order_time: formatDateData(state.filterData.create_time)[1],
    begin_update_time: formatDateData(state.filterData.edit_time)[0],
    end_update_time: formatDateData(state.filterData.edit_time)[1],
    begin_audit_date: formatDateData(state.filterData.audit_time)[0],
    end_audit_date: formatDateData(state.filterData.audit_time)[1],
    situ_status: state.filterData.situ_status_arr.join(','),
    ...state.filterData,
  }
  delete query.audit_time
  delete query.create_time
  delete query.edit_time
  return getFilterData(query)
})

// 获取数据
const getData = debounce(async () => {
  if (mergeFlag.value)
    await ApiCustomerList1(queryData.value)
  else
    await ApiCustomerList(queryData.value)
}, 400)

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

/**
 * 重新获取数据-清除打印选择
 * TODO: 刷新会恢复-先不做处理
 */
// watch([() => data.value, () => data1.value], () => {
//   state.multipleSelection = []
// })

const mergeRowMethod: any = ({ row, _rowIndex, column, visibleData }: any) => {
  const fields = [
    'operate',
    'audit_status',
    'image_code',
    'push_status',
    'order_update_user_name',
    'order_update_time',
    'order_auditor_name',
    'order_audit_date',
    'order_create_user_name',
    'order_create_time',
    'order_no',
    'sale_system_name',
    'voucher_number',
    'order_time',
    'receipt_time',
    'customer_name',
    'sale_user_name',
    'deposit',
    'receipt_address',
    'internal_remark',
    'order_customer_name',
    'plan_type_name',
  ]
  const cellValue = row.order_no + row[column.field]
  if (cellValue && fields.includes(column.field)) {
    const prevRow = visibleData[_rowIndex - 1]
    let nextRow = visibleData[_rowIndex + 1]
    if (prevRow && prevRow.order_no + prevRow[column.field] === cellValue) {
      return { rowspan: 0, colspan: 0 }
    }
    else {
      let countRowspan = 1
      while (
        nextRow
        && nextRow.order_no + nextRow[column.field] === cellValue
      )
        nextRow = visibleData[++countRowspan + _rowIndex]

      if (countRowspan > 1)
        return { rowspan: countRowspan, colspan: 1 }
    }
  }
}

// const mergeCells = computed(() => {
//   const tableList = data1.value?.list || []
//   const fields = [
//     'operate',
//     'audit_status',
//     'image_code',
//     'push_status',
//     'order_update_user_name',
//     'order_update_time',
//     'order_auditor_name',
//     'order_audit_date',
//     'order_create_user_name',
//     'order_create_time',
//     'order_no',
//     'sale_system_name',
//     'voucher_number',
//     'order_time',
//     'receipt_time',
//     'customer_name',
//     'sale_user_name',
//     'deposit',
//     'receipt_address',
//     'internal_remark',
//     'order_customer_name',
//     'plan_type_name',
//   ]
//   const res = []
//   tableList.forEach((row, rowIndex) => {

//   })
//   return [
//     { row: 1, col: 1, rowspan: 3, colspan: 1 },
//     { row: 1, col: 3, rowspan: 2, colspan: 1 },
//   ]
// })
const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  height: '100%',
  scrollY: { enabled: true, gt: 40 },
  showOperate: true,
  operateWidth: '220',
  showSort: false,
  fieldApiKey: 'ProductSalePlan_a',
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})
const tableConfig1 = ref({
  showFixed: true,
  loading: loading1,
  showPagition: true,
  // showSlotNums: true,
  page: page1,
  size: size1,
  total: total1,
  scrollY: { enabled: true, gt: 100 },
  // showOperate: true,
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  height: '100%',
  operateWidth: '220',
  showSort: false,
  fieldApiKey: 'ProductSalePlan_c',
  colspanMethod: mergeRowMethod,
  // mergeCells,
  handleSizeChange: (val: number) => handleSizeChange1(val),
  handleCurrentChange: (val: number) => handleCurrentChange1(val),
})

// 选择客户
async function customerChange(val: any) {
  // 只有一个营销体系的时候带出
  state.filterData.sale_system_id = val?.sale_system_ids?.length === 1 ? val.sale_system_ids[0] : ''
}

function handReset() {
  state.filterData = resetData(state.filterData)
}

const columnList = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '公司合同编号',
    width: '8%',
    soltName: 'link',
    fixed: 'left',
  },
  {
    sortable: true,
    field: 'plan_type_name',
    title: '销售类型',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'voucher_number',
    title: '合同编号',
    minWidth: 100,
  },
  {
    field: 'image_code',
    soltName: 'image_code',
    title: '凭证信息',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'order_time',
    title: '订单日期',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'receipt_time',
    title: '交货日期',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'customer_name',
    title: '客户名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_user_name',
    title: '销售员',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'deposit',
    title: '定金',
    minWidth: 80,
  },
  {
    sortable: true,
    field: 'receipt_address',
    title: '交货地址',
    minWidth: 120,
    soltName: 'receipt_address',
  },
  {
    sortable: true,
    field: 'internal_remark',
    title: '单据备注',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    minWidth: 120,
  },
  {
    field: 'create_time',
    title: '创建时间',
    sortable: true,
    minWidth: 140,
    isDate: true,
  },
  {
    field: 'audit_date',
    title: '审核时间',
    sortable: true,
    minWidth: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'auditor_name',
    title: '审核人',
    minWidth: 120,
  },
  {
    field: 'update_time',
    title: '最后修改时间',
    sortable: true,
    isDate: true,
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'order_status',
    title: '单据状态',
    fixed: 'right',
    width: '5%',
    soltName: 'audit_status',
    showOrder_status: true,
  },
])
const columnList1 = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '公司合同编号',
    width: '8%',
    soltName: 'link',
  },
  {
    sortable: true,
    field: 'plan_type_name',
    title: '销售类型',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'voucher_number',
    title: '合同编号',
    minWidth: 100,
  },
  {
    field: 'image_code',
    soltName: 'image_code',
    title: '凭证信息',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'order_time',
    title: '订单日期',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'receipt_time',
    title: '交货日期',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'order_customer_name',
    title: '客户名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_user_name',
    title: '销售员',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'deposit',
    title: '定金',
    minWidth: 80,
  },
  {
    sortable: true,
    field: 'receipt_address',
    title: '交货地址',
    minWidth: 120,
    soltName: 'receipt_address',
  },
  {
    sortable: true,
    field: 'internal_remark',
    title: '单据备注',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'detail_order_no',
    title: '详情订单编号',
    minWidth: 150,
  },
  {
    field: 'product_code',
    title: '产品编号',
    minWidth: 100,
  },
  {
    field: 'product_name',
    title: '产品名称',
    minWidth: 100,
  },
  // {
  //   field: '',
  //   title: '所属客户',
  //   minWidth: 100,
  // },
  {
    field: 'customer_account_num',
    title: '款号',
    minWidth: 100,
  },
  {
    field: 'product_color_code',
    title: '色号',
    minWidth: 100,
  },
  {
    field: 'product_color_name',
    title: '颜色',
    minWidth: 100,
  },
  {
    field: 'product_color_kind_name',
    title: '颜色类别',
    minWidth: '6%',
  },
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 100,
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 100,
  },
  {
    field: 'ingredient',
    title: '原料成分',
    minWidth: 100,
  },
  {
    field: 'finish_product_width_and_unit_name',
    title: '成品幅宽',
    minWidth: 100,
  },
  {
    field: 'finish_product_gram_weight_and_unit_name',
    title: '成品克重',
    minWidth: 110,
  },
  {
    field: 'product_level_name',
    title: '成品等级',
    minWidth: 100,
  },
  {
    field: 'paper_tube_weight',
    title: '纸筒',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'weight_error',
    title: '空差',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'measurement_unit_name',
    title: '单位',
    minWidth: 100,
  },
  {
    field: 'roll',
    title: '匹数',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'dye_roll',
    title: '已排染匹数',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'weight',
    title: '数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'upper_limit',
    title: '上限',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'lower_limit',
    title: '下限',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'unit_price',
    title: '单价',
    minWidth: 100,
    isUnitPrice: true,
  },
  {
    field: 'other_price',
    title: '其他金额',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'total_price',
    title: '总金额',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    minWidth: 120,
  },
  {
    field: 'order_create_time',
    title: '创建时间',
    sortable: true,
    minWidth: 140,
    isDate: true,
  },
  {
    field: 'order_audit_date',
    title: '审核时间',
    sortable: true,
    minWidth: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'order_auditor_name',
    title: '审核人',
    minWidth: 120,
  },
  {
    field: 'order_update_time',
    title: '最后修改时间',
    sortable: true,
    isDate: true,
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'order_update_user_name',
    title: '修改人',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'situ_status_name',
    title: '进度状态',
    minWidth: '6%',
    fixed: 'right',
  },
  {
    sortable: true,
    field: 'push_status',
    showStatus: false,
    showOrder_status: false,
    title: '下推状态',
    fixed: 'right',
    minWidth: '5%',
    soltName: 'push_status',
  },
  {
    sortable: true,
    field: 'audit_status',
    title: '单据状态',
    fixed: 'right',
    width: '5%',
    soltName: 'audit_status',
    showOrder_status: true,
  },
  {
    field: 'operate',
    title: '操作',
    fixed: 'right',
    width: '11%',
    soltName: 'operate',
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// 导出
const loadingExcel = ref(false)
async function handleExport() {
  if (mergeFlag.value) {
    if (!data1?.value.list || data1.value.list.length <= 0)
      return ElMessage.warning('当前无数据可导出')
  }
  else {
    if (!data?.value.list || data.value.list.length <= 0)
      return ElMessage.warning('当前无数据可导出')
  }
  const name_str = '成品销售计划单'
  const {
    fetchData: getFetch,
    success: getSuccess,
    msg: getMsg,
  } = getSaleProductPlanOrderListExport({ nameFile: name_str })
  loadingExcel.value = true
  await getFetch({
    ...getFilterData({
      ...queryData.value,
      ...(mergeFlag.value
        ? {
            detail_order_id:
              state.multipleSelection?.map((item: any) => item.id).join(',')
              || null,
          }
        : {
            order_id:
              state.multipleSelection?.map((item: any) => item.id).join(',')
              || null,
          }),
    }),
    download: 1,
  })
  if (getSuccess.value) {
    ElMessage({
      type: 'success',
      message: '成功',
    })
  }
  else {
    ElMessage({
      type: 'error',
      message: getMsg.value,
    })
  }
  loadingExcel.value = false
}

const router = useRouter()

function handleAdd(type: number) {
  if (type === 3) {
    router.push({
      name: 'ProductSalePlanAdd',
    })
  }
  else if (type === 2) {
    router.push({
      name: 'GreySalePlanAdd',
    })
  }
  else if (type === 1) {
    router.push({
      name: 'RawSalePlanAdd',
    })
  }
}
function getRowId(row: any) {
  return !mergeFlag.value ? row.id : row.sale_product_plan_order_id
}
function handEdit(row: any) {
  if (row?.plan_type === 3) {
    router.push({
      name: 'RawSalePlanEdit',
      query: { id: getRowId(row) },
    })
  }
  else if (row?.plan_type === 2) {
    router.push({
      name: 'GreySalePlanEdit',
      query: { id: getRowId(row) },
    })
  }
  else if (row?.plan_type === 1) {
    router.push({
      name: 'ProductSalePlanEdit',
      query: { id: getRowId(row) },
    })
  }
}

function handDetail(row: any) {
  if (row?.plan_type === 3) {
    router.push({
      name: 'RawSalePlanDetail',
      query: { id: getRowId(row) },
    })
  }
  else if (row?.plan_type === 2) {
    router.push({
      name: 'GreySalePlanDetail',
      query: { id: getRowId(row) },
    })
  }
  else if (row?.plan_type === 1) {
    router.push({
      name: 'ProductSalePlanDetail',
      query: { id: getRowId(row) },
    })
  }
}

const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = updateSaleProductPlanOrderAuditStatusPass()

// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')

  if (res) {
    await auditFetch({
      audit_status: 2,
      id: !mergeFlag.value
        ? row.id.toString()
        : row.sale_product_plan_order_id.toString(),
    })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = updateSaleProductPlanOrderAuditStatusWait()

async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')

  if (res) {
    await cancelFetch({
      audit_status: 1,
      id: !mergeFlag.value
        ? row.id.toString()
        : row.sale_product_plan_order_id.toString(),
    })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
const currentId = ref(0)
const currentPlanType = ref(0)
const showPushDownModal = ref(false)
function handlePushDown(row: any) {
  currentId.value = getRowId(row)
  currentPlanType.value = row.plan_type
  showPushDownModal.value = true
}

function mergeSpan() {
  mergeFlag.value = !mergeFlag.value
  state.multipleSelection = []
  localStorage.setItem('productSalePlanMergeFlag', String(mergeFlag.value))
  getData()
}

onActivated(() => {
  getData()
})
onMounted(() => {
  getData()
})
</script>

<template>
  <div class="flex flex-col h-full overflow-hidden">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '50px' }">
        <DescriptionsFormItem label="订单日期:" copies="2">
          <template #content>
            <SelectDate v-model="state.filterData.create_time" />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="客户名称:">
          <template #content>
            <SelectCustomerDialog
              v-model="state.filterData.customer_id"
              field="name"
              is-merge
              @change-value="customerChange"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.sale_system_id"
              api="GetSaleSystemDropdownListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证单号:">
          <template #content>
            <el-input
              v-model="state.filterData.voucher_number"
              placeholder="凭证单号"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="计划单号:">
          <template #content>
            <el-input v-model="state.filterData.order_no" placeholder="销售计划单号" clearable />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="销售员名称:">
          <template #content>
            <el-form-item prop="sale_user_id">
              <SelectComponents v-model="state.filterData.sale_user_id" :query="{ duty: EmployeeType.salesman }" api="GetEmployeeListEnum" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="产品名称:">
          <template #content>
            <el-input
              v-model="state.filterData.product_code_and_name"
              placeholder="产品编号、名称"
              clearable
            />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="色号颜色:">
          <template #content>
            <el-input
              v-model="state.filterData.color_code_and_name"
              placeholder="色号、颜色"
              clearable
            />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.audit_status"
              api="GetAuditStatusEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售类型:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.plan_type"
              api="GetPlanTypeReverseIntMap"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="进度状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.situ_status_arr"
              api="GetSituStatusReverseIntMap"
              label-field="name"
              value-field="id"
              multiple
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="款号:">
          <template #content>
            <el-input
              v-model="state.filterData.account_num"
              placeholder="款号"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="备注:">
          <template #content>
            <el-input
              v-model="state.filterData.remark"
              placeholder="备注"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard
      title=""
      class="mt-[5px] flex flex-1 flex-col overflow-hidden h-full"
    >
      <template #right-top>
        <el-button
          v-has="'ProductSalePlanAdd'"
          size="small"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handleAdd(3)"
        >
          新建成品计划
        </el-button>
        <el-button
          v-has="'ProductSalePlanAdd'"
          plain
          size="small"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handleAdd(1)"
        >
          新建原料计划
        </el-button>
        <el-button
          v-has="'ProductSalePlanAdd'"
          size="small"
          plain
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handleAdd(2)"
        >
          新建坯布计划
        </el-button>
        <el-button
          plain
          size="small"
          style="margin-left: 10px" type="primary" @click="mergeSpan"
        >
          合并切换
        </el-button>
        <BottonExcel
          v-has="'ProductSalePlanExport'"
          :loading="loadingExcel"
          title="导出文件"
          @on-click-excel="handleExport"
        />
      </template>
      <Table
        v-show="mergeFlag"
        :config="tableConfig1"
        :table-list="data1?.list"
        :column-list="columnList1"
      >
        <template #link="{ row }">
          <el-link type="primary" :underline="false" @click="handDetail(row)">
            {{ row?.order_no }}
          </el-link>
        </template>
        <template #image_code="{ row }">
          <CoverImage :file-url="row.cover_texture_url" />
        </template>
        <template #receipt_address="{ row }">
          {{ row.location }}{{ row.receipt_address }}
        </template>
        <template #push_status="{ row }">
          <div
            v-if="row.push_status === 1"
            class="w-[60px] h-[20px] bg-[#dcdfe6] rounded-[20px] text-[#f2f2f2] text-center"
          >
            {{ row?.push_status_name }}
          </div>
          <div
            v-else-if="row.push_status === 2"
            class="w-[60px] h-[20px] bg-[#409eff] rounded-[20px] text-white text-center"
          >
            {{ row?.push_status_name }}
          </div>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <PrintPopoverBtn
              :id="getRowId(row)"
              :key="row.id"
              print-btn-text="打印"
              print-btn-type="text"
              api="getSaleProductPlanOrder"
              :options="options"
            />
            <el-button
              v-has="'ProductSalePlanDetail'"
              type="primary"
              text
              link
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-button>
            <el-button
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'ProductSalePlanEdit'"
              type="primary"
              text
              link
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="row.audit_status === 1"
              v-has="'ProductSalePlanPass'"
              :underline="false"
              type="primary"
              text
              link
              @click="handAudit(row)"
            >
              审核
            </el-button>
            <el-button
              v-if="row.audit_status === 2"
              v-has="'ProductSalePlanWait'"
              :underline="false"
              type="primary"
              text
              link
              @click="handApproved(row)"
            >
              消审
            </el-button>
            <el-button
              v-if="(row.plan_type === SaleTypeEnum.FinishProduct || row.plan_type === SaleTypeEnum.GreyFabric) && row.audit_status === 2"
              v-has="'ProductSalePlanPushDown'"
              type="warning"
              text
              link
              :underline="false"
              @click="handlePushDown(row)"
            >
              下推
            </el-button>
          </el-space>
        </template>
      </Table>
      <Table
        v-show="!mergeFlag"
        :config="tableConfig"
        :table-list="data?.list"
        :column-list="columnList"
      >
        <template #link="{ row }">
          <el-link type="primary" :underline="false">
            {{ row?.order_no }}
          </el-link>
        </template>
        <template #image_code="{ row }">
          <CoverImage :file-url="row.cover_texture_url" />
        </template>
        <template #receipt_address="{ row }">
          {{ row.location }}{{ row.receipt_address }}
        </template>
        <template #operate="{ row }">
          <el-button
            v-has="'ProductSalePlanDetail'"
            type="primary"
            text
            link
            @click="handDetail(row)"
          >
            查看
          </el-button>
          <el-button
            v-if="row.audit_status === 1 || row.audit_status === 3"
            v-has="'ProductSalePlanEdit'"
            type="primary"
            text
            link
            @click="handEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="row.audit_status === 1"
            v-has="'ProductSalePlanPass'"
            type="primary"
            text
            link
            @click="handAudit(row)"
          >
            审核
          </el-button>
          <el-button
            v-if="row.audit_status === 2"
            v-has="'ProductSalePlanWait'"
            type="primary"
            text
            link
            @click="handApproved(row)"
          >
            消审
          </el-button>
          <PrintPopoverBtn
            :id="row.id"
            print-btn-text="打印"
            api="getSaleProductPlanOrder"
            :options="options"
          />
          <!--          <el-popover -->
          <!--            placement="left" -->
          <!--            title="选择打印" -->
          <!--            :width="160" -->
          <!--            trigger="hover" -->
          <!--          > -->
          <!--            <template #reference> -->
          <!--              <el-button type="text">打印</el-button> -->
          <!--            </template> -->
          <!--            &lt;!&ndash; 1656313773261056 &ndash;&gt; -->
          <!--            <PrintBtn -->
          <!--              btnText="产品销售合同" -->
          <!--              type="salePlanOrder" -->
          <!--              :tid="1656313773261056" -->
          <!--              :id="row.id" -->
          <!--              api="getSaleProductPlanOrder" -->
          <!--            /> -->
          <!--            <PrintBtn -->
          <!--              style="margin: 10px 0 0 0" -->
          <!--              btnText="产品销售清单" -->
          <!--              type="salePlanOrder" -->
          <!--              :tid="1655275977494784" -->
          <!--              :id="row.id" -->
          <!--              api="getSaleProductPlanOrder" -->
          <!--            /> -->
          <!--          </el-popover> -->
        </template>
      </Table>
    </FildCard>
  </div>
  <PushDown :id="currentId" v-model="showPushDownModal" :plan-type="currentPlanType" />
</template>

<style lang="scss" scoped>
::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}

.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
