<script setup lang="ts">
import { ref, watchEffect } from 'vue'
import { pullAt } from 'lodash-es'
import currency from 'currency.js'
import { ElMessage } from 'element-plus'
import { useToggle } from '@vueuse/core'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import GridTable from '@/components/GridTable/index.vue'
import createColumnList from '@/pages/procurementManaging/blankFabricManagement/components/AddressConfig/columnList'
import { sumNum } from '@/common/format'

interface Props {
  isEdit?: boolean
  row: Modal.AddressConfigRow
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: true,
})

const emits = defineEmits(['submit'])

const data = ref([])

function computedData() {
  if (props.row?.item_addr_data) {
    data.value = props.row.item_addr_data?.map((item) => {
      return {
        ...item,
        recipient_entity_id: item?.recipient_entity_id || '',
        average_weight: props.row.average_weight,
      }
    })
    return
  }
  // 如果item_addr_data不存在或者是null，则push一条进去
  if (props.row?.item_addr_data === null || props.row?.item_addr_data === void 0) {
    data.value.push({
      ...props.row,
    })
  }
}
function clearData() {
  data.value = []
}

function addDataRow() {
  data.value.push({
    ...props.row,
  })
}

function deleteDataRow(rowIndex: number) {
  pullAt(data.value, rowIndex)
}

const show = defineModel()

watchEffect(() => {
  if (show.value)
    computedData()
  else
    clearData()
})

function onClose() {
  show.value = false
}

const [buttonLoading, toggleButtonLoading] = useToggle(false)

function checkError(msg: string) {
  toggleButtonLoading(false)
  return ElMessage.error(`收货地址输入框内${msg}与分录行的${msg}不一致`)
}

function onSubmit() {
  toggleButtonLoading(true)
  // 校验数量总计总和与外面的数量总计是否一致
  const allNumber = sumNum(data.value, 'number')
  if (currency(allNumber).value !== currency(props.row.number).value)
    return checkError('总匹数')

  const allTotalWeight = sumNum(data.value, 'total_weight')
  if (currency(allTotalWeight).value !== currency(props.row.total_weight).value)
    return checkError('数量总计')

  // 关闭loading，关闭弹窗
  toggleButtonLoading(false)
  emits('submit', data.value)
}

const tableConfig = ref({
  showSeq: true,
  height: '100%',
  footerMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        if (['number', 'total_weight'].includes(column.property))
          return `${sumNum(data, column.property) as unknown as number}`
        return null
      }),
    ]
  },
})

const columnList = createColumnList(deleteDataRow)
</script>

<template>
  <vxe-modal v-model="show" title="坯布收货地址" show-footer width="1000" height="70vh" :mask="false" :lock-view="false" :esc-closable="true" resize @close="onClose">
    <section class="list-page">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="坯布编号:">
          <template #content>
            {{ row?.code }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布名称:">
          <template #content>
            {{ row?.name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="采购匹数:">
          <template #content>
            {{ row?.number }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="数量总计:">
          <template #content>
            {{ row?.total_weight }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button size="small" type="primary" @click="addDataRow">
              新增
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
      <div class="table-card-full">
        <GridTable :config="tableConfig" :columns="columnList" :data="data" />
      </div>
    </section>
    <template #footer>
      <el-button type="primary" :loading="buttonLoading" @click="onSubmit">
        提交
      </el-button>
    </template>
  </vxe-modal>
</template>
