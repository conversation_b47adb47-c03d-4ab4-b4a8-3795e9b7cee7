<script setup lang="ts">
import { nextTick, reactive, ref, watch } from 'vue'
import { GetProductionNotifyOrderDropdownListPage } from '@/api/productionNotice'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatHashTag, formatTime } from '@/common/format'
import { debounce, getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import SelectDate from '@/components/SelectDate/index.vue'

export interface Props {
  order_no: string
  modelValue: boolean
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  order_no: '',
  title: '选择关联的生产通知单',
  modelValue: false,
})
const emit = defineEmits(['update:modelValue', 'submit'])
const state = reactive({
  filterData: {
    order_no: '',
    notify_date: '',
    grey_fabric_name: '',
    grey_fabric_code: '',
    weaving_mill_id: '',
    start_notify_date: '',
    end_notify_date: '',
  },
  tableList: [],
  isBatch: false,
})
const isComplete = ref(false)
const tablesRef = ref()
const { fetchData, data: dataList, page, size, total, loading, handleSizeChange, handleCurrentChange } = GetProductionNotifyOrderDropdownListPage()
async function getData() {
  await fetchData(getFilterData({ ...state.filterData, status: isComplete.value ? 2 : undefined }))
  const res: any = dataList.value.list?.filter((item: any) => {
    return props.order_no === item.order_no
  })
  nextTick(() => {
    tablesRef.value.tableRef.setRadioRow(res[0])
  })
}

const showModal = ref<boolean>(false)
watch(
  () => [props.modelValue, props.order_no],
  () => {
    if (props.modelValue)
      getData()

    if (!props.modelValue) {
      state.filterData = {
        order_no: '',
        notify_date: '',
        grey_fabric_name: '',
        grey_fabric_code: '',
        weaving_mill_id: '',
        start_notify_date: '',
        end_notify_date: '',
      }
    }
    showModal.value = props.modelValue
  },
)
watch(
  () => state.filterData.notify_date,
  (val) => {
    state.filterData.start_notify_date = formatTime(val[0])
    state.filterData.end_notify_date = formatTime(val[1])
  },
)

watch(
  [() => state.filterData, isComplete],
  debounce(() => {
    getData()
  }, 400),
  { deep: true },
)

const columnList = ref([
  {
    title: '生产通知单号',
    field: 'order_no',
  },
  {
    title: '通知日期',
    field: 'notify_date',
    width: 100,
    isDate: true,
    formatTime: 'YYYY-MM-DD',
  },
  {
    title: '织厂名称',
    field: 'weaving_mill_name',
    width: 100,
  },
  // {
  //   title: '坯布编号',
  //   field: 'grey_fabric_code',
  //   width: 100,
  // },
  {
    title: '坯布名称',
    field: 'grey_fabric_name',
    soltName: 'grey_fabric_name',
    width: 140,
  },
  {
    title: '坯布幅宽',
    field: 'grey_fabric_width',
    width: 100,
  },
  {
    title: '坯布克重',
    field: 'grey_fabric_gram_weight',
    width: 100,
  },
  {
    title: '针寸数',
    field: 'needle_size',
    width: 100,
  },
  {
    title: '织造单价',
    field: 'process_price',
    width: 100,
    isUnitPrice: true,
  },
])

function onClose() {
  emit('update:modelValue', false)
}

const selectList = ref<any[]>()
function radioChangeEvent({ row }: any) {
  selectList.value = row
}

function submit() {
  emit('submit', selectList.value)
}

const tableConfig = ref({
  showRadio: true,
  loading,
  radioChangeEvent,
  handleSizeChange,
  handleCurrentChange,
  showPagition: true,
  height: 'auto',
  page,
  size,
  total,
})

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="showModal" show-footer :title="props.title" width="1000" height="670" :mask="false" :lock-view="false" :esc-closable="true" resize @close="onClose">
    <div class="flex flex-col h-full">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="生产通知单:">
          <template #content>
            <el-input v-model="state.filterData.order_no" placeholder="生产通知单" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="生产日期:" width="330">
          <template #content>
            <SelectDate v-model="state.filterData.notify_date" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布名称:">
          <template #content>
            <el-input v-model="state.filterData.grey_fabric_name" placeholder="坯布名称" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布编号:">
          <template #content>
            <el-input v-model="state.filterData.grey_fabric_code" placeholder="坯布编号" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织厂名称:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.weaving_mill_id"
              :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory }"
              style="width: 200px"
              api="GetBusinessUnitListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-checkbox v-model="isComplete">
              显示已完成单据
            </el-checkbox>
          </template>
        </DescriptionsFormItem>
      </div>
      <div class="flex-1 overflow-y-hidden">
        <Table ref="tablesRef" :config="tableConfig" :table-list="dataList.list" :column-list="columnList">
          <template #creatTime />
          <template #grey_fabric_name="{ row }">
            {{ formatHashTag(row.grey_fabric_code, row.grey_fabric_name) }}
          </template>
        </Table>
      </div>
    </div>
    <template #footer>
      <el-button type="primary" size="small" @click="submit">
        提交
      </el-button>
    </template>
  </vxe-modal>
</template>
