<script lang="ts" setup name="FinishedProductPurchaseReturnsAdd">
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { reactive, ref, watchEffect } from 'vue'
import currency from 'currency.js'
import SelectPurchasing from './components/SelectPurchasing/index.vue'
import SelectRawMaterial from './components/SelectRawMaterial/index.vue'
import { AddPurchaseProductReturnOrder } from '@/api/finishedProductPurchaseReturns'
import { BusinessUnitIdEnum, WarehouseTypeIdEnum } from '@/common/enum'
import { formatDate, formatLengthMul, formatPriceMul, formatTwoDecimalsDiv, formatTwoDecimalsMul, formatUnitPriceMul, formatWeightMul, sumNum } from '@/common/format'
import { getDefaultSaleSystem, getFilterData, isMainUnit } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import SelectSaleMode from '@/components/SelectSaleMode/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'
import type { TableColumn } from '@/components/Table/type'
import { SaleModeEnum } from '@/enum/orderEnum'

const routerList = useRouterList()

const state = reactive({
  form: {
    sale_system_id: getDefaultSaleSystem()?.default_sale_system_id,
    supplier_id: 0,
    supplier_name: '',
    return_time: '',
    warehouse_return_id: '',
    invoice_header_id: '',
    sale_mode: SaleModeEnum.Bulk,
    remark: '',
    item_data: [] as any[],
  },
  tableData: [] as any[],
})

const fromRules = {
  sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
  supplier_id: [{ required: true, message: '请选择供应商', trigger: 'change' }],
  warehouse_return_id: [{ required: true, message: '请选择退货仓库名称', trigger: 'change' }],
  return_time: [{ required: true, message: '请选择退货日期', trigger: 'change' }],
}

const showAdd = ref(false)
const bulkShow = ref(false)
const multipleSelection = ref<any[]>([])

function handAdd() {
  if (!state.form.warehouse_return_id)
    return ElMessage.warning('请选择退货仓库名称')
  showAdd.value = true
}

function handEdit() {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请选择批量修改的数据')
  bulkShow.value = true
}

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  recipien_entity_name: '',
})

const columnList = ref<TableColumn[]>([
  {
    field: 'product_code',
    title: '成品编号',
    fixed: 'left',
    width: 150,
  },
  {
    field: 'product_name',
    title: '成品名称',
    fixed: 'left',
    width: 100,
  },
  {
    field: 'product_purchase_order_no',
    title: '采购单号',
    fixed: 'left',
    width: 200,
    soltName: 'product_purchase_order_no',
  },
  {
    field: 'customer_name',
    title: '所属客户',
    width: 130,
    // soltName: 'customer_id',
  },
  {
    field: 'product_color_code',
    title: '颜色编号',
    width: 100,
  },
  {
    field: 'product_color_name',
    title: '颜色名称',
    width: 100,
  },
  {
    field: 'dye_factory_dyelot_number',
    title: '染厂缸号',
    width: 100,
  },
  {
    field: 'product_craft',
    title: '成品工艺',
    width: 100,
  },
  {
    field: 'product_level_name',
    title: '成品等级',
    width: 100,
  },
  {
    field: 'product_ingredient',
    title: '成品成分',
    width: 100,
  },
  {
    field: 'product_remark',
    title: '成品备注',
    width: 130,
  },
  {
    field: 'remark',
    title: '备注',
    width: 150,
    soltName: 'remark',
  },

  {
    field: 'available_roll', // 不用传
    title: '可用库存',
    width: 100,
    isPrice: true,
  },
  {
    field: 'return_roll',
    title: '退货数',
    width: 100,
    soltName: 'return_roll',
  },
  {
    field: 'avg_weight',
    title: '均重',
    width: 100,
    soltName: 'avg_weight',
  },
  {
    field: 'return_weight',
    title: '退货数量',
    width: 100,
    soltName: 'return_weight',
  },
  {
    field: 'measurement_unit_name',
    title: '单位',
    width: 100,
  },
  {
    field: 'auxiliary_unit_id',
    title: '结算单位',
    width: 100,
    soltName: 'auxiliary_unit_id',
  },
  {
    field: 'weight_unit_price',
    title: '数量单价',
    width: 100,
    soltName: 'weight_unit_price',
    required: true,
  },
  {
    field: 'return_length',
    title: '辅助数量',
    width: 100,
    soltName: 'return_length',
  },
  {
    field: 'length_unit_price',
    title: '辅助数量单价',
    width: 100,
    soltName: 'length_unit_price',
  },
  {
    field: 'return_price',
    title: '退货金额',
    width: 100,
  },
])

const tablesRef = ref()

function footerMethod({ columns, data }: { columns: any, data: any }) {
  const footerData = [
    columns.map((column: any, _columnIndex: number) => {
      if (['product_code'].includes(column.field))
        return '合计'

      if (['return_roll'].includes(column.field))
        return sumNum(data, column.field)

      if (['return_weight'].includes(column.field))
        return sumNum(data, column.field)

      if (['return_price'].includes(column.field))
        return `¥${sumNum(data, column.field)}`
    }),
  ]
  return footerData
}

const selectPurchaseOrder = ref()
const showPurchasing = ref(false)
const orderFilterData = ref({
  sale_system_id: 0,
  supplier_id: 0,
  code: '',
  name: '',
})
function openPurchaseOrder(row: any) {
  if (state.form.sale_system_id && state.form.supplier_id) {
    showPurchasing.value = true
    selectPurchaseOrder.value = row
    orderFilterData.value.sale_system_id = state.form?.sale_system_id
    orderFilterData.value.supplier_id = state.form?.supplier_id
    orderFilterData.value.code = row?.product_code
    orderFilterData.value.name = row?.product_name
  }
  else {
    ElMessage.warning('请先选择营销体系和供应商')
  }
}
function clearPur(row: any) {
  row.product_purchase_order_no = ''
}

function onSubmit(row: any) {
  showAdd.value = false
  const val = row?.map((item: any) => {
    const temp = {
      ...item,
      // customer_id: saleSaleSystemInfo.value.default_customer_id,
      dye_factory_dyelot_number: item?.dyelot_number,
      product_craft: item?.finish_product_craft,
      product_ingredient: item?.finish_product_ingredient,
      product_remark: item?.product_remark,
      stock_roll: item?.roll,
      weight_unit_price: item?.unit_price || '',
      length_unit_price: item?.length_unit_price || '',
      return_roll: '',
      avg_weight: '',
      return_length: '',
      selected: false,
      sum_stock_id: item?.stock_product_id,
      return_weight: item?.return_weight || '',
    }
    return conductUnitPrice(temp, true)
  })
  state.tableData = [...state.tableData, ...val]
}

function onSubmitPurchasing(row: any) {
  showPurchasing.value = false
  selectPurchaseOrder.value.product_purchase_order_no = row.order_no
}

const changeRow = ref<any>()
function changeData(row: any, key?: string) {
  changeRow.value = row

  if (key && ['avg_weight', 'return_roll'].includes(key))
    computedReturnWeight(changeRow.value)
}

/**
 * 根据结算单位是否为主单位显示单价
 * @param item 成品数据
 * @param isInit 是否需要初始化单位
 */
function conductUnitPrice(item: any, isInit = false) {
  // 初始化结算单位
  if (isInit && !item.auxiliary_unit_id) {
    item.auxiliary_unit_id = item.measurement_unit_id
    item.auxiliary_unit_name = item.unit_name
  }
  if (isMainUnit(item))
    item.length_unit_price = 0 // 主单位-把辅助单价置0

  else
    item.weight_unit_price = 0 // 辅助单位-把单价置0
  computedReturnPrice(item)
  return item
}

watchEffect(() => {
  computedReturnPrice(changeRow.value)
})

const { fetchData: fetchDataAdd, data: addData, success: successAdd, msg: msgAdd } = AddPurchaseProductReturnOrder()
const ruleFormRef = ref()
async function handSubmit() {
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      if (validateList())
        return false
      const tableData = fomatData()
      state.form.item_data = tableData
      await fetchDataAdd(getFilterData({ ...state.form, return_time: formatDate(state.form.return_time) }))
      if (successAdd.value) {
        ElMessage.success('添加成功')
        routerList.push({
          name: 'FinishedProductPurchaseReturnsDetail',
          params: { id: addData.value.id },
        })
      }
      else {
        ElMessage.error(msgAdd.value)
      }
    }
  })
}

// 整理坯布信息
function fomatData() {
  return state.tableData?.map((item: any) => {
    return {
      ...item,
      avg_weight: formatWeightMul(item?.avg_weight || 0),
      return_weight: formatWeightMul(item?.return_weight || 0),
      weight_unit_price: formatUnitPriceMul(item?.weight_unit_price || 0),
      return_length: formatLengthMul(item?.return_length || 0),
      length_unit_price: formatUnitPriceMul(item?.length_unit_price || 0),
      return_price: formatPriceMul(item?.return_price || 0),
      return_roll: formatTwoDecimalsMul(item?.return_roll || 0),
    }
  })
}

// 验证坯布信息字段
function validateList() {
  let msg = ''
  if (!state.tableData || state.tableData.length === 0) {
    msg = '成品信息不能为空'
  }
  else {
    state.tableData?.some((item: any) => {
      // if (!item.customer_id) {
      //   msg = `成品编号为${item.product_code}的数据,所属客户不能为空`
      //   return true
      // }

      if (!Number(item.return_weight)) {
        msg = `成品编号为${item.product_code}的数据,退货数量不能为空且不能为0`
        return true
      }
      if (!item.auxiliary_unit_id) {
        msg = `成品编号为${item.product_code}的数据,结算单位不能为空`
        return true
      }
      if (item.weight_unit_price === '' && isMainUnit(item)) {
        msg = `成品编号为${item.product_code}的数据,数量单价不能为空`
        return true
      }
      if (Number(!item.return_length) && !isMainUnit(item)) {
        msg = `成品编号为${item.product_code}的数据,辅助数量不能为空且不能为0`
        return true
      }
      if (item.length_unit_price === '' && !isMainUnit(item)) {
        msg = `成品编号为${item.product_code}的数据,辅助数量单价不能为空`
        return true
      }
    })
  }
  msg && ElMessage.error(msg)
  return msg
}

function handDel(row: any, rowIndex: number) {
  state.tableData.splice(rowIndex, 1)
}

const saleSaleSystemInfo = ref()
function getSaleSystem(row: any) {
  saleSaleSystemInfo.value = row
  clearData(row)
}

const bulkSetting = ref<any>({
  address: {
    receipt_unit_name: '',
    receipt_unit_id: '',
    receipt_person: '',
    receipt_address: '',
  },
})
function handBulkClose() {
  bulkShow.value = false
}

const bulkList = reactive<any>([
  {
    field: 'customer_id',
    field_name: 'customer_name',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
    query: { sale_system_id: state.form.sale_system_id },
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
  },
  {
    field: 'return_roll',
    title: '退货数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'avg_weight',
    title: '均重',
    component: 'input',
    type: 'float',
    digits: '4',
  },
  {
    field: 'return_weight',
    title: '退货数量',
    component: 'input',
    type: 'float',
    digits: '4',
  },
  {
    field: 'weight_unit_price',
    title: '数量单价',
    component: 'input',
    type: 'float',
    digits: '2',
  },
  {
    field: 'return_length',
    title: '辅助数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'length_unit_price',
    title: '辅助数量单价',
    component: 'input',
    type: 'float',
    digits: '2',
  },
])

async function bulkSubmit({ row, value, quickInputResult }: any) {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  multipleSelection.value?.map((item: any, index: number) => {
    if (item?.selected) {
      if (row.quickInput && quickInputResult?.[index]) {
        item[row.field] = quickInputResult[index]
        return item
      }

      item[row.field] = value[row.field]
      return item
    }
    computedReturnWeight(item)
    computedReturnPrice(item)
  })
  bulkShow.value = false
  ElMessage.success('设置成功')
}
// 计算退货数量:退货数*均重
function computedReturnWeight(changeRow: any) {
  if (!changeRow)
    return
  changeRow.return_weight = currency(Number(changeRow?.return_roll || 0)).multiply(Number(changeRow?.avg_weight || 0)).value
}
// 计算退货金额=(退货数量*数量单价)+(辅助数量*辅助数量单价)
function computedReturnPrice(changeRow: any) {
  if (!changeRow || changeRow?.return_weight < 0)
    return
  const price_weight = currency(changeRow?.return_weight || 0).multiply(changeRow?.weight_unit_price || 0)
  const price_length = currency(changeRow?.return_length || 0).multiply(changeRow?.length_unit_price || 0)
  changeRow.return_price = currency(price_weight).add(price_length).value
  tablesRef.value.tableRef.updateFooter()
}
function handAllSelect({ records }: any) {
  multipleSelection.value = records
}

function handleSelectionChange({ records }: any) {
  multipleSelection.value = records
}

function clearData(row: any) {
  state.tableData?.map((item) => {
    item.customer_id = saleSaleSystemInfo.value?.default_customer_id || ''
    item.customer_name = saleSaleSystemInfo.value?.default_customer_name || ''
  })
  bulkList[0].query = { sale_system_id: row.id }
  bulkSetting.value.customer_id = ''
}
const tableConfig = ref({
  showCheckBox: true,
  showOperate: true,
  operateWidth: 100,
  footerMethod,
  handAllSelect,
  handleSelectionChange,
  fieldApiKey: 'FinishedProductPurchaseReturnsAdd',
})
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <div class="line" />
    <template #right-top>
      <el-button v-btnAntiShake="handSubmit" type="primary">
        提交
      </el-button>
    </template>
    <slot>
      <el-form ref="ruleFormRef" :model="state.form" label-width="60px" label-position="top" :rules="fromRules">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '106px' }">
          <DescriptionsFormItem required label="营销体系名称:">
            <template #content>
              <el-form-item prop="sale_system_id">
                <SelectComponents
                  v-model="state.form.sale_system_id"
                  :default-status="true"
                  api="GetSaleSystemDropdownListApi"
                  label-field="name"
                  value-field="id"
                  @select="getSaleSystem"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="供应商名称:">
            <template #content>
              <el-form-item prop="supplier_id">
                <!-- <SelectComponents
                  @select="val => (state.form.supplier_name = val?.name)"
                  v-model="state.form.supplier_id"
                  :query="{ unit_type_id: BusinessUnitIdEnum.finishedProduct }"
                  api="BusinessUnitSupplierEnumlist"
                  label-field="name"
                  value-field="id"
                ></SelectComponents> -->

                <SelectDialog
                  v-model="state.form.supplier_id"
                  :query="{ unit_type_id: BusinessUnitIdEnum.finishedProduct, name: componentRemoteSearch.name }"
                  api="BusinessUnitSupplierEnumlist"
                  :column-list="[
                    {
                      field: 'name',
                      title: '名称',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'name',
                          isEdit: true,
                          title: '名称',
                          minWidth: 100,
                        },
                      ],
                    },
                    {
                      field: 'code',
                      title: '供应商编号',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'code',
                          isEdit: true,
                          title: '供应商编号',
                          minWidth: 100,
                        },
                      ],
                    },
                  ]"
                  @change-value="val => (state.form.supplier_name = val?.name)"
                  @change-input="val => (componentRemoteSearch.name = val)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="退货日期:">
            <template #content>
              <el-form-item prop="return_time">
                <SelectDate v-model="state.form.return_time" type="date" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="退货仓库名称:">
            <template #content>
              <el-form-item prop="warehouse_return_id">
                <SelectComponents
                  v-model="state.form.warehouse_return_id"
                  :query="{ warehouse_type_id: WarehouseTypeIdEnum.finishProduction }"
                  api="GetPhysicalWarehouseDropdownList"
                  label-field="name"
                  value-field="id"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="订单类型:" :required="true">
            <template #content>
              <el-form-item prop="sale_mode">
                <SelectSaleMode v-model="state.form.sale_mode" :show-customer-book="false" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="发票抬头:">
            <template #content>
              <el-form-item prop="invoice_header_id">
                <SelectComponents v-model="state.form.invoice_header_id" api="GetInfoPurchaseInvoiceHeaderListUseByOther" label-field="name" value-field="id" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="备注:" copies="2">
            <template #content>
              <el-form-item prop="remark">
                <el-input v-model="state.form.remark" type="textarea" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
        </div>
      </el-form>
    </slot>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]">
    <template #right-top>
      <el-button style="margin-left: 10px" type="primary" @click="handEdit">
        批量操作
      </el-button>
      <el-button style="margin-left: 10px" type="primary" :icon="Plus" :disabled="!state.form.warehouse_return_id" @click="handAdd">
        根据库存添加
      </el-button>
    </template>
    <Table ref="tablesRef" :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
      <template #product_purchase_order_no="{ row }">
        <el-tag v-if="row.product_purchase_order_no" closable @close="clearPur(row)">
          {{ row.product_purchase_order_no }}
        </el-tag>
        <el-link v-else type="primary" @click="openPurchaseOrder(row)">
          选择
        </el-link>
      </template>
      <template #customer_id="{ row }">
        <SelectDialog
          v-model="row.customer_id"
          :query="{ sale_system_id: state.form.sale_system_id, name: componentRemoteSearch.customer_name }"
          api="GetCustomerEnumList"
          :column-list="[
            {
              title: '客户编号',
              minWidth: 100,
              required: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'code',
                  isEdit: true,
                  title: '客户编号',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '客户名称',
              minWidth: 100,
              colGroupHeader: true,
              required: true,
              childrenList: [
                {
                  isEdit: true,
                  field: 'name',
                  title: '客户名称',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '电话',
              colGroupHeader: true,
              minWidth: 100,
              childrenList: [
                {
                  field: 'phone',
                  isEdit: true,
                  title: '电话',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '销售员',
              minWidth: 100,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'seller_name',
                  title: '销售员',
                  soltName: 'seller_name',
                  isEdit: true,
                  minWidth: 100,
                },
              ],
            },
          ]"
          @change-input="val => (componentRemoteSearch.customer_name = val)"
        />
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" size="mini" maxlength="200" />
      </template>
      <template #return_roll="{ row }">
        <vxe-input v-model="row.return_roll" size="mini" type="float" :max="formatTwoDecimalsDiv(row.available_roll)" :min="0" @input="changeData(row, 'return_roll')" />
      </template>
      <template #avg_weight="{ row }">
        <vxe-input v-model="row.avg_weight" size="mini" type="float" :digits="4" :min="0" @input="changeData(row, 'avg_weight')" />
      </template>
      <template #return_weight="{ row }">
        <vxe-input v-model="row.return_weight" size="mini" type="float" :digits="4" :min="0" @input="changeData(row)" />
      </template>
      <!-- 结算单位 -->
      <template #auxiliary_unit_id="{ row }">
        <SelectComponents v-model="row.auxiliary_unit_id" size="small" style="width: 100%;" api="getInfoBaseMeasurementUnitList" label-field="name" value-field="id" @select="conductUnitPrice(row)" />
      </template>
      <!-- 单价 -->
      <template #weight_unit_price="{ row }">
        <vxe-input v-show="isMainUnit(row)" v-model="row.weight_unit_price" size="mini" type="float" :digits="2" @change="changeData(row)" />
        <span v-show="!isMainUnit(row)">{{ row.weight_unit_price }}</span>
      </template>
      <template #return_length="{ row }">
        <vxe-input v-model="row.return_length" :min="0" size="mini" type="float" @input="changeData(row)" />
      </template>
      <!-- 辅助数量单价 -->
      <template #length_unit_price="{ row }">
        <vxe-input v-show="!isMainUnit(row)" v-model="row.length_unit_price" size="mini" type="float" :digits="2" @input="changeData(row)" />
        <span v-show="isMainUnit(row)">{{ row.length_unit_price }}</span>
      </template>
      <template #return_price="{ row }">
        {{ row.return_price }}
      </template>

      <template #operate="{ row, rowIndex }">
        <el-button type="text" @click="handDel(row, rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <SelectRawMaterial v-model="showAdd" :warehouse_id="state.form.warehouse_return_id" @submit="onSubmit" />
  <SelectPurchasing v-model="showPurchasing" :code="orderFilterData?.code" :name="orderFilterData?.name" :supplier_id="orderFilterData?.supplier_id" :sale_system_id="orderFilterData?.sale_system_id" @submit="onSubmitPurchasing" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
</template>

<style lang="scss" scoped></style>
