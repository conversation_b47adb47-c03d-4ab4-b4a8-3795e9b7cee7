declare namespace Api.CustomerDetail {
  export interface Request {
    /**
     * id
     */
    id: number

    [property: string]: any
  }

  /**
   * system.GetBizUnitDetailResponse
   */
  export interface Response {
    /**
     * 地址
     */
    address?: string
    /**
     * 布飞前缀
     */
    bf_prefix?: string
    /**
     * 布飞卷号规则
     */
    bf_sequence_number_rule?: number
    /**
     * 布飞卷号规则
     */
    bf_sequence_number_rule_name?: string
    /**
     * 坯布销售最大数量
     */
    blank_fabric_max?: number
    /**
     * 坯布销售最小数量
     */
    blank_fabric_min?: number
    /**
     * 进位
     */
    carry?: number
    /**
     * 进位
     */
    carry_name?: string
    /**
     * 类别 1客户 2供应商
     */
    category?: number
    /**
     * 编号
     */
    code?: string
    /**
     * 联系人名称
     */
    contact_name?: string
    /**
     * 社会统一信用代码
     */
    credit_code?: string
    /**
     * 信用等级
     */
    credit_level?: number
    /**
     * 信用等级名称
     */
    credit_level_name?: string
    /**
     * 信用额度
     */
    credit_limit?: number
    /**
     * 坯布位数
     */
    decimal_point?: number
    /**
     * 坯布位数
     */
    decimal_point_name?: string
    /**
     * 供应商
     */
    dnf_charging_method?: number
    /**
     * 染费收费方式名
     */
    dnf_charging_method_name?: string
    /**
     * 邮箱
     */
    email?: string
    /**
     * 坯布最大重量
     */
    fabric_max_weight?: number
    /**
     * 坯布最小重量
     */
    fabric_min_weight?: number
    /**
     * 工厂物流
     */
    factory_logistics?: SystemBizUnitFactoryLogisticsItem[]
    /**
     * 传真
     */
    fax_number?: string
    /**
     * 全称
     */
    full_name?: string
    id?: number
    /**
     * 客户
     */
    is_blacklist?: boolean
    /**
     * 省市区
     */
    location?: string
    /**
     * 主要往来单位类型类型
     */
    main_biz_unit_type?: number
    /**
     * 主要类型id
     */
    main_unit_type_id?: number
    /**
     * 主要往来单位类型名称
     */
    main_unit_type_name?: string
    /**
     * 名称
     */
    name?: string
    /**
     * 跟单员id
     */
    order_follower_id?: number
    /**
     * 跟单员名称
     */
    order_follower_name?: string
    /**
     * 跟单QC员id
     */
    order_qc_user_id?: number
    /**
     * 跟单QC员名称
     */
    order_qc_user_name?: string
    /**
     * 联系电话
     */
    phone?: string
    /**
     * 拼音
     */
    pin_yin?: string
    /**
     * 企业微信客户
     */
    qywx_customers?: SystemQYWXCustomer[]
    /**
     * 企业微信群聊
     */
    qywx_groups?: SystemQYWXGroupChat[]
    /**
     * 备注
     */
    remark?: string
    /**
     * 销售区域id
     */
    sale_area_id?: number
    /**
     * 销售区域名称
     */
    sale_area_name?: string
    /**
     * 销售群体id
     */
    sale_group_id?: number
    /**
     * 销售群体名称
     */
    sale_group_name?: string
    /**
     * 所属营销体系id
     */
    sale_system_id?: number
    /**
     * 所属营销体系ids
     */
    sale_system_ids?: number[]
    /**
     * 所属营销体系名称
     */
    sale_system_name?: string
    /**
     * 所属营销体系名称s
     */
    sale_system_names?: string
    /**
     * 销售员id
     */
    seller_id?: number
    /**
     * 销售员名称
     */
    seller_name?: string
    /**
     * 结算天数
     */
    settle_cycle?: number
    /**
     * 结算类型
     */
    settle_type?: number
    /**
     * 结算类型名称
     */
    settle_type_name?: string
    /**
     * 类型id
     */
    unit_type_id?: number[]
    /**
     * 往来单位类型名称
     */
    unit_type_name?: string
    /**
     * 织造入库方式
     */
    weaving_storage_method?: number
    /**
     * 织造入库方式
     */
    weaving_storage_method_name?: string
    /**
     * 称重验布流程
     */
    wfi_process?: number
    /**
     * 称重验布流程
     */
    wfi_process_name?: string

    [property: string]: any
  }

  /**
   * system.BizUnitFactoryLogisticsItem
   */
  export interface SystemBizUnitFactoryLogisticsItem {
    /**
     * 地址
     */
    address?: string
    /**
     * 联系人
     */
    contact_name?: string
    /**
     * 是否默认
     */
    is_default?: boolean
    /**
     * 定位
     */
    location?: string
    /**
     * 物流区域
     */
    logistics_area?: string
    /**
     * 物流公司名称
     */
    logistics_company?: string
    /**
     * 名称
     */
    name?: string
    /**
     * 联系电话
     */
    phone?: string
    /**
     * 打印标签
     */
    print_tag?: string

    [property: string]: any
  }

  /**
   * system.QYWXCustomer
   */
  export interface SystemQYWXCustomer {
    id?: string
    name?: string
    type?: string

    [property: string]: any
  }

  /**
   * system.QYWXGroupChat
   */
  export interface SystemQYWXGroupChat {
    id?: string
    name?: string
    notify_type?: number
    notify_type_name?: string

    [property: string]: any
  }
}
