<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { userUpdatePassword } from '@/api/personCenter'

function checkPassword(rule: any, value: any, callback: any) {
  const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,12}$/

  if (passwordRegex.test(value))
    callback()
  else
    callback(new Error('密码必须由6到12位字母和数字组成'))
}

const state = reactive({
  showModal: false,
  modalName: '修改登录密码',
  form: {
    password: '',
    repeat_password: '',
    source_password: '',
  },
  fromRules: {
    password: [{ validator: checkPassword, trigger: 'blur', required: true }],
    repeat_password: [{ validator: checkPassword, trigger: 'blur', required: true }],
    source_password: [{ validator: checkPassword, trigger: 'blur', required: true }],
  },
})
const ruleFormRef = ref()

function handCancel() {
  state.showModal = false
}

const { fetchData, success, msg } = userUpdatePassword()

async function handleSure() {
  if (!ruleFormRef.value)
    return
  await ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await fetchData({ ...state.form })
      if (success.value) {
        ElMessage.success('修改成功')
        state.showModal = false
      }
      else {
        ElMessage.error(msg.value)
      }
    }
  })
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="500" height="400" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <el-form ref="ruleFormRef" :model="state.form" label-width="100px" label-position="top" :rules="state.fromRules">
      <el-form-item label="旧密码" prop="source_password">
        <el-input v-model="state.form.source_password" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="新密码" prop="password">
        <el-input v-model="state.form.password" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="确认密码" prop="repeat_password">
        <el-input v-model="state.form.repeat_password" placeholder="请输入" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
