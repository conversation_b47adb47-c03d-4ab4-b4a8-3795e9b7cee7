<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import { GetGreyFabricInfoListUseByOthers } from '@/api/greyFabricInformation'
import { debounce, getFilterData } from '@/common/util'
import Table from '@/components/Table.vue'

export interface Props {
  id: number
  modelValue: boolean
  defaultSelecteIds?: number[]
}

const props = withDefaults(defineProps<Props>(), {
  id: 0,
  modelValue: false,
})
const emit = defineEmits(['update:modelValue', 'submit'])
const state = reactive({
  filterData: {
    code: '',
    name: '',
    full_name: '',
    grey_fabric_width: '',
    grey_fabric_gram_weight: '',
    total_needle_size: '',
    gray_fabric_color_id: '',
    single_price_min: null,
    single_price_max: null,
  },
})

const showModal = ref<boolean>(false)
watch(
  () => props.modelValue,
  (show) => {
    showModal.value = show
    if (show) {
      getData()
    }
    else {
      state.filterData = {
        code: '',
        name: '',
        full_name: '',
        grey_fabric_width: '',
        grey_fabric_gram_weight: '',
        total_needle_size: '',
        gray_fabric_color_id: '',
        single_price_min: null,
        single_price_max: null,
      }
    }
  },
)

const columnList = ref([
  {
    title: '坯布编号',
    field: 'code',
  },
  {
    title: '坯布名称',
    field: 'name',
  },
  {
    title: '坯布全称',
    field: 'full_name',
  },
  {
    title: '幅宽',
    field: 'grey_fabric_width',
  },
  {
    title: '克重',
    field: 'grey_fabric_gram_weight',
  },
  {
    title: '针寸数',
    field: 'needle_size',
  },
  {
    title: '总针数',
    field: 'total_needle_size',
  },
  {
    title: '织坯颜色',
    field: 'gray_fabric_color_name',
  },
  {
    title: '织造工艺',
    field: 'weaving_process',
  },
  {
    title: '单位',
    // field: 'weaving_process',
  },
  {
    title: '坯布单价',
  },
])

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  { deep: true },
)

const { fetchData: fetchDataList, data: dataList, total, page, size, loading, handleSizeChange, handleCurrentChange } = GetGreyFabricInfoListUseByOthers()
async function getData() {
  await fetchDataList(getFilterData(state.filterData))
}

function onClose() {
  emit('update:modelValue', false)
}

const selectList = ref<any[]>()
function handAllSelect({ records }: any) {
  selectList.value = records
}

function handleSelectionChange({ records }: any) {
  selectList.value = records
}

function submit() {
  emit('submit', selectList.value)
}
const tablesRef = ref()
// 添加点击行时切换选中状态的函数
function handleRowClick({ row }: any) {
  // 获取表格实例
  const tableRef = tablesRef.value.tableRef

  // 判断当前行是否已选中
  const isSelected = tableRef.isCheckedByCheckboxRow(row)

  if (isSelected) {
    // 如果已选中，则取消选中
    tableRef.setCheckboxRow(row, false)
  }
  else {
    // 选中当前行
    tableRef.setCheckboxRow(row, true)
  }

  // 更新选中列表
  selectList.value = tableRef.getCheckboxRecords()
}
const tableConfig = reactive({
  showCheckBox: true,
  handAllSelect,
  handleSelectionChange,
  handleSizeChange,
  handleCurrentChange,
  showPagition: true,
  cellClick: (val: any) => handleRowClick(val),
  loading,
  size,
  page,
  total,
  checkRowKeys: [] as number[],
})

watch(
  () => props.defaultSelecteIds,
  (val) => {
    tableConfig.checkRowKeys = val || []
  },
)
</script>

<template>
  <vxe-modal v-model="showModal" show-footer title="添加坯布商品" width="80vw" height="80vh" :mask="false" :lock-view="false" :esc-closable="true" resize @close="onClose">
    <el-descriptions title="" :column="4" border size="small" class="mb-[20px]">
      <el-descriptions-item label="坯布编号">
        <el-input v-model="state.filterData.code" size="small" placeholder="坯布编号" clearable />
      </el-descriptions-item>
      <el-descriptions-item label="坯布名称">
        <el-input v-model="state.filterData.name" size="small" placeholder="坯布名称" clearable />
      </el-descriptions-item>
      <!--      <el-descriptions-item label="坯布全称"> -->
      <!--        <el-input size="small" placeholder="坯布名称" v-model="state.filterData.full_name" clearable></el-input> -->
      <!--      </el-descriptions-item> -->
      <el-descriptions-item label="幅宽">
        <el-input v-model="state.filterData.grey_fabric_width" size="small" placeholder="幅宽" clearable />
      </el-descriptions-item>
      <el-descriptions-item label="克重">
        <el-input v-model="state.filterData.grey_fabric_gram_weight" size="small" placeholder="克重" clearable />
      </el-descriptions-item>
      <el-descriptions-item label="总针数">
        <el-input v-model="state.filterData.total_needle_size" size="small" placeholder="总针数" clearable />
      </el-descriptions-item>
      <el-descriptions-item label="织坯颜色">
        <el-input v-model="state.filterData.gray_fabric_color_id" size="small" placeholder="织坯颜色" clearable />
      </el-descriptions-item>
      <!--      <el-descriptions-item label="坯布单价"> -->
      <!--        <el-input-number v-model="state.filterData.single_price_min" size="small" controls-position="right" clearable /> -->
      <!--        - -->
      <!--        <el-input-number v-model="state.filterData.single_price_max" size="small" controls-position="right" clearable /> -->
      <!--      </el-descriptions-item> -->
    </el-descriptions>
    <Table v-if="modelValue" ref="tablesRef" :config="tableConfig" :table-list="dataList.list" :column-list="columnList">
      <template #creatTime />
    </Table>
    <template #footer>
      <el-button type="primary" size="small" @click="submit">
        提交
      </el-button>
    </template>
  </vxe-modal>
</template>
