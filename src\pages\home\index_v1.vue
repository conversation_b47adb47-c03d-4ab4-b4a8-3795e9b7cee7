<script lang="ts" setup>
import { onMounted, onUnmounted, provide, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import AgencyMattersa from './components/AgencyMattersa/index.vue'
import Alert from './components/alert/index.vue'
import Banner from './components/Banner/index.vue'
import Card from './components/card/index.vue'
import CommonFunctions from './components/CommonFunctions/index.vue'
import FabricType from './components/FabricType/index.vue'
import MessageNotification from './components/MessageNotificatioin/index.vue'
import SaleRanking from './components/SalesRanking/index.vue'
import SaleRankingSalesman from './components/SalesRankingSalesman/index.vue'
import SalesTrend from './components/SalesTrend/index.vue'
import SelectTimeBtn from './components/SelectTimeBtn/index.vue'
import StoreSales from './components/StoreSales/index.vue'
import UserGuide from './components/UserGuide/index.vue'
import Version from './components/Version/index.vue'

const agency_mattersa = [
  { id: 1, icon: 'dsp', title: '待审批', count: 120, timeout: '超时 23 笔' },
  { id: 2, icon: 'wdcs', title: '未读的抄送', count: 120, timeout: '超时 23 笔' },
  { id: 3, icon: 'dpbdd', title: '待配布订单', count: 120, timeout: '超时 23 笔' },
  { id: 4, icon: 'dfhdd', title: '待发货订单', count: 120, timeout: '超时 23 笔' },
  { id: 5, icon: 'dfkdd', title: '待付款订单', count: 120, timeout: '超时 23 笔' },
  { id: 6, icon: 'dclsh', title: '待处理售后', count: 120, timeout: '超时 23 笔' },
]

const clientWidth = ref<number>(document.body.clientWidth)
const state = reactive<
  { type: 'see' | 'edit' }
>({
  type: 'see',
})

onMounted(() => {
  window.onresize = () => {
    clientWidth.value = document.body.clientWidth
  }
})
onUnmounted(() => {
  window.onresize = null
})

const router = useRouter()
function toPage({ path }: { path: string }) {
  router.push({
    name: path,
  })
}
provide('changeSize', clientWidth)
</script>

<template>
  <div class="min-w-[1501px] overflow-y-scroll">
    <!-- <Alert /> -->
    <div class="flex justify-between">
      <div class="flex-1">
        <Card title="常用功能">
          <template #right_title>
            <div v-if="state.type === 'edit'" class="flex">
              <p class="text-[13px] cursor-pointer ml-[12px] flex items-center text-[#0E7EFFFF] font-bold" @click="state.type = 'see'">
                <svg-icon name="huishouzhan" size="16" color="#0E7EFFFF" />
                <span class="ml-[6px]">取消</span>
              </p>
            </div>
            <p v-else class="text-[13px] cursor-pointer flex items-center text-[#0E7EFFFF] font-bold" @click="state.type = 'edit'">
              <svg-icon name="setting" size="16" />
              <span class="ml-[6px]">自定义常用功能</span>
            </p>
          </template>
          <template #default>
            <CommonFunctions :type="state.type" @click="toPage" />
          </template>
        </Card>
        <div class="mt-[24px]">
          <Card title="代办事项">
            <template #default>
              <AgencyMattersa :list="agency_mattersa" />
            </template>
          </Card>
        </div>
        <div class="mt-[24px] flex justify-between">
          <div class="left_two">
            <Card style-tw="h-full" title="销售排行（客户）">
              <template #right_title>
                <SelectTimeBtn />
              </template>
              <template #default>
                <SaleRanking />
              </template>
            </Card>
          </div>
          <div class="left_two">
            <Card style-tw="h-full" title="布种排行（KG）">
              <template #right_title>
                <SelectTimeBtn />
              </template>
              <template #default>
                <FabricType />
              </template>
            </Card>
          </div>
          <div class="left_two">
            <Card style-tw="h-full" title="销量走势">
              <template #right_title>
                <SelectTimeBtn />
              </template>
              <template #default>
                <SalesTrend />
              </template>
            </Card>
          </div>
        </div>
        <div class="mt-[24px] flex justify-between">
          <div class="left_two">
            <Card title="销售排行（销售员）">
              <template #right_title>
                <SelectTimeBtn />
              </template>
              <template #default>
                <SaleRankingSalesman />
              </template>
            </Card>
          </div>
          <div class="w-[66%]">
            <Card title="门店销量">
              <template #right_title>
                <SelectTimeBtn />
              </template>
              <template #default>
                <StoreSales />
              </template>
            </Card>
          </div>
        </div>
      </div>
      <div class="w-[320px] ml-5 bg-[#fff]">
        <Card title="热门活动">
          <template #default>
            <Banner />
          </template>
        </Card>
        <Card title="消息通知">
          <template #right_title>
            <span class="message_notification_more">查看更多</span>
          </template>
          <template #default>
            <MessageNotification />
          </template>
        </Card>
        <Card title="使用指南">
          <template #right_title>
            <span class="message_notification_more">查看更多</span>
          </template>
          <template #default>
            <UserGuide />
          </template>
        </Card>
      </div>
    </div>
  </div>
  <Version />
</template>

<style scoped lang="scss">
.left_two {
  width: 32%;
}
.message_notification_more {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  cursor: pointer;
}
</style>
