<script lang="ts" setup>
import { Plus } from '@element-plus/icons-vue'
import Big from 'big.js'
import { ElMessage } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import AddressAdd from './components/AddressAdd/index.vue'
import SelectProductionNotice from './components/SelectProductionNotice/index.vue'
import SelectRawMaterial from './components/SelectRawMaterial/index.vue'
import { PurchaseOrderRawMaterial } from '@/api/rawMaterialSourcing'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatDate, formatPriceMul, formatUnitPriceMul, formatWeightMul, sumNum } from '@/common/format'
import { getDefaultSaleSystem, getFilterData } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'

const bulkShow = ref(false)
const multipleSelection = ref<any[]>([])
const state = reactive({
  form: {
    sale_system_id: getDefaultSaleSystem()?.default_sale_system_id,
    supplier_id: '',
    receipt_unit_id: '',
    purchase_date: '',
    receipt_date: '',
    fapiao_title: '',
    remark: '',
    sale_system_name: '',
    supplier_name: '',
    receipt_unit_name: '',
    items: [] as any[],
  },
  tableData: [] as any[],
  fromRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    supplier_id: [{ required: true, message: '请选择供应商', trigger: 'change' }],
    receipt_unit_id: [{ required: true, message: '请选择收货单位', trigger: 'change' }],
    purchase_date: [{ required: true, message: '请选择采购日期', trigger: 'change' }],
    receipt_date: '',
    fapiao_title: '',
    remark: '',
  },
})

const showAdd = ref(false)
const showAddress = ref(false)
const showProduct = ref(false)

function handAdd() {
  showAdd.value = true
}
function handEdit() {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请选择批量修改的数据')
  bulkShow.value = true
}

const selectAssociationsRow = ref()
function getNotificationOrder(val: any) {
  showProduct.value = false
  selectAssociationsRow.value.production_order_num = val.order_no
  selectAssociationsRow.value.blank_fabric_code = val.grey_fabric_code
  selectAssociationsRow.value.blank_fabric_code_disabled = true
  selectAssociationsRow.value.blank_fabric_name = val.grey_fabric_name
  selectAssociationsRow.value.blank_fabric_name_disabled = true
}

function openAssociations(row: any) {
  showProduct.value = true
  selectAssociationsRow.value = row
}

const customerRef = ref()
const columnList = ref([
  {
    field: 'raw_material_code',
    title: '原料编号',
    fixed: 'left',
    width: 100,
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    fixed: 'left',
    width: 100,
  },
  {
    field: 'customer_id',
    title: '所属客户',
    width: 100,
    soltName: 'customer_id',
  },
  {
    field: 'brand',
    title: '原料品牌',
    width: 100,
    soltName: 'brand',
  },
  {
    field: 'craft',
    title: '原料工艺',
    width: 100,
  },
  {
    field: 'color_scheme',
    title: '原料色系',
    width: 100,
    soltName: 'color_scheme',
  },
  {
    field: 'production_date',
    title: '生产日期',
    width: 170,
    soltName: 'production_date',
  },
  {
    field: 'blank_fabric_code',
    title: '坯布编号',
    width: 100,
    soltName: 'blank_fabric_code',
  },
  {
    field: 'blank_fabric_name',
    title: '坯布名称',
    width: 100,
    soltName: 'blank_fabric_name',
  },
  {
    field: 'production_order_num',
    title: '生产通知单号',
    width: 130,
    soltName: 'production_order_num',
  },
  {
    field: 'level',
    title: '原料等级',
    width: 100,
    soltName: 'level',
  },
  {
    field: 'tax_included',
    title: '含税',
    width: 100,
    soltName: 'tax_included',
  },
  {
    field: 'package_price',
    title: '包装价格',
    width: 100,
    soltName: 'package_price',
  },
  {
    field: 'spinning_type',
    title: '纺纱类型',
    width: 100,
    soltName: 'spinning_type',
  },
  {
    field: 'remark',
    title: '备注',
    width: 100,
    soltName: 'remark',
  },
  {
    field: 'address',
    title: '收货地址',
    fixed: 'right',
    width: 170,
    soltName: 'address',
  },
  {
    field: 'whole_piece_count',
    title: '整件件数',
    fixed: 'right',
    width: 100,
    soltName: 'whole_piece_count',
  },
  {
    field: 'whole_piece_weight',
    title: '整件数量',
    fixed: 'right',
    width: 100,
    soltName: 'whole_piece_weight',
  },
  {
    field: 'count_weight',
    title: '整件总重',
    fixed: 'right',
    width: 100,
    soltName: 'count_weight',
  },
  {
    field: 'bulk_piece_count',
    title: '散件件数',
    fixed: 'right',
    width: 100,
    soltName: 'bulk_piece_count',
  },
  {
    field: 'bulk_piece_weight',
    title: '散件数量',
    fixed: 'right',
    width: 100,
    soltName: 'bulk_piece_weight',
  },
  {
    field: 'bulk_count_weight',
    title: '散件总重',
    fixed: 'right',
    width: 100,
    soltName: 'bulk_count_weight',
  },
  {
    field: 'bulk_weight',
    title: '总数量',
    fixed: 'right',
    width: 100,
    soltName: 'bulk_weight',
  },
  {
    field: 'unit_price',
    title: '单价(kg/元)',
    fixed: 'right',
    width: 100,
    soltName: 'unit_price',
  },
  {
    field: 'total_weight',
    title: '金额',
    fixed: 'right',
    width: 100,
    soltName: 'total_weight',
  },
])

const tablesRef = ref()

function footerMethod({ columns, data }: { columns: any, data: any }) {
  const footerData = [
    columns.map((column: any, _columnIndex: number) => {
      if (['code'].includes(column.field))
        return '合计'

      if (['number', 'whole_piece_count', 'whole_piece_weight', 'bulk_piece_count', 'count_weight', 'bulk_piece_weight', 'bulk_count_weight', 'bulk_weight', 'count_price'].includes(column.field))
        return sumNum(data, column.field)
    }),
  ]
  return footerData
}

const selectRow = ref()
function openAddress(row: any) {
  if (!row.whole_piece_count || !row.whole_piece_weight)
    return ElMessage.warning('整件件数和件重不能为空')

  selectRow.value = row
  showAddress.value = true
}

function onSubmit(row: any) {
  showAdd.value = false
  const val = row?.map((item: any) => {
    return {
      raw_material_code: item.code,
      raw_material_name: item.name,
      raw_material_id: item.id,
      customer_id: '',
      brand: '',
      craft: item.craft,
      color_scheme: '',
      production_date: '',
      blank_fabric_code: '',
      blank_fabric_name: '',
      production_order_num: '',
      level: '',
      tax_included: '',
      package_price: '',
      spinning_type: '',
      remark: '',
      whole_piece_count: '',
      whole_piece_weight: '',
      bulk_piece_count: '',
      bulk_piece_weight: '',
      bulk_weight: '',
      unit_price: '',
      logistics: [] as any[],
    }
  })
  state.tableData = [...state.tableData, ...val]
}

const changeRow = ref<any>()
function changeData(row: any) {
  changeRow.value = row
}

watch(
  () => [changeRow.value?.whole_piece_count, changeRow.value?.whole_piece_weight, changeRow.value?.bulk_piece_count, changeRow.value?.bulk_piece_weight, changeRow.value?.unit_price],
  async () => {
    computedData(changeRow.value)
  },
  {
    deep: true,
  },
)
const router = useRouter()
const { fetchData: fetchDataAdd, success: successAdd, msg: msgAdd } = PurchaseOrderRawMaterial()
const ruleFormRef = ref()
async function handSubmit() {
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      if (validateList())
        return false
      const tableData = fomatData()
      state.form.items = tableData
      await fetchDataAdd(getFilterData({ ...state.form, purchase_date: formatDate(state.form.purchase_date), receipt_date: formatDate(state.form.receipt_date) }))
      if (successAdd.value) {
        ElMessage.success('添加成功')
        router.push({
          name: 'RawMaterialSourcing',
        })
      }
      else {
        ElMessage.error(msgAdd.value)
      }
    }
  })
}

// 整理坯布信息
function fomatData() {
  return state.tableData?.map((item: any) => {
    const logistics = item.logistics?.map((citem: any) => {
      return {
        ...citem,
        weight: formatWeightMul(citem.weight),
        piece_count: Number.parseInt(citem.piece_count),
      }
    })
    return {
      ...item,
      bulk_piece_weight: formatWeightMul(item.bulk_piece_weight),
      whole_piece_weight: formatWeightMul(item.whole_piece_weight),
      unit_price: formatUnitPriceMul(item.unit_price),
      tax_included: item.tax_included ? formatPriceMul(item.tax_included) : 0,
      package_price: formatPriceMul(item.package_price),
      whole_piece_count: Number.parseInt(item.whole_piece_count),
      bulk_piece_count: Number.parseInt(item.bulk_piece_count),
      logistics,
    }
  })
}

// 验证坯布信息字段
function validateList() {
  let msg = ''
  if (!state.tableData || state.tableData.length === 0) {
    msg = '原料信息不能为空'
  }
  else {
    state.tableData?.some((item: any) => {
      if (!item.customer_id) {
        msg = `原料编号为${item.code}的数据,所属客户不能为空`
        return true
      }
      if (!item.whole_piece_count) {
        msg = `原料编号为${item.code}的数据,整件件数不能为空`
        return true
      }
      if (!item.whole_piece_weight) {
        msg = `原料编号为${item.code}的数据,件重不能为空`
        return true
      }
      if (!item.bulk_piece_count) {
        msg = `原料编号为${item.code}的数据,散件件数不能为空`
        return true
      }
      if (!item.bulk_piece_weight) {
        msg = `原料编号为${item.code}的数据,散件数量不能为空`
        return true
      }
    })
  }
  msg && ElMessage.error(msg)
  return msg
}

function handDel(row: any, rowIndex: number) {
  state.tableData.splice(rowIndex, 1)
}

function onAddress(row: any) {
  selectRow.value.logistics = row?.map((item: any) => {
    return {
      piece_count: Number.parseInt(item.piece_count || 0),
      receipt_address: item.receipt_address || '',
      receipt_person: item.receipt_person || '',
      receipt_phone: item.receipt_phone || '',
      receipt_unit_name: item.receipt_unit_name || '',
      weight: Number.parseInt(item.weight || 0),
      receipt_unit_id: Number.parseInt(item.receipt_unit_id || 0),
      number: Number.parseInt(item.number || 0),
    }
  })
  showAddress.value = false
}

const saleSaleSystemInfo = ref()
function getSaleSystem(row: any) {
  state.form.sale_system_name = row?.name
  saleSaleSystemInfo.value = row
  clearData(row)
}

const bulkSetting = ref<any>({
  address: {
    receipt_unit_name: '',
    receipt_unit_id: '',
    receipt_person: '',
    receipt_address: '',
  },
})
function handBulkClose() {
  bulkShow.value = false
}

const bulkList = reactive<any>([
  {
    field: 'customer_id',
    field_name: 'customer_name',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
    query: { sale_system_id: state.form.sale_system_id },
  },
  {
    field: 'brand',
    title: '原料品牌',
    component: 'input',
  },
  {
    field: 'color_scheme',
    title: '原料色系',
    component: 'input',
    type: 'text',
  },
  {
    field: 'production_date',
    title: '生产日期',
    component: 'selectDate',
    type: 'date',
  },
  {
    field: 'blank_fabric_code',
    title: '坯布编号',
    width: 100,
    component: 'select',
    api: 'GetGreyFabricInfoListUseByOthersMenu',
  },
  {
    field: 'blank_fabric_name',
    title: '坯布名称',
    width: 100,
    component: 'select',
    api: 'GetGreyFabricInfoListUseByOthersMenu',
  },
  {
    field: 'tax_included',
    title: '含税',
    component: 'input',
    type: 'text',
  },
  {
    field: 'package_price',
    title: '包装价格',
    component: 'input',
    type: 'float',
  },
  {
    field: 'spinning_type',
    title: '纺纱类型',
    component: 'input',
    type: 'text',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
  {
    field: 'whole_piece_count',
    title: '整件件数',
    component: 'input',
    type: 'integer',
  },
  {
    field: 'whole_piece_weight',
    title: '整件件重',
    component: 'input',
    type: 'float',
  },
  {
    field: 'bulk_piece_count',
    title: '散件件数',
    component: 'input',
    type: 'integer',
  },
  {
    field: 'bulk_piece_weight',
    title: '散装件重',
    component: 'input',
    type: 'float',
  },
  {
    field: 'unit_price',
    title: '单价(kg/元)',
    component: 'input',
    type: 'float',
  },

  {
    field: 'address',
    title: '收货地址',
    hideTitle: true,
    rule: {
      receipt_unit_id: [{ required: true, message: '请选择收货单位', trigger: 'change' }],
      receipt_person: [{ required: true, message: '请填写收货联系人', trigger: 'change' }],
      receipt_phone: [{ required: true, message: '请填写收货电话', trigger: 'change' }],
      receipt_address: [{ required: true, message: '请填写收货地址', trigger: 'change' }],
    },
  },
])

function getFabricInfo(row: any, val: any) {
  row.blank_fabric_name = val.name
  row.blank_fabric_id = val.id
  row.blank_fabric_code = val.code
}

const bulkFormRef = ref()
async function bulkSubmit({ row, value, selectData }: any) {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请填写批量修改的数据')

  let tf = false
  if (row.field !== 'address') {
    if (!value[row.field]) {
      ElMessage.error('请输入参数')
      tf = true
    }
  }
  else {
    await bulkFormRef.value.validate((valid: any) => {
      if (!valid)
        tf = true
    })
  }
  if (tf)
    return
  multipleSelection.value?.map((item: any) => {
    if (row.field === 'blank_fabric_code' || row.field === 'blank_fabric_name') {
      item.blank_fabric_name = selectData?.name
      item.blank_fabric_id = selectData?.id
      item.blank_fabric_code = selectData?.code
    }
    else if (row.field === 'address') {
      item.logistics = [{ ...value[row.field], piece_count: 1, weight: Number.parseFloat(item.whole_piece_weight) }]
    }
    else {
      item[row.field] = value[row.field]
    }
    computedData(item)
  })
  ElMessage.success('设置成功')
}

async function computedData(changeRow: any) {
  // 计算整件总数量
  if (changeRow?.whole_piece_count && changeRow?.whole_piece_weight)
    changeRow.count_weight = Number.parseFloat(Big(changeRow?.whole_piece_count).times(changeRow?.whole_piece_weight).toFixed(2))

  // 计算散件总数量
  if (changeRow?.bulk_piece_count && changeRow?.bulk_piece_weight)
    changeRow.bulk_count_weight = Number.parseFloat(Big(changeRow?.bulk_piece_count).times(changeRow?.bulk_piece_weight).toFixed(2))

  // 计算总数量
  if (changeRow?.count_weight && changeRow?.bulk_count_weight)
    changeRow.bulk_weight = Number.parseFloat(Big(changeRow.count_weight).plus(changeRow?.bulk_count_weight).toFixed(2))

  // 计算金额
  if (changeRow?.unit_price && changeRow?.bulk_weight)
    changeRow.total_weight = Number.parseFloat(Big(changeRow.unit_price).times(changeRow?.bulk_weight).toFixed(2))

  await tablesRef.value.tableRef.updateFooter()
}

function handAllSelect({ records }: any) {
  multipleSelection.value = records
}

function handleSelectionChange({ records }: any) {
  multipleSelection.value = records
}

function clearData(row: any) {
  state.tableData?.map((item) => {
    item.customer_id = saleSaleSystemInfo.value?.default_customer_id || ''
    item.customer_name = saleSaleSystemInfo.value?.default_customer_name || ''
  })
  bulkList[0].query = { sale_system_id: row.id }
  bulkSetting.value.customer_id = ''
}
const tableConfig = ref({
  showCheckBox: true,
  showOperate: true,
  operateWidth: 100,
  footerMethod,
  handAllSelect,
  handleSelectionChange,
})
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <div class="line" />
    <template #right-top>
      <el-button v-btnAntiShake="handSubmit" type="primary">
        提交
      </el-button>
    </template>
    <slot>
      <el-form ref="ruleFormRef" :model="state.form" label-width="60px" label-position="top" :rules="state.fromRules">
        <el-descriptions :column="3" border size="small">
          <el-descriptions-item label="营销体系名称">
            <template #label>
              营销体系名称
              <span style="color: red">*</span>
            </template>
            <el-form-item prop="sale_system_id">
              <SelectComponents
                v-model="state.form.sale_system_id"
                :default-status="true"
                api="GetSaleSystemDropdownListApi"
                label-field="name"
                value-field="id"
                @select="getSaleSystem"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="供应商名称">
            <template #label>
              供应商名称
              <span style="color: red">*</span>
            </template>
            <el-form-item prop="supplier_id">
              <SelectComponents
                v-model="state.form.supplier_id"
                api="BusinessUnitSupplierEnumAll"
                label-field="name"
                value-field="id"
                @select="(val:any) => state.form.supplier_name = val?.name"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="收货单位名称">
            <template #label>
              收货单位名称
              <span style="color: red">*</span>
            </template>
            <el-form-item prop="receipt_unit_id">
              <SelectComponents
                v-model="state.form.receipt_unit_id"
                :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory }"
                api="GetBusinessUnitListApi"
                label-field="name"
                value-field="id"
                @select="(val:any) => state.form.receipt_unit_name = val?.name"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="采购日期">
            <template #label>
              采购日期
              <span style="color: red">*</span>
            </template>
            <el-form-item prop="purchase_date">
              <SelectDate v-model="state.form.purchase_date" type="date" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="收货日期">
            <el-form-item prop="receipt_date">
              <SelectDate v-model="state.form.receipt_date" type="date" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="发票抬头">
            <el-form-item prop="fapiao_title">
              <el-input v-model="state.form.fapiao_title" placeholder="请输入发票抬头" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="备注">
            <el-form-item prop="remark">
              <el-input v-model="state.form.remark" type="textarea" />
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
    </slot>
  </FildCard>
  <FildCard title="原料信息" :tool-bar="false">
    <template #right-top>
      <el-button style="margin-left: 10px" type="primary" @click="handEdit">
        批量操作
      </el-button>
      <el-button style="margin-left: 10px" type="primary" :icon="Plus" @click="handAdd">
        根据资料添加
      </el-button>
    </template>
    <Table ref="tablesRef" :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
      <template #customer_id="{ row }">
        <SelectComponents
          ref="customerRef"
          v-model="row.customer_id"
          size="small"
          :query="{ sale_system_id: state.form.sale_system_id }"
          api="GetCustomerEnumList"
          label-field="name"
          value-field="id"
        />
      </template>
      <template #brand="{ row }">
        <vxe-input v-model="row.brand" size="mini" maxlength="200" />
      </template>
      <template #color_scheme="{ row }">
        <vxe-input v-model="row.color_scheme" size="mini" maxlength="200" />
      </template>
      <template #production_date="{ row }">
        <SelectDate v-model="row.production_date" type="date" style="width: 150px" />
      </template>
      <template #blank_fabric_code="{ row }">
        <SelectComponents
          v-model="row.blank_fabric_code"
          :disabled="row.blank_fabric_code_disabled"
          size="small"
          api="GetGreyFabricInfoListUseByOthersMenu"
          label-field="code"
          value-field="code"
          @change-value="val => getFabricInfo(row, val)"
        />
      </template>
      <template #blank_fabric_name="{ row }">
        <SelectComponents
          v-model="row.blank_fabric_name"
          :disabled="row.blank_fabric_name_disabled"
          size="small"
          api="GetGreyFabricInfoListUseByOthersMenu"
          label-field="name"
          value-field="name"
          @change-value="val => getFabricInfo(row, val)"
        />
      </template>
      <template #production_order_num="{ row }">
        <el-link type="primary" @click="openAssociations(row)">
          {{ row.production_order_num || '关联' }}
        </el-link>
      </template>
      <template #level="{ row }">
        <SelectComponents v-model="row.level_id" size="small" api="GetInfoBaseGreyFabricLevelListUseByOther" label-field="name" value-field="id" />
      </template>
      <template #tax_included="{ row }">
        <vxe-input v-model="row.tax_included" size="mini" type="float" :min="0" />
      </template>
      <template #package_price="{ row }">
        <vxe-input v-model="row.package_price" size="mini" type="float" :min="0" />
      </template>
      <template #spinning_type="{ row }">
        <vxe-input v-model="row.spinning_type" size="mini" maxlength="200" />
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" maxlength="200" />
      </template>
      <template #address="{ row }">
        <el-link type="primary" @click="openAddress(row)">
          编辑
        </el-link>
        <span class="text-black-40" :style="`color:${(!row.logistics || row.logistics?.length === 0) && 'red'}`">
          {{ row.logistics && row.logistics?.length > 0 ? '（已配置）' : '（未配置）' }}
        </span>
      </template>
      <template #whole_piece_count="{ row }">
        <vxe-input v-model="row.whole_piece_count" size="mini" type="integer" :min="0" @input="changeData(row)" />
      </template>
      <template #whole_piece_weight="{ row }">
        <vxe-input v-model="row.whole_piece_weight" size="mini" type="integer" :min="0" @input="changeData(row)" />
      </template>
      <template #count_weight="{ row }">
        {{ row.count_weight }}
      </template>
      <template #bulk_piece_count="{ row }">
        <vxe-input v-model="row.bulk_piece_count" size="mini" type="integer" :min="0" @input="changeData(row)" />
      </template>
      <template #bulk_piece_weight="{ row }">
        <vxe-input v-model="row.bulk_piece_weight" size="mini" type="float" :min="0" @input="changeData(row)" />
      </template>
      <template #bulk_count_weight="{ row }">
        {{ row.bulk_count_weight }}
      </template>
      <template #bulk_weight="{ row }">
        {{ row.bulk_weight }}
      </template>
      <template #unit_price="{ row }">
        <vxe-input v-model="row.unit_price" size="mini" type="float" :digits="4" :min="0" @input="changeData(row)" />
      </template>
      <template #total_weight="{ row }">
        {{ row.total_weight }}
      </template>
      <template #operate="{ row, rowIndex }">
        <el-button type="text" @click="handDel(row, rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <SelectRawMaterial v-model="showAdd" @submit="onSubmit" />
  <AddressAdd v-model="showAddress" :row="selectRow" :default-list="[...(selectRow?.logistics || [])]" @submit="onAddress" />
  <SelectProductionNotice v-model="showProduct" :order_no="selectAssociationsRow?.production_order_num" @submit="getNotificationOrder" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose">
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
    <template #address="{ row }">
      <el-form ref="bulkFormRef" :model="bulkSetting[row.field]" :rules="row.rule" label-width="120px" label-position="left">
        <el-form-item prop="receipt_unit_id" label="收货单位">
          <SelectComponents
            v-model="bulkSetting[row.field].receipt_unit_id"
            api="GetBusinessUnitListApi"
            label-field="name"
            value-field="id"
            @select="val => (bulkSetting[row.field].receipt_unit_name = val.name)"
          />
        </el-form-item>
        <el-form-item prop="receipt_person" label="收货联系人">
          <el-input v-model="bulkSetting[row.field].receipt_person" />
        </el-form-item>
        <el-form-item prop="receipt_phone" label="收货电话">
          <el-input v-model="bulkSetting[row.field].receipt_phone" />
        </el-form-item>
        <el-form-item prop="receipt_address" label="收货地址">
          <el-input v-model="bulkSetting[row.field].receipt_address" />
        </el-form-item>
      </el-form>
    </template>
  </BulkSetting>
</template>

<style lang="scss" scoped></style>
