<script lang="ts" setup name="RawMaterialSalesOrderAdd">
import { Plus } from '@element-plus/icons-vue'
import Big from 'big.js'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import SelectInformation from './components/SelectInformation/index.vue'
import { SaleOrderAdd } from '@/api/rawMaterIalSaleOrder'
import { BusinessUnitIdEnum, EmployeeType } from '@/common/enum'
import {
  formatDate,
  formatPriceDiv,
  formatPriceMul,
  formatUnitPriceMul,
  formatWeightDiv,
  formatWeightMul,
  sumNum,
} from '@/common/format'
import { getDefaultSaleSystem, getFilterData } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import SelectBusinessDialog from '@/components/SelectBusinessDialog/index.vue'

const routerList = useRouterList()
const state = reactive({
  form: {
    sale_system_id: getDefaultSaleSystem()?.default_sale_system_id,
    sale_system_name: '',
    customer_name: '',
    customer_id: '',
    sale_unit_name: '',
    sale_unit_id: '',
    sale_date: dayjs().format('YYYY-MM-DD'),
    receipt_address: '',
    receipt_phone: '',
    warehouse_manager_name: '',
    warehouse_manager_id: '',
    seller_name: '',
    seller_id: '',
    remark: '',
  },
  tableData: [] as any[],
  fromRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    customer_id: [{ required: true, message: '请选择客户名称', trigger: 'change' }],
    sale_unit_id: [{ required: true, message: '请选择出货单位', trigger: 'change' }],
    sale_date: [{ required: true, message: '请选择销售日期', trigger: 'change' }],
  },
})

const showAdd = ref(false)

function handDel(row: any, rowIndex: number) {
  ElMessageBox.confirm('是否删除该数据?', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      state.tableData.splice(rowIndex, 1)
    })
    .catch(() => {})
}
function handAdd() {
  if (!state.form.sale_unit_id)
    return ElMessage.error('请选择出货单位')
  showAdd.value = true
}

const columnList = ref([
  {
    title: '原料信息',
    childrenList: [
      {
        field: 'raw_material_code',
        title: '原料编号',
        fixed: 'left',
        width: 150,
      },
      {
        field: 'raw_material_name',
        title: '原料名称',
        fixed: 'left',
        width: 100,
      },
      {
        field: 'customer_name',
        title: '所属客户',
        width: 100,
        // soltName: 'customer_name',
      },
      {
        field: 'brand',
        title: '原料品牌',
        width: 100,
      },
      {
        field: 'craft',
        title: '原料工艺',
        width: 100,
      },
      {
        field: 'color_name',
        title: '原料颜色',
        width: 100,
      },
      {
        field: 'dyelot_number',
        title: '缸号',
        width: 100,
      },
      {
        field: 'measurement_unit_name',
        title: '单位',
        width: 100,
      },
      {
        field: 'supplier_name',
        title: '供应商',
        width: 100,
      },
      {
        field: 'batch_num',
        title: '原料批号',
        width: 100,
      },
      {
        field: 'level_name',
        title: '原料等级',
        width: 100,
      },
      {
        field: 'raw_material_remark',
        title: '原料备注',
        width: 100,
      },
      {
        field: 'production_date',
        title: '生产日期',
        width: 100,
        isDate: true,
      },
      {
        field: 'spinning_type',
        title: '纺纱类型',
        width: 100,
      },
      {
        field: 'cotton_origin',
        title: '棉花产地',
        width: 100,
      },
      {
        field: 'yarn_origin',
        title: '棉纱产地',
        width: 100,
      },
      {
        field: 'carton_num',
        title: '装箱单号',
        width: 100,
      },
      {
        field: 'fapiao_num',
        title: '发票号',
        width: 100,
      },
      {
        field: 'total_weight_stock',
        title: '库存数量',
        width: 100,
      },
    ],
  },
  {
    title: '整件',
    childrenList: [
      {
        field: 'whole_piece_count_stock',
        title: '库存件数',
        width: 150,
      },
      {
        field: 'whole_piece_count',
        title: '销售件数',
        width: 150,
        soltName: 'whole_piece_count',
      },
      {
        field: 'whole_piece_weight',
        title: '销售件重(kg)',
        width: 150,
        isWeight: true,
        soltName: 'whole_piece_weight',
      },
    ],
  },
  {
    title: '散件',
    childrenList: [
      {
        field: 'bulk_piece_count_stock',
        title: '库存件数',
        width: 150,
        soltName: '',
      },
      {
        field: 'bulk_piece_count',
        title: '销售件数',
        width: 150,
        soltName: 'bulk_piece_count',
      },
      {
        field: 'bulk_weight',
        title: '销售数量',
        width: 150,
        isWeight: true,
        soltName: 'bulk_weight',
      },
    ],
  },
  {
    title: '',
    childrenList: [
      {
        field: 'total_weight',
        title: '总销售数量',
        width: 150,
        isWeight: true,
        soltName: 'total_weight',
      },
    ],
  },
  {
    title: '金额信息',
    childrenList: [
      {
        field: 'other_price',
        title: '其他金额',
        width: 150,
        isPrice: true,
        soltName: 'other_price',
      },
      {
        field: 'unit_price',
        title: '单价',
        width: 150,
        isPrice: true,
        soltName: 'unit_price',
      },
      {
        field: 'total_price',
        title: '总金额',
        width: 150,
        isPrice: true,
        soltName: 'total_price',
      },
    ],
  },
  {
    title: '',
    childrenList: [
      {
        field: 'remark',
        title: '备注',
        width: 150,
        soltName: 'remark',
      },
    ],
  },
])

const changeRow = ref<any>()
function changeData(row: any) {
  changeRow.value = row
}

const tablesRef = ref()
watch(
  () => changeRow.value,
  () => {
    computedData(changeRow.value)
  },
  {
    deep: true,
  },
)

function computedData(row: any) {
  // 总采购数量
  row.total_weight = Number.parseFloat(
    Big(row.whole_piece_count || 0)
      .times(row?.whole_piece_weight || 0)
      .plus(row?.bulk_weight || 0)
      .toFixed(4),
  )

  // 金额
  row.total_price = Number.parseFloat(
    Big(row?.total_weight || 0)
      .times(row?.unit_price || 0)
      .plus(row?.other_price || 0)
      .toFixed(2),
  )

  tablesRef.value.tableRef.updateFooter()
}

const bulkShow = ref(false)
const bulkSetting = ref<any>({
  address: {},
})

const multipleSelection = ref<any[]>([])
async function bulkSubmit({ row, value }: any) {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  multipleSelection.value?.map((item: any) => {
    item[row.field] = value[row.field]
    computedData(item)
  })
  ElMessage.success('设置成功')
}

function bulkHand() {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')
  bulkShow.value = true
}

function onSubmit(row: any) {
  showAdd.value = false
  const data = row?.map((item: any) => {
    return {
      raw_material_code: item.raw_material_code,
      raw_material_name: item.raw_material_name,
      craft: item.craft,
      raw_material_id: item.id,
      customer_id: item.customer_id,
      customer_name: item.customer_name,
      brand: item.brand,
      color_scheme: item.color_scheme,
      batch_num: item.batch_num,
      supplier_name: item.supplier_name,
      level: item.level,
      raw_material_remark: item.remark,
      production_date: item.production_date,
      spinning_type: item.spinning_type,
      cotton_origin: item.cotton_origin,
      yarn_origin: item.yarn_origin,
      carton_num: item.carton_num,
      fapiao_num: item.fapiao_num,
      whole_weight: formatWeightDiv(item.whole_weight),
      bulk_piece_count_stock: formatPriceDiv(item.bulk_piece_count),
      bulk_stock_weight: formatWeightDiv(item.bulk_weight),
      stock_item_id: item.id,
      total_weight_stock: formatWeightDiv(item.total_weight),
      whole_piece_count_stock: formatPriceDiv(item.whole_piece_count),
      level_name: item.level_name,
      level_id: item.level_id,
      whole_piece_count: 0,
      whole_piece_weight: 0,
      bulk_piece_count: 0,
      bulk_weight: 0,
      other_price: 0,
      unit_price: 0,
      color_name: item.color_name,
      measurement_unit_name: item.measurement_unit_name,
      dyelot_number: item.dyelot_number,
      color_id: item.color_id,
      measurement_unit_id: item.measurement_unit_id,
    }
  })
  state.tableData = [...state.tableData, ...data]
}

const { fetchData: fetchDataAdd, data: addData, success: successAdd, msg: msgAdd } = SaleOrderAdd()
const ruleFormRef = ref()
async function handSubmit() {
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      if (validateList())
        return false
      const res = fomatData()
      await fetchDataAdd(getFilterData({ ...state.form, sale_date: formatDate(state.form.sale_date), items: res || [] }))
      if (successAdd.value) {
        ElMessage.success('添加成功')
        routerList.push({
          name: 'RawMaterialSalesOrderDetail',
          params: { id: addData.value.id },
        })
      }
      else {
        ElMessage.error(msgAdd.value)
      }
    }
  })
}

// 验证坯布信息字段
function validateList() {
  let msg = ''
  if (!state.tableData || state.tableData.length === 0) {
    msg = '原料信息不能为空'
  }
  else {
    state.tableData?.some((item: any) => {
      if (!item.raw_material_code) {
        msg = `编号为${item.raw_material_code}的数据,原料编号不能为空`
        return true
      }
      if (!item.raw_material_name) {
        msg = `编号为${item.raw_material_code}的数据,原料名称不能为空`
        return true
      }
      if (!item.total_weight) {
        msg = `编号为${item.raw_material_code}的数据, 总销售数量必须大于0`
        return true
      }
      // if (!item.whole_piece_count) {
      //   msg = `编号为${item.raw_material_code}的数据,整件销售件数不能为空`
      //   return true
      // }
      // if (!item.whole_piece_weight) {
      //   msg = `编号为${item.raw_material_code}的数据,整件销售件重不能为空`
      //   return true
      // }
      // if (!item.unit_price) {
      //   msg = `编号为${item.raw_material_code}的数据,单价不能为空`
      //   return true
      // }
    })
  }
  msg && ElMessage.error(msg)
  return msg
}

function getCustomerInfo(val: any) {
  state.form.sale_system_id = val.select_sale_system_id
  if (val) {
    state.form.receipt_address = val.address
    state.form.receipt_phone = val.phone
    state.form.customer_name = val.name
    state.form.seller_id = val.seller_id
    state.form.seller_name = val.seller_name
  }
}

// // 整理提交信息
function fomatData() {
  return state.tableData?.map((item: any) => {
    const { bulk_weight, bulk_piece_count, total_price, unit_price, whole_piece_count, whole_piece_weight, other_price } = item
    return {
      ...item,
      bulk_piece_count: formatPriceMul(bulk_piece_count || 0),
      bulk_weight: formatWeightMul(bulk_weight || 0),
      total_price: formatPriceMul(total_price || 0),
      unit_price: formatUnitPriceMul(unit_price || 0),
      whole_piece_count: formatPriceMul(whole_piece_count || 0),
      whole_piece_weight: formatWeightMul(whole_piece_weight || 0),
      other_price: formatPriceMul(other_price || 0),
    }
  })
}

const bulkList = reactive<any>([
  {
    field: 'whole_piece_count',
    title: '整件销售件数',
    component: 'input',
    type: 'integer',
  },
  {
    field: 'whole_piece_weight',
    title: '整件销售件重',
    component: 'input',
    type: 'float',
  },
  {
    field: 'bulk_piece_count',
    title: '散件销售件数',
    component: 'input',
    type: 'integer',
  },
  {
    field: 'bulk_weight',
    title: '散件销售数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'other_price',
    title: '其他金额(kg)',
    component: 'input',
    type: 'float',
  },
  {
    field: 'unit_price',
    title: '单价',
    component: 'input',
    type: 'float',
    digits: 4,
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'textarea',
  },
])

function handAllSelect({ records }: any) {
  multipleSelection.value = records
}

function handleSelectionChange({ records }: any) {
  multipleSelection.value = records
}

function footerMethod({ columns, data }: { columns: any, data: any }) {
  const footerData = [
    columns.map((column: any, _columnIndex: number) => {
      if (['raw_material_code'].includes(column.field))
        return '合计'

      if (['whole_piece_count', 'whole_piece_weight', 'bulk_piece_count', 'bulk_weight', 'total_weight', 'other_price', 'total_price'].includes(column.field))
        return sumNum(data, column.field)
    }),
  ]
  return footerData
}
function handBulkClose() {
  bulkShow.value = false
}

const tableConfig = ref({
  showOperate: true,
  operateWidth: 100,
  bulkSubmit,
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  footerMethod,
  showSpanHeader: true,
})
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <div class="line" />
    <template #right-top>
      <el-button v-btnAntiShake="handSubmit" type="primary">
        提交
      </el-button>
    </template>
    <slot>
      <el-form ref="ruleFormRef" :model="state.form" label-width="60px" label-position="top" :rules="state.fromRules">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem required label="客户名称:">
            <template #content>
              <el-form-item prop="customer_id">
                <!--                <SelectCustomerDialog v-model="state.form.customer_id" @change-value="val => getCustomerInfo(val)" /> -->
                <SelectCustomerDialog
                  v-model="state.form.customer_id"
                  is-merge
                  field="name"
                  show-choice-system
                  @change-value="getCustomerInfo"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="营销体系名称:">
            <template #content>
              <el-form-item prop="sale_system_id">
                <SelectComponents
                  v-model="state.form.sale_system_id"
                  :default-status="true"
                  api="GetSaleSystemDropdownListApi"
                  label-field="name"
                  value-field="id"
                  @change-value="row => (state.form.sale_system_name = row?.name)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="出货单位:">
            <template #content>
              <el-form-item prop="sale_unit_id">
                <SelectBusinessDialog
                  v-model="state.form.sale_unit_id"
                  :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory }"
                  api="GetBusinessUnitListApi"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="销售日期:">
            <template #content>
              <el-form-item prop="sale_date">
                <SelectDate v-model="state.form.sale_date" type="date" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="收货地址:">
            <template #content>
              <el-form-item prop="receipt_address">
                <el-input v-model="state.form.receipt_address" placeholder="收货地址" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="收货电话:">
            <template #content>
              <el-form-item prop="receipt_phone">
                <el-input v-model="state.form.receipt_phone" placeholder="收货电话" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="仓管员:">
            <template #content>
              <el-form-item prop="warehouse_manager_id">
                <SelectComponents
                  v-model="state.form.warehouse_manager_id"
                  api="Adminemployeelist"
                  :query="{ duty: EmployeeType.warehouseManager }"
                  label-field="name"
                  value-field="id"
                  @change-value="row => (state.form.warehouse_manager_name = row.name)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="销售员:">
            <template #content>
              <el-form-item prop="seller_id">
                <SelectComponents
                  v-model="state.form.seller_id"
                  api="GetEmployeeListEnum"
                  :query="{ duty: EmployeeType.salesman }"
                  label-field="name"
                  value-field="id"
                  @change-value="row => (state.form.seller_name = row.name)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="备注:" copies="2">
            <template #content>
              <el-form-item prop="userName">
                <el-input v-model="state.form.remark" type="textarea" placeholder="请输入备注" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
        </div>
      </el-form>
    </slot>
  </FildCard>
  <FildCard title="原料信息" :tool-bar="false" class="mt-[5px]">
    <template #right-top>
      <el-button style="margin-left: 10px" type="primary" @click="bulkHand">
        批量操作
      </el-button>
      <el-button style="margin-left: 10px" type="primary" :icon="Plus" @click="handAdd">
        根据库存添加
      </el-button>
    </template>
    <Table ref="tablesRef" :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
      <template #whole_piece_count="{ row }">
        <vxe-input v-model="row.whole_piece_count" maxlength="200" type="float" :min="0" size="mini" @input="changeData(row)" />
      </template>
      <template #whole_piece_weight="{ row }">
        <vxe-input v-model="row.whole_piece_weight" maxlength="200" type="float" :min="0" size="mini" @input="changeData(row)" />
      </template>
      <template #bulk_piece_count="{ row }">
        <vxe-input v-model="row.bulk_piece_count" maxlength="200" type="float" :min="0" size="mini" @input="changeData(row)" />
      </template>
      <template #bulk_weight="{ row }">
        <vxe-input v-model="row.bulk_weight" maxlength="200" type="float" :min="0" size="mini" @input="changeData(row)" />
      </template>
      <template #total_weight="{ row }">
        {{ row.total_weight }}
      </template>
      <template #other_price="{ row }">
        <vxe-input v-model="row.other_price" maxlength="200" type="float" :min="0" size="mini" @input="changeData(row)" />
      </template>
      <template #unit_price="{ row }">
        <vxe-input v-model="row.unit_price" maxlength="200" type="float" :digits="4" :min="0" size="mini" @input="changeData(row)" />
      </template>
      <template #total_price="{ row }">
        {{ row.total_price }}
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" maxlength="200" size="mini" />
      </template>
      <template #operate="{ row, rowIndex }">
        <el-button type="text" @click="handDel(row, rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <SelectInformation v-model="showAdd" :unit_id="state.form.sale_unit_id" @submit="onSubmit" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}
</style>
