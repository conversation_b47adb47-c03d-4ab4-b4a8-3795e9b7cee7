<script lang="ts" setup>
import { onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'
import FineSizeDeliveryOrderDetail from '../components/FineSizeDeliveryOrderDetail.vue'
import {
  getFpmOutReservationOrder,
  updateFpmOutReservationOrderStatusCancel,
  updateFpmOutReservationOrderStatusPass,
  updateFpmOutReservationOrderStatusReject,
  updateFpmOutReservationOrderStatusWait,
} from '@/api/fpSubscribeWarehouseOrder'
import { formatDate, formatLengthDiv, formatTwoDecimalsDiv, formatWeightDiv } from '@/common/format'
import { orderStatusConfirmBox } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import { WarehouseGoodOutEnum } from '@/enum/productEnum'

const form_options = [
  {
    text: '预约类型',
    key: 'reservation_type_name',
  },
  {
    text: '营销体系名称',
    key: 'sale_system_name',
  },
  {
    text: '调出仓库名称',
    key: 'out_warehouse_name',
    showType: [WarehouseGoodOutEnum.InternalAllocate],
  },
  {
    text: '调入仓库名称',
    key: 'in_warehouse_name',
    showType: [WarehouseGoodOutEnum.InternalAllocate],
  },
  {
    text: '收货单位名称',
    key: 'biz_unit_name',
    showType: [WarehouseGoodOutEnum.Other],
  },
  {
    text: '仓库名称',
    key: 'warehouse_name',
    showType: [WarehouseGoodOutEnum.Other, WarehouseGoodOutEnum.Processing, WarehouseGoodOutEnum.Repair, WarehouseGoodOutEnum.Deduct],
  },
  {
    text: '加工单位名称',
    key: 'biz_unit_name',
    showType: [WarehouseGoodOutEnum.Processing, WarehouseGoodOutEnum.Repair],
  },
  {
    text: '退货单位名称',
    key: 'biz_unit_name',
    showType: [WarehouseGoodOutEnum.Deduct],
  },
  {
    text: '预约日期',
    key: 'reservation_time',
  },
  {
    text: '仓管员',
    key: 'store_keeper_name',
  },
  {
    text: '备注',
    key: 'remark',
    copies: 2,
  },
]
const route = useRoute()

const state = reactive<any>({
  baseData: {
    reservation_type_name: '',
    reservation_type_id: '',
    out_warehouse_id: '',
    sale_system_id: '',
    in_warehouse_id: '',
    biz_unit_id: '',
    warehouse_id: '',
    warehouse_out_time: new Date(),
    store_keeper_id: '',
    remark: '',
  },
})

// 成品信息
const finishProductionOptions = reactive({
  detailShow: true,
  tableConfig: {
    fieldApiKey: 'FpSubscribeWarehouseOrderDetail',
    showSlotNums: false,
    showSpanHeader: true,
    filterStatus: false,
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 出仓数量 结算数量 辅助数量 其他金额 结算金额
        if (['reservation_weight'].includes(column.field))
          return sumNum(data, 'reservation_weight', '', 'float')

        if (['reservation_length'].includes(column.field))
          return sumNum(data, 'reservation_length', '', 'int')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      field: 'A',
      childrenList: [
        {
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'B',
      title: '成品信息',
      childrenList: [
        {
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },

        {
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          field: 'reservation_roll',
          title: '出仓匹数',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'C',
      title: '出仓数量信息',
      childrenList: [
        {
          field: 'reservation_weight',
          title: '总数量',
          minWidth: 100,
        },
        {
          field: 'unit_name',
          title: '单位',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'D',
      title: '出仓辅助数量信息',
      childrenList: [
        {
          field: 'reservation_length',
          title: '辅助数量',
          minWidth: 100,
        },
      ],
    },

    {
      field: 'E',
      title: '单据备注信息',
      childrenList: [
        {
          field: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
  ],
})
const { fetchData, data: detailData } = getFpmOutReservationOrder()
onMounted(() => {
  getData()
})
async function getData() {
  await fetchData({ id: route.query.id })
  const { reservation_time, out_order_type_name, out_order_type, out_warehouse_name, in_warehouse_name, sale_system_name, biz_unit_name, warehouse_name, store_keeper_name, remark } = detailData.value
  state.baseData = {
    ...detailData.value,
    reservation_type_name: out_order_type_name,
    reservation_type_id: out_order_type,
    out_warehouse_name,
    sale_system_name,
    in_warehouse_name,
    biz_unit_name,
    warehouse_name,
    reservation_time: formatDate(reservation_time),
    store_keeper_name,
    remark,
  }
  finishProductionOptions.datalist = detailData.value?.item_data?.map((item: any) => {
    return {
      ...item,
      reservation_roll: formatTwoDecimalsDiv(Number(item.reservation_roll)),
      sum_stock_roll: formatTwoDecimalsDiv(Number(item.sum_stock_roll)),
      sum_stock_weight: formatWeightDiv(Number(item.sum_stock_weight)),
      sum_stock_length: formatLengthDiv(Number(item.sum_stock_length)),
      reservation_weight: formatWeightDiv(Number(item.reservation_weight)),
      reservation_length: formatLengthDiv(Number(item.reservation_length)),
    }
  })
}
async function updateStatus(audit_status: number) {
  const id: any = route.query.id?.toString()
  const options: Record<number, any> = {
    1: {
      message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' },
      api: updateFpmOutReservationOrderStatusWait,
    },
    2: {
      message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' },
      api: updateFpmOutReservationOrderStatusPass,
    },
    3: {
      message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' },
      api: updateFpmOutReservationOrderStatusReject,
    },
    4: {
      message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' },
      api: updateFpmOutReservationOrderStatusCancel,
    },
  }
  const { message, api } = options[audit_status]
  await orderStatusConfirmBox({ id, audit_status, message, api })
  getData()
}
</script>

<template>
  <StatusColumn
    :order_no="state.baseData.order_no"
    :order_id="state.baseData.id"
    :status="state.baseData.audit_status"
    :status_name="state.baseData.audit_status_name"
    permission_print_key=""
    permission_wait_key="FpSubscribeWarehouseOrder_wait"
    permission_reject_key="FpSubscribeWarehouseOrder_reject"
    permission_pass_key="FpSubscribeWarehouseOrder_pass"
    permission_cancel_key="FpSubscribeWarehouseOrder_cancel"
    permission_edit_key="FpSubscribeWarehouseOrder_edit"
    edit_router_name="FpSubscribeWarehouseOrderEdit"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  />
  <FildCard class="mt-[5px]" title="基础信息" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '120px' }">
      <template v-for="(item, index) in form_options" :key="index">
        <DescriptionsFormItem
          v-if="item.showType?.includes(state.baseData.reservation_type_id) || !item.showType"
          :label="`${item.text}:`"
          :copies="item.copies || 1"
        >
          <template #content>
            {{ state.baseData[item.key] }}
          </template>
        </DescriptionsFormItem>
      </template>
    </div>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]">
    <Table :config="finishProductionOptions.tableConfig" :table-list="finishProductionOptions.datalist" :column-list="finishProductionOptions.columnList" />
  </FildCard>
  <FineSizeDeliveryOrderDetail />
</template>

<style lang="scss" scoped>
.oreder_code {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.form_container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  color: #666;

  .form_container_item {
    display: flex;
    margin-bottom: 15px;

    .label {
      color: #ccc;
      margin-right: 10px;
    }
  }
}

.el-link {
  color: #0e7eff;
}
</style>
