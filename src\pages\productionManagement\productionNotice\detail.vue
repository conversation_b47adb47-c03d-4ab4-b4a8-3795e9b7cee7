<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import RelevanceSalesPlanOrder from '../components/RelevanceSalesPlanOrder.vue'
import ProcessRequirementCard from './components/ProcessRequirementCard.vue'
import {
  byIdOrderChangeLogItemsProductionNotice,
  cancelApprovedSheetOfProductionNotify,
  checkSheetOfProductionNotify,
  closeProductionNotify,
  deleteCancelSheetOfProductionNotify,
  getProductionNotifyOrder,
  rejectSheetOfProductionNotify,
} from '@/api/productionNotice'
import { formatDate, formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'
import { orderStatusConfirmBox } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import { processDataOut } from '@/common/handBinary'

const router = useRouter()
const route = useRoute()
const form_options = [
  {
    text: '生产计划单号',
    key: 'production_plan_order_no',
    solt: true,
  },
  {
    text: '通知日期',
    key: 'notify_date',
  },
  {
    text: '营销体系',
    key: 'sale_system_name',
  },
  {
    text: '织厂名称',
    key: 'weaving_mill_name',
  },
  {
    text: '织厂跟单',
    key: 'weaving_mill_order_follower_name',
  },
  {
    text: '跟单电话',
    key: 'weaving_mill_order_follower_phone',
  },
  {
    text: '客户名称',
    key: 'customer_name',
  },
  {
    text: '跟单员',
    key: 'customer_order_follower_name',
  },
  {
    text: '销售员',
    key: 'sale_user_name',
  },

  {
    text: '跟单QC员',
    key: 'order_qc_user_name',
  },
  {
    text: '付款期限',
    key: 'payment_term_name',
  },
  {
    text: '交坯日期',
    key: 'receipt_grey_fabric_date',
  },
  {
    text: '结算方式',
    key: 'settle_type_name',
  },
  {
    text: '收坯地址',
    key: 'receipt_grey_fabric_address',
    copies: 2,
  },
  {
    text: '发票抬头',
    key: 'invoice_header_name',
    copies: 2,
  },
  {
    text: '单据备注',
    key: 'order_remark',
    copies: 2,
  },
]

const state = reactive<any>({
  form: {},
  greyList: [],
  allList: [],
  masterList: [],
  technologicalRequirementForm: {},
  orderChangeLogItemsList: [],
  order_no: '',
  status: 0,
  status_name: '',
})

// ProcessRequirementCard 组件的数据
const processRequirementData = ref({
  weavingForm: {
    weaving_specifications_id: [],
    needle_size: '',
    total_needle_size: '',
    weaving_loss: '',
    loom_brand: '',
    loom_model_id: '',
    machines_num: '',
    upper_needle: '',
    yarn_batch: '',
    lower_needle: '',
    yarn_arrange: '',
    yarn_length: '',
    packaging_require: '',
  },
  shuttleWeavingData: {
    warp_density: '',
    weft_density: '',
    reed_inner_width: '',
    reed_outer_width: '',
    reed_no: '',
    penetration_number: '',
    upper_weft_density: '',
    lower_weft_density: '',
    gf_theory_gram_width: '',
    total_warp_pieces: '',
    tableData: [],
    warp_arrangement: '',
    tableData1: [],
    weft_arrangement: '',
  },
  fabricFlyData: {
    tableData1: [],
    tableData2: [],
  },
})
const {
  fetchData: detailFetch,
  data,
  success: detailSuccess,
  msg: detailMsg,
} = getProductionNotifyOrder()

onMounted(() => {
  getData()
})
const detailData = ref()
async function getData() {
  await detailFetch({ id: route.query.id })
  getChangeLogsData() // 变更记录
  detailData.value = processDataOut(data.value)
  if (detailSuccess.value) {
    // 基础信息
    state.form = {
      ...data.value,
      notify_date: formatDate(data.value.notify_date),
      receipt_grey_fabric_date: formatDate(data.value.receipt_grey_fabric_date),
    }
    // 坯布信息
    state.greyList = [
      {
        ...data.value,
        scheduling_roll: formatTwoDecimalsDiv(Number(data.value.scheduling_roll)), // 排产匹数
        total_scheduling_roll: formatTwoDecimalsDiv(Number(data.value.total_scheduling_roll)),
        produced_roll: formatTwoDecimalsDiv(Number(data.value.produced_roll)), // 已产匹数
        change_roll: formatTwoDecimalsDiv(Number(data.value.change_roll)), // 变更匹数
        producing_roll: formatTwoDecimalsDiv(Number(data.value.producing_roll)), // 未产匹数
        change_weight: formatWeightDiv(Number(data.value.change_weight)), // 布匹定重
        producing_weight: formatWeightDiv(Number(data.value.producing_weight)), // 布匹定重
        other_weight: formatWeightDiv(Number(data.value.other_weight)), // 布匹定重
        weight_of_fabric: formatWeightDiv(Number(data.value.weight_of_fabric)), // 布匹定重
        scheduling_weight: formatWeightDiv(Number(data.value.scheduling_weight)), // 排产定重
        total_scheduling_weight: formatWeightDiv(Number(data.value.total_scheduling_weight)),
        produced_weight: formatWeightDiv(Number(data.value.produced_weight)), // 已产定重
        process_price: formatUnitPriceDiv(Number(data.value.process_price)), // 加工单价
      },
    ]
    // 用料比例
    state.masterList
        = data.value.production_notify_material_ratio?.map((item: any) => {
        return {
          ...item,
          yarn_loss: formatTwoDecimalsDiv(item.yarn_loss),
          yarn_ratio: formatTwoDecimalsDiv(item.yarn_ratio),
          grey_fabric_color_name: data.value.grey_fabric_color_name,
          use_yarn_quantity: formatWeightDiv(item.use_yarn_quantity),
          send_yarn_quantity: formatWeightDiv(item.send_yarn_quantity),
          change_use_yarn_quantity: formatWeightDiv(item.change_use_yarn_quantity),
          change_send_yarn_quantity: formatWeightDiv(item.change_send_yarn_quantity),
          final_use_yarn_quantity: formatWeightDiv(item.final_use_yarn_quantity),
          final_send_yarn_quantity: formatWeightDiv(item.final_send_yarn_quantity),
        }
      }) || []

    // 设置 ProcessRequirementCard 组件的数据
    processRequirementData.value = {
      weavingForm: {
        weaving_specifications_id: detailData.value.weaving_specifications?.map((v: any) => v.id) || [],
        weaving_specifications_name_list: detailData.value.weaving_specifications
          ?.map((v: any) => v.weaving_specifications_name)
          .join(',') || '',
        needle_size: detailData.value.needle_size || '',
        total_needle_size: detailData.value.total_needle_size || '',
        weaving_loss: detailData.value.weaving_loss || '',
        loom_brand: detailData.value.loom_brand || '',
        loom_model_id: detailData.value.loom_model_id || '',
        loom_model_name: detailData.value.loom_model_name || '',
        machines_num: detailData.value.machines_num || '',
        upper_needle: detailData.value.upper_needle || '',
        yarn_batch: detailData.value.yarn_batch || '',
        lower_needle: detailData.value.lower_needle || '',
        yarn_arrange: detailData.value.yarn_arrange || '',
        yarn_length: detailData.value.yarn_length || '',
        packaging_require: detailData.value.packaging_require || '',
      },
      shuttleWeavingData: {
        warp_density: detailData.value.warp_density || '',
        weft_density: detailData.value.weft_density || '',
        reed_inner_width: detailData.value.reed_inner_width || '',
        reed_outer_width: detailData.value.reed_outer_width || '',
        reed_no: detailData.value.reed_no || '',
        penetration_number: detailData.value.penetration_number || '',
        upper_weft_density: detailData.value.upper_weft_density || '',
        lower_weft_density: detailData.value.lower_weft_density || '',
        gf_theory_gram_width: detailData.value.gf_theory_gram_width || '',
        total_warp_pieces: detailData.value.total_warp_pieces || '',
        tableData: detailData.value.warp_datas || [],
        warp_arrangement: detailData.value.warp_arrangement || '',
        tableData1: detailData.value.weft_datas || [],
        weft_arrangement: detailData.value.weft_arrangement || '',
      },
      fabricFlyData: {
        tableData1: [],
        tableData2: [],
      },
    }

    state.order_no = data.value.order_no
    state.status = data.value.status
    state.status_name = data.value.status_name
  }
  else {
    ElMessage.error(detailMsg.value)
  }
}

// 坯布信息表格配置
const columnList_fabic_config = ref({
  fieldApiKey: 'ProductionNoticeDetail2',
  showSlotNums: false,
  footerMethod: (val: any) => FooterMethod(val),
})
const columnList_fabic = ref([
  {
    field: 'sale_plan_order_no',
    title: '销售计划单号',
    soltName: 'sale_plan_order_no',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_width_and_unit_name',
    title: '坯布幅宽',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_gram_weight_and_unit_name',
    title: '坯布克重',
    minWidth: 100,
  },
  {
    field: 'finish_product_width_and_unit_name',
    title: '成品幅宽',
    minWidth: 100,
  },
  {
    field: 'finish_product_gram_weight_and_unit_name',
    title: '成品克重',
    minWidth: 100,
  },
  {
    field: 'weight_of_fabric',
    soltName: 'weight_of_fabric',
    title: '布匹定重',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_color_name',
    title: '织坯颜色',
    minWidth: 100,
  },
  {
    field: 'process_price',
    soltName: 'process_price',
    title: '加工单价',
    minWidth: 140,
  },
  {
    field: 'total_scheduling_roll',
    soltName: 'total_scheduling_roll',
    title: '排产匹数',
    minWidth: 100,
  },
  {
    field: 'total_scheduling_weight',
    soltName: 'total_scheduling_weight',
    title: '排产数量',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_remark',
    title: '备注',
    minWidth: 100,
  },
  {
    field: 'produced_roll',
    title: '已产匹数',
    minWidth: 100,
  },
  {
    field: 'produced_weight',
    soltName: 'produced_weight',
    title: '已产数量',
    minWidth: 100,
  },
  {
    field: 'producing_roll',
    title: '未产匹数',
    minWidth: 100,
  },
  {
    field: 'producing_weight',
    soltName: 'producing_weight',
    title: '未产数量',
    minWidth: 100,
  },
  {
    field: 'other_weight',
    soltName: 'other_weight',
    title: '其他数量',
    minWidth: 100,
  },
])

// 用料比例表格配置
const columnList_master_config = ref({
  fieldApiKey: 'ProductionNoticeDetail_B',
  showSlotNums: true,
  footerMethod: (val: any) => FooterMethod(val),
})
const columnList_master = ref([
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 100,
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 100,
  },
  {
    field: 'weaving_category',
    title: '织造类别',
    minWidth: 100,
  },
  {
    field: 'raw_material_brand',
    title: '原料品牌',
    minWidth: 100,
  },
  {
    field: 'raw_material_batch_number',
    title: '原料批号',
    minWidth: 100,
  },
  {
    field: 'color_scheme',
    title: '原料颜色',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_color_name',
    title: '织坯颜色',
    minWidth: 100,
  },
  {
    field: 'supplier_name',
    title: '供方',
    minWidth: 100,
  },
  {
    field: 'yarn_ratio',
    soltName: 'yarn_ratio',
    title: '用纱比例',
    minWidth: 100,
  },
  {
    field: 'yarn_loss',
    soltName: 'yarn_loss',
    title: '用纱损耗',
    minWidth: 100,
  },
  {
    field: 'final_use_yarn_quantity',
    soltName: 'final_use_yarn_quantity',
    title: '用纱量',
    minWidth: 100,
  },
  // {
  //   field: 'change_use_yarn_quantity',
  //   soltName: 'change_use_yarn_quantity',
  //   title: '变更用纱量',
  //   minWidth: 100,
  // },
  {
    field: 'final_send_yarn_quantity',
    soltName: 'final_send_yarn_quantity',
    title: '发纱量',
    minWidth: 100,
  },
  // {
  //   field: 'change_send_yarn_quantity',
  //   soltName: 'change_send_yarn_quantity',
  //   title: '变更发纱量',
  //   minWidth: 100,
  // },
  {
    field: 'mill_private_yarn',
    soltName: 'mill_private_yarn',
    title: '织厂出料',
    minWidth: 100,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
])
function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, columnIndex: number) => {
      if (columnIndex === 0)
        return '汇总'
      // 排产匹数
      if (['total_scheduling_roll'].includes(column.field))
        return sumNum(data, 'total_scheduling_roll', '')
      // 排产数量
      if (['total_scheduling_weight'].includes(column.field))
        return sumNum(data, 'total_scheduling_weight', '')
      // 已产匹数
      if (['produced_roll'].includes(column.field))
        return sumNum(data, 'produced_roll', '')
      // 已产数量
      if (['produced_weight', 'change_weight', 'other_weight'].includes(column.field))
        return sumNum(data, column.field, '')
      // 变更匹数
      if (['change_roll'].includes(column.field))
        return sumNum(data, 'change_roll', '')
      // 未产匹数
      if (['producing_roll'].includes(column.field))
        return sumNum(data, 'producing_roll', '')
      // 用纱量
      if (['use_yarn_quantity', 'final_use_yarn_quantity'].includes(column.field))
        return sumNum(data, column.field, '')
      // 发纱量
      if (['send_yarn_quantity', 'final_send_yarn_quantity'].includes(column.field))
        return sumNum(data, column.field, '')

      return null
    }),
  ]
}

// 变更记录
const columnList_orderChangeLogItems_config = ref({
  showSlotNums: false,
})
const columnList_orderChangeLogItems = ref([
  {
    field: 'production_change_order_no',
    soltName: 'production_change_order_no',
    title: '生产变更单号',
    minWidth: 100,
  },
  {
    field: 'total_roll',
    soltName: 'total_roll',
    title: '匹数',
    minWidth: 100,
  },
  {
    field: 'total_weight',
    soltName: 'total_weight',
    title: '数量',
    minWidth: 100,
  },
  {
    field: 'created_date',
    title: '创建时间',
    minWidth: 100,
    isDate: true,
  },
  {
    field: 'audit_date',
    title: '审核时间',
    minWidth: 100,
    isDate: true,
  },
  {
    field: 'status',
    soltName: 'status',
    title: '单据状态',
    showOrder_status: true,
    minWidth: 100,
  },
])

// 获取变更记录
const {
  fetchData: changeLogsFetch,
  data: changeDataList,
  success: changeLogsSuccess,
  msg: changeLogsMsg,
} = byIdOrderChangeLogItemsProductionNotice()
async function getChangeLogsData() {
  await changeLogsFetch({ id: route.query.id })
  if (changeLogsSuccess.value) {
    state.orderChangeLogItemsList
        = changeDataList.value?.list?.map((item: any) => {
        return {
          ...item,
          change_roll: formatTwoDecimalsDiv(Number(item.change_roll)),
          total_roll: formatTwoDecimalsDiv(Number(item.total_roll)),
          change_weight: formatWeightDiv(Number(item.change_weight)),
          weight: formatWeightDiv(Number(item.weight)),
        }
      }) || []
  }
  else {
    ElMessage.error(changeLogsMsg.value)
  }
}

// // 作废
// const { fetchData: deleteCancelFetch, success: deleteCancelSuccess, msg: deleteCancelMsg } = deleteCancelSheetOfProductionNotify()
// const cancel = async (status: number) => {
//   const res = await deleteToast('确认作废嘛？')
//   if (res) {
//     await deleteCancelFetch({ status, id: route.query.id })
//     if (deleteCancelSuccess.value) {
//       ElMessage.success('成功')
//       getData()
//     } else {
//       ElMessage.error(deleteCancelMsg.value)
//     }
//   }
// }
// // 驳回
// const { fetchData: rejectFetch, success: rejectSuccess, msg: rejectMsg } = rejectSheetOfProductionNotify()
// const reject = async (status: number) => {
//   const res = await deleteToast('确认驳回嘛？')
//   if (res) {
//     await rejectFetch({ status, id: route.query.id })
//     if (rejectSuccess.value) {
//       ElMessage.success('成功')
//       getData()
//     } else {
//       ElMessage.error(rejectMsg.value)
//     }
//   }
// }
// // 审核
// const { fetchData: auditlFetch, success: auditlSuccess, msg: auditlMsg } = checkSheetOfProductionNotify()
// const audit = async (status: number) => {
//   await auditlFetch({ status, id: route.query.id })
//   if (auditlSuccess.value) {
//     ElMessage.success('成功')
//     getData()
//   } else {
//     ElMessage.error(auditlMsg.value)
//   }
// }
// // 消审
// const { fetchData: cancelFetch, success: cancelSuccess, msg: cancelMsg } = cancelApprovedSheetOfProductionNotify()
// const eliminate = async (status: number) => {
//   const res = await deleteToast('确认取消审核嘛？')
//   if (res) {
//     await cancelFetch({ status, id: route.query.id })
//     if (cancelSuccess.value) {
//       ElMessage.success('成功')
//       getData()
//     } else {
//       ElMessage.error(cancelMsg.value)
//     }
//   }
// }
const closeLoading = ref(false)
const cancelLoading = ref(false)
const rejectLoading = ref(false)
const auditLoading = ref(false)
const eliminateLoading = ref(false)
async function updateStatus(audit_status: number) {
  const id = route.query.id as string
  if (audit_status === 5) {
    await orderStatusConfirmBox({
      id,
      message: { desc: '点击确定后订单将被关闭', title: '是否关闭该订单？' },
      api: closeProductionNotify,
      loadingRef: closeLoading,
      params: {
        business_close: 2,
      },
    })
  }
  if (audit_status === 4) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' },
      api: deleteCancelSheetOfProductionNotify,
      loadingRef: cancelLoading,
    })
  }
  if (audit_status === 3) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' },
      api: rejectSheetOfProductionNotify,
      loadingRef: rejectLoading,
    })
  }
  if (audit_status === 2) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' },
      api: checkSheetOfProductionNotify,
      loadingRef: auditLoading,
    })
  }
  if (audit_status === 1) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: {
        desc: '点击消审后订单将变为待审核状态',
        title: '是否消审该订单？',
      },
      api: cancelApprovedSheetOfProductionNotify,
      loadingRef: eliminateLoading,
    })
  }
  getData()
}

// 跳转单号详情
async function jumpPlanDetail(id: number, type: number) {
  const options: any = {
    1: 'SheetOfProductionPlanDetail',
    2: 'ProductionChangeDetail',
  }
  router.push({
    name: options[type],
    query: {
      id,
    },
  })
}

function jumpNoticeAdd() {
  router.push({
    name: 'ProductionChangeAdd',
    query: {
      id: route.query.id,
    },
  })
}

// 查看销售关联
const SaleAllocationRef = ref()
function customSaleShow(row: any) {
  SaleAllocationRef.value.showDialog({
    ...row,
    production_notify_grey_fabric_detail:
        row.production_notify_grey_fabric_detail?.map((item: any) => {
          return {
            ...item,
            roll: formatTwoDecimalsDiv(item.roll),
            weight: formatWeightDiv(item.weight),
            planed_roll: formatTwoDecimalsDiv(item.planed_roll),
            planed_weight: formatWeightDiv(item.planed_weight),
            scheduling_roll: formatTwoDecimalsDiv(item.scheduling_roll),
            total_scheduling_roll: formatTwoDecimalsDiv(item.total_scheduling_roll),
            scheduling_weight: formatWeightDiv(item.scheduling_weight),
            total_scheduling_weight: formatWeightDiv(item.total_scheduling_weight),
            produced_roll: formatTwoDecimalsDiv(item.produced_roll),
            produced_weight: formatWeightDiv(item.produced_weight),
            use_stock_roll: formatTwoDecimalsDiv(item.use_stock_roll),
            can_scheduling_roll: formatTwoDecimalsDiv(item.can_scheduling_roll),
            this_scheduling_roll: formatTwoDecimalsDiv(item.this_scheduling_roll),
          }
        }) || [],
  })
}

// ProcessRequirementCard 组件引用
const processRequirementCardRef = ref()
</script>

<template>
  <StatusColumn
    :close-loading="closeLoading"
    :cancel-loading="cancelLoading"
    :reject-loading="rejectLoading"
    :audit-loading="auditLoading"
    :eliminate-loading="eliminateLoading"
    v-bind="data"
    :change-btn="true"
    :order_no="state.order_no"
    :order_id="Number(route.query.id)"
    :status_name="state.status_name"
    :status="state.status"
    permission_close_key="ProductionNotice_close"
    permission_wait_key="ProductionNotice_wait"
    permission_reject_key="ProductionNotice_reject"
    permission_pass_key="ProductionNotice_pass"
    permission_cancel_key="ProductionNotice_cancel"
    permission_change_key="ProductionNotice_change"
    permission_edit_key="ProductionNotice_edit"
    edit_router_name="ProductionNoticeEdit"
    @close="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
    @reject="updateStatus"
    @eliminate="updateStatus"
    @change="jumpNoticeAdd"
  >
    <template #print>
      <PrintPopoverBtn
        :id="route.query.id"
        print-btn-text="打印"
        :print-type="PrintType.PrintTemplateTypeProduceNotify"
        :data-type="PrintDataType.Product"
        api="getProductionNotifyOrder"
      />
      <!--      <PrintBtn
        type="productNotice"
        :tid="1719290605719552"
        api="getProductionNotifyOrder"
        :id="route.query.id"
      /> -->
    </template>
  </StatusColumn>
  <FildCard class="mt-[5px]" :tool-bar="false" title="基础信息">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem
        v-for="(item, index) in form_options"
        :key="index"
        :label="`${item.text}:`"
        :copies="item.copies || 1"
      >
        <template #content>
          <span v-if="!item.solt">{{ state.form[item.key] }}</span>
          <el-link
            v-else
            @click="jumpPlanDetail(state.form.production_plan_order_id, 1)"
          >
            {{ state.form[item.key] }}
          </el-link>
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard class="mt-[5px]" title="坯布信息">
    <Table
      :config="columnList_fabic_config"
      :table-list="state.greyList"
      :column-list="columnList_fabic"
    >
      <template #sale_plan_order_no="{ row }">
        <el-link
          v-if="row?.production_notify_grey_fabric_detail?.length"
          @click="customSaleShow(row)"
        >
          查看
        </el-link>
      </template>
      <!-- 排产匹数 -->
      <template #total_scheduling_roll="{ row }">
        <span>{{ row.total_scheduling_roll }}</span>
        <span v-if="row.change_roll">
          (
          <span v-if="row.change_roll > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_roll }}</span>
          <span v-else-if="row.change_roll < 0" class="negative"> <el-icon><Bottom /></el-icon>
            {{ Math.abs(row?.change_roll) }}
          </span>
          )
        </span>
      </template>

      <!-- 排产数量 -->
      <template #total_scheduling_weight="{ row }">
        <span>{{ row.total_scheduling_weight }}</span>
        <span v-if="row.change_weight">
          (
          <span v-if="row.change_weight > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_weight }}</span>
          <span v-else-if="row.change_weight < 0" class="negative"> <el-icon><Bottom /></el-icon>
            {{ Math.abs(row?.change_weight) }}
          </span>
          )
        </span>
        {{ row.unit_name }}
      </template>
      <!-- 布匹定重 -->
      <template #weight_of_fabric="{ row }">
        {{ row?.weight_of_fabric }}{{ row.unit_name }}
      </template>
      <!-- 加工单价 -->
      <template #process_price="{ row }">
        {{ row?.process_price }}{{ row.unit_name ? `元/${row.unit_name}` : '' }}
      </template>
      <!-- 已产数量 -->
      <template #produced_weight="{ row }">
        {{ row?.produced_weight }}{{ row.unit_name }}
      </template>
      <!-- 已产数量 -->
      <template #producing_weight="{ row }">
        {{ row?.producing_weight }}{{ row.unit_name }}
      </template>
      <template #other_weight="{ row }">
        {{ row?.other_weight }}{{ row.unit_name }}
      </template>
      <!-- 变更匹数 -->
      <template #change_roll="{ row }">
        <!-- 结余匹数 = 排产匹数 + 变更匹数 - 收货匹数 -->
        <span v-if="row.change_roll > 0" class="positive_number">+{{ row?.change_roll }}</span>
        <span v-if="row.change_roll < 0" class="negative">{{
          row?.change_roll
        }}</span>
      </template>
      <template #grey_fabric_width="{ row }">
        {{ row?.grey_fabric_width }}
        {{ row?.grey_fabric_width_unit_name }}
      </template>
      <template #grey_fabric_gram_weight="{ row }">
        {{ row?.grey_fabric_gram_weight }}
        {{ row?.grey_fabric_gram_weight_unit_name }}
      </template>
      <template #finish_product_width="{ row }">
        {{ row?.finish_product_width }}
        {{ row?.finish_product_width_unit_name }}
      </template>
      <template #finish_product_gram_weight="{ row }">
        {{ row?.finish_product_gram_weight }}
        {{ row?.finish_product_gram_weight_unit_name }}
      </template>
    </Table>
  </FildCard>
  <FildCard class="mt-[10px]" title="用料比例">
    <Table
      :config="columnList_master_config"
      :table-list="state.masterList"
      :column-list="columnList_master"
    >
      <!-- 用纱比例 -->
      <template #yarn_ratio="{ row }">
        {{ row?.yarn_ratio }}%
      </template>
      <!-- 用纱损耗 -->
      <template #yarn_loss="{ row }">
        {{ row?.yarn_loss }}%
      </template>
      <!-- 用纱量 -->
      <template #final_use_yarn_quantity="{ row }">
        <span>{{ row?.final_use_yarn_quantity }}</span>
        <span v-if="row.change_use_yarn_quantity">
          (
          <span v-if="row.change_use_yarn_quantity > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_use_yarn_quantity }}</span>
          <span v-else-if="row.change_use_yarn_quantity < 0" class="negative"> <el-icon><Bottom /></el-icon>
            {{ Math.abs(row?.change_use_yarn_quantity) }}
          </span>
          )
        </span>
        {{ row.unit_name }}
      </template>
      <!-- 发纱量 -->
      <template #final_send_yarn_quantity="{ row }">
        <span>{{ row?.final_send_yarn_quantity }}</span>
        <span v-if="row.change_send_yarn_quantity">
          (
          <span v-if="row.change_send_yarn_quantity > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_send_yarn_quantity }}</span>
          <span v-else-if="row.change_send_yarn_quantity < 0" class="negative"> <el-icon><Bottom /></el-icon>
            {{ Math.abs(row?.change_send_yarn_quantity) }}
          </span>
          )
        </span>
        {{ row.unit_name }}
      </template>
      <!-- 织厂出料 -->
      <template #mill_private_yarn="{ row }">
        <el-checkbox
          v-model="row.mill_private_yarn"
          :disabled="true"
        />
      </template>
    </Table>
  </FildCard>
  <!-- 使用 ProcessRequirementCard 组件替代原有的工艺要求部分 -->
  <ProcessRequirementCard
    ref="processRequirementCardRef"
    mode="detail"
    :model-value="processRequirementData"
  />
  <FildCard class="mt-[10px]" :tool-bar="false" title="变更记录">
    <Table
      :config="columnList_orderChangeLogItems_config"
      :table-list="state.orderChangeLogItemsList"
      :column-list="columnList_orderChangeLogItems"
    >
      <template #production_change_order_no="{ row }">
        <el-link @click="jumpPlanDetail(row.id, 2)">
          {{
            row?.production_change_order_no
          }}
        </el-link>
      </template>
      <!-- 匹数 -->
      <template #total_roll="{ row }">
        <div>
          {{ row?.total_roll }}
          <span v-if="row?.change_roll !== 0">
            (
            <span v-if="row.change_roll > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_roll }}</span>
            <span v-else-if="row.change_roll < 0" class="negative"> <el-icon><Bottom /></el-icon>{{ Math.abs(row?.change_roll) }}</span>
            )
          </span>
        </div>
      </template>
      <!-- 数量 -->
      <template #total_weight="{ row }">
        <div>
          {{ row?.weight }}
          <span v-if="row?.change_weight !== 0">
            (
            <span v-if="row.change_weight > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_weight }}</span>
            <span v-else-if="row.change_weight < 0" class="negative"> <el-icon><Bottom /></el-icon>{{ Math.abs(row?.change_weight) }}</span>
            )
          </span>
        </div>
      </template>
    </Table>
  </FildCard>
  <RelevanceSalesPlanOrder
    ref="SaleAllocationRef"
    type="detail"
    @handle-sure="() => {}"
  />
</template>

<style lang="scss" scoped>
.form_container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  color: #666;

  .form_container_item {
    display: flex;
    margin-bottom: 15px;

    .label {
      color: #ccc;
      margin-right: 10px;
      min-width: 100px;
      text-align: right;
    }
  }
}

.cols {
  display: flex;
  margin-bottom: 20px;

  .col_item {
    width: 20%;
    text-align: center;

    .label {
      font-weight: 700;
    }
  }
}

.positive_number {
  color: green;
}

.negative {
  color: red;
}

.el-link {
  color: #0e7eff;
}
</style>
