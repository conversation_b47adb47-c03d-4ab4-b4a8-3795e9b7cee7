<script setup lang="ts" name="FinishedProductProcurement">
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import {
  FinishProductCancel,
  FinishProductDetail,
  FinishProductList,
  FinishProductList_export,
  FinishProductPass,
} from '@/api/finishedProductProcurement'
import {
  formatDate,
  formatPriceDiv,
  formatTwoDecimalsDiv,
  formatUnitPriceDiv,
  formatWeightDiv,
  sumNum,
} from '@/common/format'
import {
  debounce,
  getFilterData,
  orderStatusConfirmBox,
  resetData,
} from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import StatusTag from '@/components/StatusTag/index.vue'
import Table from '@/components/Table.vue'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import { usePrintTemplate } from '@/components/PrintPopoverBtn/index'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import { BusinessUnitIdEnum } from '@/common/enum'

const router = useRouter()
const state = reactive({
  filterData: {
    order_num: '',
    supplier_id: '',
    receipt_unit_id: '',
    status: '',
    purchase_start_date: '',
    purchase_end_date: '',
  },
  address_list: [] as any,
  multipleSelection: [],
  tableData: [] as any[],
  tableUserData: [] as any[],
})
const componentRemoteSearch = reactive({
  supplieName: '',
})
const { options } = usePrintTemplate({
  printType: PrintType.PrintTemplateTypePurOrder,
  dataType: PrintDataType.Product,
})
watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  { deep: true },
)

const purchase_time = ref()
watch(
  () => purchase_time.value,
  (value: any) => {
    state.filterData.purchase_start_date = formatDate(value?.[0]) || ''
    state.filterData.purchase_end_date = formatDate(value?.[1]) || ''
  },
)
const showAddress = ref(false)
function openAddress(row: any) {
  showAddress.value = true

  state.address_list = row || []
}

const columnList = ref([
  {
    sortable: true,
    field: 'order_num',
    title: '订单编号',
    soltName: 'order_num',
    width: '9%',
    fixed: 'left',
  },
  {
    sortable: true,
    field: 'supplier_name',
    title: '供应商',
    width: 120,
  },
  {
    sortable: true,
    field: 'receipt_unit_name',
    title: '收货单位名称',
    width: 120,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系名称',
    width: 120,
  },
  {
    sortable: true,
    field: 'sale_mode_name',
    title: '订单类型',
    width: 100,
  },
  {
    sortable: true,
    field: 'receipt_date',
    title: '收货日期',
    width: 120,
  },
  {
    sortable: true,
    field: 'purchase_date',
    title: '采购日期',
    width: 120,
  },
  {
    sortable: true,
    field: 'total_price',
    title: '单据金额',
    isPrice: true,
    width: 120,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    width: 120,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    isDate: true,
    width: 150,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    width: 120,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '修改时间',
    isDate: true,
    width: 150,
  },
  {
    sortable: true,
    field: 'auditor_name',
    title: '审核人',
    width: 120,
  },
  {
    sortable: true,
    field: 'audit_time',
    title: '审核时间',
    isDate: true,
    width: 120,
  },
  {
    sortable: true,
    field: 'status',
    title: '状态',
    showOrder_status: true,
    soltName: 'status',
    width: '5%',
    fixed: 'right',
  },
])

const userColumnList = ref([
  {
    sortable: true,
    field: 'finish_product_code',
    title: '成品编号',
    fixed: 'left',
    width: 150,
  },
  {
    sortable: true,
    field: 'finish_product_name',
    title: '成品名称',
    fixed: 'left',
    width: 100,
  },
  {
    sortable: true,
    field: 'customer_name',
    title: '所属客户',
    width: 100,
  },
  {
    sortable: true,
    field: 'color_type_name',
    title: '颜色类别',
    width: '6%',
  },
  {
    sortable: true,
    field: 'color_code',
    title: '颜色编号',
    width: '6%',
  },
  {
    sortable: true,
    field: 'fabric_type_name',
    title: '布种类型',
    width: 100,
  },
  {
    sortable: true,
    field: 'color_Name',
    title: '颜色名称',
    width: 100,
  },
  {
    sortable: true,
    field: 'dye_factory_color',
    title: '染厂颜色',
    width: 100,
  },
  {
    sortable: true,
    field: 'dye_factory_color_num',
    title: '染厂色号',
    width: 100,
  },
  {
    sortable: true,
    field: 'dye_factory_vat_code',
    title: '染厂缸号',
    width: 100,
  },
  {
    sortable: true,
    field: 'finish_product_level',
    title: '成品等级',
    width: 100,
  },
  // {
  //   sortable: true,
  //   field: 'dye_craft',
  //   title: '染整工艺',
  //   width: 100,
  // },
  {
    sortable: true,
    field: 'finish_product_width',
    title: '成品幅宽',
    width: '5%',
  },
  {
    sortable: true,
    field: 'finish_product_gram_weight',
    title: '成品克重',
    width: '5%',
  },
  {
    sortable: true,
    field: 'paper_tube_weight',
    title: '纸筒重量',
    width: '5%',
    isWeight: true,
  },
  {
    sortable: true,
    field: 'finish_product_ingredient',
    title: '成品成分',
    width: '5%',
  },
  {
    sortable: true,
    field: 'finish_product_craft', // TODO 后端没返回
    title: '成品工艺',
    width: '5%',
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
    width: 100,
  },
  {
    sortable: true,
    field: 'piece_count',
    title: '匹数',
    width: '5%',
    isPrice: true,
  },
  {
    sortable: true,
    field: 'piece_weight',
    title: '均重',
    width: '5%',
    isWeight: true,
  },
  {
    sortable: true,
    field: 'total_weight', // TODO 后端没返回
    title: '数量总计',
    width: '5%',
    isWeight: true,
  },
  {
    field: 'unit',
    title: '单位',
    width: '5%',
  },
  {
    field: 'auxiliary_unit_name',
    title: '结算单位',
    width: '5%',
  },
  {
    sortable: true,
    field: 'unit_price',
    title: '数量单价',
    width: '5%',
    isUnitPrice: true,
  },
  {
    sortable: true,
    field: 'length',
    title: '辅助数量',
    width: '5%',
    isLength: true,
  },
  {
    sortable: true,
    field: 'length_unit_price',
    title: '辅助数量单价',
    width: '5%',
    isUnitPrice: true,
  },
  {
    sortable: true,
    field: 'total_price',
    title: '金额',
    width: '5%',
    isPrice: true,
  },
])

onMounted(() => {
  getData()
})
const {
  fetchData: fetchDataList,
  data: dataList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
} = FinishProductList()
async function getData() {
  await fetchDataList(getListQuery())
  if (dataList.value?.list)
    showDetail(dataList.value.list[0])
}

// 获取请求参数
function getListQuery() {
  const status = ((state.filterData.status as unknown as []) || []).join(',')
  return getFilterData({ ...state.filterData, status })
}

const selectRow = ref<any[]>([])
function handAllSelect({ records }: any) {
  selectRow.value = records
}

function handleSelectionChange({ records }: any) {
  selectRow.value = records
}

function handAdd() {
  router.push({ name: 'FinishedProductProcurementAdd' })
}

function handReset() {
  state.filterData = resetData(state.filterData)
}

function handEdit(row: any) {
  router.push({
    name: 'FinishedProductProcurementEdit',
    params: { id: row?.id },
  })
}

const {
  fetchData: fetchDataDetail,
  data: dataDetail,
  loading: loadingDetail,
} = FinishProductDetail()
const userShow = ref(false)
async function showDetail(row: any) {
  await fetchDataDetail({ id: row.id })
  userShow.value = true
}

function handDetail(row: any) {
  router.push({
    name: 'FinishedProductProcurementDetail',
    params: { id: row?.id },
  })
}

async function handPass(id: number) {
  const msg = { title: '是否审核该订单', desc: '点击审核后订单将审核通过' }
  await orderStatusConfirmBox({ id, message: msg, api: FinishProductPass })
  getData()
}
async function handCancel(id: number) {
  const msg = {
    title: '是否消审该订单',
    desc: '点击消审后订单将变回待审核状态',
  }
  await orderStatusConfirmBox({ id, message: msg, api: FinishProductCancel })
  getData()
}

function footerMethod({ columns, data }: { columns: any, data: any }) {
  const footerData = [
    columns.map((column: any, _columnIndex: number) => {
      if (['finish_product_code'].includes(column.field))
        return '合计'

      if (['piece_count'].includes(column.field)) {
        return formatTwoDecimalsDiv(
          sumNum(data, column.field) as unknown as number,
        )
      }
      if (['piece_weight'].includes(column.field))
        return formatWeightDiv(sumNum(data, column.field) as unknown as number)
    }),
  ]
  return footerData
}

// 导出
const loadingExcel = ref(false)
async function handleExport() {
  const name_str = '成品采购'
  const { fetchData: getFetch, success: getSuccess, msg: getMsg } = FinishProductList_export({ nameFile: name_str })
  loadingExcel.value = true
  await getFetch({
    ...getListQuery(),
    download: 1,
  })
  if (getSuccess.value) {
    ElMessage({
      type: 'success',
      message: '成功',
    })
  }
  else {
    ElMessage({
      type: 'error',
      message: getMsg.value,
    })
  }
  loadingExcel.value = false
}

const tableConfig = ref({
  fieldApiKey: 'FinishedProductProcurement',
  showSlotNums: true,
  loading: loadingList,
  showPagition: true,
  page: pageList,
  size: sizeList,
  total: totalList,
  showCheckBox: true,
  height: '100%',
  showOperate: true,
  operateWidth: '11%',
  showSort: false,
  handleSizeChange,
  handleCurrentChange,
  handAllSelect,
  handleSelectionChange,
})

const userTableConfig = ref({
  fieldApiKey: 'FinishedProductProcurement_B',
  loading: loadingDetail,
  showSlotNums: true,
  height: '100%',
  footerMethod,
})
onActivated(() => {
  getData()
})
</script>

<template>
  <div class="list-page">
    <FildCard title="" :tool-bar="false" class="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="订单编号:">
          <template #content>
            <el-input
              v-model="state.filterData.order_num"
              placeholder="订单编号"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="供应商:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.supplier_id"
              api="BusinessUnitSupplierEnumlist"
              :query="{
                unit_type_id: BusinessUnitIdEnum.finishedProduct,
                name: componentRemoteSearch.supplieName,
              }"
              :column-list="[
                {
                  field: 'name',
                  title: '供应商名称',
                  minWidth: 100,
                  isEdit: true,
                },
                {
                  field: 'code',
                  title: '供应商编号',
                  minWidth: 100,
                  isEdit: true,
                },
                {
                  field: 'address',
                  title: '地址',
                  minWidth: 100,
                  isEdit: true,
                },
              ]"
              :valid-config="{
                name: [
                  { required: true, message: '请输入名称' },
                  {
                    validator({ cellValue: string }) {
                      if (cellValue === '') {
                        new Error('供应商名称');
                      }
                    },
                  },
                ],
              }"
              :editable="true"
              @change-input="val => (componentRemoteSearch.supplieName = val)"
            />
            <!-- <SelectComponents style="width: 200px" api="BusinessUnitSupplierEnumlist" label-field="name" value-field="id" v-model="state.filterData.supplier_id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货单位:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.receipt_unit_id"
              style="width: 200px"
              api="GetBusinessUnitListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="采购时间:" width="330">
          <template #content>
            <SelectDate v-model="purchase_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.status"
              :multiple="true"
              style="width: 200px"
              api="GetAuditStatusEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard class="table-card-full">
      <template #right-top>
        <el-button
          v-has="'FinishedProductProcurementAdd'"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handAdd"
        >
          新建
        </el-button>
        <BottonExcel v-has="'FinishedProductProcurementExport'" :loading="loadingExcel" title="导出文件" @on-click-excel="handleExport" />
      </template>
      <Table
        :config="tableConfig"
        :table-list="dataList.list"
        :column-list="columnList"
      >
        <template #order_num="{ row }">
          <el-link type="primary" @click="showDetail(row)">
            {{
              row?.order_num
            }}
          </el-link>
        </template>
        <template #status="{ row }">
          <StatusTag :status="row.status" />
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'FinishedProductProcurementDetail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.status === 1 || row.status === 3"
              v-has="'FinishedProductProcurementEdit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.status === 1"
              v-has="'FinishedProductProcurement_PASS'"
              type="primary"
              :underline="false"
              @click="handPass(row.id)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.status === 2"
              v-has="'FinishedProductProcurement_cancel'"
              type="primary"
              :underline="false"
              @click="handCancel(row.id)"
            >
              消审
            </el-link>
            <PrintPopoverBtn
              :id="row.id"
              :key="row.id"
              is-link
              style="width: auto"
              print-btn-text="打印"
              print-btn-type="text"
              api="FinishProductDetail"
              :options="options"
            />
          </el-space>
        <!--        <PrintBtn
          type="finishedProductProcurement"
          btnType="text"
          api="FinishProductDetail"
          :tid="1656351675572480"
          :id="row.id"
        /> -->
        </template>
      </Table>
    </FildCard>

    <FildCard title="" class="table-card-bottom">
      <Table
        :config="userTableConfig"
        :table-list="dataDetail.items"
        :column-list="userColumnList"
      >
        <template #logistics="{ row }">
          <el-link type="primary" @click="openAddress(row)">
            查看
          </el-link>
        </template>
        <template #whole_piece_weight="{ row }">
          {{ formatWeightDiv(row?.whole_piece_weight) }}
        </template>
        <template #whole_weight="{ row }">
          {{ formatWeightDiv(row?.whole_weight) }}
        </template>
        <template #bulk_weight="{ row }">
          {{ formatWeightDiv(row?.bulk_weight) }}
        </template>
        <template #total_weight="{ row }">
          {{ formatWeightDiv(row?.total_weight) }}
        </template>
        <template #unit_price="{ row }">
          {{ formatUnitPriceDiv(row?.unit_price) }}
        </template>
        <template #total_price="{ row }">
          {{ formatPriceDiv(row?.total_price) }}
        </template>
      </Table>
    </FildCard>
  </div>
</template>

<style lang="scss">
.yuan {
  width: 10px;
  height: 10px;
  background: #51c41b;
  border-radius: 50%;
  margin-right: 10px;
}

.yuan_red {
  width: 10px;
  height: 10px;
  background: #f5232d;
  border-radius: 50%;
  margin-right: 10px;
}
</style>
