<template>
  <FildCard title="基础信息" :tool-bar="false">
    <div class="line"></div>
    <template v-slot:right-top>
      <el-button type="primary" v-btnAntiShake="handSubmit">提交</el-button>
    </template>
    <slot>
      <el-form ref="ruleFormRef" :model="state.form" label-width="60px" label-position="top" :rules="fromRules">
        <el-descriptions :column="3" border size="small">
          <el-descriptions-item label="营销体系名称">
            <template #label>
              营销体系名称
              <span style="color: red">*</span>
            </template>
            <el-form-item prop="sale_system_id">
              <SelectComponents
                :default-status="true"
                @select="getSaleSystem"
                v-model="state.form.sale_system_id"
                api="GetSaleSystemDropdownListApi"
                label-field="name"
                value-field="id"
              ></SelectComponents>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="供应商名称">
            <template #label>
              供应商名称
              <span style="color: red">*</span>
            </template>
            <el-form-item prop="supplier_id">
              <SelectComponents
                @select="(val:any) => state.form.supplier_name = val?.name"
                :query="{ unit_type_id: BusinessUnitIdEnum.finishedProduct }"
                v-model="state.form.supplier_id"
                api="BusinessUnitSupplierEnumAll"
                label-field="name"
                value-field="id"
              ></SelectComponents>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="收货单位名称">
            <template #label>
              收货单位名称
              <span style="color: red">*</span>
            </template>
            <el-form-item prop="receipt_unit_id">
              <SelectComponents
                :query="{ or_unit_type_id: BusinessUnitIdEnum.customer + ',' + BusinessUnitIdEnum.dyeFactory }"
                @select="getUnitInfo"
                v-model="state.form.receipt_unit_id"
                api="GetBusinessUnitListApi"
                label-field="name"
                value-field="id"
              ></SelectComponents>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="采购日期">
            <template #label>
              采购日期
              <span style="color: red">*</span>
            </template>
            <el-form-item prop="purchase_date">
              <SelectDate type="date" v-model="state.form.purchase_date" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="收货日期">
            <el-form-item prop="receipt_date">
              <SelectDate type="date" v-model="state.form.receipt_date" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="收货地址">
            <el-form-item prop="receipt_address">
              <el-input v-model="state.form.receipt_address" placeholder="请输入收货地址"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="收货电话">
            <el-form-item prop="receipt_phone">
              <el-input v-model="state.form.receipt_phone" placeholder="请输入收货电话"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="发票抬头">
            <el-form-item prop="fapiao_title">
              <SelectComponents
                v-model="state.form.fapiao_id"
                @change-value="row => (state.form.fapiao_title = row.name)"
                api="GetInfoPurchaseInvoiceHeaderListUseByOther"
                label-field="name"
                value-field="id"
              ></SelectComponents>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="备注">
            <el-form-item prop="remark">
              <el-input type="textarea" v-model="state.form.remark"></el-input>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
    </slot>
  </FildCard>
  <FildCard title="成品信息" :tool-bar="false" class="mt-[10px]">
    <template v-slot:right-top>
      <el-button style="margin-left: 10px" type="primary" @click="handEdit">批量操作</el-button>
      <el-button style="margin-left: 10px" type="primary" :icon="Plus" @click="handAdd">根据资料添加</el-button>
    </template>
    <Table ref="tablesRef" :config="tableConfig" :tableList="state.tableData" :column-list="columnList">
      <template #customer_id="{ row }">
        <SelectComponents
          :query="{ sale_system_id: state.form.sale_system_id }"
          size="small"
          ref="customerRef"
          v-model="row.customer_id"
          api="GetCustomerEnumList"
          label-field="name"
          value-field="id"
        ></SelectComponents>
      </template>
      <template #color_code="{ row }">
        <SelectComponents
          size="small"
          @change-value="val => getColorInfo(row, val)"
          v-model="row.color_id"
          api="GetFinishProductColorDropdownList"
          :query="{ finish_product_id: row.finish_product_id }"
          label-field="product_color_code"
          value-field="id"
        ></SelectComponents>
      </template>
      <template #dye_factory_color="{ row }">
        <vxe-input size="mini" v-model="row.dye_factory_color" maxlength="200"></vxe-input>
      </template>
      <template #dye_factory_color_num="{ row }">
        <vxe-input size="mini" v-model="row.dye_factory_color_num" maxlength="200"></vxe-input>
      </template>
      <template #dye_factory_vat_code="{ row }">
        <vxe-input size="mini" v-model="row.dye_factory_vat_code" maxlength="200"></vxe-input>
      </template>
      <template #finish_product_level="{ row }">
        <SelectComponents
          size="small"
          @select="val => (row.finish_product_level = val.name)"
          v-model="row.finish_product_level_id"
          api="GetInfoBaseFinishedProductLevelEnumList"
          label-field="name"
          value-field="id"
        ></SelectComponents>
      </template>
      <template #finish_product_width="{ row }">
        <vxe-input size="mini" v-model="row.finish_product_width" maxlength="200"></vxe-input>
      </template>
      <template #finish_product_gram_weight="{ row }">
        <vxe-input size="mini" v-model="row.finish_product_gram_weight" maxlength="200"></vxe-input>
      </template>
      <template #paper_tube_weight="{ row }">
        <vxe-input size="mini" v-model="row.paper_tube_weight" maxlength="200"></vxe-input>
      </template>
      <template #remark="{ row }">
        <vxe-input size="mini" v-model="row.remark" maxlength="200"></vxe-input>
      </template>
      <template #piece_count="{ row }">
        <vxe-input size="mini" type="float" :min="0" v-model="row.piece_count" @input="changeData(row)"></vxe-input>
      </template>
      <template #piece_weight="{ row }">
        <vxe-input size="mini" type="float" :min="0" v-model="row.piece_weight" @input="changeData(row)"></vxe-input>
      </template>
      <template #total_weight="{ row }">
        {{ row.total_weight }}
      </template>
      <template #unit_price="{ row }">
        <vxe-input size="mini" type="float" :min="0" :digits="4" v-model="row.unit_price" @input="changeData(row)"></vxe-input>
      </template>
      <template #length="{ row }">
        <vxe-input size="mini" type="float" :min="0" v-model="row.length" @input="changeData(row)"></vxe-input>
      </template>
      <template #length_unit_price="{ row }">
        <vxe-input size="mini" type="float" :min="0" :digits="4" v-model="row.length_unit_price" @input="changeData(row)"></vxe-input>
      </template>
      <template #total_price="{ row }">
        {{ row.total_price }}
      </template>
      <template #operate="{ row, rowIndex }">
        <el-button type="text" @click="handDel(row, rowIndex)">删除</el-button>
      </template>
    </Table>
  </FildCard>
  <SelectRawMaterial v-model="showAdd" @submit="onSubmit" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" @submit="bulkSubmit" :show="bulkShow" @close="handBulkClose"></BulkSetting>
</template>

<script lang="ts" setup>
import { FinishProductAdd } from '@/api/finishedProductProcurement'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatDate, formatTwoDecimalsMul, formatUnitPriceMul, formatWeightDiv, formatWeightMul, sumNum } from '@/common/format'
import { formValidatePass } from '@/common/rule'
import { filterNumber, getDefaultSaleSystem, getFilterData } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import { Plus } from '@element-plus/icons-vue'
import Big from 'big.js'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import SelectRawMaterial from './components/SelectRawMaterial/index.vue'

const router = useRouter()

const state = reactive({
  form: {
    fapiao_id: 0,
    sale_system_id: getDefaultSaleSystem()?.default_sale_system_id,
    supplier_id: '',
    receipt_unit_id: '',
    purchase_date: dayjs().format('YYYY-MM-DD'),
    receipt_date: '',
    fapiao_title: '',
    remark: '',
    sale_system_name: '',
    supplier_name: '',
    receipt_unit_name: '',
    receipt_address: '',
    receipt_phone: '',
    items: [] as any[],
  },
  tableData: [] as any[],
})

const fromRules = {
  sale_system_id: [{ required: true, validator: formValidatePass.sale_system_id(), message: '请选择营销体系', trigger: 'change' }],
  supplier_id: [{ required: true, message: '请选择供应商', trigger: 'change' }],
  receipt_unit_id: [{ required: true, message: '请选择收货单位', trigger: 'change' }],
  purchase_date: [{ required: true, message: '请选择采购日期', trigger: 'change' }],
  receipt_phone: [{ validator: formValidatePass.phone('收货电话格式不正确'), trigger: 'change' }],
  // receipt_date: [{ validator: formValidatePass.timeRange(state.form.purchase_date, state.form.receipt_date, '采购日期不能大于收货日期'), trigger: 'change' }],
}

const showAdd = ref(false)

const handAdd = () => {
  showAdd.value = true
}
const handEdit = () => {
  if (multipleSelection.value?.length <= 0) return ElMessage.error('请选择批量修改的数据')
  bulkShow.value = true
}

const customerRef = ref()
const columnList = ref([
  {
    field: 'finish_product_code',
    title: '成品编号',
    fixed: 'left',
    width: 100,
  },
  {
    field: 'finish_product_name',
    title: '成品名称',
    fixed: 'left',
    width: 110,
  },
  {
    field: 'customer_id',
    title: '所属客户',
    width: 130,
    soltName: 'customer_id',
  },
  {
    field: 'color_code',
    title: '颜色编号',
    width: 130,
    soltName: 'color_code',
  },
  {
    field: 'color_type_name',
    title: '颜色类别',
    width: 110,
  },
  {
    field: 'color_Name',
    title: '颜色名称',
    width: 110,
  },

  {
    field: 'fabric_type_name',
    title: '布种类型',
    width: 110,
  },

  {
    field: 'dye_factory_color',
    title: '染厂颜色',
    width: 110,
    soltName: 'dye_factory_color',
  },
  {
    field: 'dye_factory_color_num',
    title: '染厂色号',
    width: 110,
    soltName: 'dye_factory_color_num',
  },
  {
    field: 'dye_factory_vat_code',
    title: '染厂缸号',
    width: 110,
    isPrice: true,
    soltName: 'dye_factory_vat_code',
  },
  {
    field: 'finish_product_level',
    title: '成品等级',
    width: 110,
    soltName: 'finish_product_level',
  },
  {
    field: 'dye_craft',
    title: '染整工艺',
    width: 110,
  },
  {
    field: 'finish_product_width',
    title: '成品幅宽',
    width: 110,
  },
  {
    field: 'finish_product_gram_weight',
    title: '成品克重',
    width: 110,
  },
  {
    field: 'paper_tube_weight',
    title: '纸筒重量',
    width: 110,
  },
  {
    field: 'finish_product_ingredient',
    title: '成品成分',
    width: 110,
  },
  {
    field: 'finish_product_craft', // TODO 后端没定义
    title: '成品工艺',
    width: 110,
  },
  {
    field: 'remark',
    title: '备注',
    width: 200,
    soltName: 'remark',
  },
  {
    field: 'piece_count',
    title: '匹数',
    fixed: 'right',
    width: 90,
    soltName: 'piece_count',
  },
  {
    field: 'piece_weight',
    title: '均重',
    fixed: 'right',
    width: 90,
    soltName: 'piece_weight',
  },
  {
    field: 'total_weight',
    title: '采购数量',
    fixed: 'right',
    width: 90,
    soltName: 'total_weight',
  },
  {
    field: 'unit',
    title: '单位',
    fixed: 'right',
    width: 90,
  },
  {
    field: 'unit_price',
    title: '单价',
    fixed: 'right',
    width: 90,
    soltName: 'unit_price',
  },
  {
    field: 'length',
    title: '辅助数量',
    fixed: 'right',
    width: 90,
    soltName: 'length',
  },
  {
    field: 'length_unit_price',
    title: '辅助数量单价',
    fixed: 'right',
    width: 90,
    soltName: 'length_unit_price',
  },
  {
    field: 'total_price',
    title: '金额',
    fixed: 'right',
    width: 90,
    soltName: 'total_price',
  },
])

const tablesRef = ref()

const footerMethod = ({ columns, data }: { columns: any; data: any }) => {
  const footerData = [
    columns.map((column: any, _columnIndex: number) => {
      if (['code'].includes(column.field)) {
        return '合计'
      }
      if (['number', 'whole_piece_count', 'whole_piece_weight', 'bulk_piece_count', 'count_weight', 'bulk_piece_weight', 'bulk_count_weight', 'bulk_weight', 'count_price'].includes(column.field)) {
        return sumNum(data, column.field)
      }
    }),
  ]
  return footerData
}

const onSubmit = (row: any) => {
  showAdd.value = false
  const val = row?.map((item: any) => {
    return {
      ...item,
      paper_tube_weight: formatWeightDiv(item.paper_tube_weight),
      unit: item.measurement_unit_name,
      fabric_type_name: item.type_grey_fabric_name,
      selected: false,
      customer_id: saleSaleSystemInfo.value?.default_customer_id || '',
      customer_name: saleSaleSystemInfo.value?.default_customer_name || '',
      finish_product_id: parseInt(item.id),
    }
  })
  state.tableData = [...state.tableData, ...val]
}

const changeRow = ref<any>()
const changeData = (row: any) => {
  changeRow.value = row
}

const getColorInfo = (row: any, val: any) => {
  row.color_code = val.product_color_code
  row.color_type_name = val.type_finished_product_kind_name
  row.color_type_id = val.type_finished_product_kind_id
  row.color_Name = val.product_color_name
}

watch(
  () => [changeRow.value?.piece_count, changeRow.value?.piece_weight, changeRow.value?.unit_price, changeRow.value?.total_weight, changeRow.value?.length, changeRow.value?.length_unit_price],
  async () => {
    computedData(changeRow.value)
  },
  {
    deep: true,
  }
)

const { fetchData: fetchDataAdd, success: successAdd, msg: msgAdd } = FinishProductAdd()
const ruleFormRef = ref()
const handSubmit = async () => {
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      if (validateList()) return false
      const tableData = fomatData()
      state.form.items = tableData
      await fetchDataAdd(getFilterData({ ...state.form, purchase_date: formatDate(state.form.purchase_date), receipt_date: formatDate(state.form.receipt_date) }))
      if (successAdd.value) {
        ElMessage.success('添加成功')
        router.push({
          name: 'FinishedProductProcurement',
        })
      } else {
        ElMessage.error(msgAdd.value)
      }
    }
  })
}

// 整理坯布信息
const fomatData = () => {
  return state.tableData?.map((item: any) => {
    return {
      ...item,
      piece_weight: formatWeightMul(item.piece_weight),
      unit_price: formatUnitPriceMul(item.unit_price),
      length: formatTwoDecimalsMul(item.length),
      length_unit_price: formatUnitPriceMul(item.length_unit_price),
      paper_tube_weight: formatWeightMul(item.paper_tube_weight),
      piece_count: formatTwoDecimalsMul(item.piece_count || 0),
      finish_product_id: parseInt(item.id),
    }
  })
}

// 验证坯布信息字段
const validateList = () => {
  let msg = ''
  if (!state.tableData || state.tableData.length === 0) {
    msg = '成品信息不能为空'
  } else {
    state.tableData?.some((item: any) => {
      if (!item.customer_id) {
        msg = `成品编号为${item.finish_product_code}的数据,所属客户不能为空`
        return true
      }
      if (!item.finish_product_code) {
        msg = `成品编号不能为空`
        return true
      }
      if (!item.color_code) {
        msg = `成品编号为${item.finish_product_code}的数据,颜色编号不能为空`
        return true
      }
      if (!item.color_Name) {
        msg = `成品编号为${item.finish_product_code}的数据,颜色名称不能为空`
        return true
      }
      if (!item.piece_count) {
        msg = `成品编号为${item.finish_product_code}的数据,匹数不能为空`
        return true
      }
      if (!item.piece_weight) {
        msg = `成品编号为${item.finish_product_code}的数据,均重不能为空`
        return true
      }
      if (!item.unit_price) {
        msg = `成品编号为${item.finish_product_code}的数据,单价不能为空`
        return true
      }
    })
  }
  msg && ElMessage.error(msg)
  return msg
}

const handDel = (row: any, rowIndex: number) => {
  state.tableData.splice(rowIndex, 1)
}

const saleSaleSystemInfo = ref()
const getSaleSystem = (row: any) => {
  state.form.sale_system_name = row?.name
  saleSaleSystemInfo.value = row
  clearData(row)
}
const bulkShow = ref(false)
const bulkSetting = ref<any>({
  address: {
    receipt_unit_name: '',
    receipt_unit_id: '',
    receipt_person: '',
    receipt_address: '',
  },
})
const handBulkClose = () => {
  bulkShow.value = false
}

const bulkList = reactive<any>([
  {
    field: 'customer_id',
    field_name: 'customer_name',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
    query: { sale_system_id: state.form.sale_system_id },
  },
  // {
  //   field: 'color_code',
  //   title: '颜色编号',
  //   component: 'select',
  //   api: 'GetFinishProductColorDropdownList',
  //   labelField: 'product_color_code',
  // },
  {
    field: 'dye_factory_color',
    title: '染厂颜色',
    component: 'input',
    type: 'text',
  },
  {
    field: 'dye_factory_color_num',
    title: '染厂色号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'dye_factory_vat_code',
    title: '染厂缸号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'finish_product_level',
    title: '成品等级',
    component: 'select',
    api: 'GetInfoBaseFinishedProductLevelEnumList',
  },
  {
    field: 'piece_count',
    title: '匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'piece_weight',
    title: '数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'unit_price',
    title: '单价',
    component: 'input',
    type: 'float',
    digits: 4,
  },
  {
    field: 'length',
    title: '辅助数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'length_unit_price',
    title: '辅助数量单价',
    component: 'input',
    type: 'float',
    digits: 4,
  },
])

const multipleSelection = ref<any[]>([])
const bulkSubmit = async ({ row, value, selectData }: any) => {
  if (multipleSelection.value?.length <= 0) {
    return ElMessage.error('请填写批量修改的数据')
  }
  if (!value[row.field]) {
    return ElMessage.error('请输入参数')
  }

  multipleSelection.value?.map((item: any) => {
    if (row.field === 'finish_product_level') {
      item.finish_product_level = selectData?.name
      item.finish_product_level_id = selectData?.id
    } else {
      item[row.field] = value[row.field]
    }
    computedData(item)
  })
  ElMessage.success('设置成功')
}

const computedData = async (changeRow: any) => {
  // 计算总数量
  if (filterNumber(changeRow?.piece_count) && filterNumber(changeRow?.piece_weight)) {
    changeRow.total_weight = parseFloat(Big(changeRow.piece_count).times(changeRow?.piece_weight).toFixed(2))
  }
  // 计算金额
  if (filterNumber(changeRow?.unit_price) && filterNumber(changeRow?.total_weight) && filterNumber(changeRow?.length) && filterNumber(changeRow?.length_unit_price)) {
    const price_weight = Big(changeRow.unit_price).times(changeRow?.total_weight)
    const price_length = Big(changeRow.length).times(changeRow?.length_unit_price)
    changeRow.total_price = parseFloat(price_weight.plus(price_length).toFixed(2))
  }
  await tablesRef.value.tableRef.updateFooter()
}

const handAllSelect = ({ records }: any) => {
  multipleSelection.value = records
}

const handleSelectionChange = ({ records }: any) => {
  multipleSelection.value = records
}

const getUnitInfo = (val: any) => {
  state.form.receipt_unit_name = val?.name
  state.form.receipt_address = val?.address
  state.form.receipt_phone = val?.phone
}

const clearData = (row: any) => {
  state.tableData?.map(item => {
    item.customer_id = saleSaleSystemInfo.value?.default_customer_id || ''
    item.customer_name = saleSaleSystemInfo.value?.default_customer_name || ''
  })
  bulkList[0].query = { sale_system_id: row.id }
  bulkSetting.value.customer_id = ''
}

const tableConfig = ref({
  showCheckBox: true,
  showOperate: true,
  operateWidth: 100,
  footerMethod,
  handAllSelect,
  handleSelectionChange,
})
</script>

<style lang="scss" scoped></style>
