<script setup lang="ts" name="ProductionNoticeAdd">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { isArray } from 'lodash-es'
import currency from 'currency.js'
import AddBaseInfoForm from './components/AddBaseInfoForm.vue'
import AddGreyInfoCard from './components/AddGreyInfoCard.vue'
import AddUseMaterScaleCard from './components/AddUseMaterScaleCard.vue'
import ProcessRequirementCard from './components/ProcessRequirementCard.vue'

import {
  addProductionNotifyOrder,
  getByIdProcessRequirement,
} from '@/api/productionNotice'
import {
  formatDate,
  formatTwoDecimalsDiv,
} from '@/common/format'
import { PHONE_REGEXP } from '@/common/rule'
import useRouterList from '@/use/useRouterList'
import { processDataIn } from '@/common/handBinary'
import { GetGlobalConfig } from '@/api/common'
import { debounce } from '@/common/util'
import { GlobalEnum } from '@/common/enum'

const routerList = useRouterList()

const state = reactive<any>({
  sale_system_id: 0,
  form: {
    weaving_specifications_id: [],
    needle_size: '',
    total_needle_size: '',
    weaving_loss: '',
    loom_brand: '',
    loom_model_id: '',
    machines_num: '',
    upper_needle: '',
    yarn_batch: '',
    lower_needle: '',
    yarn_arrange: '',
    yarn_length: '',
    packaging_require: '',
  },
  schedulingWeight: 0,
  baseData: {},
})

interface customStateType {
  warp_density: string | number
  weft_density: string | number
  reed_inner_width: string | number
  reed_outer_width: string | number
  reed_no: string | number
  penetration_number: string | number
  upper_weft_density: string | number
  lower_weft_density: string | number
  gf_theory_gram_width: string | number
  total_warp_pieces: string | number
  tableData: any[]
  warp_arrangement: string | number
  tableData1: any[]
  weft_arrangement: string | number
  handleAdd: (type: number) => void
  handleDel: (type: number, rowIndex: number) => void
}

const customState = reactive<customStateType>({
  warp_density: '', // number
  weft_density: '', // number
  reed_inner_width: '', // number
  reed_outer_width: '', // number
  reed_no: '', // number
  penetration_number: '', // number
  upper_weft_density: '', // number
  lower_weft_density: '', // number
  gf_theory_gram_width: '', // number
  total_warp_pieces: '', // number
  tableData: [], // 经纱
  warp_arrangement: '',
  tableData1: [], // 纬纱
  weft_arrangement: '',
  handleAdd: (type: number) => {
    if (type === 1)
      customState.tableData.push({})
    else if (type === 2)
      customState.tableData1.push({})
  },
  handleDel: (type: number, rowIndex: number) => {
    if (type === 1) {
      customState.tableData = customState.tableData.filter(
        (item: any, index: number) => rowIndex !== index,
      )
    }
    else if (type === 2) {
      customState.tableData1 = customState.tableData1.filter(
        (item: any, index: number) => index !== rowIndex,
      )
    }
  },
})

// 工艺要求数据
const processRequirementData = ref({
  weavingForm: {
    weaving_specifications_id: [],
    needle_size: '',
    total_needle_size: '',
    weaving_loss: '',
    loom_brand: '',
    loom_model_id: '',
    machines_num: '',
    upper_needle: '',
    yarn_batch: '',
    lower_needle: '',
    yarn_arrange: '',
    yarn_length: '',
    packaging_require: '',
  },
  shuttleWeavingData: {
    warp_density: '',
    weft_density: '',
    reed_inner_width: '',
    reed_outer_width: '',
    reed_no: '',
    penetration_number: '',
    upper_weft_density: '',
    lower_weft_density: '',
    gf_theory_gram_width: '',
    total_warp_pieces: '',
    tableData: [],
    warp_arrangement: '',
    tableData1: [],
    weft_arrangement: '',
  },
  fabricFlyData: {
    tableData1: [],
    tableData2: [],
  },
})

const processRequirementCardRef = ref()
const addGreyInfoCard = ref()

// 处理工艺要求数据更新
function handleProcessRequirementUpdate(data: any) {
  processRequirementData.value = data
  // 同步到原有的 state.form 和 customState
  Object.assign(state.form, data.weavingForm)
  Object.assign(customState, data.shuttleWeavingData)
}

// 处理织机机型选择
function handleLoomModelSelect(item: any) {
  state.form.loom_model_name = item.name
}

function getGreyInfoList(list: any, { sale_system_id, production_plan_order_id }: any) {
  addGreyInfoCard.value.setList(list, sale_system_id, production_plan_order_id)
}
// item营销体系 id生产计划单
function selectSaleSystemId(item: any) {
  addGreyInfoCard.value.selectSaleSystemId(item)
  state.sale_system_id = item.id
}
function selectCustomer(item: any) {
  addGreyInfoCard.value.selectCustomerId(item)
}
// 拿到排产数量
function setSchedulingWeight(schedulingWeight: number) {
  state.schedulingWeight = schedulingWeight
}

// 添加原料需要坯布id
const AddUseMaterScaleCardRef = ref()

function setGreyId(id: any) {
  setProcessRequirementData(id)
  AddUseMaterScaleCardRef.value.setGreyId(id)
}
function setOrderNo(order_no: any) {
  addGreyInfoCard.value.setProductionOrderNo(order_no)
}
const isSync = ref(false)
// 获取月结日
const { fetchData: fetchGlobalConfig, data: globalConfigData } = GetGlobalConfig()
const getGlobalConfig = debounce(async () => {
  await fetchGlobalConfig({
    id: GlobalEnum.ProductionNoticeCustomerSync,
  })
  isSync.value = globalConfigData.value.options === '同步'
}, 500)
// 获取工艺要求
const {
  fetchData: ApiProcessRequirementList,
  data: processRequirementdatalist,
  success: ProcessRequirementSuccess,
  msg: ProcessRequirementMsg,
} = getByIdProcessRequirement()
async function setProcessRequirementData(id: any) {
  await ApiProcessRequirementList({ id })
  if (ProcessRequirementSuccess.value) {
    const arr = [
      'needle_size',
      'total_needle_size',
      'upper_needle',
      'lower_needle',
      'weaving_loss',
      'yarn_batch',
      'yarn_length',
      'yarn_arrange',
    ]
    arr.forEach((key: string) => {
      state.form[key] = processRequirementdatalist.value[key]
    })
    state.form.weaving_loss = formatTwoDecimalsDiv(
      processRequirementdatalist.value.weaving_loss,
    )

    // 匹配坯布完整信息--带出单位
    addGreyInfoCard.value.setLineData(processRequirementdatalist.value)
  }
  else {
    ElMessage.error(ProcessRequirementMsg.value)
  }
}

function getFormData(form: any) {
  state.baseData = {
    ...form,
  }
}

interface MaterList {
  supplier_id: number | number[] | null
  // 其他属性
}
function checkSupplierId(materList: MaterList[]): boolean {
  if (materList.length === 0)
    return true
  for (let i = 0; i < materList.length; i++) {
    // 如果supplier_id是null，返回false
    if (materList[i].supplier_id === null)
      return false

    // 如果supplier_id是数组，把它的值变成数组的第0项
    if (isArray(materList[i].supplier_id)) {
      // 数组是空的也不正常
      if (
        (materList[i].supplier_id as number[]).length === 0
        || materList[i].supplier_id === null
      )
        return false
      materList[i].supplier_id = (materList[i].supplier_id as number[])[0]
    }
  }
  // 其他情况返回false
  return true
}

// 提交所有数据
const {
  fetchData: ApiNotifyList,
  data: successData,
  success: notifySuccess,
  msg: notifyMsg,
} = addProductionNotifyOrder()
const AddBaseInfoFormRef = ref()
async function submitData() {
  await AddBaseInfoFormRef.value.submitData()

  const greyInfo = addGreyInfoCard.value.handSubmit()

  const materList = AddUseMaterScaleCardRef.value.submitData()

  if (!checkSupplierId(materList))
    return ElMessage.error('请选择供应商')

  if (!state.baseData.sale_system_id)
    return ElMessage.error('营销体系为空')

  if (!state.baseData.weaving_mill_id)
    return ElMessage.error('织厂名称为空')

  if (!state.baseData.customer_id)
    return ElMessage.error('客户名称为空')

  if (!state.baseData.sale_user_id)
    return ElMessage.error('销售员为空')

  // 坯布信息校验 start
  let valid = true
  const greyData = [greyInfo].filter((v: any) => v)
  greyData.forEach((item: any) => {
    if (!item.customer_id)
      valid = false
  })
  if (!valid)
    return ElMessage.error('请选择所属客户')
  if (!greyData.length)
    return ElMessage.error('请添加坯布信息')

  const all_scheduling_roll
    = greyData[0].production_notify_grey_fabric_detail?.reduce(
      (pre: any, val: any) => pre + Number(val.this_scheduling_roll),
      0,
    ) || 0

  if (Number(greyData[0].scheduling_roll) < all_scheduling_roll)
    return ElMessage.error('排产匹数已超过关联的销售计划单可排产匹数之和')

  // 坯布信息校验 end

  // 原料信息校验 start
  let allRate = 0
  for (let i = 0; i < materList.length; i++) {
    if (!Number(materList[i].yarn_loss))
      return ElMessage.error('用纱损耗不能空')

    if (!Number(materList[i].yarn_ratio))
      return ElMessage.error('用纱比例不能空')

    allRate = currency(materList[i].yarn_ratio).add(allRate).value
  }
  if (materList.length && allRate !== 100)
    return ElMessage.error('用纱比例总和需为100%')
  const processRequirementCurrentData = processRequirementCardRef.value?.getData() || processRequirementData.value

  // 原料信息校验 end
  const query = processDataIn({
    ...customState,
    ...state.baseData,
    settle_type_id: Number(state.baseData.settle_type_id),
    production_notify_material_ratio: materList || [], // 用料信息
    ...state.form,
    ...greyInfo, // 坯布信息
    production_notify_grey_fabric_detail:
      greyInfo.production_notify_grey_fabric_detail.map((item: any) => {
        return {
          ...item,
          create_time: formatDate(item.create_time),
          order_time: formatDate(item.order_time),
        }
      }) || [],
    // 从 ProcessRequirementCard 组件获取的织造工艺数据
    ...processRequirementCurrentData.weavingForm,
    // 从 ProcessRequirementCard 组件获取的梭织工艺数据
    ...processRequirementCurrentData.shuttleWeavingData,
    // 织造规格ID
    weaving_specifications_ids: processRequirementCurrentData.weavingForm.weaving_specifications_id.join(','),
    warp_datas: processRequirementCurrentData.shuttleWeavingData.tableData.map((item: any) => {
      return {
        ...item,
        material_type: 1,
      }
    }),
    weft_datas: processRequirementCurrentData.shuttleWeavingData.tableData1.map((item: any) => {
      return {
        ...item,
        material_type: 2,
      }
    }),
  })
  query.weaving_mill_id = Number(state.baseData.weaving_mill_id)

  // 再次校验基础信息
  if (
    !(
      query.notify_date
      && query.sale_system_id
      && query.weaving_mill_id
      && query.customer_id
    )
  )
    return ElMessage.error('请补充基础信息')

  if (
    query.weaving_mill_order_follower_phone
    && !PHONE_REGEXP.test(query.weaving_mill_order_follower_phone)
  )
    return ElMessage.error('基础信息-请输入正确的跟单电话')

  await ApiNotifyList(query)
  if (notifySuccess.value) {
    ElMessage.success('提交成功')
    routerList.push({
      name: 'ProductionNoticeDetail',
      query: {
        id: successData.value.id,
      },
    })
  }
  else {
    ElMessage.error(notifyMsg.value)
  }
}

function setForm(form: any) {
  AddBaseInfoFormRef.value.setForm(form)
}

function clearRawList() {
  AddUseMaterScaleCardRef.value.clearRawList()
}
onMounted(getGlobalConfig)
</script>

<template>
  <AddBaseInfoForm
    ref="AddBaseInfoFormRef"
    @set-order-no="setOrderNo"
    @get-form-data="getFormData"
    @get-grey-info-list="getGreyInfoList"
    @select-sale-system-id="selectSaleSystemId"
    @select-customer="selectCustomer"
    @submit-data="submitData"
  />
  <AddGreyInfoCard
    ref="addGreyInfoCard"
    :is-sync="isSync"
    :sale_system_id="state.sale_system_id"
    @clear-raw-list="clearRawList"
    @set-form="setForm"
    @set-grey-id="setGreyId"
    @set-scheduling-weight="setSchedulingWeight"
  />
  <AddUseMaterScaleCard
    ref="AddUseMaterScaleCardRef"
    :scheduling-weight="state.schedulingWeight"
  />
  <!-- 工艺要求 -->
  <ProcessRequirementCard
    ref="processRequirementCardRef"
    v-model="processRequirementData"
    mode="add"
    @update:model-value="handleProcessRequirementUpdate"
    @loom-model-select="handleLoomModelSelect"
  />
</template>

<style lang="scss" scoped>
::v-deep .el-form-item {
  width: 100%;
}

.button_text_content {
  display: flex;
  margin-top: 4px;

  .custom_button {
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    font-size: 26px;
    cursor: pointer;
    border-radius: 2px;
    user-select: none;

    &:hover {
      background: rgba(14, 126, 255, 0.1);
    }
  }
}
</style>
