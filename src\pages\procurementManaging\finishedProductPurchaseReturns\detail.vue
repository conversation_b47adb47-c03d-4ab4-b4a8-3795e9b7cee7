<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import {
  GetPurchaseProductReturnOrder,
  UpdatePurchaseProductReturnOrderStatusCancel,
  UpdatePurchaseProductReturnOrderStatusPass,
  UpdatePurchaseProductReturnOrderStatusReject,
  UpdatePurchaseProductReturnOrderStatusWait,
} from '@/api/finishedProductPurchaseReturns'
import { formatDate, formatLengthDiv, formatPriceDiv, formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv, sumNum } from '@/common/format'
import { getDefaultSaleSystem, orderStatusConfirmBox } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'

const router = useRoute()

const state = reactive({
  form: {
    sale_system_id: getDefaultSaleSystem()?.default_sale_system_id,
    supplier_id: '',
    supplier_name: '',
    return_time: '',
    warehouse_return_id: '',
    invoice_header_id: '',
    sale_mode_name: '',
    remark: '',
    item_data: [] as any[],
  },
  tableData: [] as any[],
})

const fromRules = {
  sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
  supplier_id: [{ required: true, message: '请选择供应商', trigger: 'change' }],
  warehouse_return_id: [{ required: true, message: '请选择退货仓库名称', trigger: 'change' }],
  return_time: [{ required: true, message: '请选择退货日期', trigger: 'change' }],
}

const { fetchData: detailFetch, data: detalData, success, msg } = GetPurchaseProductReturnOrder()

onMounted(async () => {
  getData()
})

async function getData() {
  await detailFetch({
    id: router.params.id,
  })

  if (success.value) {
    state.form.sale_system_id = detalData.value.sale_system_id
    state.form.supplier_id = detalData.value.supplier_id
    state.form.supplier_name = detalData.value.supplier_name
    state.form.return_time = detalData.value.return_time
    state.form.warehouse_return_id = detalData.value.warehouse_return_id
    state.form.invoice_header_id = detalData.value.invoice_header_id
    state.form.sale_mode_name = detalData.value.sale_mode_name
    state.form.remark = detalData.value.remark
    state.tableData = detalData.value?.item_data?.map((item: any) => {
      return {
        ...item,
        return_roll: formatTwoDecimalsDiv(item?.return_roll || 0),
        avg_weight: formatWeightDiv(item?.avg_weight || 0),
        return_weight: formatWeightDiv(item?.return_weight || 0),
        weight_unit_price: formatUnitPriceDiv(item?.weight_unit_price || 0),
        return_length: formatLengthDiv(item?.return_length || 0),
        length_unit_price: formatUnitPriceDiv(item?.length_unit_price || 0),
        return_price: formatPriceDiv(item?.return_price || 0),
        out_roll: formatPriceDiv(item?.out_roll || 0),
        out_weight: formatWeightDiv(item?.out_weight || 0),
        out_length: formatLengthDiv(item?.out_length || 0),
      }
    })
  }
  else {
    return ElMessage.error(msg.value)
  }
}

const columnList = ref([
  {
    field: 'product_code',
    title: '成品编号',
    fixed: 'left',
    width: 150,
  },
  {
    field: 'product_name',
    title: '成品名称',
    fixed: 'left',
    width: 100,
  },
  {
    field: 'product_purchase_order_no',
    title: '采购单号',
    fixed: 'left',
    width: 130,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    width: 100,
  },
  {
    field: 'product_color_code',
    title: '色号',
    width: 100,
  },
  {
    field: 'product_color_name',
    title: '颜色名称',
    width: 100,
  },
  {
    field: 'dye_factory_dyelot_number',
    title: '染厂缸号',
    width: 100,
  },
  {
    field: 'product_craft', // TODO 后端没返回
    title: '成品工艺',
    width: 100,
  },
  {
    field: 'product_level_name',
    title: '成品等级',
    width: 100,
  },
  {
    field: 'product_ingredient',
    title: '成品成分',
    width: 100,
  },
  {
    field: 'product_remark',
    title: '成品备注',
    width: 100,
  },
  {
    field: 'return_roll',
    title: '退货数',
    width: 100,
  },
  {
    field: 'avg_weight',
    title: '均重',
    width: 100,
  },
  {
    field: 'return_weight', // TODO 后端没返回
    title: '退货数量',
    width: 100,
  },
  {
    field: 'measurement_unit_name',
    title: '单位',
    width: 70,
  },
  {
    field: 'auxiliary_unit_name',
    title: '结算单位',
    width: 100,
  },
  {
    field: 'weight_unit_price',
    title: '数量单价',
    width: 100,
  },
  {
    field: 'return_length',
    title: '辅助数量',
    width: 100,
  },
  {
    field: 'length_unit_price',
    title: '辅助数量单价',
    width: 110,
  },
  {
    field: 'return_price',
    title: '退货金额',
    width: 100,
  },
  {
    field: 'out_roll',
    title: '出仓匹数',
    width: 100,
  },
  {
    field: 'out_weight',
    title: '出仓数量',
    width: 100,
  },
  {
    field: 'out_length',
    title: '出仓辅助数量',
    width: 100,
  },
  {
    field: 'remark',
    title: '备注',
    width: 100,
  },
])

const tablesRef = ref()

function footerMethod({ columns, data }: { columns: any, data: any }) {
  const footerData = [
    columns.map((column: any, _columnIndex: number) => {
      if (['product_code'].includes(column.field))
        return '合计'

      if (['return_roll'].includes(column.field))
        return sumNum(data, column.field)

      if (['return_weight'].includes(column.field))
        return sumNum(data, column.field)

      if (['return_price'].includes(column.field))
        return `¥${sumNum(data, column.field)}`
    }),
  ]
  return footerData
}

async function updateStatus(audit_status: number) {
  const id: string = router.params.id.toString()
  if (audit_status === 4)
    await orderStatusConfirmBox({ id, message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' }, api: UpdatePurchaseProductReturnOrderStatusCancel })

  if (audit_status === 3)
    await orderStatusConfirmBox({ id, message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' }, api: UpdatePurchaseProductReturnOrderStatusReject })

  if (audit_status === 2)
    await orderStatusConfirmBox({ id, message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' }, api: UpdatePurchaseProductReturnOrderStatusPass })

  if (audit_status === 1)
    await orderStatusConfirmBox({ id, message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' }, api: UpdatePurchaseProductReturnOrderStatusWait })

  getData()
}

const tableConfig = ref({
  operateWidth: 100,
  footerMethod,
  fieldApiKey: 'FinishedProductPurchaseReturnsDetail',
})
</script>

<template>
  <StatusColumn
    :order_no="detalData.order_no"
    :order_id="detalData.id"
    :status="detalData.audit_status"
    :status_name="detalData.audit_status_name"
    permission_cancel_key="FinishedProductPurchaseReturns_Cancel"
    permission_reject_key="FinishedProductPurchaseReturns_Reject"
    permission_pass_key="FinishedProductPurchaseReturns_Pass"
    permission_wait_key="FinishedProductPurchaseReturns_Wait"
    permission_edit_key="FinishedProductPurchaseReturnsEdit"
    edit_router_name="FinishedProductPurchaseReturnsEdit"
    edit_router_type="params"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  />
  <FildCard title="基础信息" :tool-bar="false">
    <div class="line" />
    <slot>
      <el-form :model="state.form" label-width="60px" label-position="top" :rules="fromRules">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem label="营销体系名称:">
            <template #content>
              {{ detalData.sale_system_name }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="供应商名称:">
            <template #content>
              {{ detalData.supplier_name }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="退货日期:">
            <template #content>
              {{ formatDate(detalData.return_time) }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="退货仓库名称:">
            <template #content>
              {{ detalData.warehouse_return_name }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="发票抬头:">
            <template #content>
              {{ detalData.invoice_header_name }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="订单类型:">
            <template #content>
              {{ detalData.sale_mode_name }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="备注:" copies="2">
            <template #content>
              {{ detalData.remark }}
            </template>
          </DescriptionsFormItem>
        </div>
      </el-form>
    </slot>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]">
    <Table ref="tablesRef" :config="tableConfig" :table-list="state.tableData" :column-list="columnList" />
  </FildCard>
</template>

<style lang="scss" scoped></style>
