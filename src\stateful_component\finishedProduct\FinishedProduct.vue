<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import GridTable from '@/components/GridTable/index.vue'
import { GetFinishProductDropdownList } from '@/api/finishPurchaseWarehouseEntry'
import { getFilterData } from '@/common/util'

const TableRef = ref<InstanceType<typeof GridTable> | null>()
const columnList = ref<any>([
  {
    sortable: true,
    field: 'finish_product_code',
    title: '成品编号',
  },
  {
    sortable: true,
    field: 'finish_product_name',
    title: '成品名称',
  },
])
// 获取数据的请求
const query = ref({

})

const inint = ref(true) // 是否初始化状态
const pulldownRef = ref() // 最外层下拉框实例
const inputRef = ref() // 输入框实例
const girdRef = ref() // 模糊搜索的table实例
const selectList = ref([]) // 下拉列表的数据
const tabelList = ref([]) // 弹出窗的选择数据
const showModal = ref(false) // 是否展示弹出窗
const { fetchData: getData, loading, total } = GetFinishProductDropdownList() // 获取下拉列表信息

// 表格主体配置
const _TableConfig = ref<any>({
  showSeq: false,
  showCheckBox: false,
  showRadio: true,
})

const paginationConfig = ref<any>(() => ({
  page: 1, // 当前页
  size: 10, // 每页条数
  total, // 总条数
  defaultPageSize: 50,
  pageSizes: [10, 20, 50, 100], // 可选的每页条数
  pageLayout: 'total, sizes, prev, pager, next, jumper', // 分页组件的布局
  handleSizeChange: (newSize: number) => {
    paginationConfig.value.size.value = newSize
    // getDataList() // 每页条数改变时重新加载数据
  },
  handleCurrentChange: (newPage: number) => {
    paginationConfig.value.page.value = newPage
    // getDataList() // 页码改变时重新加载数据
  },
}))

// 获取信息的实际方法
async function getDataList() {
  const result = await getData(getFilterData(query.value))
  if (result.success && result.total > 0) {
    // 如果是第一次初始化的时候，那么就让selectList赋值一次
    if (inint.value) {
      selectList.value = result.data.list
      inint.value = false
    }
    // 后续所有的变化都仅让表格的参与更新
    tabelList.value = result.data.list
  }
  // 如果请求不通过，而且没有数据的时候，那么就清空数据
  else {
    selectList.value = []
    tabelList.value = []
  }
}
// 当组件准备完毕的时候执行
onMounted(async () => {
  inint.value = true
  await getDataList()
})
// 当聚焦输入框的时候
function focusEvent() {
  // 如果下拉实例已经准备完毕，那就直接展示
  const $pulldown = pulldownRef.value
  if ($pulldown) {
    $pulldown.showPanel()
    nextTick(() => {
      // 如果事件回调完成，那么就让表格的第一行高亮
      girdRef.value?.setCurrentRow(girdRef.value?.getData(0))
    })
  }
}
// 聚焦输入框的时候点击键盘上下和回车键事件
function handleInputKeyup({ $event }: { $event: any }) {
  const currentRow = girdRef.value?.getCurrentRecord() // 获取当前行
  // 如果有数据
  if (currentRow) {
    const currentIndex = girdRef.value?.getRowIndex(currentRow)
    switch ($event.keyCode) {
      case 38: // 上键
        if (girdRef.value) {
          const prev = girdRef.value?.getData(currentIndex - 1)
          if (prev) {
            girdRef.value?.setCurrentRow(prev)
            girdRef.value?.scrollToRow(prev)
          }
        }
        break
      case 40: // 下键
        // 下
        if (girdRef.value) {
          const next = girdRef.value?.getData(currentIndex + 1)
          if (next) {
            girdRef.value?.setCurrentRow(next)
            girdRef.value?.scrollToRow(next)
          }
        }
        break
      case 13: // 回车键
        // cellClickEvent({ row: girdRef.value?.getData(currentIndex) })
        girdRef.value?.scrollToRow(currentRow)
        pulldownRef.value.hidePanel()
        // 使输入框失去焦点
        inputRef.value.blur()
        break
      default:
        break
    }
  }

  // const currentIndex = girdRef.value?.getRowIndex(currentRow) || 0 // 数据当前行的索引
}
// 失去焦点
function blurEvent() {
  // 如果下拉实例已经准备完毕而且在显示状态，那么就关闭
  const $pulldown = pulldownRef.value
  if ($pulldown)
    $pulldown.hidePanel()
}
// 点击输入框的右侧图标显示弹出窗
function handlecClickInfos() {
  showModal.value = true
}
// 点击弹出窗的取消按钮
function handCancel() {
  showModal.value = false
}
// 点击弹出窗的确定按钮
function handleSure() {
  showModal.value = false
}
</script>

<template>
  <!-- 下拉组件 -->
  <vxe-pulldown ref="pulldownRef" transfer>
    <!-- 输入框 -->
    <template #default>
      <vxe-input
        ref="inputRef"
        placeholder="请输入"
        @focus="focusEvent"
        @blur="blurEvent"
        @keyup="handleInputKeyup"
      >
        <template #suffix>
          <!--          清除按钮 -->
          <i class="vxe-icon-close vxe_icon" />
          <!--          展开模态框按钮 -->
          <i class="vxe-icon-table vxe_icon" @click="handlecClickInfos" />
        </template>
      </vxe-input>
    </template>
    <template #dropdown>
      <el-scrollbar max-height="310">
        <VxeGrid
          ref="girdRef"
          border
          gird-refborder
          auto-resize
          :loading="loading"
          :scroll-y="{ enabled: true }"
          height="300px"
          :row-config="{ isHover: true, isCurrent: true }"
          :keyboard-config="{
            isArrow: true,
            isEnter: true,
          }"
          :data="selectList"
          :columns="[{ title: '成品编号', field: 'finish_product_code' }]"
        />
      </el-scrollbar>
    </template>
  </vxe-pulldown>
  <Teleport to="body">
    <vxe-modal
      v-model="showModal"
      destroy-on-close
      show-zoom
      resize
      show-footer
      title="从质检报表中查询"
      width="1000"
      height="550"
      :mask="false"
      :lock-view="false"
      :esc-closable="true"
    >
      <div>
        <!-- 搜索表单 -->
        <el-form :inline="true">
          <el-form-item label="缸号">
            <el-input placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="来源供应商">
            <el-input placeholder="请输入" clearable />
          </el-form-item>
        </el-form>
        <!-- 展示的表格 -->
        <GridTable
          ref="TableRef"
          :config="_TableConfig"
          :columns="columnList"
          :data="tabelList"
          height="350"
          :el-pagination-config="paginationConfig"
          :show-pagition="true"
        />
      </div>
      <template #footer>
        <el-button @click="handCancel">
          取消
        </el-button>
        <el-button type="primary" @click="handleSure">
          确认
        </el-button>
      </template>
    </vxe-modal>
  </Teleport>
</template>

<style lang="scss" scoped>
:deep(.vxe-input--suffix) {
  width: auto;
}

:deep(.vxe-input.is--suffix .vxe-input--inner) {
  padding-right: 46px;
}
.vxe-table--render-default .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-footer--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
  padding: 6px 0 !important;
}

.vxe-table--render-default .vxe-body--column:not(.col--ellipsis) {
  cursor: pointer;
}

.vxe_icon {
  margin-right: 6px;
  font-size: 14px;
  cursor: pointer;
}
:deep(.el-form-item--default){
  margin-bottom: 0;
}
</style>
