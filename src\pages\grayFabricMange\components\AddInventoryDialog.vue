<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { computed, reactive, ref, watch } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import { getGfmWarehouseSumList } from '@/api/greyFabricPurchaseReturn'
import { getFilterData, resetData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'
import { BusinessUnitIdEnum } from '@/common/enum'

const emits = defineEmits(['handleSure'])

const state = reactive({
  api: getGfmWarehouseSumList,
  filterData: {
    grey_fabric_code: '',
    grey_fabric_name: '',
    custom_id: '',
    supplier_id: '',
    warehouse_id: '',
  },
  showModal: false,
  modalName: '从库存中添加坯布',
  multipleSelection: [],
  sale_system_id: '',
})
const apiResult = ref()
watch(() => state.api, (api) => {
  apiResult.value = api()
}, {
  immediate: true,
})
// watch(
//   () => state.showModal,
//   () => {
//     if (state.showModal) {
//       getData()
//     }
//   }
// )

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
})

watch(
  () => [state.filterData, state.showModal],
  (newVal) => {
    if (newVal[1])
      getData()
  },
  {
    deep: true,
  },
)

const tableConfig = computed<any>(() => ({
  loading: apiResult.value.loading,
  showPagition: true,
  showSlotNums: true,
  page: apiResult.value.page,
  size: apiResult.value.size,
  total: apiResult.value.total,
  height: '100%',
  showCheckBox: true,
  showSort: false,
  keyField: 'id',
  checkRowKeys: [],
  handleSizeChange: (val: number) => apiResult.value.handleSizeChange(val),
  handleCurrentChange: (val: number) => apiResult.value.handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
}))

function getData() {
  apiResult.value.fetchData(getFilterData(state.filterData))
}

const columnList = ref([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
  },
  {
    field: 'supplier_name',
    title: '供方名称',
  },
  {
    field: 'customer_name',
    title: '所属客户',
  },
  {
    field: 'yarn_batch',
    title: '纱批',
  },
  {
    field: 'machine_number',
    title: '机台号',
  },
  {
    field: 'gray_fabric_color_name',
    title: '织坯颜色',
  },
  {
    field: 'grey_fabric_level_name',
    title: '坯布等级',
  },
  {
    field: 'unit_name',
    title: '单位',
  },
  //   {
  //     field: 'warehouse_bin_Name',
  //     title: '仓位',
  //   },
  //   {
  //     field: 'finish_product_width',
  //     title: '成品幅宽',
  //   },
  //   {
  //     field: 'finish_product_gram_weight',
  //     title: '成品克重',
  //   },
  {
    field: 'needle_size',
    title: '针寸数',
  },
  {
    field: 'source_remark',
    title: '坯布备注',
  },
  {
    field: 'stock_roll',
    title: '匹数',
    isPrice: true,
  },
  {
    field: 'stock_weight',
    title: '数量',
    isWeight: true,
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handCancel() {
  state.showModal = false
}

function handleSure() {
  if (!state.multipleSelection.length)
    return ElMessage.error('请选择一条数据')
  else
    emits('handleSure', state.multipleSelection)
}

function handReset() {
  state.filterData = resetData(state.filterData)
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="80vw" height="80vh" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-col overflow-hidden h-full">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="坯布编号:">
          <template #content>
            <el-input v-model="state.filterData.grey_fabric_code" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布名称:">
          <template #content>
            <el-input v-model="state.filterData.grey_fabric_name" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="供方名称:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.supplier_id"
              api="BusinessUnitSupplierEnumlist"
              :query="{ unit_type_id: BusinessUnitIdEnum.blankFabric, name: componentRemoteSearch.name }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :valid-config="{
                name: [
                  { required: true, message: '请输入名称' },
                  {
                    validator({ cellValue }) {
                      if (cellValue === '') {
                        new Error('供应商名称')
                      }
                    },
                  },
                ],
              }"
              :editable="true"
              @change-input="val => (componentRemoteSearch.name = val)"
            />
            <!-- <SelectComponents api="BusinessUnitSupplierEnumlist" label-field="name" value-field="id" v-model="state.filterData.supplier_id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="所属客户:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.custom_id"
              :query="{ sale_system_id: state.sale_system_id, name: componentRemoteSearch.customer_name }"
              api="GetCustomerEnumList"
              :column-list="[
                {
                  title: '客户编号',
                  minWidth: 100,
                  required: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      title: '客户编号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '客户名称',
                  minWidth: 100,
                  colGroupHeader: true,
                  required: true,
                  childrenList: [
                    {
                      isEdit: true,
                      field: 'name',
                      title: '客户名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '电话',
                  colGroupHeader: true,
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'phone',
                      isEdit: true,
                      title: '电话',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '销售员',
                  minWidth: 100,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'seller_name',
                      title: '销售员',
                      soltName: 'seller_name',
                      isEdit: true,
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @on-input="val => (componentRemoteSearch.customer_name = val)"
            />
            <!-- <SelectComponents :query="{ sale_system_id: state.sale_system_id }" api="GetCustomerEnumList" label-field="name" value-field="id" v-model="state.filterData.custom_id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
      <div class="flex flex-1 flex-col overflow-hidden h-full">
        <Table :config="tableConfig" :table-list="apiResult.data?.list" :column-list="columnList" />
      </div>
    </div>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
