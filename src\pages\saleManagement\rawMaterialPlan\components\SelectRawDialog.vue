<script lang="ts" setup>
import { nextTick, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import Table from '@/components/Table.vue'
import { formatPriceDiv } from '@/common/format'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { debounce, getFilterData, resetData } from '@/common/util'
import { yarn_by_detail_id } from '@/api/materialPlan'
import { raw_material_list_dropdown } from '@/api/rawInformation'

const emits = defineEmits(['handleSure'])

const state = reactive<any>({
  filterData: {
    raw_material_id: '',
    type: '',
    count: '',
  },
  showModal: false,
  modalName: '选择原料',
  showOther: '1',
  rowIndex: -1,
  multipleSelection: [],
  list: [],
  info: {},
})

const {
  fetchData: fetchData1,
  data: data1,
  total: total1,
  loading: loading1,
  page: page1,
  size: size1,
  handleSizeChange: handleSizeChange1,
  handleCurrentChange: handleCurrentChange1,
} = yarn_by_detail_id()

const {
  fetchData: fetchData2,
  data: data2,
  total: total2,
  loading: loading2,
  page: page2,
  size: size2,
  handleSizeChange: handleSizeChange2,
  handleCurrentChange: handleCurrentChange2,
} = raw_material_list_dropdown()

const tableConfig = reactive<any>({
  loading: state.showOther === '1' ? loading1.value : loading2.value,
  showPagition: true,
  showSlotNums: true,
  height: '100%',
  page: state.showOther === '1' ? page1 : page2,
  size: state.showOther === '1' ? size1 : size2,
  total: state.showOther === '1' ? total1 : total2,
  showRadio: true,
  showSort: false,
  handleSizeChange: (val: number) => (state.showOther === '1' ? handleSizeChange1(val) : handleSizeChange2(val)),
  handleCurrentChange: (val: number) => (state.showOther === '1' ? handleCurrentChange1(val) : handleCurrentChange2(val)),
  radioChangeEvent: (val: any) => handleSelectionChange(val),
})

function handReset() {
  state.filterData = resetData(state.filterData)
}

function handleSelectionChange({ newValue }: any) {
  state.multipleSelection = [newValue]
}

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

watch(
  () => [state.filterData, state.showOther],
  debounce(() => {
    getData()
  }, 400),
  {
    deep: true,
  },
)

async function getData() {
  const query = {
    ...state.filterData,
    id: state.info.raw_material_id,
  }
  state.showOther === '1' ? await fetchData1(getFilterData(query)) : await fetchData2(getFilterData(state.filterData))
}

function handCancel() {
  state.showModal = false
}

function handleSure() {
  if (!state.multipleSelection.length && state.multipleSelection.length > 1) {
    return ElMessage.error('请选择一条数据')
  }
  else {
    state.showModal = false
    emits('handleSure', state)
  }
}

const tableRef = ref()

watch(
  [() => data1.value, () => data2.value],
  () => {
    const list = state.showOther === '1' ? data1.value?.list : data2.value?.list
    state.list = list
    tableConfig.loading = state.showOther === '1' ? loading1.value : loading2.value
    tableConfig.total = state.showOther === '1' ? total1 : total2
    tableConfig.page = state.showOther === '1' ? page1 : page2
    tableConfig.size = state.showOther === '1' ? size1 : size2
    nextTick(() => {
      tableRef.value.tableRef.refreshColumn()
    })
  },
  { deep: true },
)

function handChange(val: any) {
  state.showOther = val.toString()
}

const columnList = ref([
  {
    field: 'code',
    minWidth: 100,
    title: '原料编号',
  },
  {
    field: 'name',
    minWidth: 100,
    title: '原料名称',
  },
  {
    field: 'type_name',
    minWidth: 100,
    title: '原料类型',
  },
  {
    field: 'color',
    minWidth: 100,
    title: '原料颜色',
  },
  {
    field: 'ingredient',
    minWidth: 100,
    title: '原料成分',
  },
  {
    field: 'count',
    minWidth: 100,
    title: '原料支数',
  },
  {
    field: 'supplier_name',
    minWidth: 100,
    title: '供应商',
  },
  {
    field: '',
    minWidth: 100,
    title: '用纱损耗',
    soltName: 'yarn_loss',
  },
  {
    field: '',
    minWidth: 100,
    title: '用纱比例',
    soltName: 'yarn_ratio',
  },
])

const columnListTwo = ref([
  {
    field: 'code',
    title: '原料编号',
    width: 125,
    fixed: 'left',
    sortable: true,
  },
  {
    field: 'name',
    title: '原料名称',
    width: 125,
    fixed: 'left',
    sortable: true,
  },
  {
    field: 'type_name',
    title: '原料类型',
    width: 125,
    sortable: true,
  },
  {
    field: 'ingredient',
    title: '原料成分3',
    width: 125,
    sortable: true,
  },
  {
    field: 'craft',
    title: '原料工艺',
    width: 125,
    sortable: true,
  },
  {
    field: 'count',
    title: '原料支数',
    width: 125,
    sortable: true,
  },
  {
    field: 'value',
    title: '用途',
    width: 125,
    sortable: true,
  },
  {
    field: 'supplier_name',
    title: '供应商',
    width: 125,
  },
  {
    field: 'bleach',
    title: '漂染性',
    width: 125,
    sortable: true,
  },
  {
    sortable: true,
    field: 'unit_name',
    title: '单位',
    width: 125,
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
    width: 125,
  },
])

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="80vw" height="70vh" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-col overflow-hidden h-full">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="原料编号:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.raw_material_id"
              label-field="code"
              value-field="id"
              api="rawmaterialMenu"
              :column-list="[
                {
                  field: 'name',
                  title: '原料名称',
                  minWidth: 100,
                },
                {
                  field: 'code',
                  title: '原料编号',
                  minWidth: 100,
                },
              ]"
              :table-column="[
                {
                  field: 'code',
                  title: '编号',
                },
              ]"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原料名称:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.raw_material_id"
              label-field="name"
              value-field="id"
              api="rawmaterialMenu"
              :column-list="[
                {
                  field: 'name',
                  title: '原料名称',
                  minWidth: 100,
                },
                {
                  field: 'code',
                  title: '原料编号',
                  minWidth: 100,
                },
              ]"
              :table-column="[
                {
                  field: 'name',
                  title: '名称',
                },
              ]"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原料类型:">
          <template #content>
            <SelectComponents v-model="state.filterData.type" api="getTypeWarehouseList" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原料支数:">
          <template #content>
            <el-input v-model="state.filterData.count" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-radio-group v-model="state.showOther" @change="val => handChange(val)">
              <el-radio label="1" size="large">
                仅显示该原料关联的用纱信息
              </el-radio>
              <el-radio label="2" size="large">
                显示全部原料
              </el-radio>
            </el-radio-group>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
      <div class="flex-1 flex overflow-hidden h-full">
        <Table ref="tableRef" :config="tableConfig" :table-list="state?.list" :column-list="state.showOther === '1' ? columnList : columnListTwo">
          <template #yarn_loss="{ row }">
            {{ formatPriceDiv(row?.wastage) }}%
          </template>
          <template #yarn_ratio="{ row }">
            {{ formatPriceDiv(row?.ratio) }}%
          </template>
        </Table>
      </div>
    </div>
    <template #footer>
      <el-button type="primary" @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
