<script setup lang="ts">
import { ref, watchEffect } from 'vue'
import { cloneDeep } from 'lodash-es'
import FildCard from '@/components/FildCard.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { GetCheckOrderItemReport } from '@/api'
import Table from '@/components/Table.vue'
import { processDataOut } from '@/common/handBinary'
import { formatDate, formatHashTag } from '@/common/format'
import SelectMergeComponent from '@/components/SelectMergeComponent/index.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'

const queryState = ref({
  /**
   * 审核时间
   */
  audit_date: '',
  /**
   * 单据状态
   */
  audit_status: '',
  /**
   * 颜色id
   */
  color_id: '',
  /**
   * 颜色名称
   */
  color_name: '',
  /**
   * 创建时间结束
   */
  create_date: '',
  /**
   * 客户id
   */
  customer_id: '',
  /**
   * 客户name
   */
  customer_name: '',
  /**
   * 缸号
   */
  dyelot_number: '',
  /**
   * 单据时间
   */
  order_date: '',
  /**
   * 单号
   */
  order_no: '',
  /**
   * 主单id
   */
  product_check_order_id: '',
  /**
   * 成品id
   */
  product_id: '',
  /**
   * 成品name
   */
  product_name: '',
  /**
   * 仓位id
   */
  warehouse_bin_id: '',
  /**
   * 仓库id
   */
  warehouse_id: '',
})

const { fetchData: getCheckOrderItemReport, data: checkOrderItemReportList, loading, page, size, total, handleSizeChange, handleCurrentChange } = GetCheckOrderItemReport()
const tableData = ref<any>([])

async function getData() {
  const query: any = cloneDeep({
    ...queryState.value,
    create_start_date: queryState.value.create_date?.[0] ? formatDate(queryState.value.create_date?.[0]) : '',
    create_end_date: queryState.value.create_date?.[1] ? formatDate(queryState.value.create_date?.[1]) : '',
    audit_start_date: queryState.value.audit_date?.[0] ? formatDate(queryState.value.audit_date?.[0]) : '',
    audit_end_date: queryState.value.audit_date?.[1] ? formatDate(queryState.value.audit_date?.[1]) : '',
    order_start_date: queryState.value.order_date?.[0] ? formatDate(queryState.value.order_date?.[0]) : '',
    order_end_date: queryState.value.order_date?.[1] ? formatDate(queryState.value.order_date?.[1]) : '',
    page: page.value,
    size: size.value,
  })
  delete query.create_date
  delete query.audit_date
  delete query.order_date
  await getCheckOrderItemReport(query)
  tableData.value = processDataOut(checkOrderItemReportList.value.list)
}

// onMounted(async () => {
//   getData()
// })

watchEffect(() => {
  getData()
})

const tableConfig = ref({
  fieldApiKey: 'checkOrderItemReportList',
  loading,
  showPagition: true,
  showSlotNums: true,
  scrollY: { enabled: true, gt: 100 },
  showCheckBox: false,
  showOperate: false,
  operateWidth: '100',
  showSort: false,
  showSpanHeader: false,
  page,
  size,
  total,
  height: '100%',
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
})
const columns = [
  { field: 'account_num', title: '款号', minWidth: 100, sortable: true },
  { field: 'act_roll', title: '实盘条数', minWidth: 100, sortType: 'number', sortable: true },
  { field: 'act_weight', title: '实盘数量', minWidth: 100, sortType: 'number', sortable: true },
  { field: 'arrange_order_no', title: '配布单号', minWidth: 100, sortable: true },
  { field: 'audit_time', title: '审核时间', minWidth: 100, isDate: true, sortable: true },
  { field: 'auditor_name', title: '审核人名称', minWidth: 100, sortable: true },
  { field: 'biz_unit_name', title: '往来单位', minWidth: 100, sortable: true },
  { field: 'check_status_name', title: '盈亏状态', minWidth: 100, sortable: true },
  { field: 'color_code', title: '色号', minWidth: 100, sortable: true },
  { field: 'color_name', title: '颜色', minWidth: 100, sortable: true },
  { field: 'create_order_time', title: '制单时间', minWidth: 100, isDate: true, sortable: true },
  { field: 'create_order_user_name', title: '制单人', minWidth: 100, sortable: true },
  { field: 'customer_name', title: '所属客户', minWidth: 100, sortable: true },
  { field: 'customer_po_num', title: '客户PO号', minWidth: 100, sortable: true },
  { field: 'dyelot_number', title: '缸号', minWidth: 100, sortable: true },
  // { field: 'finish_product_gram_weight', title: '成品克重', minWidth: 100 },
  { field: 'finish_product_gram_weight_and_unit_name', title: '成品克重', minWidth: 100, sortType: 'number', sortable: true },
  // { field: 'finish_product_gram_weight_unit_id', title: '成品克重单位id(字典)', minWidth: 100 },
  // { field: 'finish_product_gram_weight_unit_name', title: '成品克重单位名称', minWidth: 100 },
  // { field: 'finish_product_width', title: '成品幅宽(2)', minWidth: 100 },
  { field: 'finish_product_width_and_unit_name', title: '成品幅宽', minWidth: 100, sortType: 'number', sortable: true },
  // { field: 'finish_product_width_unit_id', title: '成品幅宽单位id(字典)', minWidth: 100 },
  // { field: 'finish_product_width_unit_name', title: '成品幅宽单位名称', minWidth: 100 },
  // { field: 'id', title: 'ID', minWidth: 100 },
  { field: 'manual_number', title: '手工单号', minWidth: 100, sortable: true },
  { field: 'needle_size', title: '针寸数', minWidth: 100, sortable: true },
  { field: 'order_date', title: '单据日期', minWidth: 100, isDate: true, sortable: true },
  { field: 'order_no', title: '单据编号', minWidth: '8%', sortable: true },
  { field: 'order_number', title: '订单号', minWidth: 100, sortable: true },
  { field: 'order_type_name', title: '单据类型名', minWidth: 100, sortable: true },
  { field: 'pre_roll', title: '盘前条数', sortType: 'number', minWidth: 100, sortable: true },
  { field: 'pre_weight', title: '盘前数量', sortType: 'number', minWidth: 100, sortable: true },
  { field: 'product_code', title: '成品编号', minWidth: 100, sortable: true },
  // { field: 'product_gram_weight', title: '成品克重', minWidth: 100 },
  // { field: 'product_id', title: '成品id', minWidth: 100 },
  { field: 'product_level_name', title: '成品等级', minWidth: 100, sortable: true },
  { field: 'product_name', title: '成品名称', minWidth: 100, sortable: true },
  { field: 'product_remark', title: '成品备注', minWidth: 100, sortable: true },
  // { field: 'product_width', title: '成品幅宽(1)', minWidth: 100 },
  { field: 'production_order_no', title: '排产单号', minWidth: 100, sortable: true },
  { field: 'remark', title: '备注(单据)', minWidth: 100, sortable: true },
  { field: 'roll', title: '条数', sortType: 'number', minWidth: 100, sortable: true },
  { field: 'src_warehouse_name', title: '来源仓库名', minWidth: 100, sortable: true },
  { field: 'store_keeper_name', title: '仓管员', minWidth: 100, sortable: true },
  { field: 'store_type_name', title: '进出仓类型', minWidth: 100, sortable: true },
  { field: 'total_needle_size', title: '总针数', minWidth: 100, sortable: true },
  { field: 'total_courses', title: '总路数', minWidth: 100, sortable: true },
  { field: 'total_spacing', title: '总间距', minWidth: 100, sortable: true },
  { field: 'voucher_number', title: '凭证号', minWidth: 100, sortable: true },
  { field: 'warehouse_bin_name', title: '仓位', minWidth: 100, sortable: true },
  { field: 'warehouse_name', title: '仓库名称', minWidth: 100, sortable: true },
  { field: 'weaving_unit_name', title: '织厂名称', minWidth: 100, sortable: true },
  { field: 'weaving_lethod', title: '织造方式', minWidth: 100, sortable: true },
  { field: 'weight', title: '数量', sortType: 'number', minWidth: 100, sortable: true },
  { field: 'yarn_length', title: '纱长', minWidth: 100, sortable: true },
]
</script>

<template>
  <section class="flex flex-col h-full overflow-hidden">
    <FildCard title="" :tool-bar="false">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据状态">
          <template #content>
            <SelectComponents v-model="queryState.audit_status" api="GetAuditStatusEnum" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称:">
          <template #content>
            <SelectMergeComponent
              v-model="queryState.product_id"
              :custom-label="(row:any) => `${formatHashTag(row.finish_product_code, row.finish_product_name)}`"
              :multiple="false"
              api-name="GetFinishProductDropdownList"
              remote
              remote-key="finish_product_code_or_name"
              remote-show-suffix
              placeholder="成品编号、成品名称"
              value-field="id"
              @change="queryState.color_id = ''"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="色号颜色:">
          <template #content>
            <SelectMergeComponent
              v-model="queryState.color_id"
              :custom-label="(row:any) => `${formatHashTag(row.product_color_code, row.product_color_name)}`"
              :multiple="false"
              :disabled="!queryState.product_id"
              :query="{
                finish_product_id: queryState.product_id,
              }"
              api-name="GetFinishProductColorDropdownList"
              remote
              remote-key="product_color_code_or_name"
              remote-show-suffix
              placeholder="成品颜色、色号"
              value-field="id"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:">
          <template #content>
            <SelectCustomerDialog v-model="queryState.customer_id" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="缸号:">
          <template #content>
            <vxe-input v-model="queryState.dyelot_number" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单号:">
          <template #content>
            <vxe-input v-model="queryState.order_no" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓库名称:">
          <template #content>
            <SelectComponents v-model="queryState.warehouse_id" api="GetPhysicalWarehouseDropdownList" label-field="name" value-field="id" clearable @change-value="() => queryState.warehouse_bin_id = ''" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓位:">
          <template #content>
            <SelectComponents
              v-model="queryState.warehouse_bin_id"
              :disabled="!queryState.warehouse_id"
              :query="{ physical_warehouse_id: queryState.warehouse_id }"
              api="GetPhysicalWarehouseBinListEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="创建时间:">
          <template #content>
            <SelectDate v-model="queryState.create_date" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="审核时间:">
          <template #content>
            <SelectDate v-model="queryState.audit_date" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据时间:">
          <template #content>
            <SelectDate v-model="queryState.order_date" />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>

    <FildCard class="mt-[5px] flex flex-col h-full overflow-hidden">
      <Table :config="tableConfig" :table-list="tableData" :column-list="columns" />
    </FildCard>
  </section>
</template>
