<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import {
  GetSalePriceAdjustOrder,
  UpdateSalePriceAdjustOrderAuditStatusCancel,
  UpdateSalePriceAdjustOrderAuditStatusPass,
  UpdateSalePriceAdjustOrderAuditStatusReject,
  UpdateSalePriceAdjustOrderAuditStatusWait,
} from '@/api/salesPriceAdjustmentOrder'
import { formatTime, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'
import { orderStatusConfirmBox } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'

const router = useRoute()

interface Params {
  weight_error: number // 空差减重
  id: number
  sale_level_id: number // 销售等级id
  source_bulk_sale_price: number // 原售价
  level_price_adjust: number // 调整
  bulk_sale_price: number // 新售价
  sale_level_name: string // 等级名称
}

onMounted(() => {
  getList()
})

// 获取列表
const { fetchData: fetchDataList, data: listData } = GetSalePriceAdjustOrder()
async function getList() {
  await fetchDataList({ id: router.params.id })
  formatData()
}

const formData = ref<any>({
  effective_time: '',
  deadline_time: '',
  remark: '',
  item_data: [],
})

const state = reactive<any>({
  tableData: [],
})

// 整理数据
const levelPriceColumn = ref<Params[]>([])
function formatData() {
  state.tableData = listData.value.item?.map((item: any, index: number) => {
    if (index === 0)
      levelPriceColumn.value = item?.level_item

    return {
      ...item,
      target_length_cut_sale_price: formatUnitPriceDiv(item.target_length_cut_sale_price || 0),
      target_weight_cut_sale_price: formatUnitPriceDiv(item.target_weight_cut_sale_price || 0),
      weight_error: formatWeightDiv(item.weight_error || 0),
      source_bulk_sale_price: formatUnitPriceDiv(item.source_bulk_sale_price || 0),
      source_bulk_sale_limit_price: formatUnitPriceDiv(item.source_bulk_sale_limit_price || 0),
      adjust_bulk_sale_price: formatUnitPriceDiv(item.adjust_bulk_sale_price || 0),
      adjust_bulk_sale_limit_price: formatUnitPriceDiv(item.adjust_bulk_sale_limit_price || 0),
      target_bulk_sale_price: formatUnitPriceDiv(item.target_bulk_sale_price || 0),
      target_bulk_sale_limit_price: formatUnitPriceDiv(item.target_bulk_sale_limit_price || 0),
      product_paper_tube_weight: formatWeightDiv(item.product_paper_tube_weight || 0),
      product_weight_error: formatWeightDiv(item.product_weight_error || 0),
      level_item: item?.level_item?.map((level: any) => {
        return {
          ...level,
          weight_error: formatWeightDiv(level.weight_error || 0),
          source_bulk_sale_price: formatUnitPriceDiv(level.source_bulk_sale_price || 0),
          target_bulk_sale_price: formatUnitPriceDiv(level.target_bulk_sale_price || 0),
          level_price_adjust: formatWeightDiv(level.level_price_adjust || 0),
        }
      }),
    }
  })
  formData.value = {
    ...listData.value,
    effective_time: formatTime(listData?.value.effective_time),
    deadline_time: formatTime(listData?.value.deadline_time),
    remark: listData?.value.remark,
    item_data: state.tableData,
  }
}

async function updateStatus(audit_status: number) {
  const id = Number.parseInt(router.params.id as string)
  if (audit_status === 4)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' }, api: UpdateSalePriceAdjustOrderAuditStatusCancel })

  if (audit_status === 3)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' }, api: UpdateSalePriceAdjustOrderAuditStatusReject })

  if (audit_status === 2)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' }, api: UpdateSalePriceAdjustOrderAuditStatusPass })

  if (audit_status === 1)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' }, api: UpdateSalePriceAdjustOrderAuditStatusWait })

  getList()
}

function formatColorName(list: any) {
  return list
    ?.map((item: any) => {
      return item.product_color_code + item.product_color_name
    })
    .join(';')
}
</script>

<template>
  <StatusColumn
    :order_no="formData.order_no"
    :order_id="formData.id"
    :status="formData.audit_status"
    :status_name="formData.audit_status_name"
    permission_wait_key="SalesPriceAdjustmentOrder_Wait"
    permission_reject_key="SalesPriceAdjustmentOrder_Reject"
    permission_pass_key="SalesPriceAdjustmentOrder_Pass"
    permission_cancel_key="SalesPriceAdjustmentOrder_Cancel"
    permission_edit_key="SalesPriceAdjustmentOrderEdit"
    edit_router_name="SalesPriceAdjustmentOrderEdit"
    edit_router_type="params"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  />
  <FildCard :tool-bar="false" title="基础信息">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="生效时间:">
        <template #content>
          {{ formData.effective_time }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="截止时间:">
        <template #content>
          {{ formData.deadline_time }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="备注:" copies="2">
        <template #content>
          {{ formData.remark }}
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard title="" :tool-bar="false" class="mt-[10px]">
    <vxe-table :column-config="{ resizable: true }" border height="500" :data="state.tableData" size="mini">
      <vxe-colgroup title="成品颜色类别">
        <vxe-column field="product_kind_name" title="布种类别" width="100px" fixed="left" />
        <vxe-column field="product_code" title="成品编号" width="100px" fixed="left" />
        <vxe-column field="product_name" title="成品名称" width="100px" fixed="left" />
        <vxe-column field="product_paper_tube_weight" title="成品纸筒重量（公斤）" width="150px" fixed="left">
          <template #default="{ row }">
            {{ row.product_paper_tube_weight }}
          </template>
        </vxe-column>
        <vxe-column field="product_weight_error" title="成品空差数量（公斤）" width="150px" fixed="left">
          <template #default="{ row }">
            {{ row.product_weight_error }}
          </template>
        </vxe-column>
        <vxe-column field="product_color_kind_name" title="颜色类别" width="100px" fixed="left" />
        <vxe-column field="product_color_list" title="色号颜色" width="200px" fixed="left">
          <template #default="{ row }">
            {{ formatColorName(row.product_color_list) }}
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="标准报价">
        <vxe-column field="target_length_cut_sale_price" title="剪版售价（元/米）" width="143px">
          <template #default="{ row }">
            {{ row.target_length_cut_sale_price }}
          </template>
        </vxe-column>
        <vxe-column field="target_weight_cut_sale_price" title="剪版售价（元/公斤）" width="143px">
          <template #default="{ row }">
            {{ row.target_weight_cut_sale_price }}
          </template>
        </vxe-column>
        <vxe-column field="weight_error" title="空差减重（公斤）" width="143px">
          <template #default="{ row }">
            {{ row.weight_error }}
          </template>
        </vxe-column>
        <vxe-column field="source_bulk_sale_price" title="原标准售价（元/公斤）" width="150px" />
        <vxe-column field="adjust_bulk_sale_price" title="调整（元/公斤）" width="143px">
          <template #default="{ row }">
            {{ row.adjust_bulk_sale_price }}
          </template>
        </vxe-column>
        <vxe-column field="target_bulk_sale_price" title="新标准售价（元/公斤）" width="160px">
          <template #default="{ row }">
            {{ row.target_bulk_sale_price }}
          </template>
        </vxe-column>
        <vxe-column field="source_bulk_sale_limit_price" title="原最低售价（元/公斤）" width="150px">
          <template #default="{ row }">
            {{ row.source_bulk_sale_limit_price }}
          </template>
        </vxe-column>
        <vxe-column field="adjust_bulk_sale_limit_price" title="调整（元/公斤）" width="143px">
          <template #default="{ row }">
            {{ row.adjust_bulk_sale_limit_price }}
          </template>
        </vxe-column>
        <vxe-column field="target_bulk_sale_limit_price" title="新最低售价(元/公斤）" width="150px">
          <template #default="{ row }">
            {{ row.target_bulk_sale_limit_price }}
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup v-for="(item, index) in levelPriceColumn" :key="index" :title="item.sale_level_name">
        <vxe-column field="weight_error" title="空差减重（公斤）" width="137px">
          <template #default="{ row }">
            {{ row.level_item[index].weight_error }}
          </template>
        </vxe-column>
        <vxe-column field="source_bulk_sale_price" title="原售价（元/公斤）" width="137px" />
        <vxe-column field="level_price_adjust" title="调整（元/公斤）" width="137px">
          <template #default="{ row }">
            {{ row.level_item[index].level_price_adjust }}
          </template>
        </vxe-column>
        <vxe-column field="target_bulk_sale_price" title="新售价（元/公斤）" width="137px">
          <template #default="{ row }">
            {{ row.level_item[index].target_bulk_sale_price }}
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="其它信息">
        <vxe-column field="product_ingredient" title="成分" width="100" />
      </vxe-colgroup>
    </vxe-table>
  </FildCard>
</template>

<style lang="scss" scoped>
::v-deep(.el-form-item) {
  margin-bottom: 0;
}
::v-deep(.el-descriptions) {
  margin-bottom: 10px;
}
</style>
