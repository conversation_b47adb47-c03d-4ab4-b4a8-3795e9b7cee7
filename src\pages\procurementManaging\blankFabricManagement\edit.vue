<script lang="ts" setup name="BlanketManagementEdit">
import { Plus } from '@element-plus/icons-vue'
import Big from 'big.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useToggle } from '@vueuse/core'
import currency from 'currency.js'
import SelectProductionNotice from './components/SelectProductionNotice/index.vue'
import { isConfigured1 } from './utils'
import AddressConfig from './components/AddressConfig/index.vue'
import SelectRawMaterial from '@/components/SelectRawMaterial/index.vue'
import {
  GetPurchaseGreyFarbricById,
  UpdatePurchaseGreyFarbric,
} from '@/api/blanketManagement'
import { BusinessUnitIdEnum } from '@/common/enum'
import {
  sumNum,
} from '@/common/format'
import { getFilterData } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'
import { processDataIn, processDataOut } from '@/common/handBinary'
import SelectSettleTypeDialog from '@/components/SelectSettleTypeDialog/index.vue'

const routerList = useRouterList()
const router = useRoute()
const state = reactive({
  form: {
    sale_system_id: '',
    supplier_id: '',
    recipien_entity_id: '',
    purchase_time: '',
    receiving_time: '',
    invoice_header_id: '',
    remark: '',
    sale_system_name: '',
    item_params: [] as any[],
    place_order_type: '', // 下单方式
    settle_type_id: '', // 结算方式
    include_tax: '', // 是否含税
    tax_rate: '' as string | number, // 税率
  },
  tableData: [] as any[],
  fromRules: {
    sale_system_id: [
      { required: true, message: '请选择营销体系', trigger: 'change' },
    ],
    supplier_id: [
      { required: true, message: '请选择供应商', trigger: 'change' },
    ],
    recipien_entity_id: [
      { required: false, message: '请选择收货单位', trigger: 'change' },
    ],
    purchase_time: [
      { required: true, message: '请选择采购日期', trigger: 'change' },
    ],
  },
})

const showAdd = ref(false)
const showProduct = ref(false)

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  recipien_entity_name: '',
})
const selectRecipienDialogRef = ref()
const defaultAddress = ref<any>([])
const bulkSetting = ref<any>({
  address: {},
})
const { fetchData: detailFetch, data: detalData, success, msg }
  = GetPurchaseGreyFarbricById()
onMounted(() => {
  getData()
})
async function getData() {
  await detailFetch({
    id: router.params.id,
  })

  if (success.value) {
    detalData.value = processDataOut(detalData.value)

    // supplierIdRef.value.inputLabel = val.supplier_name
    state.form.sale_system_id = detalData.value.sale_system_id
    state.form.supplier_id = detalData.value.supplier_id
    state.form.recipien_entity_id = detalData.value.recipien_entity_id
    state.form.purchase_time = detalData.value.purchase_time
    state.form.receiving_time = detalData.value.receiving_time
    state.form.invoice_header_id = detalData.value.invoice_header_id
    state.form.remark = detalData.value.remark
    state.tableData = detalData.value.item_data
    state.form.place_order_type = detalData.value.place_order_type
    state.form.settle_type_id = detalData.value.settle_type_id
    state.form.include_tax = detalData.value.include_tax || ''
    state.form.tax_rate = detalData.value.tax_rate

    getSaleSystem({
      id: detalData.value.sale_system_id,
      name: detalData.value.sale_system_name,
    })

    detalData.value.item_data?.map((item: any) => {
      item.isOpened = false
      computedData(item)
    })

    // 读取当前获取的收货单位为默认地址
    nextTick(() => {
      const editDefaultAddress = selectRecipienDialogRef.value.item
      bulkSetting.value.address = {
        recipient_entity_name: editDefaultAddress?.name,
        recipient_entity_id: editDefaultAddress?.id,
        contacts: editDefaultAddress?.contact_name,
        contact_phone: editDefaultAddress?.phone,
        addr: editDefaultAddress?.address,
      }
      defaultAddress.value = [bulkSetting.value.address]
    })
  }
  else {
    return ElMessage.error(msg.value)
  }
}
const tablesRef = ref()

const saleSaleSystemInfo = ref()
function getSaleSystem(row: any) {
  state.form.sale_system_name = row?.name
  state.form.sale_system_id = row?.id
  saleSaleSystemInfo.value = row

  clearData(row)
}

function handDel(row: any, rowIndex: number) {
  ElMessageBox.confirm(`确认后商品将被删除`, '是否删除该商品', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
  })
    .then(async () => {
      state.tableData.splice(rowIndex, 1)
    })
    .catch(() => {})
}
function handAdd() {
  showAdd.value = true
}

const multipleSelection = ref<any[]>([])
const bulkFormRef = ref()
const bulkShow = ref(false)

function handEdit() {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请选择批量修改的数据')
  bulkShow.value = true
}

const columnList = ref([
  {
    field: 'sale_plan_order_item_no',
    title: '销售计划详情单号',
    fixed: 'left',
    width: 150,
  },
  {
    field: 'code',
    title: '坯布编号',
    fixed: 'left',
    width: 150,
  },
  {
    field: 'name',
    title: '坯布名称',
    fixed: 'left',
    width: 100,
  },
  {
    field: 'customer_id',
    title: '所属客户',
    width: 130,
    soltName: 'customer_id',
    required: true,
  },
  {
    field: 'grey_fabric_width',
    soltName: 'grey_fabric_width',
    title: '幅宽',
    width: 100,
  },
  {
    field: 'grey_fabric_gram_weight',
    soltName: 'grey_fabric_gram_weight',
    title: '克重',
    width: 100,
  },
  {
    field: 'needle_size',
    title: '针寸数',
    width: 100,
  },
  {
    field: 'total_needle_size',
    title: '总针数',
    width: 100,
  },
  {
    field: 'gray_fabric_color_name',
    title: '织坯颜色',
    width: 100,
    // soltName: 'color',
  },
  {
    field: 'yarn_batch',
    title: '纱批',
    width: 100,
    soltName: 'yarn_batch',
  },
  {
    field: 'machine_combination_number',
    title: '机台号',
    width: 100,
    soltName: 'machine_combination_number',
  },
  {
    field: 'grey_fabric_level_id',
    title: '坯布等级',
    width: 100,
    soltName: 'grey_fabric_level_id',
  },
  {
    field: 'weave_factory_name_id',
    title: '织厂名称',
    width: 130,
    soltName: 'weave_factory_name_id',
    query: { unit_type_id: BusinessUnitIdEnum.knittingFactory },
  },
  {
    field: 'remark',
    title: '备注',
    width: 100,
    soltName: 'remark',
  },
  {
    field: 'address',
    title: '收货地址',
    fixed: 'right',
    width: 130,
    soltName: 'address',
  },
  {
    field: 'number',
    title: '匹数',
    fixed: 'right',
    width: 100,
    soltName: 'number',
    required: true,
  },
  {
    field: 'average_weight',
    title: '均重',
    fixed: 'right',
    width: 100,
    soltName: 'average_weight',
  },
  {
    field: 'total_weight',
    title: '数量总计',
    fixed: 'right',
    width: 100,
    soltName: 'total_weight',
    required: true,
  },
  {
    field: 'unit_name',
    title: '单位',
    fixed: 'right',
    width: 100,
  },
  {
    field: 'single_price',
    title: '单价',
    fixed: 'right',
    width: 100,
    soltName: 'single_price',
    required: true,
  },
  {
    field: 'count_price',
    title: '金额',
    fixed: 'right',
    width: 100,
    soltName: 'count_price',
  },
])

const changeRow = ref<any>()
const changeField = ref<string>()
function changeData(row: any, field = '') {
  changeRow.value = row
  changeField.value = field

  // 修改匹数/数量总计，若收货地址有且仅有一条的数据，则赋值于收货地址数据
  const editField = ['number', 'total_weight']
  if (row?.item_addr_data?.length === 1 && editField.includes(field))
    row.item_addr_data[0][field] = row[field]
}

watch(
  () => [
    changeRow.value?.number,
    changeRow.value?.average_weight,
    changeRow.value?.single_price,
    changeRow.value?.total_weight,
  ],
  async () => {
    computedData(changeRow.value)
  },
  {
    deep: true,
  },
)

function computedData(row: any) {
  if (
    row?.average_weight !== ''
    && (changeField.value === 'average_weight' || changeField.value === 'number')
  ) {
    row.total_weight = Number.parseFloat(
      Big(row.number || 0)
        .times(row?.average_weight || 0)
        .toFixed(2),
    )
  }

  // 计算金额
  row.count_price = Number.parseFloat(
    Big(row.single_price || 0)
      .times(row?.total_weight || 0)
      .toFixed(2),
  )
  tablesRef.value.tableRef.updateFooter()
}

async function bulkSubmit({ row, value }: any) {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  let tf = false
  if (row.field !== 'address') {
    if (!value[row.field]) {
      ElMessage.error('请输入参数')
      tf = true
    }
  }
  else {
    await bulkFormRef.value.validate((valid: any) => {
      if (!valid)
        tf = true
    })
  }
  if (tf)
    return
  multipleSelection.value?.map((item: any) => {
    if (row.field === 'address') {
      if (item.number && item.average_weight) {
        item.item_addr_data = [
          {
            ...value[row.field],
            number: item.number,
            total_weight: Number.parseFloat(item.total_weight || 0),
          },
        ]
        ElMessage.success('设置成功')
      }
      else {
        ElMessage.warning('填写匹数和均重')
      }
    }
    else {
      item[row.field] = value[row.field]
      ElMessage.success('设置成功')
    }
    computedData(item)
  })
}

function handAllSelect({ records }: any) {
  multipleSelection.value = records
}

function handleSelectionChange({ records }: any) {
  multipleSelection.value = records
}

function onSubmit(row: any) {
  showAdd.value = false
  const data = row?.map((item: any) => {
    item = {
      ...item,
      id: 0,
      grey_fabric_id: item.id,
      customer_id: saleSaleSystemInfo.value?.default_customer_id || '',
      customer_name: saleSaleSystemInfo.value?.default_customer_name || '',
      selected: false,
      item_addr_data: JSON.parse(JSON.stringify(defaultAddress.value)),
      number: item.number || 0,
      isOpened: false,
    }
    return item
  })

  state.tableData = [...state.tableData, ...data]
}

const [showAddress, toggleAddressShow] = useToggle(false)
const [canSubmit, toggleSubmit] = useToggle(false)

function formatDate(dateString: string): string {
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = (`0${date.getMonth() + 1}`).slice(-2) // Months are zero indexed, so we add one
  const day = (`0${date.getDate()}`).slice(-2)
  return `${year}-${month}-${day}`
}

const {
  fetchData: fetchDataAdd,
  data: addData,
  success: successAdd,
  msg: msgAdd,
} = UpdatePurchaseGreyFarbric()
const ruleFormRef = ref()
async function handSubmit() {
  ruleFormRef.value.validate(async (valid: any) => {
    if (!valid)
      return

    validateList()
    if (!canSubmit.value)
      return false

    const res = processDataIn(state.tableData)

    const purchase_time = formatDate(state.form.purchase_time)
    const receiving_time = formatDate(state.form.receiving_time)

    await fetchDataAdd(
      getFilterData({
        ...processDataIn(state.form),
        purchase_time,
        receiving_time,
        id: Number.parseInt(router.params.id as string),
        item_params: res,
      }),
    )
    if (successAdd.value) {
      ElMessage.success('编辑成功')
      routerList.push({
        name: 'BlanketManagementDetail',
        params: { id: addData.value.id },
      })
    }
    else {
      ElMessage.error(msgAdd.value)
    }
  })
}

// 验证坯布信息字段
function validateList() {
  toggleSubmit(true)

  if (showAddress.value) {
    ElMessage.error('当前有正在编辑的收货地址，请编辑完后再提交')
    toggleSubmit(false)
    return
  }

  if (!state.tableData || state.tableData.length === 0) {
    ElMessage.error('坯布信息不能为空')
    toggleSubmit(false)
    return
  }
  state.tableData?.some((item: any) => {
    if (
      item.number === ''
      || item.total_weight === ''
      || Number(item.total_weight) === 0
      || item.single_price === ''
      || !item.customer_id
    ) {
      ElMessage.error(`编号为${item.code}的数据所属客户、匹数、数量总计、单价不能为空`)
      toggleSubmit(false)
      return
    }

    // 开始校验收货地址
    if (item.item_addr_data?.length === 0) {
      ElMessage.error(`编号为${item.code}的收货地址为空`)
      toggleSubmit(false)
      return
    }

    const data = item.item_addr_data
    // 校验数量总计总和与外面的数量总计是否一致
    const allNumber = sumNum(data, 'number')
    if (currency(allNumber).value !== currency(item.number).value) {
      ElMessage.error(`编号为${item.code}的收货地址输入框内总匹数与分录行的总匹数不一致`)
      toggleSubmit(false)
      return
    }

    const allTotalWeight = sumNum(data, 'total_weight')
    if (currency(allTotalWeight).value !== currency(item.total_weight).value) {
      ElMessage.error(`编号为${item.code}的收货地址输入框内数量总计与分录行的数量总计不一致`)
      toggleSubmit(false)
    }
  })
}

const bulkList = reactive<any>([
  {
    field: 'customer_id',
    field_name: 'customer_name',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
    query: { sale_system_id: state.form.sale_system_id },
  },
  {
    field: 'grey_fabric_level_id',
    title: '坯布等级',
    component: 'select',
    api: 'GetInfoBaseGreyFabricLevelListUseByOther',
  },
  {
    field: 'yarn_batch',
    title: '纱批',
    component: 'input',
    type: 'text',
  },
  {
    field: 'machine_combination_number',
    title: '机台号',
    component: 'input',
    type: 'text',
  },

  {
    field: 'weave_factory_name_id',
    title: '织厂名称',
    component: 'select',
    api: 'GetBusinessUnitListApi',
    query: { unit_type_id: BusinessUnitIdEnum.knittingFactory },
  },
  {
    field: 'number',
    title: '匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'average_weight',
    title: '均重',
    component: 'input',
    type: 'float',
  },
  {
    field: 'single_price',
    title: '单价',
    component: 'input',
    type: 'float',
  },
  {
    field: 'address',
    title: '收货地址',
    hideTitle: true,
    rule: {
      recipient_entity_id: [
        { required: true, message: '请选择收货单位', trigger: 'change' },
      ],
      contacts: [
        { required: true, message: '请填写收货联系人', trigger: 'change' },
      ],
      contact_phone: [
        { required: true, message: '请填写收货电话', trigger: 'change' },
      ],
      addr: [{ required: true, message: '请填写收货地址', trigger: 'change' }],
    },
  },
])

function footerMethod({ columns, data }: { columns: any, data: any }) {
  const footerData = [
    columns.map((column: any, _columnIndex: number) => {
      if (['code'].includes(column.field))
        return '合计'

      if (
        ['number', 'average_weight', 'total_weight', 'count_price'].includes(
          column.field,
        )
      )
        return sumNum(data, column.field)
    }),
  ]
  return footerData
}

function handBulkClose() {
  bulkShow.value = false
}

let oneStatus = true
function clearData(row: any) {
  if (!oneStatus) {
    state.tableData?.map((item) => {
      item.customer_id = saleSaleSystemInfo.value?.default_customer_id || ''
      item.customer_name
        = saleSaleSystemInfo.value?.default_customer_name || ''
    })
  }
  oneStatus = false
  bulkList[0].query = { sale_system_id: row.id }
  bulkSetting.value.customer_id = ''
}

const isChangeRecipienEntity = ref(false) // 是否重新选择过收货单位
// 切换收货单位
function getBusinessUnitListInfo(val: any) {
  if (val) {
    isChangeRecipienEntity.value = true
    bulkSetting.value.address = {
      recipient_entity_name: val?.name,
      recipient_entity_id: val?.id,
      contacts: val?.contact_name,
      contact_phone: val?.phone,
      addr: val?.address,
    }

    defaultAddress.value = [bulkSetting.value.address]

    state.tableData = state.tableData?.map((item: any) => {
      item.item_addr_data = defaultAddress.value.map((it: any) => {
        it.number = item.number
        it.total_weight = item.total_weight
        return it
      })

      setAddrData(item)
      return item
    })
  }
  else {
    defaultAddress.value = null
  }
}

// 配置收货地址,整件件数和整件件重
function setAddrData(row: any) {
  if (row?.item_addr_data?.length <= 1 && !isChangeRecipienEntity.value) {
    // 如果没有重新选择收货单位-则使用原来的
    row.item_addr_data = [{
      ...row.item_addr_data?.[0],
      number: row.number,
      total_weight: row.total_weight,
    }]
  }
  else if (row?.item_addr_data?.length <= 1) {
    // 如果重新选择收货单位-则使用新的
    row.item_addr_data = [{
      ...(defaultAddress.value?.[0] || {}),
      number: row.number,
      total_weight: row.total_weight,
    }]
  }
}

watch(
  () => [
    changeRow.value?.number,
    changeRow.value?.average_weight,
    changeRow.value?.total_weight,
  ],
  () => {
    setAddrData(changeRow.value)
  },
)

const tableConfig = ref({
  showOperate: true,
  operateWidth: 100,
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  footerMethod,
  fieldApiKey: 'BlanketManagementEdit',
})

const selectRow = ref()
const selectIndex = ref<number>(-1)

function openAddress(row: any, rowIndex: number) {
  if (showAddress.value)
    return ElMessage.error('当前已有正在编辑的地址')
  selectRow.value = row
  selectIndex.value = rowIndex
  toggleAddressShow(true)
  row.isOpened = true
}

function onAddressSubmit(row: any) {
  state.tableData[selectIndex.value].item_addr_data = row

  toggleAddressShow(false)
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <div class="line" />
    <template #right-top>
      <el-button v-btnAntiShake="handSubmit" type="primary">
        提交
      </el-button>
    </template>
    <slot>
      <el-form
        ref="ruleFormRef"
        :model="state.form"
        label-width="60px"
        label-position="top"
        :rules="state.fromRules"
      >
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem required label="营销体系名称:">
            <template #content>
              <el-form-item prop="sale_system_id">
                <SelectComponents
                  v-model="state.form.sale_system_id"
                  api="GetSaleSystemDropdownListApi"
                  label-field="name"
                  value-field="id"
                  @select="getSaleSystem"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="供应商名称:">
            <template #content>
              <el-form-item prop="supplier_id">
                <!-- <SelectComponents
                  v-model="state.form.supplier_id"
                  :query="{ unit_type_id: BusinessUnitIdEnum.blankFabric }"
                  api="BusinessUnitSupplierEnumlist"
                  label-field="name"
                  value-field="id"
                ></SelectComponents> -->
                <SelectDialog
                  v-model="state.form.supplier_id"
                  :query="{
                    unit_type_id: BusinessUnitIdEnum.blankFabric,
                    name: componentRemoteSearch.name,
                  }"
                  api="BusinessUnitSupplierEnumlist"
                  :column-list="[
                    {
                      field: 'name',
                      title: '名称',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'name',
                          isEdit: true,
                          title: '名称',
                          minWidth: 100,
                        },
                      ],
                    },
                    {
                      field: 'code',
                      title: '供应商编号',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'code',
                          isEdit: true,
                          title: '供应商编号',
                          minWidth: 100,
                        },
                      ],
                    },
                  ]"
                  @change-input="(val) => (componentRemoteSearch.name = val)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="收货单位名称:">
            <template #content>
              <el-form-item prop="recipien_entity_id">
                <SelectDialog
                  ref="selectRecipienDialogRef"
                  v-model="state.form.recipien_entity_id"
                  :query="{
                    unit_type_id: BusinessUnitIdEnum.dyeFactory,
                    name: componentRemoteSearch.recipien_entity_name,
                  }"
                  api="BusinessUnitSupplierEnumlist"
                  :column-list="[
                    {
                      field: 'name',
                      title: '名称',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'name',
                          isEdit: true,
                          title: '名称',
                          minWidth: 100,
                        },
                      ],
                    },
                    {
                      field: 'code',
                      title: '编号',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'code',
                          isEdit: true,
                          title: '编号',
                          minWidth: 100,
                        },
                      ],
                    },
                  ]"
                  @change-value="getBusinessUnitListInfo"
                  @change-input="
                    (val) => (componentRemoteSearch.recipien_entity_name = val)
                  "
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="采购日期:">
            <template #content>
              <el-form-item prop="purchase_time">
                <SelectDate v-model="state.form.purchase_time" type="date" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="收货日期:">
            <template #content>
              <el-form-item prop="receiving_time">
                <SelectDate v-model="state.form.receiving_time" type="date" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="下单方式:">
            <template #content>
              <el-input v-model="state.form.place_order_type" maxlength="255" />
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="结算方式:">
            <template #content>
              <el-form-item prop="settle_type_id">
                <SelectSettleTypeDialog
                  v-model="state.form.settle_type_id"
                  field="name"
                />
                <!--                <SelectComponents -->
                <!--                  v-model="state.form.settle_type_id" -->
                <!--                  api="GetInfoSaleSettlementMethodEnumList" -->
                <!--                  label-field="name" -->
                <!--                  value-field="id" -->
                <!--                  clearable -->
                <!--                /> -->
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="发票抬头:">
            <template #content>
              <el-form-item prop="invoice_header_id">
                <SelectComponents
                  v-model="state.form.invoice_header_id"
                  api="GetInfoPurchaseInvoiceHeaderListUseByOther"
                  label-field="name"
                  value-field="id"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="税率:">
            <template #content>
              <vxe-input v-model="state.form.tax_rate" type="float" clearable placeholder="税率" :disabled="!state.form.include_tax" :min="0">
                <template #suffix>
                  %
                </template>
              </vxe-input>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem>
            <template #content>
              <el-checkbox
                v-model="state.form.include_tax"
                label="是否含税"
                size="large"
              />
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="备注:" copies="2">
            <template #content>
              <el-form-item prop="userName">
                <vxe-textarea
                  v-model="state.form.remark"
                  maxlength="500"
                  show-word-count
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
        </div>
      </el-form>
    </slot>
  </FildCard>
  <FildCard title="坯布信息" class="mt-[5px]">
    <template #right-top>
      <el-button style="margin-left: 10px" type="primary" :disabled="multipleSelection.length <= 0" @click="handEdit">
        批量操作
      </el-button>
      <el-button
        style="margin-left: 10px"
        type="primary"
        :icon="Plus"
        @click="handAdd"
      >
        根据资料添加
      </el-button>
    </template>
    <Table
      ref="tablesRef"
      :config="tableConfig"
      :table-list="state.tableData"
      :column-list="columnList"
    >
      <template #customer_id="{ row }">
        <SelectDialog
          v-model="row.customer_id"
          :label-name="row.customer_name"
          :query="{
            sale_system_id: state.form.sale_system_id,
            name: componentRemoteSearch.customer_name,
          }"
          api="GetCustomerEnumList"
          :column-list="[
            {
              title: '客户编号',
              minWidth: 100,
              required: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'code',
                  isEdit: true,
                  title: '客户编号',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '客户名称',
              minWidth: 100,
              colGroupHeader: true,
              required: true,
              childrenList: [
                {
                  isEdit: true,
                  field: 'name',
                  title: '客户名称',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '电话',
              colGroupHeader: true,
              minWidth: 100,
              childrenList: [
                {
                  field: 'phone',
                  isEdit: true,
                  title: '电话',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '销售员',
              minWidth: 100,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'seller_name',
                  title: '销售员',
                  soltName: 'seller_name',
                  isEdit: true,
                  minWidth: 100,
                },
              ],
            },
          ]"
          @change-input="(val) => (componentRemoteSearch.customer_name = val)"
        />
      </template>
      <template #color="{ row }">
        {{ row?.color }}
      </template>
      <template #yarn_batch="{ row }">
        <vxe-input
          v-model="row.yarn_batch"
          size="mini"
          maxlength="200"
        />
      </template>
      <template #machine_combination_number="{ row }">
        <vxe-input
          v-model="row.machine_combination_number"
          size="mini"
        />
      </template>
      <template #grey_fabric_level_id="{ row }">
        <SelectComponents
          v-model="row.grey_fabric_level_id"
          size="small"
          api="GetInfoBaseGreyFabricLevelListUseByOther"
          label-field="name"
          value-field="id"
        />
      </template>
      <template #weave_factory_name_id="{ row }">
        <SelectComponents
          v-model="row.weave_factory_name_id"
          size="small"
          :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory }"
          api="GetBusinessUnitListApi"
          label-field="name"
          value-field="id"
        />
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" size="mini" maxlength="200" />
      </template>
      <template #number="{ row }">
        <vxe-input
          v-model="row.number"
          size="mini"
          type="float"
          :min="0"
          @change="changeData(row, 'number')"
        />
      </template>
      <template #average_weight="{ row }">
        <vxe-input
          v-model="row.average_weight"
          size="mini"
          type="float"
          :min="0"
          @change="changeData(row, 'average_weight')"
        />
      </template>
      <template #total_weight="{ row }">
        <vxe-input
          v-model="row.total_weight"
          size="mini"
          type="float"
          :min="0"
          @change="changeData(row, 'total_weight')"
        />
      </template>
      <template #single_price="{ row }">
        <vxe-input
          v-model="row.single_price"
          size="mini"
          type="float"
          :min="0"
          @change="changeData(row)"
        />
      </template>
      <template #count_price="{ row }">
        {{ row?.count_price }}
      </template>
      <template #address="{ row, rowIndex }">
        <el-link type="primary" @click="openAddress(row, rowIndex)">
          编辑
        </el-link>
        <span
          class="text-black-40"
          :style="`color:${!isConfigured1(row) && 'red'}`"
        >
          {{ isConfigured1(row) ? "（已配置）" : "（未配置）" }}
        </span>
      </template>
      <template #operate="{ row, rowIndex }">
        <el-button type="primary" text link @click="handDel(row, rowIndex)">
          删除
        </el-button>
      </template>
      <template #grey_fabric_width="{ row }">
        {{ row?.grey_fabric_width }}
        {{ row?.grey_fabric_width_unit_name }}
      </template>
      <template #grey_fabric_gram_weight="{ row }">
        {{ row?.grey_fabric_gram_weight }}
        {{ row?.grey_fabric_gram_weight_unit_name }}
      </template>
    </Table>
  </FildCard>
  <SelectRawMaterial v-model="showAdd" @submit="onSubmit" />
  <!--  <AddressAdd -->
  <!--    v-model="showAddress" -->
  <!--    :row="selectRow" -->
  <!--    :default-list="[...(selectRow?.item_addr_data || [])]" -->
  <!--    @submit="onAddress" -->
  <!--  /> -->
  <AddressConfig v-model="showAddress" :row="selectRow" @submit="onAddressSubmit" />
  <SelectProductionNotice v-model="showProduct" />
  <BulkSetting
    v-model="bulkSetting"
    :column-list="bulkList"
    :show="bulkShow"
    @submit="bulkSubmit"
    @close="handBulkClose"
  >
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
    <template #address="{ row }">
      <el-form
        ref="bulkFormRef"
        :model="bulkSetting[row.field]"
        :rules="row.rule"
        label-width="120px"
        label-position="left"
      >
        <el-form-item prop="receipt_unit_id" label="收货单位">
          <SelectComponents
            v-model="bulkSetting[row.field].recipient_entity_id"
            api="GetBusinessUnitListApi"
            label-field="name"
            value-field="id"
            :clearable="false"
            @select="
              (val) =>
                (bulkSetting[row.field].recipient_entity_name = val.name)
            "
          />
        </el-form-item>
        <el-form-item prop="contacts" label="收货联系人">
          <el-input v-model="bulkSetting[row.field].contacts" />
        </el-form-item>
        <el-form-item prop="contact_phone" label="收货电话">
          <el-input v-model="bulkSetting[row.field].contact_phone" />
        </el-form-item>
        <el-form-item prop="addr" label="收货地址">
          <el-input v-model="bulkSetting[row.field].addr" />
        </el-form-item>
      </el-form>
    </template>
  </BulkSetting>
</template>

<style lang="scss" scoped></style>
