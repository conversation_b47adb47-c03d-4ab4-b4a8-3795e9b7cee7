<script setup lang="ts">
import { useRouter } from 'vue-router'

const props = defineProps<{
  // eslint-disable-next-line vue/prop-name-casing
  wait_orders: {
    product_sale_order_count: number
    payable_order_count: number
  }
}>()
const emit = defineEmits(['openTour'])

function openTour() {
  emit('openTour')
}

const router = useRouter()
function toPage(path: string) {
  router.push(path)
}
</script>

<template>
  <div class="flex flex-col h-full gap-2">
    <div class="flex gap-2">
      <el-card id="TransferOrder" class="font-semibold flex-1 !rounded-[12px] bg-[#f9fafb]">
        <div class="flex justify-between mb-2">
          <p>调货订单</p>
        </div>
        <div class="flex gap-6">
          <!-- 快速下单 -->
          <div
            class="flex flex-col items-center justify-center flex-1 h-24 rounded-xl bg-[#f2f7fe] cursor-pointer"
            @click="toPage('/saleManagement/transferSalesAdd')"
          >
            <svg-icon name="quickOrder" size="28px" color="#1976d2" class="mb-2" />
            <span class="text-base text-[#222]">快速下单</span>
          </div>
          <!-- 退货处理 -->
          <div
            class="flex flex-col items-center justify-center flex-1 h-24 rounded-xl bg-[#fff7f0] cursor-pointer"
            @click="toPage('/saleManagement/returnProductsAdd')"
          >
            <svg-icon name="returnGoods" size="28px" color="#f57c00" class="mb-2" />
            <span class="text-base text-[#222]">退货处理</span>
          </div>
        </div>
      <!--      <div class="relative"> -->
      <!--        &lt;!&ndash; 快速下单 &ndash;&gt; -->
      <!--        <div class="w-20 h-24 absolute top-8 left-12 cursor-pointer" @click="toPage('/saleManagement/transferSalesAdd')" /> -->
      <!--        &lt;!&ndash; 退货处理 &ndash;&gt; -->
      <!--        <div class="w-20 h-24 absolute top-7 left-52 cursor-pointer" @click="toPage('/saleManagement/returnProductsAdd')" /> -->
      <!--      </div> -->
      <!--      <img src="https://cdn.zzfzyc.com/erp/homeImage1.png" alt="" class="h-[7rem] mt-6 ml-12"> -->
      </el-card>

      <el-card class="font-semibold flex-1 !rounded-[12px] bg-[#f9fafb]">
        <div class="flex justify-between mb-2">
          <p>待审核单据</p>
          <p class="text-slate-300 text-xs font-thin cursor-pointer flex items-center" @click="openTour">
            <el-icon size="14" color="#cbd5e1">
              <QuestionFilled />
            </el-icon>
            查看引导
          </p>
        </div>
        <div class="flex gap-6">
          <!-- 快速下单 -->
          <div
            class="flex flex-col items-center justify-center flex-1 h-24 rounded-xl cursor-pointer"
            @click="toPage('/saleManagement/transferSales')"
          >
            <div class="mb-4 text-[#2376da] text-xl">
              {{ props.wait_orders.product_sale_order_count }}
            </div>
            <span class="text-base text-[#222]">销售单</span>
          </div>
          <!-- 退货处理 -->
          <div
            class="flex flex-col items-center justify-center flex-1 h-24 rounded-xl cursor-pointer"
            @click="toPage('/meetMange/finishPay')"
          >
            <div class="mb-4 text-[#64748b] text-xl">
              {{ props.wait_orders.payable_order_count }}
            </div>
            <span class="text-base text-[#222]">应付单</span>
          </div>
        </div>
      </el-card>
    </div>
    <el-card id="MainFlow" class="font-semibold !rounded-[12px] flex-1 bg-[#f9fafb]">
      <p class="mt-2">
        主要流程
      </p>
      <div class="flex flex-col mt-8 gap-16">
        <!-- 第一行 三列布局 -->
        <div class="grid grid-cols-3 gap-16 justify-items-center">
          <div class="mainflow-card w-full relative" @click="toPage('/receivableManagement/customerArrearage')">
            <div class="icon-bg icon-bg-blue">
              <svg-icon name="dashboard_SVG-9" size="2rem" />
            </div>
            <div>客户欠款表</div>
            <div class="absolute right-[-50px]">
              <svg-icon name="jian_right" size="40px" />
            </div>
          </div>
          <div class="mainflow-card w-full" @click="toPage('/receivableManagement/fundsReceived')">
            <div class="icon-bg icon-bg-yellow">
              <svg-icon name="dashboard_SVG-5" size="2rem" />
            </div>
            <div>客户收款</div>
          </div>
        </div>
        <!-- 第二行 -->
        <div class="grid grid-cols-4 gap-16 justify-items-center">
          <div class="mainflow-card w-full relative" @click="toPage('/saleManagement/transferSales')">
            <div class="icon-bg icon-bg-green">
              <svg-icon name="dashboard_SVG-7" size="2rem" />
            </div>
            <div>销售订单</div>
            <div class="absolute bottom-[-70px]">
              <svg-icon name="jian_down" width="40px" height="70px" />
            </div>
            <div class="absolute top-[-70px] rotate-180">
              <svg-icon name="jian_down" width="40px" height="70px" />
            </div>
            <div class="absolute right-[-50px]">
              <svg-icon name="jian_right" size="40px" />
            </div>
          </div>
          <div class="mainflow-card w-full relative" @click="toPage('/saleManagement/salesReport')">
            <div class="icon-bg icon-bg-blue2">
              <svg-icon name="dashboard_SVG-4" size="2rem" />
            </div>
            <div>销售报表</div>
            <div class="absolute right-[-50px]">
              <svg-icon name="jian_right" size="40px" />
            </div>
          </div>
          <div class="mainflow-card w-full relative" @click="toPage('/meetMange/grossProfitAnalysis')">
            <div class="icon-bg icon-bg-purple">
              <svg-icon name="dashboard_SVG-6" size="2rem" />
            </div>
            <div>毛利分析</div>
            <div class="absolute right-[-50px]">
              <svg-icon name="jian_right" size="40px" />
            </div>
          </div>
          <div class="mainflow-card w-full" @click="toPage('/meetMange/businessAnalysis')">
            <div class="icon-bg icon-bg-blue2">
              <svg-icon name="dashboard_SVG-3" size="2rem" />
            </div>
            <div>经营分析</div>
          </div>
        </div>
        <!-- 第三行 -->
        <div class="grid grid-cols-3 gap-16 justify-items-center">
          <div class="mainflow-card w-full relative" @click="toPage('/meetMange/finishPay')">
            <div class="icon-bg icon-bg-pink">
              <svg-icon name="dashboard_SVG-2" size="2rem" />
            </div>
            <div>供应商采购单</div>
            <div class="absolute right-[-50px]">
              <svg-icon name="jian_right" size="40px" />
            </div>
          </div>
          <div class="mainflow-card w-full relative" @click="toPage('/meetMange/supplierBalanceSheet')">
            <div class="icon-bg icon-bg-yellow">
              <svg-icon name="dashboard_SVG-8" size="2rem" />
            </div>
            <div>供应商应付表</div>
            <div class="absolute right-[-50px]">
              <svg-icon name="jian_right" size="40px" />
            </div>
          </div>
          <div class="mainflow-card w-full" @click="toPage('/meetMange/actualPay')">
            <div class="icon-bg icon-bg-red">
              <svg-icon name="dashboard_SVG" size="2rem" />
            </div>
            <div>供应商付款</div>
          </div>
        </div>
      </div>
      <!-- <div class="relative">
        <div class="w-16 h-24 absolute top-36 left-10 cursor-pointer" @click="toPage('/saleManagement/transferSales')" />
        <div class="w-24 h-20 absolute top-9 left-[16rem] cursor-pointer" @click="toPage('/receivableManagement/customerArrearage')" />
        <div class="w-20 h-20 absolute top-9 left-[28rem] cursor-pointer" @click="toPage('/receivableManagement/fundsReceived')" />
        <div class="w-20 h-20 absolute top-[10rem] left-[16.5rem] cursor-pointer" @click="toPage('/saleManagement/salesReport')" />
        <div class="w-20 h-20 absolute top-[10rem] left-[28.5rem] cursor-pointer" @click="toPage('/meetMange/grossProfitAnalysis')" />
        <div class="w-16 h-20 absolute top-[10rem] left-[41rem] cursor-pointer" @click="toPage('/meetMange/businessAnalysis')" />
        <div class="w-28 h-24 absolute top-[17.5rem] left-[16rem] cursor-pointer" @click="toPage('/meetMange/finishPay')" />
        <div class="w-28 h-24 absolute top-[17.5rem] left-[27rem] cursor-pointer" @click="toPage('/meetMange/supplierBalanceSheet')" />
        <div class="w-28 h-24 absolute top-[17.5rem] left-[40rem] cursor-pointer" @click="toPage('/meetMange/actualPay')" />
      </div> -->
      <!-- <img src="https://cdn.zzfzyc.com/erp/主流程.png" alt="" class="max-w-[842.5px] w-full mt-6 ml-4"> -->
    </el-card>
  </div>
</template>

<style scoped>
.mainflow-card {
  @apply flex flex-col items-center justify-center bg-white rounded-xl shadow-lg h-32 cursor-pointer hover:shadow-lg transition;
}
.icon-bg {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  margin-bottom: 8px;
  background: #f5f6fa;
}
.icon-bg-blue {
  background: #e6f0ff;
}
.icon-bg-yellow {
  background: #fff7e6;
}
.icon-bg-green {
  background: #e6fff2;
}
.icon-bg-purple {
  background: #f3e6ff;
}
.icon-bg-pink {
  background: #ffe6ee;
}
.icon-bg-red {
  background: #ffeaea;
}
.icon-bg-blue2 {
  background: #f0f4ff;
}
</style>
