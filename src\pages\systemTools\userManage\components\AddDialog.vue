<script lang="ts" setup>
import { reactive, ref } from 'vue'

const emits = defineEmits(['handleSure'])

function checkPhone(rule: any, value: any, callback: any) {
  const reg = /^(?:(?:\+|00)86)?1\d{10}$/
  if (reg.test(value))
    callback()
  else
    callback(new Error('请输入正确的手机号码'))
}

function checkPassword(rule: any, value: any, callback: any) {
  const reg
    = /[\u4E00-\u9FA5|\u3002|\uFF1F|\uFF01|\uFF0C|\u3001|\uFF1B|\uFF1A|\u201C|\u201D|\u2018|\u2019|\uFF08|\uFF09|\u300A|\u300B|\u3008|\u3009|\u3010|\u3011|\u300E|\u300F|\u300C|\u300D|\uFE43|\uFE44|\u3014|\u3015|\u2026|\u2014|\uFF5E|\uFE4F|\uFFE5]/
  if (!reg.test(value))
    callback()
  else
    callback(new Error('不允许夹杂中文符号'))
}

function checkUserName(rule: any, value: any, callback: any) {
  const reg = /^[\u4E00-\u9FA5A-Za-z0-9]+$/
  if (reg.test(value))
    callback()
  else
    callback(new Error('请输入正确的用户名'))
}

const state = reactive({
  showModal: false,
  modalName: '新基建用户',
  form: {
    userName: '',
    userPhone: '',
    userEmail: '',
    userRoll: '',
    userPassword: '',
  },
  fromRules: {
    userName: [
      { required: true, message: '请填写用户名称', trigger: 'blur' },
      { required: true, validator: checkUserName, trigger: 'blur' },
    ],
    userPhone: [{ required: true, trigger: 'blur', validator: checkPhone }],
    // userEmail: [{ required: true, trigger: 'blur', validator: checkEmail }],
    // userRoll: [{ required: true, message: '请选择用户角色', trigger: 'blur' }],
    userPassword: [
      { min: 6, max: 16, trigger: 'blur', message: '密码范围为6~16位', required: true },
      { validator: checkPassword, trigger: 'blur', required: true },
    ],
  },
})
const ruleFormRef = ref()

function handCancel() {
  state.showModal = false
}

async function handleSure() {
  if (!ruleFormRef.value)
    return
  await ruleFormRef.value.validate((valid: any) => {
    if (valid)
      emits('handleSure', state.form)
  })
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="500" height="400" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <el-form ref="ruleFormRef" :model="state.form" label-width="100px" label-position="top" :rules="state.fromRules">
      <!-- <el-form-item label="绑定员工" prop="bindStaff">
        <SelectOptions></SelectOptions>
      </el-form-item> -->
      <el-form-item label="用户名" prop="userName">
        <el-input v-model="state.form.userName" placeholder="仅支持英文、中文、数字组成" />
      </el-form-item>
      <el-form-item label="手机号码" prop="userPhone">
        <el-input v-model="state.form.userPhone" maxlength="11" placeholder="手机号码" />
      </el-form-item>
      <el-form-item label="登录密码" prop="userPassword">
        <el-input v-model="state.form.userPassword" placeholder="登录密码" show-password />
      </el-form-item>
      <!-- <el-form-item label="用户角色" prop="userRoll">
        <SelectOptions></SelectOptions>
      </el-form-item>
      <el-form-item label="用户密码" prop="userPassword">
        <el-input v-model="state.form.userPassword" placeholder="若不输入则使用默认密码123456"></el-input>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
