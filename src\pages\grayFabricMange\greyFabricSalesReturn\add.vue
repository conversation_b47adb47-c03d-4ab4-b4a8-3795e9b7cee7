<script setup lang="ts" name="GreyFabricSalesReturnAdd">
import { ElMessage } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import AddSalesFabircDialog from '../components/AddSalesFabircDialog.vue'
import AddSalesXimaDialog from '../components/AddSalesXimaDialog.vue'
import AddXimaDialog from '../components/AddXimaDialog.vue'
import { addGfmSaleReturnOrder } from '@/api/greyFabricSalesReturn'
import { BusinessUnitIdEnum, DictionaryType, EmployeeType } from '@/common/enum'
import { formatDate, formatPriceMul, formatUnitPriceMul, formatWeightMul, sumNum } from '@/common/format'
import { deepClone, getCurrentDate, getDefaultSaleSystem } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'
import SelectRawMaterial from '@/components/SelectRawMaterial/index.vue'

const routerList = useRouterList()

const bulkSetting = ref<any>({})

const state = reactive<any>({
  form: {
    marketingSystem: '', // 营销体系
    consignee: '', // 接收单位
    customer_id: '', // 货源单位
    date: '', // 退货日期
    remark: '',
    dye_unit_use_order_no: '', // 染厂用坯单号
    warehouse_keeper: '', // 仓管员
    sales_user_id: '', // 销售员
    address: '',
  },
  formRules: {
    marketingSystem: [{ required: true, message: '请选择营销体系', trigger: 'blur' }],
    consignee: [{ required: true, message: '请选择接收单位', trigger: 'change' }],
    destprovidername: [{ required: true, message: '请选择供方名称', trigger: 'blur' }],
    date: [{ required: true, message: '请选择退货日期', trigger: 'blur' }],
    address: [{ required: true, message: '请填写退货收货地址', trigger: 'blur' }],
    sales_user_id: [{ required: true, message: '请选择销售人员', trigger: 'blur' }],
  },
  tableList: [],
  isInformation: true,
  multipleSelection: [],
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  customer_nameTwo: '',
  unit_name: '',
  raw_code: '',
  raw_name: '',
  color_code: '',
  color_name: '',
})

onMounted(() => {
  state.form.date = getCurrentDate()
  const resDes = getDefaultSaleSystem()
  state.form.marketingSystem = resDes?.default_sale_system_id
})

const tableConfig = ref({
  // FooterMethod: (val: any) => FooterMethod(val),
  showSlotNums: true,
  showOperate: true,
  operateWidth: '80',
  footerMethod: (val: any) => FooterMethod(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

function getConsignee(val: any) {
  state.form.address = val?.address
}

const columnList = ref([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    fixed: 'left',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    fixed: 'left',
    minWidth: 150,
  },
  {
    field: 'sale_delivery_order_code',
    title: '坯布销售出货单号',
    minWidth: 150,
  },
  {
    field: 'customer_id',
    title: '所属客户',
    required: true,
    minWidth: 150,
    soltName: 'SubordinateCustomer',
  },
  {
    field: 'raw_material_yarn_name',
    title: '原料纱名',
    minWidth: 150,
    soltName: 'raw_material_yarn_name',
  },
  {
    field: 'raw_material_batch_num',
    title: '原料批号',
    minWidth: 150,
    soltName: 'raw_material_batch_num',
  },
  {
    field: 'raw_material_batch_brand',
    title: '原料品牌',
    minWidth: 150,
    soltName: 'raw_material_batch_brand',
  },
  {
    field: 'grey_fabric_width',
    title: '幅宽',
    minWidth: 180,
    soltName: 'breadth',
  },
  {
    field: 'grey_fabric_gram_weight',
    title: '克重',
    minWidth: 180,
    soltName: 'weight',
  },
  {
    field: 'needle_size',
    title: '针寸数',
    minWidth: 150,
    soltName: 'needles',
  },
  //   {
  //     field: 'total_needle_size',
  //     title: '总针数',
  //     minWidth: 150,
  //     soltName: 'totalStitches',
  //   },
  {
    field: 'yarn_batch',
    title: '纱批',
    minWidth: 150,
    soltName: 'yarnBatch',
  },
  {
    field: 'gray_fabric_color_id',
    title: '织坯颜色',
    minWidth: 150,
    soltName: 'blankColor',
  },
  //   {
  //     field: 'weaving_process',
  //     title: '织造工艺',
  //     minWidth: 150,
  //     soltName: 'weavingProcess',
  //   },
  {
    field: 'grey_fabric_level_id',
    title: '坯布等级',
    minWidth: 150,
    soltName: 'greyFabricGrade',
  },
  {
    field: 'machine_number',
    title: '机台号',
    minWidth: 150,
    soltName: 'machineNumber',
  },
  {
    field: 'roll',
    title: '退货匹数',
    minWidth: 150,
    soltName: 'horsepower',
    required: true,
  },
  {
    field: 'remark',
    title: '细码',
    minWidth: 150,
    soltName: 'xima',
    required: true,
  },
  {
    field: 'total_weight',
    title: '总数量',
    minWidth: 150,
  },
  {
    field: 'adjust_weight',
    title: '调整数量',
    minWidth: 150,
    soltName: 'adjustingWeight',
  },
  {
    field: 'return_weight',
    title: '退货数量',
    minWidth: 150,
    // soltName: 'returnWeight',
  },
  {
    field: 'single_price',
    title: '单价',
    minWidth: 150,
    soltName: 'unitPrice',
    required: true,
  },
  {
    field: 'other_price',
    title: '其他金额',
    minWidth: 150,
    soltName: 'otherPrice',
  },
  {
    field: 'total_price',
    title: '总金额',
    minWidth: 150,
    // soltName: 'totalPrice',
  },
  {
    field: 'grey_fabric_remark',
    title: '坯布备注',
    minWidth: 150,
    soltName: 'fabircRemark',
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 150,
    soltName: 'remark',
  },
])

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${sumNum(data, 'roll')}`

      if (['total_weight'].includes(column.property))
        return `${sumNum(data, 'total_weight')}`

      if (['other_price'].includes(column.property))
        return `￥${sumNum(data, 'other_price')}`

      if (['total_price'].includes(column.property))
        return `￥${sumNum(data, 'total_price')}`

      if (['return_weight'].includes(column.property))
        return `${sumNum(data, 'return_weight')}`

      // if (['weight'].includes(column.property)) {
      //   return `${formatWeightDiv(sumNum(data, 'weight'))}`
      // }
      return null
    }),
  ]
}

const AddXimaDialogRef = ref()

const AddSalesXimaDialogRef = ref()

function handWrite(row: any, rowIndex: number) {
  if (row.roll === '')
    return ElMessage.error('请先输入匹数')

  if (row?.sale_delivery_order_item_id === 0) {
    AddXimaDialogRef.value.state.showModal = true
    const arr = [
      {
        roll: row.roll,
        position: '',
        volume_number: '',
        weight: '',
      },
    ]
    if (!row?.item_fc_data?.length)
      AddXimaDialogRef.value.state.tableData = arr
    else
      AddXimaDialogRef.value.state.tableData = deepClone(row?.item_fc_data || [])

    AddXimaDialogRef.value.state.canEnter = row.roll
    AddXimaDialogRef.value.state.isAdd = true
    AddXimaDialogRef.value.state.horsepower = row.roll
    AddXimaDialogRef.value.state.code = row.grey_fabric_code
    AddXimaDialogRef.value.state.name = row.grey_fabric_name
    AddXimaDialogRef.value.state.rowIndex = rowIndex
    AddXimaDialogRef.value.state.id = row.id
  }
  else {
    AddSalesXimaDialogRef.value.state.showModal = true
    AddSalesXimaDialogRef.value.state.canEnter = row.roll
    AddSalesXimaDialogRef.value.state.ximaList = row?.item_fc_data || []
    AddSalesXimaDialogRef.value.state.rowIndex = rowIndex
    AddSalesXimaDialogRef.value.state.gfm_sale_delivery_item_id = row.sale_delivery_order_item_id
  }
}

function handleSureXimaSales(val: any) {
  state.tableList?.map((item: any, index: number) => {
    if (index === val.rowIndex) {
      item.item_fc_data = val.ximaList
      item.total_weight = sumNum(val.ximaList, 'weight')
      return item
    }
  })
  AddSalesXimaDialogRef.value.state.showModal = false
}

const AddFabricDialogRef = ref()
const showAdd = ref(false)
function handAdd(val: number) {
  if (val === 1) {
    showAdd.value = true
    state.isInformation = true
  }
  else {
    AddFabricDialogRef.value.state.showModal = true
    AddFabricDialogRef.value.state.customer_id = state?.form.customer_id
    state.isInformation = false
    AddFabricDialogRef.value.state.apiString = val
  }
}

function handFabric(list: any) {
  showAdd.value = false
  list.forEach((item: any) => {
    // const idNum = Math.floor(Math.random() * 10000)
    state.tableList.push({
      ...item,
      grey_fabric_code: item?.grey_fabric_code || item.code,
      grey_fabric_name: item?.grey_fabric_name || item.name,
      sale_delivery_order_code: item?.document_code || '',
      custom_id: item?.customer_id || '',
      grey_fabric_width: item?.grey_fabric_width,
      grey_fabric_gram_weight: item?.grey_fabric_gram_weight,
      needle_size: item?.needle_size,
      total_needle_size: item?.total_needle_size,
      item_fc_data: [],
      yarn_batch: item?.yarn_batch,
      gray_fabric_color_id: item?.gray_fabric_color_id,
      weaving_process: item?.weaving_process,
      grey_fabric_level_id: item?.grey_fabric_level_id,
      machine_number: item?.machine_number,
      grey_fabric_purchase_code: item?.order_code,
      grey_fabric_purchase_item_id: item?.id,
      // 用接口是否有这个值区分是从销售出货单添加
      sale_delivery_order_item_id: item.grey_fabric_delivery_id ? item?.id : 0,
      roll: '',
      total_weight: '',
      single_price: '',
      return_weight: '',
      adjust_weight: '',
      other_price: '',
      grey_fabric_remark: '',
      total_price: '',
      item_source: state.isInformation ? 1 : 2,
      raw_material_yarn_name: item?.raw_material_yarn_name,
      raw_material_batch_num: item?.raw_material_batch_num,
      raw_material_batch_brand: item?.raw_material_batch_brand,
      custom_name: item.customer_name,
      remark: '',
      grey_fabric_id: state.isInformation ? item?.id || 0 : item?.grey_fabric_id || 0,
      warehouse_sum_id: item.warehouse_sum_id || 0,

      //   id: idNum,
    })
  })
  AddFabricDialogRef.value.state.showModal = false
}

// 录入细码
function handleSureXima(val: any) {
  state.tableList?.map((item: any, index: number) => {
    if (index === val.rowIndex) {
      item.item_fc_data = val.tableData
      item.total_weight = sumNum(val.tableData, 'weight')
      item.return_weight = Number(item?.total_weight) + Number(item.adjust_weight)
      return item
    }
  })
  AddXimaDialogRef.value.state.showModal = false
}

const tableRef = ref()

watch(
  () => state.tableList,
  () => {
    if (state.tableList?.length > 0) {
      state.tableList?.map((item: any) => {
        item.total_price = (Number(item?.single_price) * Number(item?.return_weight) + Number(item?.other_price)).toFixed(2)
        item.return_weight = (Number(item?.total_weight) + Number(item.adjust_weight)).toFixed(2)
        return item
      })
      //

      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

const ruleFormRef = ref()

const { fetchData: addPost, data: addData, success: addSuccess, msg: addMsg } = addGfmSaleReturnOrder()

// 提交数据
async function handleSure() {
  if (!state.tableList.length)
    return ElMessage.error('至少添加一条坯布信息')

  const list = deepClone(state.tableList)

  for (let i = 0; i < list.length; i++) {
    if (list[i].custom_id === '')
      return ElMessage.error('所属客户不可为空')

    if (list[i].roll === '')
      return ElMessage.error('匹数不可为空')

    if (Number(list[i].roll) - Number(sumNum(list[i]?.item_fc_data, 'roll')) !== 0 && Number(list[i].roll) !== 0)
      return ElMessage.error(`第${i + 1}行的细码必须录完`)

    if (list[i].single_price === '')
      return ElMessage.error('单价不可为空')

    list[i].grey_fabric_gram_weight = list[i]?.grey_fabric_gram_weight.toString()
    list[i].roll = Number(formatPriceMul(list[i].roll || 0))
    list[i].single_price = Number(formatUnitPriceMul(list[i].single_price || 0))
    list[i].adjust_weight = Number(formatWeightMul(list[i].adjust_weight || 0))

    list[i].other_price = Number(formatPriceMul(list[i].other_price || 0))
    // list[i].roll = Number(list[i].roll || 0)
    list[i].total_weight = Number(formatWeightMul(list[i].total_weight || 0))
    list[i].return_weight = Number(formatWeightMul(list[i].return_weight || 0))
    list[i].total_price = Number(formatPriceMul(list[i].total_price || 0))

    // 将坯布信息的数量乘以一千给后端
    for (let q = 0; q < list[i].item_fc_data?.length; q++) {
      if (list[i].item_fc_data[q].weight === '')
        return ElMessage.error('细码数量不可为空')

      if (list[i].item_fc_data[q].roll === '')
        return ElMessage.error('细码匹数不可为空')

      list[i].item_fc_data[q].weight = Number(formatWeightMul(list[i].item_fc_data[q].weight))
      list[i].item_fc_data[q].roll = Number(formatPriceMul(list[i].item_fc_data[q].roll))
      list[i].item_fc_data[q].grey_fabric_stock_id = Number(list[i].item_fc_data[q].grey_fabric_stock_id)
    }
  }
  const query = {
    return_time: formatDate(state.form.date),
    sale_system_id: state?.form.marketingSystem,
    return_receive_unit_id: state.form.consignee || 0,
    store_keeper_id: state?.form.warehouse_keeper || 0,
    salesman_id: state?.form.sales_user_id || 0,
    customer_id: state?.form.customer_id || 0,
    return_receive_addr: state.form.address,
    remark: state.form.remark,
    dye_unit_use_order_no: state.form.dye_unit_use_order_no,
    item_data: list,
  }
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(query)

      if (addSuccess.value) {
        ElMessage.success('成功')
        routerList.push({ name: 'GreyFabricSalesReturnDetail', query: { id: addData.value.id } })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

function handDelete(index: number) {
  state.tableList.splice(index, 1)
}

const bulkShow = ref(false)

function bulkHand() {
  bulkShow.value = true
}

function handBulkClose() {
  bulkShow.value = false
}

async function bulkSubmit({ row, value }: any) {
  if (state.multipleSelection?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  state.tableList?.map((item: any) => {
    if (item?.selected) {
      item[row.field] = value[row.field]
      return item
    }
  })
  bulkShow.value = false
  ElMessage.success('设置成功')
}

const bulkList = reactive<any>([
  {
    field: 'custom_id',
    // field_name: 'customer_name',
    title: '所属客户',
    component: 'select',
    api: 'BusinessUnitSupplierEnumlist',
    query: { sale_system_id: state.form.marketingSystem },
  },
  {
    field: 'raw_material_yarn_name',
    title: '原料纱名',
    component: 'input',
    type: 'text',
  },
  {
    field: 'raw_material_batch_num',
    title: '原料批号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'raw_material_batch_brand',
    title: '原料品牌',
    component: 'input',
    type: 'text',
  },
  {
    field: 'grey_fabric_width',
    title: '幅宽',
    component: 'input',
    type: 'text',
  },
  {
    field: 'grey_fabric_gram_weight',
    title: '克重',
    component: 'input',
    type: 'text',
  },
  {
    field: 'needle_size',
    title: '针寸数',
    component: 'input',
    type: 'text',
  },
  //   {
  //     field: 'total_needle_size',
  //     title: '总针数',
  //     component: 'input',
  //     type: 'text',
  //   },
  {
    field: 'yarn_batch',
    title: '纱批',
    component: 'input',
    type: 'text',
  },
  {
    field: 'gray_fabric_color_id',
    title: '织坯颜色',
    component: 'select',
    api: 'getInfoProductGrayFabricColorList',
  },
  //   {
  //     field: 'weaving_process',
  //     title: '织造工艺',
  //     component: 'input',
  //     type: 'text',
  //   },
  {
    field: 'grey_fabric_level_id',
    title: '坯布等级',
    component: 'select',
    api: 'getInfoBaseGreyFabricLevelList',
  },
  {
    field: 'machine_number',
    title: '机台号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'adjust_weight',
    title: '调整数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'roll',
    title: '退货匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'single_price',
    title: '单价',
    component: 'input',
    type: 'float',
  },
  {
    field: 'other_price',
    title: '其他金额',
    component: 'input',
    type: 'float',
  },
  {
    field: 'grey_fabric_remark',
    title: '坯布备注',
    component: 'input',
    type: 'text',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])

watch(
  () => state.form.marketingSystem,
  () => {
    state.tableList.map((item: any) => {
      item.custom_id = ''
      return item
    })
    bulkList[0].query = { sale_system_id: state.form.marketingSystem }
  },
  {
    immediate: true,
  },
)

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem required label="营销体系:">
          <template #content>
            <el-form-item prop="marketingSystem">
              <SelectComponents v-model="state.form.marketingSystem" api="AdminsaleSystemgetSaleSystemDropdownList" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="货源单位:">
          <template #content>
            <el-form-item prop="customer_id">
              <SelectDialog
                v-model="state.form.customer_id"
                :query="{ name: componentRemoteSearch.customer_name }"
                api="GetCustomerEnumList"
                :column-list="[
                  {
                    title: '客户编号',
                    minWidth: 100,
                    required: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '客户编号',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    title: '货源单位',
                    minWidth: 100,
                    colGroupHeader: true,
                    required: true,
                    childrenList: [
                      {
                        isEdit: true,
                        field: 'name',
                        title: '货源单位',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    title: '电话',
                    colGroupHeader: true,
                    minWidth: 100,
                    childrenList: [
                      {
                        field: 'phone',
                        isEdit: true,
                        title: '电话',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    title: '销售员',
                    minWidth: 100,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'seller_name',
                        title: '销售员',
                        soltName: 'seller_name',
                        isEdit: true,
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-value="val => ((state.form.sales_user_id = val.seller_id), (state.form.address = val.address))"
                @change-input="val => (componentRemoteSearch.customer_name = val)"
              />
              <!-- <SelectComponents api="GetCustomerEnumList" label-field="name" value-field="id" v-model="state.form.customer_id" clearable /> -->
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="销售员:">
          <template #content>
            <el-form-item prop="sales_user_id">
              <SelectComponents v-model="state.form.sales_user_id" :query="{ duty: EmployeeType.salesman }" api="Adminemployeelist" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="接收单位:">
          <template #content>
            <el-form-item prop="consignee">
              <SelectDialog
                v-model="state.form.consignee"
                :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory, name: componentRemoteSearch.unit_name }"
                api="BusinessUnitSupplierEnumlist"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-value="val => getConsignee(val)"
                @change-input="val => (componentRemoteSearch.unit_name = val)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓管员:">
          <template #content>
            <el-form-item prop="warehouse_keeper">
              <SelectComponents v-model="state.form.warehouse_keeper" :query="{ duty: EmployeeType.warehouseManager }" api="Adminemployeelist" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="退货日期:">
          <template #content>
            <el-form-item prop="date">
              <el-date-picker v-model="state.form.date" type="date" placeholder="退货日期" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="退货收货地址:" copies="2">
          <template #content>
            <el-form-item prop="address">
              <el-input v-model="state.form.address" clearable placeholder="退货收货地址" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据备注:" copies="2">
          <template #content>
            <el-form-item prop="remark">
              <el-input v-model="state.form.remark" placeholder="单据备注" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染厂用坯单号:">
          <template #content>
            <el-form-item prop="dye_unit_use_order_no">
              <el-input v-model="state.form.dye_unit_use_order_no" placeholder="染厂用坯单号" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="坯布信息" :tool-bar="false" class="mt-[5px]">
    <template #right-top>
      <el-button type="primary" @click="bulkHand">
        批量操作
      </el-button>
      <el-button type="primary" @click="handAdd(1)">
        从资料中添加
      </el-button>
      <el-button :disabled="state.form.marketingSystem === '' || state.form.customer_id === ''" type="primary" @click="handAdd(2)">
        从销售出货单添加
      </el-button>
      <!-- <el-button @click="handAdd(2)" type="primary">从销售出货单添加</el-button> -->
    </template>
    <Table ref="tableRef" :config="tableConfig" :table-list="state.tableList" :column-list="columnList">
      <template #SubordinateCustomer="{ row }">
        <SelectDialog
          v-if="!row.sale_delivery_order_code"
          v-model="row.custom_id"
          :label-name="row?.custom_name"
          :query="{ sale_system_id: state.form.marketingSystem, name: componentRemoteSearch.customer_nameTwo }"
          api="GetCustomerEnumList"
          :column-list="[
            {
              title: '客户编号',
              minWidth: 100,
              required: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'code',
                  isEdit: true,
                  title: '客户编号',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '货源单位',
              minWidth: 100,
              colGroupHeader: true,
              required: true,
              childrenList: [
                {
                  isEdit: true,
                  field: 'name',
                  title: '货源单位',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '电话',
              colGroupHeader: true,
              minWidth: 100,
              childrenList: [
                {
                  field: 'phone',
                  isEdit: true,
                  title: '电话',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '销售员',
              minWidth: 100,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'seller_name',
                  title: '销售员',
                  soltName: 'seller_name',
                  isEdit: true,
                  minWidth: 100,
                },
              ],
            },
          ]"
          @change-input="val => (componentRemoteSearch.customer_nameTwo = val)"
        />
        <span v-else>{{ row.custom_name }}</span>
      </template>
      <template #breadth="{ row }">
        <el-input v-if="!row.sale_delivery_order_code" v-model="row.grey_fabric_width" clearable class="input-with-select">
          <template #append>
            <SelectComponents
              v-model="row.grey_fabric_width_unit_id"
              placeholder="单位"
              style="width: 80px"
              :query="{ dictionary_id: DictionaryType.width_unit }"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </el-input>
        <span v-else>{{ row.grey_fabric_width_and_unit_name }}</span>
      </template>
      <template #weight="{ row }">
        <el-input v-if="!row.sale_delivery_order_code" v-model="row.grey_fabric_gram_weight" clearable class="input-with-select">
          <template #append>
            <SelectComponents
              v-model="row.grey_fabric_gram_weight_unit_id"
              placeholder="单位"
              style="width: 80px"
              :query="{ dictionary_id: DictionaryType.gram_weight_unit }"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </el-input>
        <span v-else>{{ row.grey_fabric_gram_weight_and_unit_name }}</span>
      </template>
      <template #needles="{ row }">
        <vxe-input v-if="!row.sale_delivery_order_code" v-model="row.needle_size" placeholder="请输入" />
        <span v-else>{{ row.needle_size }}</span>
      </template>
      <!-- <template #totalStitches="{ row }">
        <vxe-input v-model="row.total_needle_size" placeholder="请输入"></vxe-input>
      </template> -->
      <template #yarnBatch="{ row }">
        <vxe-input v-if="!row.sale_delivery_order_code" v-model="row.yarn_batch" placeholder="请输入" />
        <span v-else>{{ row.yarn_batch }}</span>
      </template>
      <template #blankColor="{ row }">
        <SelectComponents v-if="!row.sale_delivery_order_code" v-model="row.gray_fabric_color_id" api="getInfoProductGrayFabricColorList" label-field="name" value-field="id" clearable />
        <span v-else>{{ row.gray_fabric_color_name }}</span>
      </template>
      <!-- <template #weavingProcess="{ row }">
        <vxe-input v-model="row.weaving_process" placeholder="请输入"></vxe-input>
      </template> -->
      <template #greyFabricGrade="{ row }">
        <SelectComponents v-if="!row.sale_delivery_order_code" v-model="row.grey_fabric_level_id" api="getInfoBaseGreyFabricLevelList" label-field="name" value-field="id" clearable />
        <span v-else>{{ row.grey_fabric_level_name }}</span>
      </template>
      <template #machineNumber="{ row }">
        <vxe-input v-if="!row.sale_delivery_order_code" v-model="row.machine_number" placeholder="请输入" />
        <span v-else>{{ row.machine_number }}</span>
      </template>
      <template #horsepower="{ row }">
        <vxe-input v-model="row.roll" :min="0" type="float" placeholder="必填" />
      </template>
      <template #adjustingWeight="{ row }">
        <vxe-input v-model="row.adjust_weight" type="float" placeholder="必填" />
      </template>
      <template #xima="{ row, rowIndex }">
        <div class="flex items-center">
          <el-button type="primary" text link @click="handWrite(row, rowIndex)">
            录入
          </el-button>
          <div v-if="Number(sumNum(row?.item_fc_data, 'roll')) >= row.roll && row.roll > 0 && row.roll !== ''" class="text-[#b5b39f] ml-[5px]">
            (已录完)
          </div>
          <div v-else class="text-[#efa6ae] ml-[5px]">
            ({{ (row?.roll - Number(sumNum(row?.item_fc_data, 'roll'))).toFixed(2) }}匹未录)
          </div>
        </div>
      </template>
      <template #unitPrice="{ row }">
        <vxe-input v-model="row.single_price" type="float" placeholder="必填" />
      </template>
      <template #otherPrice="{ row }">
        <vxe-input v-model="row.other_price" type="float" :min="0" placeholder="请输入" />
      </template>
      <!-- <template #totalPrice="{ row }">
          {{ Number(row.single_price) * Number(row.total_weight) + Number(row.other_price) || 0 }}
        </template> -->
      <template #fabircRemark="{ row }">
        <vxe-input v-if="!row.sale_delivery_order_code" v-model="row.grey_fabric_remark" placeholder="请输入" />
        <span v-else>{{ row.grey_fabric_remark }}</span>
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" placeholder="请输入" />
      </template>
      <template #raw_material_yarn_name="{ row }">
        <vxe-input v-if="!row.sale_delivery_order_code" v-model="row.raw_material_yarn_name" placeholder="请输入" />
        <span v-else>{{ row.raw_material_yarn_name }}</span>
      </template>
      <template #raw_material_batch_num="{ row }">
        <vxe-input v-if="!row.sale_delivery_order_code" v-model="row.raw_material_batch_num" placeholder="请输入" />
        <span v-else>{{ row.raw_material_batch_num }}</span>
      </template>
      <template #raw_material_batch_brand="{ row }">
        <vxe-input v-if="!row.sale_delivery_order_code" v-model="row.raw_material_batch_brand" placeholder="请输入" />
        <span v-else>{{ row.raw_material_batch_brand }}</span>
      </template>
      <template #operate="{ rowIndex }">
        <el-button text type="danger" @click="handDelete(rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <SelectRawMaterial v-model="showAdd" @submit="handFabric" />
  <AddXimaDialog ref="AddXimaDialogRef" @handle-sure="handleSureXima" />
  <AddSalesXimaDialog ref="AddSalesXimaDialogRef" @handle-sure="handleSureXimaSales" />
  <AddSalesFabircDialog ref="AddFabricDialogRef" :api="state.isInformation ? 1 : 2" @handle-sure="handFabric" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
</template>

<style></style>
