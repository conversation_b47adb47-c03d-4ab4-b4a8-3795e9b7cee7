<script setup lang="ts" name="RawMaterialSalesReturnOrder">
import { Plus } from '@element-plus/icons-vue'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import {
  SaleReturnOrderCancel,
  SaleReturnOrderDetail,
  SaleReturnOrderList,
  SaleReturnOrderListDownLoad,
  SaleReturnOrderPass,
} from '@/api/rawMaterIalSaleReturnOrder'
import { BusinessUnitIdEnum } from '@/common/enum'
import {
  formatDate,
  formatPriceDiv,
  formatWeightDiv,
  sumNum,
} from '@/common/format'
import {
  debounce,
  exportList,
  getFilterData,
  orderStatusConfirmBox,
} from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import router from '@/router'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import SelectBusinessDialog from '@/components/SelectBusinessDialog/index.vue'

const filterData = reactive({
  order_no: '',
  customer_id: '',
  return_unit_id: '',
  return_time: '',
  status: '',
  return_start_date: '',
  return_end_date: '',
})

onMounted(() => {
  getData()
})

const {
  fetchData: fetchDataList,
  data: dataList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
} = SaleReturnOrderList()
async function getData() {
  const status = ((filterData.status as unknown as []) || []).join(',')
  await fetchDataList(
    getFilterData({ ...filterData, status }, ['purchase_time', 'status_arr']),
  )
  if (dataList.value?.list)
    showDetail(dataList.value.list[0])
}
onActivated(getData)

const selectRow = ref()
const detailShow = ref()
const {
  fetchData: fetchDataDetail,
  data: dataDetail,
  loading: loadingDetail,
} = SaleReturnOrderDetail()
async function getDataDetail() {
  await fetchDataDetail({ id: selectRow.value.id })
}

watch(
  () => filterData.return_time,
  (value: any) => {
    filterData.return_start_date = formatDate(value?.[0]) || ''
    filterData.return_end_date = formatDate(value?.[1]) || ''
  },
)

watch(
  () => filterData,
  debounce(() => {
    getData()
  }, 400),
  { deep: true },
)

function showDetail(row: any) {
  selectRow.value = row
  detailShow.value = true
  getDataDetail()
}

function handDetail(row: any) {
  router.push({
    name: 'RawMaterialSalesReturnOrderDetail',
    params: { id: row?.id },
  })
}
async function handPass(id: number) {
  const msg = { title: '是否审核该订单', desc: '点击审核后订单将审核通过' }
  await orderStatusConfirmBox({ id, message: msg, api: SaleReturnOrderPass })
  getData()
}
async function handCancel(id: number) {
  const msg = {
    title: '是否消审该订单',
    desc: '点击消审后订单将变回待审核状态',
  }
  await orderStatusConfirmBox({ id, message: msg, api: SaleReturnOrderCancel })
  getData()
}

function handEdit(row: any) {
  router.push({
    name: 'RawMaterialSalesReturnOrderEdit',
    params: { id: row?.id },
  })
}

function handAdd() {
  router.push({
    name: 'RawMaterialSalesReturnOrderAdd',
  })
}

const columnList = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '订单编号',
    soltName: 'order_no',
    width: '8%',
  },
  {
    sortable: true,
    field: 'customer_name',
    title: '客户名称',
    width: 100,
  },
  {
    sortable: true,
    field: 'return_unit_name',
    title: '退货单位',
    width: 100,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系名称',
  },
  {
    sortable: true,
    field: 'return_date',
    title: '退货时间',
    width: 100,
  },
  {
    sortable: true,
    field: 'seller_name',
    title: '销售员',
    width: 100,
  },
  {
    sortable: true,
    field: 'warehouse_manager_name',
    title: '仓管员',
    width: 100,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    width: 100,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    isDate: true,
    width: 150,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    width: 100,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '修改时间',
    isDate: true,
    width: 150,
  },
  {
    sortable: true,
    field: 'auditor_name',
    title: '审核人',
    width: 100,
  },
  {
    sortable: true,
    field: 'audit_time',
    title: '审核时间',
    isDate: true,
    width: 100,
  },
  {
    sortable: true,
    field: 'status',
    title: '单据状态',
    showOrder_status: true,
    soltName: 'status',
    fixed: 'right',
    width: '5%',
  },
])

const cColumnList = ref([
  {
    title: '原料信息',
    field: 'A',
    childrenList: [
      {
        sortable: true,
        field: 'raw_material_code',
        title: '原料编号',
        fixed: 'left',
        width: 150,
      },
      {
        sortable: true,
        field: 'raw_material_name',
        title: '原料名称',
        fixed: 'left',
        width: 100,
      },
      {
        sortable: true,
        field: 'customer_name',
        title: '所属客户',
        width: 100,
      },
      {
        sortable: true,
        field: 'brand',
        title: '原料品牌',
        width: 100,
      },
      {
        sortable: true,
        field: 'craft',
        title: '原料工艺',
        width: 100,
      },
      {
        sortable: true,
        field: 'color_scheme',
        title: '原料色系',
        width: 100,
      },
      {
        sortable: true,
        field: 'supplier_name',
        title: '供应商',
        width: 100,
      },
      {
        sortable: true,
        field: 'batch_num',
        title: '原料批号',
        width: 100,
      },
      {
        sortable: true,
        field: 'level_name',
        title: '原料等级',
        width: 100,
      },
      {
        sortable: true,
        field: 'carton_num',
        title: '装箱单号',
        width: 100,
      },
      {
        sortable: true,
        field: 'fapiao_num',
        title: '发票号',
        width: 100,
        soltName: 'fapiao_num',
      },
      {
        sortable: true,
        field: 'production_date',
        title: '生产日期',
        width: 100,
        isDate: true,
      },
      {
        sortable: true,
        field: 'spinning_type',
        title: '纺纱类型',
        width: 100,
      },

      {
        sortable: true,
        field: 'cotton_origin',
        title: '棉花产地',
        width: 100,
      },
      {
        sortable: true,
        field: 'yarn_origin',
        title: '棉纱产地',
        width: 100,
      },
    ],
  },
  {
    title: '整件',
    field: 'B',
    childrenList: [
      {
        sortable: true,
        field: 'whole_piece_count',
        title: '退货件数',
        width: '5%',
      },
      {
        sortable: true,
        field: 'whole_piece_weight',
        title: '退货件重(kg)',
        width: '5%',
        isWeight: true,
      },
    ],
  },
  {
    title: '散件',
    field: 'C',
    childrenList: [
      {
        sortable: true,
        field: 'bulk_piece_count',
        title: '退货件数',
        width: '5%',
      },
      {
        sortable: true,
        field: 'bulk_weight',
        title: '退货数量',
        width: '5%',
        isWeight: true,
      },
    ],
  },
  {
    title: '',
    field: 'D',
    childrenList: [
      {
        sortable: true,
        field: 'total_weight',
        title: '总退货数量',
        width: '5%',
        isWeight: true,
      },
    ],
  },
  {
    title: '金额信息',
    field: 'E',
    childrenList: [
      {
        sortable: true,
        field: 'other_price',
        title: '其他金额',
        width: '5%',
        isPrice: true,
      },
      {
        sortable: true,
        field: 'unit_price',
        title: '单价',
        width: '5%',
        isUnitPrice: true,
      },
      {
        sortable: true,
        field: 'total_price',
        title: '总金额',
        width: '5%',
        isPrice: true,
      },
    ],
  },
  {
    title: '',
    field: 'F',
    childrenList: [
      {
        sortable: true,
        field: 'remark',
        title: '备注',
        width: 100,
      },
    ],
  },
])

function footerMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '总计'

      if (['whole_piece_count', 'bulk_piece_count'].includes(column.property))
        return `${sumNum(data, column.property)}`

      if (
        ['whole_piece_weight', 'bulk_weight', 'total_weight'].includes(
          column.property,
        )
      ) {
        return `${formatWeightDiv(
          sumNum(data, column.property) as unknown as number,
        )}`
      }
      if (['other_price', 'total_price'].includes(column.property)) {
        return `¥${formatPriceDiv(
          sumNum(data, column.property) as unknown as number,
        )}`
      }
      return null
    }),
  ]
}

const loadingExport = ref(false)
async function handleExport() {
  loadingExport.value = true
  await exportList({
    api: SaleReturnOrderListDownLoad,
    title: '原料采购退货单',
    filterData,
  })
  loadingExport.value = false
}

const tableConfig = ref({
  fieldApiKey: 'RawMaterialSalesReturnOrder_A',
  showSlotNums: true,
  loading: loadingList.value,
  showPagition: true,
  page: pageList,
  size: sizeList,
  total: totalList,
  showCheckBox: true,
  height: '100%',
  showOperate: true,
  operateWidth: '8%',
  showSort: false,
  handleSizeChange,
  handleCurrentChange,
})

const CTableConfig = ref({
  fieldApiKey: 'RawMaterialSalesReturnOrder_B',
  height: '100%',
  operateWidth: '150',
  showSort: false,
  showSpanHeader: true,
  footerMethod,
  loading: loadingDetail.value,
})
</script>

<template>
  <div class="list-page">
    <FildCard title="" :tool-bar="false" class="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input
              v-model="filterData.order_no"
              placeholder="单据编号"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:">
          <template #content>
            <SelectCustomerDialog v-model="filterData.customer_id" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="退货单位:">
          <template #content>
            <SelectBusinessDialog
              v-model="filterData.return_unit_id"
              :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory }"
              api="GetBusinessUnitListApi"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="退货日期:" width="330">
          <template #content>
            <SelectDate v-model="filterData.return_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="filterData.status"
              :multiple="true"
              api="GetAuditStatusEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard class="table-card-full">
      <template #right-top>
        <el-button
          v-has="`RawMaterialSalesReturnOrderAdd`"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handAdd"
        >
          新建
        </el-button>
        <el-button
          style="margin-left: 10px"
          type="primary"
          plain
          :icon="Plus"
          @click="handAdd"
        >
          筛选
        </el-button>
        <BottonExcel
          v-has="`RawMaterialSalesReturnOrder_export`"
          :loading="loadingExport"
          title="导出文件"
          @on-click-excel="handleExport"
        />
      </template>
      <Table
        :config="tableConfig"
        :table-list="dataList.list"
        :column-list="columnList"
      >
        <template #order_no="{ row }">
          <el-link type="primary" @click="showDetail(row)">
            {{
              row.order_no
            }}
          </el-link>
        </template>
        <template #status="{ row }">
          <StatusTag :status="row.status" />
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="`RawMaterialSalesReturnOrderDetail`"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.status === 1 || row.status === 3"
              v-has="`RawMaterialSalesReturnOrderEdit`"
              :underline="false"
              type="primary"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.status === 1"
              v-has="`RawMaterialSalesReturnOrder_pass`"
              :underline="false"
              type="primary"
              @click="handPass(row.id)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.status === 2"
              v-has="`RawMaterialSalesReturnOrder_cancel`"
              type="primary"
              :underline="false"
              @click="handCancel(row.id)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard title="" class="table-card-bottom">
      <Table
        :config="CTableConfig"
        :table-list="dataDetail.items"
        :column-list="cColumnList"
      />
    </FildCard>
  </div>
</template>
