<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import { GetGreyFabricInfoListUseByOthers } from '@/api/greyFabricInformation'
import { debounce, getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import Table from '@/components/Table.vue'

export interface Props {
  id: number
  modelValue: boolean
  defaultSelecteIds?: number[]
}

const props = withDefaults(defineProps<Props>(), {
  id: 0,
  modelValue: false,
})
const emit = defineEmits(['update:modelValue', 'submit'])
const state = reactive({
  filterData: {
    code: '',
    name: '',
    full_name: '',
    grey_fabric_width: '',
    grey_fabric_gram_weight: '',
    total_needle_size: '',
    gray_fabric_color_name: '',
    single_price_min: null,
    single_price_max: null,
    weaving_process: '',
  },
})

const showModal = ref<boolean>(false)
watch(
  () => props.modelValue,
  (show) => {
    showModal.value = show
    if (show) {
      getData()
    }
    else {
      state.filterData = {
        code: '',
        name: '',
        full_name: '',
        grey_fabric_width: '',
        grey_fabric_gram_weight: '',
        total_needle_size: '',
        gray_fabric_color_name: '',
        single_price_min: null,
        single_price_max: null,
      }
    }
  },
)

const columnList = ref([
  {
    title: '坯布编号',
    field: 'code',
  },
  {
    title: '坯布名称',
    field: 'name',
  },
  {
    title: '坯布全称',
    field: 'full_name',
  },
  {
    title: '幅宽',
    field: 'grey_fabric_width_and_unit_name',
  },
  {
    title: '克重',
    field: 'grey_fabric_gram_weight_and_unit_name',
  },
  {
    title: '针寸数',
    field: 'needle_size',
  },
  {
    title: '总针数',
    field: 'total_needle_size',
  },
  {
    title: '织坯颜色',
    field: 'gray_fabric_color_name',
  },
  {
    title: '织造工艺',
    field: 'weaving_process',
  },
  {
    title: '单位',
    field: 'unit_name',
  },
])

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  { deep: true },
)

const { fetchData: fetchDataList, data: dataList, total, page, size, loading, handleSizeChange, handleCurrentChange } = GetGreyFabricInfoListUseByOthers()
async function getData() {
  await fetchDataList(getFilterData(state.filterData))
}

function onClose() {
  emit('update:modelValue', false)
}

const selectList = ref<any[]>()
function handAllSelect({ records }: any) {
  selectList.value = records
}

function handleSelectionChange({ records }: any) {
  selectList.value = records
}

function submit() {
  if (!selectList.value?.length)
    return ElMessage.error('请选择数据')
  emit('submit', selectList.value)
  selectList.value = []
}
const tablesRef = ref()
// 添加点击行时切换选中状态的函数
function handleRowClick({ row }: any) {
  // 获取表格实例
  const tableRef = tablesRef.value.tableRef

  // 判断当前行是否已选中
  const isSelected = tableRef.isCheckedByCheckboxRow(row)

  if (isSelected) {
    // 如果已选中，则取消选中
    tableRef.setCheckboxRow(row, false)
  }
  else {
    // 选中当前行
    tableRef.setCheckboxRow(row, true)
  }

  // 更新选中列表
  selectList.value = tableRef.getCheckboxRecords()
}
const tableConfig = reactive({
  showCheckBox: true,
  handAllSelect,
  handleSelectionChange,
  handleSizeChange,
  handleCurrentChange,
  showPagition: true,
  loading,
  height: '100%',
  size,
  cellClick: (val: any) => handleRowClick(val),
  page,
  total,
  filterStatus: false,
  checkRowKeys: [] as number[],
})

watch(
  () => props.defaultSelecteIds,
  (val) => {
    tableConfig.checkRowKeys = val || []
  },
)
</script>

<template>
  <vxe-modal v-model="showModal" show-footer title="添加坯布商品" width="80vw" height="80vh" :mask="false" :lock-view="false" :esc-closable="true" resize @close="onClose">
    <div class="flex flex-col h-full overflow-hidden">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="坯布编号:">
          <template #content>
            <el-input v-model="state.filterData.code" size="small" placeholder="坯布编号" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布名称:">
          <template #content>
            <el-input v-model="state.filterData.name" size="small" placeholder="坯布名称" clearable />
          </template>
        </DescriptionsFormItem>
        <!--        <DescriptionsFormItem label="坯布全称:"> -->
        <!--          <template #content> -->
        <!--            <el-input v-model="state.filterData.full_name" size="small" placeholder="坯布名称" clearable /> -->
        <!--          </template> -->
        <!--        </DescriptionsFormItem> -->
        <DescriptionsFormItem label="幅宽:">
          <template #content>
            <el-input v-model="state.filterData.grey_fabric_width" size="small" placeholder="幅宽" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="克重:">
          <template #content>
            <el-input v-model="state.filterData.grey_fabric_gram_weight" size="small" placeholder="克重" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="总针数:">
          <template #content>
            <el-input v-model="state.filterData.total_needle_size" size="small" placeholder="总针数" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织坯颜色:">
          <template #content>
            <el-input v-model="state.filterData.gray_fabric_color_name" size="small" placeholder="织坯颜色" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织坯工艺:" width="390">
          <template #content>
            <el-input v-model="state.filterData.weaving_process" size="small" placeholder="织坯工艺" clearable />
          </template>
          <!-- <template v-slot:content>
            <el-input-number style="width: 130px !important" v-model="state.filterData.single_price_min" size="small" controls-position="right" clearable />
            -
            <el-input-number style="width: 130px !important" v-model="state.filterData.single_price_max" size="small" controls-position="right" clearable />
          </template> -->
        </DescriptionsFormItem>
      </div>
      <div class="flex-1 flex flex-col overflow-hidden h-full">
        <Table ref="tablesRef" :config="tableConfig" :table-list="dataList.list" :column-list="columnList">
          <template #creatTime />
        </Table>
      </div>
    </div>
    <template #footer>
      <el-button type="primary" size="small" @click="submit">
        提交
      </el-button>
    </template>
  </vxe-modal>
</template>
