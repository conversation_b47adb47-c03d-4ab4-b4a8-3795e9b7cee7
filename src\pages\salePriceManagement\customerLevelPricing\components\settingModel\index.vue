<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import SelectCustomer from '../selectCustomer/index.vue'
import { AddCustomerSalePriceAdjust, GetCustomerSalePriceAdjust, UpdateCustomerSalePriceAdjust } from '@/api/customerLevelPricing'
import { formatTime, formatUnitPriceDiv, formatUnitPriceMul, formatWeightDiv, formatWeightMul } from '@/common/format'
import { getFilterData } from '@/common/util'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'

export interface Props {
  modelValue: boolean
  id: number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  id: 0,
})

const emit = defineEmits(['update:modelValue', 'success'])

const form = reactive({
  product_id: '',
  product_color_kind_id: '',
  product_color_id: '',
  is_display_price: '',
  sale_level_id: '',
  offset_sale_price: 0,
  offset_weight_error: 0,
  offset_length_cut_sale_price: 0,
  offset_weight_cut_sale_price: 0,
  effective_time: '',
  deadline_time: '',
  customer_items: '' as any,
  remark: '',
})

const fromRules = reactive({
  customer_items: [{ required: true, message: '请选择客户名称', trigger: 'blur' }],
  sale_level_id: [{ required: true, message: '请选择客户等级定价', trigger: 'blur' }],
  effective_time: [{ required: true, message: '请选择生效时间', trigger: 'blur' }],
})

// watch(
//   () => form.product_id,
//   () => {
//     form.product_color_id = ''
//   }
// )
const componentRemoteSearch = reactive({
  finish_product_code: '',
  finish_product_name: '',
  product_color_name: '',
  product_color_code: '',
})

const productColorRef1 = ref()
const productColorRef2 = ref()
function changePro(data: any, stringRef: string) {
  if (stringRef === 'proRef1') {
    componentRemoteSearch.finish_product_name = data.finish_product_name
    form.product_color_id = ''
  }
  else if (stringRef === 'proRef2') {
    componentRemoteSearch.finish_product_code = data.finish_product_code
    form.product_color_id = ''
  }
  if (stringRef === 'productColorRef1') {
    productColorRef1.value.inputLabel = productColorRef1.value.item?.product_color_code
    productColorRef2.value.inputLabel = productColorRef1.value.item?.product_color_name
  }
  else if (stringRef === 'productColorRef2') {
    productColorRef1.value.inputLabel = productColorRef2.value.item?.product_color_code
    productColorRef2.value.inputLabel = productColorRef2.value.item?.product_color_name
  }
}

function handleSearch(refString, inputValue) {
  if (refString === 'proRef1')
    componentRemoteSearch.finish_product_code = inputValue
  else if (refString === 'proRef2')
    componentRemoteSearch.finish_product_name = inputValue

  if (refString === 'productColorRef1')
    componentRemoteSearch.product_color_code = inputValue
  else if (refString === 'productColorRef2')
    componentRemoteSearch.product_color_name = inputValue
}

const state = reactive<any>({
  modalName: '客户等级定价',
})
const selectCustomer = ref<any>([])

const showModal = ref<boolean>(false)
watch(
  () => [props.modelValue, props.id],
  () => {
    showModal.value = props.modelValue
    if (props.modelValue) {
      if (props.id)
        getDetail()
    }
    else {
      selectCustomer.value = []
      form.product_id = ''
      form.product_color_kind_id = ''
      form.product_color_id = ''
      form.is_display_price = ''
      form.sale_level_id = ''
      form.offset_sale_price = 0
      form.offset_weight_error = 0
      form.offset_length_cut_sale_price = 0
      form.offset_weight_cut_sale_price = 0
      form.effective_time = ''
      form.deadline_time = ''
    }
  },
)

const { fetchData: fetchDataDetail, data: dataDetail } = GetCustomerSalePriceAdjust()
async function getDetail() {
  await fetchDataDetail({ id: props.id })
  form.product_id = dataDetail.value.product_id
  form.product_color_kind_id = dataDetail.value.product_color_kind_id
  form.product_color_id = dataDetail.value.product_color_id
  form.is_display_price = dataDetail.value.is_display_price
  form.sale_level_id = dataDetail.value.sale_level_id
  form.offset_sale_price = formatUnitPriceDiv(dataDetail.value.offset_sale_price || 0)
  form.offset_weight_error = formatWeightDiv(dataDetail.value.offset_weight_error || 0)
  form.offset_length_cut_sale_price = formatUnitPriceDiv(dataDetail.value.offset_length_cut_sale_price || 0)
  form.offset_weight_cut_sale_price = formatUnitPriceDiv(dataDetail.value.offset_weight_cut_sale_price || 0)
  form.effective_time = dataDetail.value.effective_time
  form.deadline_time = dataDetail.value.deadline_time
  form.customer_items = dataDetail.value.customer_items
  form.remark = dataDetail.value.remark
  selectCustomer.value = [
    {
      id: dataDetail.value.customer_id,
      name: dataDetail.value.customer_name,
      sale_area_id: dataDetail.value.sale_area_id,
      sale_group_id: dataDetail.value.sale_group_id,
      sale_system_id: dataDetail.value.sale_system_id,
      sale_user_id: dataDetail.value.sale_user_id,
    },
  ]
}

defineExpose({
  state,
})

const showSelect = ref(false)
function openSelect() {
  showSelect.value = true
  selectCustomer.value = [...selectCustomer.value]
}

function getCustomer(data: any) {
  selectCustomer.value = data
  showSelect.value = false
}

function deleteCustomer(index: number) {
  if (props.id)
    return ElMessage.error('无法删除')
  selectCustomer.value.splice(index, 1)
  selectCustomer.value = [...selectCustomer.value]
}

watch(
  () => selectCustomer.value,
  () => {
    form.customer_items = selectCustomer.value || []
  },
)

function onClose() {
  emit('update:modelValue', false)
}

const ruleFormRef = ref()
const { fetchData: fetchDataAdd, success: successAdd, msg: msgAdd } = AddCustomerSalePriceAdjust()
const { fetchData: fetchDataEdit, success: successEdit, msg: msgEdit } = UpdateCustomerSalePriceAdjust()
function submitBind() {
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      form.offset_sale_price = formatUnitPriceMul(form.offset_sale_price)
      form.offset_weight_error = formatWeightMul(form.offset_weight_error)
      form.offset_length_cut_sale_price = formatUnitPriceMul(form.offset_length_cut_sale_price)
      form.offset_weight_cut_sale_price = formatUnitPriceMul(form.offset_weight_cut_sale_price)
      form.effective_time = form.effective_time ? formatTime(form.effective_time) : ''
      form.deadline_time = form.deadline_time ? formatTime(form.deadline_time) : ''
      form.customer_items = form.customer_items?.map((item: any) => {
        return {
          customer_id: item.id,
          customer_name: item.name,
          sale_area_id: item.sale_area_id,
          sale_group_id: item.sale_group_id,
          sale_system_id: item.sale_system_id,
          sale_user_id: item.seller_id,
        }
      })
      if (props.id) {
        const editData: any = { ...form }
        editData.customer_id = dataDetail.value.customer_id
        editData.customer_name = dataDetail.value.customer_name
        editData.sale_area_id = dataDetail.value.sale_area_id
        editData.sale_group_id = dataDetail.value.sale_group_id
        editData.sale_system_id = dataDetail.value.sale_system_id
        editData.sale_user_id = dataDetail.value.sale_user_id
        await fetchDataEdit(getFilterData({ ...editData, id: Number.parseInt(props.id as unknown as string) }, ['customer_items']))
        if (successEdit.value) {
          ElMessage.success('编辑成功')
          emit('update:modelValue', false)
          emit('success')
        }
        else {
          ElMessage.error(msgEdit.value)
        }
      }
      else {
        await fetchDataAdd(getFilterData({ ...form }))
        if (successAdd.value) {
          ElMessage.success('添加成功')
          emit('update:modelValue', false)
          emit('success')
        }
        else {
          ElMessage.error(msgAdd.value)
        }
      }
    }
  })
}
</script>

<template>
  <vxe-modal v-model="showModal" show-footer :title="state.modalName" width="80vw" height="auto" :mask="false" :lock-view="false" :esc-closable="true" @close="onClose">
    <el-form ref="ruleFormRef" :inline="true" :model="form" class="demo-form-inline" label-width="130px" :rules="fromRules">
      <el-row>
        <el-col :span="24">
          <el-form-item label="客户名称" prop="customer_items">
            <el-button v-if="!props.id" :icon="Plus" @click="openSelect">
              添加
            </el-button>
            <el-tag v-for="(tag, index) in selectCustomer" :key="tag.id" class="mx-1" closable @close="deleteCustomer(index)">
              {{ tag.name }}
            </el-tag>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="成品编号">
            <SelectProductDialog
              v-model="form.product_id"
              :label-name="componentRemoteSearch.finish_product_code"
              field="finish_product_code"
              :query="{
                finish_product_code: componentRemoteSearch.finish_product_code,
              }"
              @on-input="inputValue => (componentRemoteSearch.finish_product_code = inputValue)"
              @change-value="(data) => changePro(data, 'proRef1')"
            />
            <!--            <SelectDialog -->
            <!--              ref="proRef1" -->
            <!--              v-model="form.product_id" -->
            <!--              label-field="finish_product_code" -->
            <!--              :query="{ -->
            <!--                finish_product_code: componentRemoteSearch.finish_product_code, -->
            <!--              }" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  title: '成品名称', -->
            <!--                  colGroupHeader: true, -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      title: '成品名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '成品编号', -->
            <!--                  colGroupHeader: true, -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      title: '成品编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '成品编号', -->
            <!--                }, -->
            <!--              ]" -->
            <!--              @on-input="inputValue => handleSearch('proRef1', inputValue)" -->
            <!--              @change-value="changePro('proRef1')" -->
            <!--            /> -->
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="成品名称">
            <SelectProductDialog
              v-model="form.product_id"
              :label-name="componentRemoteSearch.finish_product_name"
              field="finish_product_name"
              :query="{
                finish_product_name: componentRemoteSearch.finish_product_name,
              }"
              @on-input="inputValue => (componentRemoteSearch.finish_product_name = inputValue)"
              @change-value="(data) => changePro(data, 'proRef2')"
            />
            <!--            <SelectDialog -->
            <!--              ref="proRef2" -->
            <!--              v-model="form.product_id" -->
            <!--              :query="{ -->
            <!--                finish_product_name: componentRemoteSearch.finish_product_name, -->
            <!--              }" -->
            <!--              label-field="finish_product_full_name" -->
            <!--              value-field="id" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品名称', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      title: '成品名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品编号', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      title: '成品编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  title: '成品名称', -->
            <!--                }, -->
            <!--              ]" -->
            <!--              @on-input="inputValue => handleSearch('proRef2', inputValue)" -->
            <!--              @change-value="changePro('proRef2')" -->
            <!--            /> -->
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="颜色类别">
            <SelectComponents v-model="form.product_color_kind_id" style="width: 200px" api="GetTypeFinishedProductColorEnumList" label-field="name" value-field="id" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="颜色编号">
            <SelectDialog
              ref="productColorRef1"
              v-model="form.product_color_id"
              :query="{
                finish_product_id: form.product_id,
                product_color_code: componentRemoteSearch.product_color_code,
              }"
              :disabled="!form.product_id"
              label-field="product_color_code"
              style="width: 200px"
              api="GetFinishProductColorDropdownList"
              :column-list="[
                {
                  field: 'product_color_name_group',
                  colGroupHeader: true,
                  title: '颜色名称',
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'product_color_name',
                      title: '颜色名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'product_color_code_group',
                  colGroupHeader: true,
                  title: '颜色编号',
                  minWidth: '6%',
                  childrenList: [
                    {
                      field: 'product_color_code',
                      title: '颜色编号',
                      minWidth: '6%',
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'product_color_code',
                  title: '颜色编号',
                },
              ]"
              @change-value="changePro('productColorRef1')"
              @on-input="inputValue => handleSearch('productColorRef1', inputValue)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="颜色名称">
            <SelectDialog
              ref="productColorRef2"
              v-model="form.product_color_id"
              :query="{
                finish_product_id: form.product_id,
                product_color_name: componentRemoteSearch.product_color_name,
              }"
              :disabled="!form.product_id"
              label-field="product_color_name"
              value-field="id"
              style="width: 200px"
              api="GetFinishProductColorDropdownList"
              :column-list="[
                {
                  field: 'product_color_name_group',
                  title: '颜色名称',
                  colGroupHeader: true,
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'product_color_name',
                      title: '颜色名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'product_color_code_group',
                  title: '颜色编号',
                  colGroupHeader: true,
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'product_color_code',
                      title: '颜色编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'product_color_name',
                  title: '颜色编号',
                },
              ]"
              @change-value="changePro('productColorRef2')"
              @on-input="inputValue => handleSearch('productColorRef2', inputValue)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="码单是否显示单价">
            <el-switch v-model="form.is_display_price" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="客户等级定价" prop="sale_level_id">
            <SelectComponents v-model="form.sale_level_id" style="width: 200px" api="GetSaleLevelDropdownList" label-field="name" value-field="id" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="大货单价优惠">
            <vxe-input v-model="form.offset_sale_price" style="width: 200px" type="float" :digits="4" class="input_class">
              <template #suffix>
                元/kg
              </template>
            </vxe-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="大货空差减重">
            <vxe-input v-model="form.offset_weight_error" style="width: 200px" type="float" :digits="4" class="input_class">
              <template #suffix>
                kg
              </template>
            </vxe-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="剪版单价优惠">
            <vxe-input v-model="form.offset_length_cut_sale_price" style="width: 200px" type="float" :digits="4" class="input_class">
              <template #suffix>
                元/m
              </template>
            </vxe-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="剪版单价优惠">
            <vxe-input v-model="form.offset_weight_cut_sale_price" style="width: 200px" type="float" :digits="4" class="input_class">
              <template #suffix>
                元/kg
              </template>
            </vxe-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="生效时间" prop="effective_time">
            <!-- <SelectDate type="datetime" style="width: 200px" v-model="form.effective_time" /> -->
            <el-date-picker v-model="form.effective_time" type="datetime" placeholder="生效时间" format="YYYY/MM/DD HH:mm:ss" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-col :span="8">
        <el-form-item label="截止时间">
          <!-- <SelectDate type="datetime" style="width: 200px" v-model="form.deadline_time" /> -->
          <el-date-picker v-model="form.deadline_time" type="datetime" placeholder="截止时间" format="YYYY/MM/DD HH:mm:ss" />
        </el-form-item>
      </el-col>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注">
            <vxe-textarea v-model="form.remark" style="width: 500px" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="submitBind">
        提交
      </el-button>
    </template>
  </vxe-modal>
  <SelectCustomer v-model="showSelect" :default-data="selectCustomer" @submit="getCustomer" />
</template>

<style lang="scss" scoped>
.input_class {
  ::v-deep(.vxe-input--suffix) {
    width: 60px;
    height: 32px;
    top: 1px;
    text-align: center;
    border-left: 1px solid #dcdfe6;
    background-color: #f5f7fa;
    cursor: pointer;
  }
}

.el-row {
  margin: 10px 0;
}
</style>
