<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { CustomTableColumn, FineCodeTable, FinfshProductColor, FinishProductColorColumn } from './pullDown'
import GridTable from '@/components/GridTable'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import { getDetailReportList } from '@/api/inventory/finishedProduct/finishedProductReport'
import { getFilterData } from '@/common/util'
import { processDataOut } from '@/common/handBinary'
import { formatDate } from '@/common/format'
import { vueEffect } from '@/use/vueEffect'
import { WarehouseTypeIdEnum } from '@/common/enum'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'

const tableRef = ref() // 当前表格的实例
const getDataLoading = ref(false) // 获取表格数据的 loading
const query = ref<any>({ // 获取表格的请求参数
  warehouse_id: [], // 仓库id
  product_id: '', // 成品id
  product_color_id: '', // 颜色id
  dyelot_number: '', // 缸号
  customer_id: '', // 所属客户
  warehouse_bin_id: '', // 仓位
  create_time: [], // 创建时间
  audit_time: [], // 审核时间
  check_time: [], // 盘点时间
  warehouse_in_order_no: '', // 单号
  product_level_id: '', //  成品等级
  page: 1,
  size: 50,
  type: 'xmpdbb',

})
const { fetchData: getFineCode, total } = getDetailReportList()
const tableData = ref([]) // 暂时存储表格数据
// 用于筛选的临时存储变量
const componentRemoteSearch = ref({
  finish_product_code: '', // 临时的成品编号
  finish_product_name: '', // 临时的成品名称
  color_name: '', // 颜色编号
  color_code: '', // 颜色名称
  customer_name: '', // 所属客户
})
// 选中成品编号或者成品名称
function changeProductSelect(val: any) {
  query.value.product_id = val?.id || '' // 成品 id
  componentRemoteSearch.value.finish_product_code = val?.finish_product_code || ''
  componentRemoteSearch.value.finish_product_name = val?.finish_product_name || ''

  query.value.product_color_id = ''
  componentRemoteSearch.value.color_name = ''
  componentRemoteSearch.value.color_code = ''
}
// 选择颜色
function changeColor(val: any) {
  query.value.product_color_id = val?.id || ''
  componentRemoteSearch.value.color_code = val?.product_color_code || ''
  componentRemoteSearch.value.color_name = val?.product_color_name || ''
}
// 选择所属客户
function changeCustomer(val: any) {
  query.value.customer_id = val?.id || ''
  componentRemoteSearch.value.customer_name = val?.name || ''
}
// 分页配置
const elPaginationConfig = computed(() => ({
  defaultPageSize: 50,
  pageSizes: [50, 100, 500, 1000],
  page: query.value.page,
  size: query.value.size,
  pageLayout: 'total, sizes, prev, pager, next, jumper',
  total: total.value,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
}))
// 当页码发生变化的时候
function handleSizeChange(val: number) {
  query.value.page = 1
  query.value.size = val
}
// 当每页数量发生变化的时候
function handleCurrentChange(val: number) {
  query.value.page = val
}
// 获取表格数据
async function getData() {
  getDataLoading.value = true
  const newQuuerData = {
    ...query.value,
    create_time: query.value.create_time && query.value.create_time.length && query.value.create_time.length ? formatDate(query.value.create_time[0]) : '',
    create_time_end: query.value.create_time && query.value.create_time.length && query.value.create_time.length ? formatDate(query.value.create_time[1]) : '',
    audit_time: query.value.audit_time && query.value.audit_time.length && query.value.audit_time.length ? formatDate(query.value.audit_time[0]) : '',
    audit_time_end: query.value.audit_time && query.value.audit_time.length && query.value.audit_time.length ? formatDate(query.value.audit_time[1]) : '',
    check_time: query.value.check_time && query.value.check_time.length && query.value.check_time.length ? formatDate(query.value.check_time[0]) : '',
    check_time_end: query.value.check_time && query.value.check_time.length && query.value.check_time.length ? formatDate(query.value.check_time[1]) : '',
    warehouse_id: query.value.warehouse_id.length ? query.value.warehouse_id.join(',') : null,
  }
  const result = await getFineCode(getFilterData(newQuuerData))

  if (result.success && result.total) {
    const temData: any = processDataOut(result.data.list)
    tableData.value = temData
  }
  else {
    tableData.value = []
  }
  getDataLoading.value = false
}
onMounted(async () => {
  await getData()
})

vueEffect(async () => {
  await getData()
}, [query.value], 300, false, 'debounce')
</script>

<template>
  <!-- 筛选信息 -->
  <FildCard title="" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="仓库名称:">
        <template #content>
          <SelectComponents v-model="query.warehouse_id" :query="{ warehouse_type_id: WarehouseTypeIdEnum.finishProduction }" api="GetPhysicalWarehouseDropdownList" multiple label-field="name" value-field="id" clearable />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="成品编号:">
        <template #content>
          <SelectProductDialog
            v-model="query.product_id"
            :label-name="componentRemoteSearch.finish_product_code"
            field="finish_product_code"
            :query="{ finish_product_code: componentRemoteSearch.finish_product_code }"
            @on-input="(val) => (componentRemoteSearch.finish_product_code = val)"
            @change-value="changeProductSelect"
          />
          <!--          <SelectDialog -->
          <!--            v-model="query.product_id" -->
          <!--            label-field="finish_product_code" -->
          <!--            :label-name="componentRemoteSearch.finish_product_code" -->
          <!--            :query="{ finish_product_code: componentRemoteSearch.finish_product_code }" -->
          <!--            api="GetFinishProductDropdownList" -->
          <!--            :column-list="FinishedProduct" -->
          <!--            :table-column="[FinishProductTableColumn[0]]" -->
          <!--            @on-input="(val) => (componentRemoteSearch.finish_product_code = val)" -->
          <!--            @change-value="changeProductSelect" -->
          <!--          /> -->
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="成品名称:">
        <template #content>
          <SelectProductDialog
            v-model="query.product_id"
            :label-name="componentRemoteSearch.finish_product_name"
            field="finish_product_name"
            :query="{ finish_product_name: componentRemoteSearch.finish_product_name }"
            @on-input="(val) => (componentRemoteSearch.finish_product_name = val)"
            @change-value="changeProductSelect"
          />
          <!--          <SelectDialog -->
          <!--            v-model="query.product_id" -->
          <!--            label-field="finish_product_name" -->
          <!--            :label-name="componentRemoteSearch.finish_product_name" -->
          <!--            :query="{ finish_product_name: componentRemoteSearch.finish_product_name }" -->
          <!--            api="GetFinishProductDropdownList" -->
          <!--            :column-list="FinishedProduct" -->
          <!--            :table-column="[FinishProductTableColumn[1]]" -->
          <!--            @on-input="(val) => (componentRemoteSearch.finish_product_name = val)" -->
          <!--            @change-value="changeProductSelect" -->
          <!--          /> -->
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="颜色编号:">
        <template #content>
          <SelectDialog
            v-model="query.product_color_id"
            :disabled="!query.product_id"
            :label-name="componentRemoteSearch.color_code"
            :query="{
              finish_product_id: query.product_id,
              product_color_code: componentRemoteSearch.color_code,
            }"
            :column-list="FinfshProductColor"
            :table-column="[FinishProductColorColumn[0]]"
            api="GetFinishProductColorDropdownList"
            label-field="product_color_code"
            @on-input="(val) => (componentRemoteSearch.color_code = val)"
            @change-value="changeColor"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="颜色名称:">
        <template #content>
          <SelectDialog
            v-model="query.product_color_id"
            :disabled="!query.product_id"
            :label-name="componentRemoteSearch.color_name"
            :query="{
              finish_product_id: query.product_id,
              product_color_name: componentRemoteSearch.color_name,
            }"
            :column-list="FinfshProductColor"
            :table-column="[FinishProductColorColumn[1]]"
            api="GetFinishProductColorDropdownList"
            label-field="product_color_name"
            @on-input="(val) => (componentRemoteSearch.color_name = val)"
            @change-value="changeColor"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="缸号:">
        <template #content>
          <vxe-input v-model="query.dyelot_number" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="所属客户:">
        <template #content>
          <!-- :query="{ sale_system_id: state.sale_system_id, name: componentRemoteSearch.customer_name }" -->
          <SelectDialog
            v-model="query.customer_id"
            label-field="name"
            :label-name="componentRemoteSearch.customer_name"
            :query="{ name: componentRemoteSearch.customer_name }"
            api="GetCustomerEnumList"
            :column-list="CustomTableColumn"
            @on-input="val => (componentRemoteSearch.customer_name = val)"
            @change-value="changeCustomer"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="仓位:">
        <template #content>
          <SelectComponents
            v-model="query.warehouse_bin_id"
            :query="{ physical_warehouse_id: query.warehouse_id }"
            api="GetPhysicalWarehouseBinListEnum"
            label-field="name"
            value-field="id"
            clearable
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="创建时间:" copies="2">
        <template #content>
          <SelectDate v-model="query.create_time" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="审核时间:" copies="2">
        <template #content>
          <SelectDate v-model="query.audit_time" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="盘点时间:" copies="2">
        <template #content>
          <SelectDate v-model="query.check_time" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="单号:">
        <template #content>
          <vxe-input v-model="query.warehouse_in_order_no" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="成品等级:">
        <template #content>
          <SelectComponents
            v-model="query.product_level_id"
            placeholder="成品等级"
            api="GetInfoBaseFinishedProductLevelEnumList"
            label-field="name"
            value-field="id"
            clearable
          />
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <!-- 表格 -->
  <FildCard title="" class="flex-1 flex flex-col overflow-y-hidden">
    <!-- <GroupingTable ref="tableRef" :columns="FineCodeTable" :data="testData" :config="tableConfig" :pagination="paginationConfig" /> -->
    <GridTable
      ref="tableRef"
      :columns="FineCodeTable"
      :data="tableData"
      :config="{
        loading: getDataLoading,
        filterConfig: {
          showIcon: false,
        },
      }"
      height="100%"
      show-pagition
      :el-pagination-config="elPaginationConfig"
    />
  </FildCard>
</template>

<style></style>
