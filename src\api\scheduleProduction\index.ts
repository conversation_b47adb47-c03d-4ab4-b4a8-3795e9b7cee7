import { useRequest } from '@/use/useRequest'

// 获取生产排产单列表
export function GetProductionScheduleOrderList() {
  return useRequest<Api.GetProductionScheduleOrderList.Request, ResponseList<Api.GetProductionScheduleOrderList.Response>>({
    url: '/admin/v1/produce/productionScheduleOrder/getList',
    method: 'get',
  })
}
// 获取打印状态枚举列表
export function EnumPrintStatus() {
  return useRequest<void, ResponseList<Api.Enum.Response>>({
    url: '/admin/v1/produce/printStatus',
    method: 'get',
  })
}

// 获取生产排产单细码列表
export function GetFineCodeList() {
  return useRequest<Api.GetFineCodeList.Request, ResponseList<Api.GetFineCodeList.Response>>({
    url: '/admin/v1/produce/productionScheduleOrder/getFineCodeList',
    method: 'get',
  })
}

// 打印排产单细码
export function PrintFineCode() {
  return useRequest<Api.PrintFineCode.Request, Api.PrintFineCode.Response>({
    url: '/admin/v1/produce/productionScheduleOrder/printFineCode',
    method: 'post',
  })
}
