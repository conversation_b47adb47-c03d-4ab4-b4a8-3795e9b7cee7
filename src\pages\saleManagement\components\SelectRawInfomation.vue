<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import Table from '@/components/Table.vue'
import { raw_material_list_dropdown } from '@/api/rawInformation'

const emits = defineEmits(['handleSure'])

const state = reactive<any>({
  showModal: false,
  modalName: '根据资料添加',
  multipleSelection: [],
})

const { fetchData, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = raw_material_list_dropdown()

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  height: '100%',
  showSort: false,
  filterStatus: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

async function getData() {
  await fetchData({ show_yarn: true })
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handCancel() {
  state.showModal = false
}

async function handleSure() {
  if (!state.multipleSelection.length)
    return ElMessage.error('至少选择一条数据')

  emits('handleSure', state.multipleSelection)
}

const columnList = ref([
  {
    field: 'code',
    title: '原料编号',
    width: 125,
    fixed: 'left',
    sortable: true,
  },
  {
    field: 'name',
    title: '原料名称',
    width: 125,
    fixed: 'left',
    sortable: true,
  },
  {
    field: 'type_name',
    title: '原料类型',
    width: 125,
    sortable: true,
  },
  {
    field: 'ingredient',
    title: '原料成分',
    width: 125,
    sortable: true,
  },
  {
    field: 'craft',
    title: '原料工艺',
    width: 125,
    sortable: true,
  },
  {
    field: 'count',
    title: '原料支数',
    width: 125,
    sortable: true,
  },
  {
    field: 'value',
    title: '用途',
    width: 125,
    sortable: true,
  },
  {
    field: 'supplier_name',
    title: '供应商',
    width: 125,
  },
  {
    field: 'bleach',
    title: '漂染性',
    width: 125,
    sortable: true,
  },
  {
    sortable: true,
    field: 'unit_name',
    title: '单位',
    width: 125,
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
    width: 125,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    width: 125,
  },
  {
    field: 'create_time',
    title: '创建时间',
    isDate: true,
    width: 125,
    sortable: true,
  },
  {
    field: 'update_user_name',
    title: '最后修改人',
    width: 125,
    sortable: true,
  },
  {
    field: 'update_time',
    title: '最后修改时间',
    isDate: true,
    width: 125,
    sortable: true,
  },

  {
    field: '',
    title: '状态',
    soltName: 'status',
    showStatus: true,
    fixed: 'right',
    width: 125,
    sortable: true,
  },
])

defineExpose({ state })
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="80vw" height="80vh" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList" />
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
