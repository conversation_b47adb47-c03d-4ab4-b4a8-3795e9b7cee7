<script lang="ts" setup>
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { GetBusinessUnitListApi } from '@/api/purchaseGreyFarbric'
import { debounce, getFilterData } from '@/common/util'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'

export interface Props {
  defaultId: number[]
}

const props = withDefaults(defineProps<Props>(), {
  defaultId: () => [],
})

const emits = defineEmits(['handleSure'])

onMounted(() => {
  getData()
})
const filterData = reactive({
  sale_system_id: '',
  nop: '',
})
const { fetchData, data } = GetBusinessUnitListApi()
async function getData() {
  await fetchData(getFilterData(filterData))
}
const tablesRef = ref()

const state = reactive({
  tableData: [{ id: 1 }],
  showModal: false,
  modalName: '选择往来单位',
})

const selectIds = ref<number[]>([])

function handAllSelect({ records }: any) {
  selectIds.value = records?.map((item: any) => {
    return item.id
  })
}

function handleSelectionChange({ records }: any) {
  selectIds.value = records?.map((item: any) => {
    return item.id
  })
}

watch(() => filterData, debounce(getData, 400), { deep: true })

watch(
  () => [state.showModal, data.value.list, props.defaultId],
  () => {
    if (props.defaultId.length && state.showModal) {
      const res = data.value.list?.filter((item: any) => {
        return props.defaultId.includes(item.id)
      })
      nextTick(() => {
        setTimeout(() => {
          tablesRef.value.tableRef.setCheckboxRow(res, true)
        }, 200)
      })
    }
  },
)

const columnList = ref([
  {
    field: 'name',
    title: '往来单位名称',
  },
  {
    field: 'phone',
    title: '联系电话',
  },
  {
    field: 'sale_system_name',
    title: '营销体系',
  },
  {
    field: 'unit_type_name',
    title: '往来单位类型',
  },
])

function handCancel() {
  state.showModal = false
}

async function handleSure() {
  emits('handleSure', selectIds.value)
}

defineExpose({
  state,
})

const tableConfig = ref({
  loading: false,
  showPagition: true,
  page: 1,
  size: 10,
  total: 0,
  showCheckBox: true,
  height: '500',
  showSlotNums: true,
  handAllSelect,
  handleSelectionChange,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="1200" height="750" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <el-descriptions :column="4" border size="small" class="mb-[20px]">
      <el-descriptions-item label="客户/电话">
        <el-input v-model="filterData.nop" />
      </el-descriptions-item>
      <el-descriptions-item label="营销体系：">
        <SelectComponents v-model="filterData.sale_system_id" label-field="name" value-field="id" api="GetSaleSystemDropdownListApi" />
      </el-descriptions-item>
      <el-descriptions-item label="往来单位类型：">
        <SelectOptions v-model="filterData.sale_system_id" label-field="name" value-field="id" api="GetBusinessUnitListApi" />
      </el-descriptions-item>
    </el-descriptions>

    <Table ref="tablesRef" :config="tableConfig" :table-list="data.list" :column-list="columnList" />
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
