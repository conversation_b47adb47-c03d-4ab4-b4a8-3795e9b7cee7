<script lang="ts" setup name="GreySalePlanAdd">
import { computed, onActivated, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import Table from '@/components/Table.vue'
import FildCard from '@/components/FildCard.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { formatDate, formatPriceDiv, sumNum } from '@/common/format'
import StatusColumn from '@/components/StatusColumn/index.vue'
import { orderStatusConfirmBox } from '@/common/util'
import {
  getSaleProductPlanOrder,
  updateSaleProductPlanOrderAuditStatusCancel,
  updateSaleProductPlanOrderAuditStatusPass,
  updateSaleProductPlanOrderAuditStatusReject,
  updateSaleProductPlanOrderAuditStatusWait,
} from '@/api/productSalePlan'
import { processDataOut } from '@/common/handBinary'
import ImageFileCard from '@/components/UploadFile/FileCard/index.vue'
import TextureMapWall from '@/components/TextureMapWall/index.vue'
import { OrderAuditStatusEnum } from '@/enum/orderEnum'
import { UpdateSaleProductPlanOrderVocherUrl } from '@/api/sale/saleProductPlanOrder'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import AddressCard from '@/components/AddressCard/index.vue'

const state = reactive<any>({
  tableList: [],
  multipleSelection: [],
  use_yarnList: [],
})

const { fetchData: getFetch, data } = getSaleProductPlanOrder()

const route = useRoute()
// 是否编辑凭证
const editCertificate = ref(false)

onActivated(() => {
  editCertificate.value = false
  getInfomation()
})
onMounted(() => {
  editCertificate.value = false
  getInfomation()
})
async function getInfomation() {
  await getFetch({ id: route.query.id })
  data.value = processDataOut(data.value)
  const fileList = data.value?.texture_url ? data.value.texture_url.split(',') : []
  data.value.fileList = fileList
}
// 计算属性处理地址数据
const addressDataProps = computed(() => {
  if (!data.value && !data.value.id)
    return null
  return {
    id: 0,
    location: data.value.location ? data.value.location.split(',') : [], // 省市区地址
    address: data.value.receipt_address, // 详细地址
    biz_uint_id: data.value.customer_id, // 客户id
    is_default: false, // 是否默认地址 (没有默认选择)
    logistics_company: data.value.logistics_company, // 物流公司
    logistics_area: data.value.logistics_area, // 物流区域
    contact_name: data.value.contact_name, // 联系人名称
    phone: data.value.customer_phone, // 手机号
    print_tag: data.value.print_tag, // 打印打印标签
    name: data.value.process_factory, // 加工厂名称
  }
})
const tableConfig = ref({
  showSlotNums: true,
  filterStatus: false,
  //   footerMethod: (val: any) => FooterMethod(val),
  cellClick: (val: any) => cellClick(val),
  footerMethod: (val: any) => FooterMethod(val),
  fieldApiKey: 'RawSalePlanDetail_A',
  showSpanHeader: true,
})

const tableConfig_ls = ref({
  showSlotNums: true,
  filterStatus: false,
  footerMethod: (val: any) => FooterMethod(val),
  fieldApiKey: 'RawSalePlanDetail_B',
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll', 'piece_count'].includes(column.property))
        return `${(sumNum(data, column.property) as any)}`

      if (['weight'].includes(column.property))
        return `${(sumNum(data, 'weight') as any)} kg`

      if (['other_price'].includes(column.property))
        return `￥${(sumNum(data, 'other_price') as any)}`

      if (['total_price'].includes(column.property))
        return `￥${(sumNum(data, 'total_price') as any)}`

      if (['yarn_ratio'].includes(column.property))
        return `${(sumNum(data, 'yarn_ratio') as any)}%`

      if (['yarn_loss'].includes(column.property))
        return `${(sumNum(data, 'yarn_loss') as any)}%`

      if (['use_yarn_quantity'].includes(column.property))
        return `${(sumNum(data, 'use_yarn_quantity') as any)} kg`

      return null
    }),
  ]
}

function cellClick(val: any) {
  state.use_yarnList = val.row?.material_ratio?.map((item: any) => {
    // item.yarn_loss = formatPriceDiv(item.material_loss)
    // item.yarn_ratio = formatPriceDiv(item.material_ratio)
    // item.total_yarn = 0
    item.weight = Number(val.row?.weight)
    item.record_id = val.row.record_id
    return item
  })
}

async function updateStatus(audit_status: number) {
  const id: any = route.query.id?.toString()
  if (audit_status === 4)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' }, api: updateSaleProductPlanOrderAuditStatusCancel })

  if (audit_status === 3)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' }, api: updateSaleProductPlanOrderAuditStatusReject })

  if (audit_status === 2)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' }, api: updateSaleProductPlanOrderAuditStatusPass })

  if (audit_status === 1)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' }, api: updateSaleProductPlanOrderAuditStatusWait })

  getInfomation()
}

// 变更
const router = useRouter()
function changeOrder() {
  // 有待审核的变更单
  if (data.value?.sale_product_change_order_id) {
    router.push({
      name: 'SalePlanChangeEdit',
      query: { id: data.value.sale_product_change_order_id, planType: data.value.plan_type },
    })
    return
  }
  router.push({
    name: 'SalePlanChangeAdd',
    query: { id: data.value.id, planType: data.value.plan_type },
  })
}

// 更新凭证信息
const { fetchData: updateData, success: editSuccess, msg: editMsg } = UpdateSaleProductPlanOrderVocherUrl()
async function updateImageList() {
  const query = {
    id: data.value?.id,
    texture_url: data.value.fileList ? data.value.fileList.join(',') : '',
  }
  await updateData(query)
  if (!editSuccess.value)
    ElMessage.error(editMsg.value)
}
const columnList = ref([
  {
    title: '',
    childrenList: [
      {
        field: 'detail_order_no',
        title: '订单编号',
        minWidth: 150,
      },
      {
        field: 'raw_material_code',
        title: '原料编号',
        minWidth: 100,
      },
      {
        field: 'raw_material_name',
        title: '原料名称',
        minWidth: 100,
      },
      {
        field: 'type_name',
        title: '原料类型',
        minWidth: 100,
      },
      {
        field: 'ingredient',
        title: '原料成分',
        minWidth: 100,
      },
      {
        field: 'craft',
        title: '原料工艺',
        minWidth: 100,
      },
      {
        field: 'count',
        title: '原料支数',
        minWidth: 100,
      },
      {
        field: 'measurement_unit_name',
        title: '单位',
        minWidth: 100,
      },
      {
        field: 'raw_material_color_code',
        title: '颜色编号',
        minWidth: 100,
      },
      {
        field: 'raw_material_color_name',
        title: '颜色名称',
        minWidth: 100,
      },
      {
        field: 'weight',
        title: '数量',
        minWidth: 100,
        soltName: 'weight',
      },
      {
        field: 'piece_count',
        title: '件数',
        minWidth: 100,
        soltName: 'piece_count',
      },
      {
        field: 'upper_limit',
        title: '上限',
        minWidth: 100,
      },
      {
        field: 'lower_limit',
        title: '下限',
        minWidth: 100,
      },
      {
        field: 'unit_price',
        title: '单价',
        minWidth: 100,
        soltName: 'unit_price',
      },
      {
        field: 'other_price',
        title: '其他金额',
        minWidth: 100,
        soltName: 'other_price',
      },
      {
        field: 'total_price',
        title: '总金额',
        minWidth: 100,
        soltName: 'total_price',
      },
      {
        field: 'remark',
        title: '备注',
        minWidth: 100,
      },
    ],
  },
  // {
  //   title: '已计划原料现货数量',
  //   childrenList: [
  //     {
  //       field: '',
  //       title: '数量',
  //       minWidth: 100,
  //       isWeight: true,
  //     },
  //   ],
  // },
  // {
  //   title: '已计划原料采购数量',
  //   childrenList: [
  //     {
  //       field: '',
  //       title: '数量',
  //       minWidth: 100,
  //       isWeight: true,
  //     },
  //   ],
  // },
  // {
  //   title: '已计划原料加工数量',
  //   childrenList: [
  //     {
  //       field: '',
  //       title: '数量',
  //       minWidth: 100,
  //       isWeight: true,
  //     },
  //   ],
  // },
])

const columnList_ls = ref([
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 100,
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 100,
  },
  {
    field: 'type_name',
    title: '原料类型',
    minWidth: 100,
  },
  {
    field: 'ingredient',
    title: '原料成分',
    minWidth: 100,
  },
  {
    field: 'craft',
    title: '原料工艺',
    minWidth: 100,
  },
  {
    field: 'count',
    title: '原料支数',
    minWidth: 100,
  },
  {
    field: 'measurement_unit_name',
    title: '单位',
    minWidth: 100,
  },
  {
    field: 'yarn_loss',
    title: '用纱损耗',
    minWidth: 100,
    soltName: 'yarn_loss',
  },
  {
    field: 'yarn_ratio',
    title: '用纱比例',
    minWidth: 100,
    soltName: 'yarn_ratio',
  },
  {
    field: 'use_yarn_quantity',
    title: '用纱量',
    minWidth: 100,
    soltName: 'use_yarn_quantity',
  },
  //   {
  //     field: '',
  //     title: '已计划用纱现货数量',
  //     minWidth: 100,
  //     isWeight: true,
  //   },
  //   {
  //     field: '',
  //     title: '已计划用纱采购数量',
  //     minWidth: 100,
  //     isWeight: true,
  //   },
  //   {
  //     field: '',
  //     title: '已计划用纱加工数量',
  //     minWidth: 100,
  //     isWeight: true,
  //   },
])
</script>

<template>
  <StatusColumn
    show-change-btn
    :order_no="data.order_no"
    :order_id="data.id"
    :status="data.audit_status"
    :status_name="data.audit_status_name"
    permission_wait_key="ProductSalePlanWait"
    permission_reject_key="ProductSalePlanReject"
    permission_pass_key="ProductSalePlanPass"
    permission_cancel_key="ProductSalePlanCancel"
    permission_change_key="ProductSalePlanChange"
    permission_edit_key="RawSalePlanEdit"
    edit_router_name="RawSalePlanEdit"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
    @on-modification="changeOrder"
  >
    <template #custom>
      <div class="mr-[90px] ml-[40px] justify-center flex flex-col items-center">
        <div class="mb-[10px] font-medium">
          销售类型
        </div>
        <div class="text-[14px] text-[#0077ff]">
          {{ data.plan_type_name }}
        </div>
      </div>
    </template>
    <template #print>
      <PrintPopoverBtn
        :id="route.query.id"
        print-btn-text="打印"
        :print-type="PrintType.PrintTemplateTypeSaleOrder"
        :data-type="PrintDataType.Product"
        api="getSaleProductPlanOrder"
      />
    </template>
  </StatusColumn>
  <FildCard title="基础信息" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="客户编号:">
        <template #content>
          {{ data?.customer_code }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="客户名称:">
        <template #content>
          {{ data?.customer_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="营销体系:">
        <template #content>
          {{ data?.sale_system_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="订单日期:">
        <template #content>
          {{ formatDate(data?.order_time) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="交货日期:">
        <template #content>
          {{ formatDate(data?.receipt_time) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="销售员:">
        <template #content>
          {{ data?.sale_user_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="合同编号:">
        <template #content>
          {{ data?.voucher_number }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="合同定金:">
        <template #content>
          {{ data?.deposit }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="结算方式:">
        <template #content>
          {{ data?.settle_method_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="是否含税:">
        <template #content>
          {{ data?.is_with_tax_rate ? '是' : '否' }}
          <span v-if="data.is_with_tax_rate">，税率{{ data?.sale_tax_rate }}%
          </span>
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="单据备注:">
        <template #content>
          {{ data?.internal_remark }}
        </template>
      </DescriptionsFormItem>
    </div>
    <div class="m-[10px]">
      <AddressCard
        v-if="data.id"
        type="Detail"
        :is-show-delivery="false"
        :sale-shipment-type-code="data.sale_shipment_type_code"
        :sale-system-ids="data.sale_system_id"
        :customer-ids="data.customer_id"
        :customer-name="data.customer_name"
        :address-data="addressDataProps"
      />
    </div>
  </FildCard>
  <FildCard title="原料信息" class="mt-[5px]">
    <Table :config="tableConfig" :table-list="data?.item_data" :column-list="columnList">
      <template #piece_count="{ row }">
        {{ row.piece_count }}
        <span v-if="row.change_piece_count > 0" class="text-red-600">(↑{{ row.change_piece_count }})</span>
        <span v-if="row.change_piece_count < 0" class="text-green-600">(↓{{ row.change_piece_count }})</span>
      </template>

      <template #weight="{ row }">
        {{ row.weight }}
        <span v-if="row.change_weight > 0" class="text-red-600">(↑{{ row.change_weight }})</span>
        <span v-if="row.change_weight < 0" class="text-green-600">(↓{{ row.change_weight }})</span>
      </template>

      <template #total_price="{ row }">
        {{ row.total_price }}
        <span v-if="row.change_total_price > 0" class="text-red-600">(↑{{ row.change_total_price }})</span>
        <span v-if="row.change_total_price < 0" class="text-green-600">(↓{{ row.change_total_price }})</span>
      </template>

      <template #unit_price="{ row }">
        {{ row.unit_price }}
        <span v-if="row.change_unit_price > 0" class="text-red-600">(↑{{ row.change_unit_price }})</span>
        <span v-if="row.change_unit_price < 0" class="text-green-600">(↓{{ row.change_unit_price }})</span>
      </template>

      <template #other_price="{ row }">
        {{ row.other_price }}
        <span v-if="row.change_other_price > 0" class="text-red-600">(↑{{ row.change_other_price }})</span>
        <span v-if="row.change_other_price < 0" class="text-green-600">(↓{{ row.change_other_price }})</span>
      </template>
    </Table>
  </FildCard>
  <FildCard title="用纱信息" class="mt-[5px]">
    <Table :config="tableConfig_ls" :table-list="state.use_yarnList" :column-list="columnList_ls">
      <template #yarn_loss="{ row }">
        {{ row?.yarn_loss }}%
      </template>
      <template #yarn_ratio="{ row }">
        {{ row?.yarn_ratio }}%
      </template>
      <template #use_yarn_quantity="{ row }">
        {{ row.use_yarn_quantity }}
        <span v-if="row.change_use_yarn_quantity > 0" class="text-red-600">(↑{{ row.change_use_yarn_quantity }})</span>
        <span v-if="row.change_use_yarn_quantity < 0" class="text-green-600">(↓{{ row.change_use_yarn_quantity }})</span>
      </template>
    </Table>
  </FildCard>
  <FildCard title="凭证信息" class="mt-[5px]" :tool-bar="false">
    <template #right-top>
      <el-button v-if="data?.audit_status && data?.audit_status !== OrderAuditStatusEnum.Pending && data?.audit_status !== OrderAuditStatusEnum.TurnDown" v-has="'ProductSalePlanTextureUrlEdit'" :type="editCertificate ? 'warning' : 'primary'" @click="editCertificate = !editCertificate">
        {{ editCertificate ? '取消编辑' : '编辑凭证' }}
      </el-button>
    </template>
    <template v-if="editCertificate">
      <TextureMapWall v-model:image-list="data.fileList" text="" @update:image-list="updateImageList" />
    </template>
    <template v-else>
      <ImageFileCard v-for="(item, index) in data?.fileList" :key="index" :file-url="item" clear-disabled />
    </template>
  </FildCard>
</template>

<style></style>
