<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import { GetFinishProductList } from '@/api/finishedProductInformation'
import { debounce, getFilterData } from '@/common/util'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import SelectCascader from '@/components/SelectCascader/productType.vue'

export interface Props {
  id: number
  modelValue: boolean
}

const props = withDefaults(defineProps<Props>(), {
  id: 0,
  modelValue: false,
})
const emit = defineEmits(['update:modelValue', 'submit'])
const state = reactive({
  filterData: {
    finish_product_code: '',
    finish_product_name: '',
    measurement_unit_id: '',
    type_grey_fabric_id: '',
    finish_product_level_id: '',
    grey_fabric_code: '',
    grey_fabric_name: '',
  },
  tableList: [],
})

const { fetchData: fetchDataList, data: dataList, total, page, size, loading, handleSizeChange, handleCurrentChange } = GetFinishProductList()
async function getData() {
  await fetchDataList(getFilterData(state.filterData))
}

const showModal = ref<boolean>(false)
watch(
  () => props.modelValue,
  (show) => {
    showModal.value = show
    if (show) {
      getData()
    }
    else {
      state.filterData = {
        finish_product_code: '',
        finish_product_name: '',
        measurement_unit_id: '',
        type_grey_fabric_id: '',
        finish_product_level_id: '',
        grey_fabric_code: '',
        grey_fabric_name: '',
      }
    }
  },
)

const columnList = ref([
  {
    title: '成品编号',
    field: 'finish_product_code',
    fixed: 'left',
    minWidth: 100,
  },
  {
    title: '成品名称',
    field: 'finish_product_name',
    fixed: 'left',
    minWidth: 100,
  },
  {
    title: '成品全称',
    field: 'finish_product_full_name',
    fixed: 'left',
    minWidth: 100,
  },
  {
    title: '布种类型',
    field: 'type_grey_fabric_name',
    minWidth: 100,
  },
  {
    title: '成品等级',
    field: 'finish_product_level_name',
    minWidth: 100,
  },
  {
    title: '染整工艺',
    field: 'dyeing_craft',
    minWidth: 100,
  },
  {
    title: '成品幅宽',
    field: 'finish_product_width',
    minWidth: 100,
  },
  {
    title: '成品克重',
    field: 'finish_product_gram_weight',
    minWidth: 100,
  },
  {
    title: '坯布编号',
    field: 'grey_fabric_code',
    minWidth: 100,
  },
  {
    title: '坯布名称',
    field: 'grey_fabric_name',
    minWidth: 100,
  },
  {
    title: '成品成分',
    field: 'finish_product_ingredient',
    minWidth: 100,
  },
  {
    title: '成品工艺',
    field: 'finish_product_craft',
    minWidth: 100,
  },
  {
    title: '单位',
    field: 'measurement_unit_name',
    fixed: 'right',
    minWidth: 100,
  },
])

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  { deep: true },
)

const selectList = ref<any[]>()
function submit() {
  emit('submit', selectList.value)
}

function onClose() {
  emit('update:modelValue', false)
}

function handAllSelect({ records }: any) {
  selectList.value = records
}

function handleSelectionChange({ records }: any) {
  selectList.value = records
}

const tableConfig = ref({
  showCheckBox: true,
  handAllSelect,
  handleSelectionChange,
  showPagition: true,
  loading,
  size,
  page,
  total,
  checkRowKeys: [] as number[],
  handleSizeChange,
  handleCurrentChange,
})
</script>

<template>
  <vxe-modal v-model="showModal" show-footer title="添加成品商品" width="1300" height="550" :mask="false" :lock-view="false" :esc-closable="true" resize @close="onClose">
    <el-descriptions title="" :column="4" border size="small" class="mb-[20px]">
      <el-descriptions-item label="成品编号">
        <el-input v-model="state.filterData.finish_product_code" size="small" placeholder="成品编号" />
      </el-descriptions-item>
      <el-descriptions-item label="成品名称">
        <el-input v-model="state.filterData.finish_product_name" size="small" placeholder="成品名称" />
      </el-descriptions-item>
      <el-descriptions-item label="坯布编号">
        <el-input v-model="state.filterData.grey_fabric_code" size="small" placeholder="坯布编号" />
      </el-descriptions-item>
      <el-descriptions-item label="坯布名称">
        <el-input v-model="state.filterData.grey_fabric_name" size="small" placeholder="坯布名称" />
      </el-descriptions-item>
      <el-descriptions-item label="单位">
        <SelectComponents v-model="state.filterData.measurement_unit_id" api="getInfoBaseMeasurementUnitList" label-field="name" value-field="id" clearable />
      </el-descriptions-item>
      <el-descriptions-item label="布种类型">
        <SelectCascader @change-value="({ ids }) => state.filterData.type_grey_fabric_id = ids?.[ids.length - 1] || ''" />
      </el-descriptions-item>
      <el-descriptions-item label="成品等级">
        <SelectComponents v-model="state.filterData.finish_product_level_id" api="GetInfoBaseFinishedProductLevelEnumList" label-field="name" value-field="id" clearable />
      </el-descriptions-item>
    </el-descriptions>
    <Table ref="tablesRef" :config="tableConfig" :table-list="dataList.list" :column-list="columnList">
      <template #creatTime />
    </Table>
    <template #footer>
      <el-button type="primary" size="small" @click="submit">
        提交
      </el-button>
    </template>
  </vxe-modal>
</template>
@/api/finishedProductInformation/finishedProductInformation
