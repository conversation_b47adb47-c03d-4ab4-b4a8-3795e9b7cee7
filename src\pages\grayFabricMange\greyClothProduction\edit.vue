<script setup lang="ts" name="GreyClothProductionEdit">
import { ElMessage } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import currency from 'currency.js'
import { VxeInput } from 'vxe-pc-ui'
import AddProductionNoDialog from '../components/AddProductionNoDialog.vue'
import AddXimaDialog from '../components/AddXimaDialog.vue'
import { autoSelectYarn } from './autoSelectUtils'
import { getGfmProduceReceiveOrder, updateGfmProduceReceiveOrder } from '@/api/greyClothProduction'
import { BusinessUnitIdEnum, DictionaryType } from '@/common/enum'
import { list_enum } from '@/api/greyFabricPurchaseReturn'
import {
  formatDate,
  formatHashTag,
  formatPriceDiv,
  formatPriceMul,
  formatRollDiv,
  formatUnitPriceDiv,
  formatUnitPriceMul,
  formatWeightDiv,
  formatWeightMul,
  sumNum,
} from '@/common/format'
import { deepClone, isXimaAlreadyEntered } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'
import { calcUseYarnQuantity } from '@/common/formula'
import UseYarnTotalModal from '@/pages/grayFabricMange/greyClothProduction/components/UseYarnTotalModal.vue'

const routerList = useRouterList()

const bulkSetting = ref<any>({})

const state = reactive<any>({
  form: {
    marketingSystem: '', // 营销体系
    consignee: '', // 接收单位
    destprovidername: '', // 货源单位
    voucherNumber: '', // 凭证单号
    date: '', // 收货日期
    remark: '',
    yarnRatioList: [],
    // yarnRatioList_total: [],
    dye_unit_use_order_no: '', // 染厂用坯单号
  },
  formRules: {
    marketingSystem: [{ required: true, message: '请选择营销体系', trigger: 'blur' }],
    consignee: [{ required: true, message: '请选择接收单位', trigger: 'change' }],
    destprovidername: [{ required: true, message: '请选择货源单位', trigger: 'change' }],
    date: [{ required: true, message: '请选择收货日期', trigger: 'blur' }],
  },
  tableList: [],
  isInformation: true,
  multipleSelection: [],
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  unit_name: '',
  raw_code: '',
  raw_name: '',
  color_code: '',
  color_name: '',
})

const tableConfig = ref({
  // FooterMethod: (val: any) => FooterMethod(val),
  showSlotNums: true,
  showOperate: true,
  height: '100%',
  operateWidth: '80',
  footerMethod: (val: any) => FooterMethod(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  // cellDBLClickEvent: (val: any) => cellDBLClickEvent(val, 1),
  fieldApiKey: 'GreyClothProductionEdit',
})

const spanFields = [
  'grey_fabric_full_name',
  'produce_notice_order_no',
  'roll',
]
const tableConfig_other = ref({
  showSlotNums: true,
  showOperate: true,
  operateWidth: '110',
  height: '100%',
  footerMethod: (val: any) => FooterMethodOnce(val),
  colspanMethod: ({ row, _rowIndex, column, visibleData }: any) => {
    const cellValue = row[column.field]
    // cellValue为0时，合并单元格
    if (String(cellValue) && spanFields.includes(column.field)) {
      const prevRow = visibleData[_rowIndex - 1]
      let nextRow = visibleData[_rowIndex + 1]
      if (
        prevRow
        && prevRow[column.field] === cellValue
        && prevRow.production_notify_order_id === row.production_notify_order_id
      ) {
        return { rowspan: 0, colspan: 0 }
      }
      else {
        let countRowspan = 1
        while (
          nextRow
          && nextRow[column.field] === cellValue
          && nextRow.production_notify_order_id === row.production_notify_order_id
        )
          nextRow = visibleData[++countRowspan + _rowIndex]

        if (countRowspan > 1)
          return { rowspan: countRowspan, colspan: 1 }
      }
    }
  },
  // cellDBLClickEvent: (val: any) => cellDBLClickEvent(val, 2),
})

const { fetchData: getFetch, data: fabricList, success, msg } = getGfmProduceReceiveOrder()

const route = useRoute()

onMounted(() => {
  getData()
})

// 获取坯布信息
async function getData() {
  await getFetch({ id: route.query.id })
  if (!success.value)
    return ElMessage.error(msg.value)

  if (fabricList.value) {
    // destprovidernameRef.value.inputLabel = fabricList.value.supplier_name
    state.form.marketingSystem = fabricList.value.sale_system_id
    state.form.consignee = fabricList.value.receive_unit_id
    state.form.destprovidername = fabricList.value.supplier_id
    state.form.voucherNumber = fabricList.value.voucher_number
    state.form.date = fabricList.value.receive_time
    state.form.remark = fabricList.value.remark
    state.form.dye_unit_use_order_no = fabricList.value.dye_unit_use_order_no
    state.tableList = fabricList.value?.item_data
    const processedItems = fabricList.value.item_data.map((item, index: number) => {
      const idNum = Math.floor(Math.random() * 10000)
      item.use_yarn_data = item.use_yarn_data?.map((it: any) => {
        it.grayIndex = index
        it.grayId = idNum
        it.unit_name = item.unit_name
        // it.produce_notice_order_no = item.produce_notice_order_no
        // it.roll = item.roll
        it.grey_fabric_full_name = formatHashTag(item?.grey_fabric_code, item?.grey_fabric_name)
        it.produce_notice_order_no = item.produce_notice_order_no
        it.roll = formatRollDiv(item.roll)
        return it
      })
      return item
    })
    state.yarnRatioList = processedItems.flatMap(item => item.use_yarn_data)
    // 数据清洗，讲金额和数量格式化单位
    for (let i = 0; i < state.tableList?.length; i++) {
      state.tableList[i].process_single_unit_price = Number(formatUnitPriceDiv(state.tableList[i].process_single_unit_price))
      state.tableList[i].other_price = Number(formatPriceDiv(state.tableList[i].other_price))
      state.tableList[i].total_weight = formatWeightDiv(state.tableList[i]?.total_weight)
      state.tableList[i].roll = Number(formatPriceDiv(state.tableList[i].roll))
      state.tableList[i].reference_weight = Number(formatWeightDiv(state.tableList[i].reference_weight)) // 数量
      state.tableList[i].avg_weight = formatWeightDiv(state.tableList[i]?.avg_weight)

      state.tableList[i].grey_fabric_gram_weight = state.tableList[i]?.grey_fabric_gram_weight?.toString()
      for (let q = 0; q < state.tableList[i].item_fc_data?.length; q++) {
        state.tableList[i].item_fc_data[q].weight = Number(formatWeightDiv(state.tableList[i].item_fc_data[q].weight))
        state.tableList[i].item_fc_data[q].roll = Number(formatPriceDiv(state.tableList[i].item_fc_data[q].roll))
      }
      for (let j = 0; j < state.tableList[i].use_yarn_data?.length; j++) {
        state.tableList[i].use_yarn_data[j].actually_use_yarn_quantity = formatWeightDiv(state.tableList[i].use_yarn_data[j]?.actually_use_yarn_quantity)
        state.tableList[i].use_yarn_data[j].use_yarn_quantity = formatWeightDiv(state.tableList[i].use_yarn_data[j]?.use_yarn_quantity)
        state.tableList[i].use_yarn_data[j].raw_remark = state.tableList[i].use_yarn_data[j]?.remark
        state.tableList[i].use_yarn_data[j].id = Number(state.tableList[i].use_yarn_data[j]?.produce_order_use_yarn_id)

        for (let c = 0; c < state.tableList[i].use_yarn_data[j].use_yarn_item_data?.length; c++) {
          // if (state.tableList[i]?.use_yarn_data[j]?.use_yarn_item_data[c]?.use_yarn_quantity) {
          state.tableList[i].use_yarn_data[j].use_yarn_item_data[c].use_yarn_quantity = formatWeightDiv(state.tableList[i]?.use_yarn_data[j]?.use_yarn_item_data[c]?.use_yarn_quantity)
          state.tableList[i].use_yarn_data[j].use_yarn_item_data[c].grayId = state.tableList[i].id
          state.tableList[i].use_yarn_data[j].use_yarn_item_data[c].parent_id = state.tableList[i].use_yarn_data[j]?.produce_order_use_yarn_id
          // }
        }
      }
    }
  }
}
function handleClickLink(row: any) {
  routerList.push({
    name: 'ProductionNoticeDetail',
    query: { id: row?.produce_notice_order_item_id },
  })
}
const columnList = ref([
  // {
  //   field: 'sale_plan_order_item_no',
  //   title: '销售计划详情单号',
  //   fixed: 'left',
  //   minWidth: 150,
  // },
  {
    field: 'grey_fabric_code',
    title: '坯布名称',
    fixed: 'left',
    minWidth: 100,
    soltName: 'grey_fabric_code',
  },
  // {
  //   field: 'grey_fabric_name',
  //   title: '坯布名称',
  //   fixed: 'left',
  //   minWidth: 150,
  // },
  {
    field: 'produce_notice_order_no',
    title: '生产通知单号',
    minWidth: 140,
    soltName: 'produce_notice_order_no',
  },
  {
    field: 'customer_id',
    title: '所属客户',
    minWidth: 150,
    soltName: 'SubordinateCustomer',
    required: true,
  },
  {
    field: 'raw_material_yarn_name',
    title: '原料纱名',
    minWidth: 150,
    soltName: 'raw_material_yarn_name',
    editRender: { autofocus: '.vxe-input--inner' },
  },
  {
    field: 'raw_material_batch_num',
    title: '原料批号',
    minWidth: 80,
    soltName: 'raw_material_batch_num',
    editRender: { autofocus: '.vxe-input--inner' },
  },
  {
    field: 'raw_material_batch_brand',
    title: '原料品牌',
    minWidth: 150,
    soltName: 'raw_material_batch_brand',
    editRender: { autofocus: '.vxe-input--inner' },
  },
  {
    field: 'grey_fabric_width',
    title: '幅宽',
    minWidth: 150,
    soltName: 'breadth',
    editRender: { autofocus: '.vxe-input--inner' },
  },
  {
    field: 'grey_fabric_gram_weight',
    title: '克重',
    minWidth: 150,
    soltName: 'weight',
    editRender: { autofocus: '.vxe-input--inner' },
  },
  {
    field: 'needle_size',
    title: '针寸数',
    minWidth: 80,
    soltName: 'needles',
    editRender: { autofocus: '.vxe-input--inner' },
  },
  //   {
  //     field: 'total_needle_size',
  //     title: '总针数',
  //     minWidth: 150,
  //     soltName: 'totalStitches',
  //   },
  {
    field: 'yarn_batch',
    title: '纱批',
    minWidth: 80,
    soltName: 'yarnBatch',
    editRender: { autofocus: '.vxe-input--inner' },
  },
  {
    field: 'gray_fabric_color_id',
    title: '织坯颜色',
    minWidth: 120,
    soltName: 'blankColor',
  },
  // {
  //   field: 'unit_name',
  //   title: '单位',
  //   minWidth: 150,
  // },
  //   {
  //     field: 'weaving_process',
  //     title: '织造工艺',
  //     minWidth: 150,
  //     soltName: 'weavingProcess',
  //   },
  {
    field: 'grey_fabric_level_id',
    title: '坯布等级',
    minWidth: 120,
    soltName: 'greyFabricGrade',
  },
  {
    field: 'machine_number',
    title: '机台号',
    minWidth: 80,
    soltName: 'machineNumber',
  },
  {
    field: 'grey_fabric_remark',
    title: '坯布备注',
    minWidth: 150,
    soltName: 'fabircRemark',
  },
  {
    field: 'total_weight',
    title: '总数量',
    minWidth: 80,
    soltName: 'total_weight',
  },
  {
    field: 'average_weight',
    title: '平均数量',
    minWidth: 80,
    soltName: 'average_weight',
  },
  {
    field: 'other_price',
    title: '其他金额',
    minWidth: 100,
    soltName: 'otherPrice',
    editRender: { autofocus: '.vxe-input--inner' },
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 150,
    soltName: 'remark',
    editRender: { autofocus: '.vxe-input--inner' },
  },
  {
    field: 'roll',
    title: '收货匹数',
    fixed: 'right',
    minWidth: 100,
    soltName: 'horsepower',
    editRender: { autofocus: '.vxe-input--inner' },
    required: true,
  },
  {
    field: 'xima',
    title: '细码',
    fixed: 'right',
    minWidth: 100,
    soltName: 'xima',
  },
  {
    field: 'process_single_unit_price',
    title: '加工单价',
    minWidth: 100,
    soltName: 'unitPrice',
    fixed: 'right',
    required: true,
    editRender: { autofocus: '.vxe-input--inner' },
  },
  {
    field: 'total_price',
    fixed: 'right',
    title: '总金额',
    minWidth: 100,
  },
])

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${sumNum(data, 'roll')}`

      if (['total_weight'].includes(column.property))
        return `${sumNum(data, 'total_weight')}`

      if (['other_price'].includes(column.property))
        return `￥${sumNum(data, 'other_price')}`

      if (['total_price'].includes(column.property))
        return `￥${sumNum(data, 'total_price')}`

      // if (['weight'].includes(column.property)) {
      //   return `${formatWeightDiv(sumNum(data, 'weight'))}`
      // }
      return null
    }),
  ]
}

const AddXimaDialogRef = ref()

function handWrite(row: any, rowIndex: number) {
  if (row.roll === '')
    return ElMessage.error('请先输入匹数')

  AddXimaDialogRef.value.state.showModal = true
  const arr = [
    {
      roll: row.roll,
      position: '',
      volume_number: '',
      weight: row.totalWeight || 0,
    },
  ]
  if (!row?.item_fc_data?.length)
    AddXimaDialogRef.value.state.tableData = arr
  else
    AddXimaDialogRef.value.state.tableData = deepClone(row?.item_fc_data) || []

  AddXimaDialogRef.value.state.canEnter = row.roll
  AddXimaDialogRef.value.state.isAdd = row.isNew
  // AddXimaDialogRef.value.state.total_roll = row.roll
  AddXimaDialogRef.value.state.horsepower = row.roll
  AddXimaDialogRef.value.state.reference_weight = row.reference_weight
  AddXimaDialogRef.value.state.code = row.grey_fabric_code
  AddXimaDialogRef.value.state.name = row.grey_fabric_name
  AddXimaDialogRef.value.state.rowIndex = rowIndex

  AddXimaDialogRef.value.state.id = row.id

  // sry，他这么写我只能继续堆shift了
  AddXimaDialogRef.value.state.receivedRoll = Number(row.roll)
  AddXimaDialogRef.value.state.totalWeight = Number(row.total_weight)
  AddXimaDialogRef.value.state.weightOfFabric = formatWeightDiv(row.weight_of_fabric)
  AddXimaDialogRef.value.computeDisplayedData()
}

const AddFabricDialogRef = ref()

function handAdd() {
  AddFabricDialogRef.value.state.showModal = true
  AddFabricDialogRef.value.state.filterData.sale_system_id = state.form.marketingSystem // 营销体系

  AddFabricDialogRef.value.state.sureMultipleSelection = state.tableList.map((item) => {
    item.id = item.produce_notice_order_item_id
    return item
  })
  AddFabricDialogRef.value.state.filterData.weaving_mill_id = state.form.destprovidername // 供方
}

function handFabric(list: any) {
  list.forEach((item: any, index: number) => {
    const idNum = Math.floor(Math.random() * 10000)
    const tableItem = {
      ...item,
      grey_fabric_code: item?.grey_fabric_code,
      grey_fabric_name: item?.grey_fabric_name,
      produce_notice_order_no: item?.order_no || '',
      // customer_id: '',
      grey_fabric_width: item?.grey_fabric_width,
      grey_fabric_gram_weight: item?.grey_fabric_gram_weight,
      needle_size: item?.needle_size,
      // total_needle_size: item?.total_needle_size,
      item_fc_data: [],
      yarn_batch: item?.yarn_batch,
      gray_fabric_color_id: item?.grey_fabric_color_id,
      // weaving_process: item?.weaving_process,
      grey_fabric_level_id: item?.grey_fabric_level_id,
      machine_number: item?.machine_number,
      // grey_fabric_purchase_code: item?.order_code,
      // grey_fabric_purchase_item_id: item?.id,
      // roll: formatPriceDiv(item.produced_roll),
      roll: formatPriceDiv(item.producing_roll),
      reference_weight: formatWeightDiv(item?.total_weight) || 0,
      // total_weight: 0,
      total_weight: formatWeightDiv(item.scheduling_weight),
      average_weight: 0,
      process_single_unit_price: formatUnitPriceDiv(item?.process_price),
      other_price: 0,
      grey_fabric_remark: item.grey_fabric_remark,
      total_price: 0,
      // item_source: state.isInformation ? 1 : 2,
      raw_material_yarn_name: '',
      raw_material_batch_num: '',
      raw_material_batch_brand: '',
      remark: '',
      isNew: true,
      produce_notice_order_item_id: item.id,
      use_yarn_data: item?.production_notify_material_ratio.map((it: any, idx: number) => {
        it.grayIndex = index
        it.grayId = idNum
        it.itemIndex = idx
        it.grey_fabric_full_name = formatHashTag(item?.grey_fabric_code, item?.grey_fabric_name)
        it.use_yarn_quantity = calcUseYarnQuantity(item.total_weight, formatPriceDiv(it.yarn_ratio), formatPriceDiv(it.yarn_loss))
        it.produce_notice_order_no = item?.order_no
        it.roll = formatPriceDiv(item.producing_roll)
        return it
      }),
      id: idNum,
      grey_fabric_id: item?.grey_fabric_id,
      customer_id: item?.customer_id || 0,
    }
    state.tableList.push(tableItem)
    state.yarnRatioList.push(...tableItem.use_yarn_data)
    componentRemoteSearch.customer_name = item?.customer_name
  })
  state.tableList.forEach((item: any) => {
    item.use_yarn_data?.map((it: any) => {
      it.produce_order_use_yarn_id = it.id
      it.use_yarn_item_data = it.use_yarn_item_data?.length ? it.use_yarn_item_data : []
      return it
    })
  })
  AddFabricDialogRef.value.state.showModal = false
}
function handleBlur(row: any) {
  // 同步更新收货匹数
  row.use_yarn_data?.map((it: any) => {
    it.roll = row.roll
    return it
  })
}
// 录入细码
function handleSureXima(val: any) {
  state.tableList?.map((item: any, index: number) => {
    if (index === val.rowIndex) {
      item.item_fc_data = val.tableData
      item.total_weight = Number(sumNum(val.tableData, 'weight'))
      item.use_yarn_data = item?.use_yarn_data?.map((it: any) => {
        it.use_yarn_quantity = calcUseYarnQuantity(item.total_weight, formatPriceDiv(it.yarn_ratio), formatPriceDiv(it.yarn_loss))
        return it
      })
      item.average_weight = Number(item.roll) !== 0 ? currency(item.total_weight).divide(item.roll).value : 0
      return item
    }
  })
  AddXimaDialogRef.value.state.showModal = false
}

const tableRef = ref()

watch(
  () => state.tableList,
  () => {
    if (state.tableList?.length > 0) {
      state.tableList?.map((item: any) => {
        item.total_price = (Number(item?.process_single_unit_price) * Number(item?.total_weight) + Number(item?.other_price)).toFixed(2)
        item.average_weight = Number(item.roll) !== 0 ? currency(item.total_weight).divide(item.roll).value : 0
        return item
      })
      //

      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

const ruleFormRef = ref()

const { fetchData: addPost, data: addData, success: addSuccess, msg: addMsg } = updateGfmProduceReceiveOrder()

// 提交数据
async function handleSure() {
  if (!state.tableList.length)
    return ElMessage.error('至少添加一条坯布信息')

  const list = deepClone(state.tableList)

  for (let i = 0; i < list.length; i++) {
    if (list[i].customer_id === '')
      return ElMessage.error('所属客户不可为空')

    if (list[i].roll === '')
      return ElMessage.error('匹数不可为空')

    if (Number(list[i].roll) - Number(sumNum(list[i]?.item_fc_data, 'roll')) !== 0 && Number(list[i].roll) !== 0)
      return ElMessage.error(`第${i + 1}行的细码必须录完`)

    if (list[i].process_single_unit_price === '')
      return ElMessage.error('加工单价不可为空')

    list[i].grey_fabric_gram_weight = list[i]?.grey_fabric_gram_weight.toString()
    list[i].roll = Number(formatPriceMul(list[i].roll))
    list[i].process_single_unit_price = Number(formatUnitPriceMul(list[i].process_single_unit_price))
    list[i].other_price = Number(formatPriceMul(list[i].other_price))
    list[i].roll = Number(list[i].roll)
    list[i].reference_weight = Number(formatWeightMul(list[i].reference_weight))
    // 将坯布信息的数量乘以一千给后端
    for (let q = 0; q < list[i].item_fc_data?.length; q++) {
      if (list[i].item_fc_data[q].weight === '')
        return ElMessage.error('细码数量不可为空')

      if (list[i].item_fc_data[q].roll === '')
        return ElMessage.error('细码匹数不可为空')

      list[i].item_fc_data[q].weight = Number(formatWeightMul(list[i].item_fc_data[q].weight))
      list[i].item_fc_data[q].roll = Number(formatPriceMul(list[i].item_fc_data[q].roll))
    }
    for (let j = 0; j < list[i].use_yarn_data?.length; j++) {
      list[i].use_yarn_data[j].actually_use_yarn_quantity = formatWeightMul(list[i].use_yarn_data[j]?.actually_use_yarn_quantity)

      for (let c = 0; c < list[i].use_yarn_data[j].use_yarn_item_data?.length; c++) {
        if (list[i]?.use_yarn_data[j]?.use_yarn_item_data[c]?.use_yarn_quantity === '')
          return ElMessage.error('用纱数量不可为空')

        if (list[i]?.use_yarn_data[j]?.use_yarn_item_data[c]?.use_yarn_quantity)
          list[i].use_yarn_data[j].use_yarn_item_data[c].use_yarn_quantity = formatWeightMul(list[i]?.use_yarn_data[j]?.use_yarn_item_data[c]?.use_yarn_quantity)
      }
    }
  }

  const query = {
    receive_time: formatDate(state.form.date),
    sale_system_id: state.form.marketingSystem,
    receive_unit_id: state.form.consignee,
    supplier_id: state.form.destprovidername,
    voucher_number: state.form.voucherNumber,
    remark: state.form.remark,
    dye_unit_use_order_no: state.form.dye_unit_use_order_no,
    item_data: list,
    id: Number(route.query.id),
  }

  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(query)
      if (addSuccess.value) {
        ElMessage.success('成功')
        routerList.push({ name: 'GreyClothProductionDetail', query: { id: addData.value.id } })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

function handDelete(index: number) {
  // 获取要删除的行数据
  const deletedItem = state.tableList[index]
  // 从 state.yarnRatioList 中删除对应的用纱数据
  if (deletedItem && deletedItem.use_yarn_data && deletedItem.use_yarn_data.length > 0) {
    // 遍历要删除的行的 use_yarn_data
    deletedItem.use_yarn_data.forEach((yarnItem: any) => {
      // 在 state.yarnRatioList 中查找并删除对应的项
      const yarnIndex = state.yarnRatioList.findIndex((item: any) =>
        item.id === yarnItem.id || item.produce_order_use_yarn_id === yarnItem.id,
      )

      if (yarnIndex !== -1)
        state.yarnRatioList.splice(yarnIndex, 1)
    })
  }
  state.tableList.splice(index, 1)
}

const bulkShow = ref(false)

function bulkHand() {
  bulkShow.value = true
}

function handBulkClose() {
  bulkShow.value = false
}

async function bulkSubmit({ row, value, quickInputResult }: any) {
  if (state.multipleSelection?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  state.tableList?.map((item: any, index: number) => {
    if (row.quickInput && quickInputResult?.[index]) {
      item[row.field] = quickInputResult[index]
      return item
    }
    item[row.field] = value[row.field]
    item[row.field_name] = value[row.field_name]
  })
  bulkShow.value = false
  ElMessage.success('设置成功')
}

const bulkList = reactive<any>([
  {
    field: 'customer_id',
    // field_name: 'customer_name',
    title: '所属客户',
    component: 'select',
    api: 'BusinessUnitSupplierEnumlist',
    query: { sale_system_id: state.form.marketingSystem },
  },
  {
    field: 'raw_material_yarn_name',
    title: '原料纱名',
    component: 'input',
    type: 'text',
  },
  {
    field: 'raw_material_batch_num',
    title: '原料批号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'raw_material_batch_brand',
    title: '原料品牌',
    component: 'input',
    type: 'text',
  },
  {
    field: 'grey_fabric_width',
    title: '幅宽',
    component: 'input',
    type: 'text',
  },
  {
    field: 'grey_fabric_gram_weight',
    title: '克重',
    component: 'input',
    type: 'text',
  },
  {
    field: 'needle_size',
    title: '针寸数',
    component: 'input',
    type: 'text',
  },
  //   {
  //     field: 'total_needle_size',
  //     title: '总针数',
  //     component: 'input',
  //     type: 'text',
  //   },
  {
    field: 'yarn_batch',
    title: '纱批',
    component: 'input',
    type: 'text',
  },
  {
    field: 'gray_fabric_color_id',
    title: '织坯颜色',
    component: 'select',
    api: 'getInfoProductGrayFabricColorList',
  },
  //   {
  //     field: 'weaving_process',
  //     title: '织造工艺',
  //     component: 'input',
  //     type: 'text',
  //   },
  {
    field: 'grey_fabric_level_id',
    title: '坯布等级',
    component: 'select',
    api: 'getInfoBaseGreyFabricLevelList',
  },
  {
    field: 'machine_number',
    title: '机台号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'roll',
    title: '匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'process_single_unit_price',
    title: '单价',
    component: 'input',
    type: 'float',
  },
  {
    field: 'other_price',
    title: '其他金额',
    component: 'input',
    type: 'float',
  },
  {
    field: 'grey_fabric_remark',
    title: '坯布备注',
    component: 'input',
    type: 'text',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])

function getMarketingSystem(val: any) {
  state.tableList.map((item: any) => {
    item.customer_id = ''
    return item
  })
  bulkList[0].query = { sale_system_id: val?.id }
  bulkSetting.value.customer_id = ''
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// 先用坯布信息的id进行匹配，再用用纱比例的id进行匹配，再用里面的汇总id进行匹配删除
function removeDuplicateRmlStockIds(arr: any, grayId: number, produce_order_use_yarn_id: number, rml_stock_id: number) {
  arr.forEach((item: any) => {
    if (item.id === grayId) {
      item.use_yarn_data.forEach((useYarn: any) => {
        if (useYarn.produce_order_use_yarn_id === produce_order_use_yarn_id) {
          useYarn.use_yarn_item_data.forEach((itemData: any, index: number) => {
            if (itemData.rml_stock_id === rml_stock_id)
              useYarn.use_yarn_item_data.splice(index, 1)
          })
        }
      })
    }
  })
  return arr
}

// 删除用纱比例里的用纱信息
function removeUseYarnData(arr: any, rml_stock_id: number) {
  arr.forEach((yarnItem: any) => {
    yarnItem.use_yarn_item_data = yarnItem.use_yarn_item_data.filter((useYarnItem: any) => useYarnItem.rml_stock_id !== rml_stock_id)
  })
  return arr
}

function FooterMethodOnce({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['use_yarn_quantity'].includes(column.property))
        return `${sumNum(data, 'use_yarn_quantity') as any}`

      if (['actually_use_yarn_quantity'].includes(column.property))
        return `${Number.parseFloat(sumNum(data, 'actually_use_yarn_quantity') as any)}`

      return null
    }),
  ]
}

const tableRefs = ref()

// watch(
//   () => state.yarnRatioList_total,
//   () => {
//     if (state.yarnRatioList_total.length > 0) {
//       state.yarnRatioList.map((item: any) => {
//         item.actually_use_yarn_quantity = sumTotal(item.use_yarn_item_data, 'use_yarn_quantity')
//         return item
//       })
//       nextTick(() => {
//         tableRefs.value.tableRef?.updateFooter()
//       })
//     }
//   },
//   { deep: true },
// )
// 处理删除用纱数据
function handleYarnDelete({ row }) {
  // 删除数据逻辑
  // state.yarnRatioList_total.splice(rowIndex, 1)
  state.tableList = removeDuplicateRmlStockIds(state.tableList, row.grayId, row.parent_id, row.rml_stock_id)
  state.yarnRatioList = removeUseYarnData(state.yarnRatioList, row.rml_stock_id)
}
function handleYarnInfoSure(info) {
  state.tableList.forEach((item: any) => {
    if (item.id === info.grayId) {
      item.use_yarn_data.map((it: any, index: number) => {
        if (index === info.itemIndex) {
          it.use_yarn_item_data = deepClone(info.list)
          return it
        }
        return item
      })
    }
  })

  state.yarnRatioList[info.rowIndex].use_yarn_item_data = deepClone(info.list) // 用纱比例
}
const useYarnTotalModalRef = ref()
// 打开弹窗的方法
function openYarnTotalModal(row: any, rowIndex: number) {
  if (!row.use_yarn_quantity)
    return ElMessage.error('请先录入细码')
  useYarnTotalModalRef.value.state.grayIndex = row.grayIndex
  useYarnTotalModalRef.value.state.grayId = row.grayId
  useYarnTotalModalRef.value.state.unitId = state.form.destprovidername
  useYarnTotalModalRef.value.state.showModal = true
  useYarnTotalModalRef.value.state.data = row
  useYarnTotalModalRef.value.state.rowIndex = rowIndex
  useYarnTotalModalRef.value.state.itemIndex = row.itemIndex
  useYarnTotalModalRef.value.state.tableData = row?.use_yarn_item_data
}
const { fetchData, success: rawMeterialSuccess, msg: rawMeterialMsg, data } = list_enum()
// 自动选择
async function handleAutoSelect(row: any, rowIndex: number) {
  if (!row.use_yarn_quantity)
    return ElMessage.error('请先录入细码')
  let originList = []
  await fetchData({ raw_material_id: Number(row.raw_material_id), unit_id: state.form.destprovidername, is_stock_type_param: true, is_has_stock: true })

  if (!rawMeterialSuccess.value)
    return ElMessage.error(rawMeterialMsg.value)

  originList = data.value.list || []

  // 使用抽离的工具函数
  const selectList = await autoSelectYarn({
    originList,
    totalWeight: row.use_yarn_quantity,
    parentId: row.id,
    grayId: row.grayId,
    extraData: {
      // 可以添加额外的数据
      grey_fabric_full_name: row.grey_fabric_full_name,
    },
  })
  handleYarnInfoSure({
    grayId: row.grayId,
    rowIndex,
    itemIndex: row.itemIndex,
    list: selectList,
  })
}
const yarnRatio_columnList = ref([
  {
    field: 'grey_fabric_full_name',
    title: '坯布名称',
    minWidth: 150,
  },
  // {
  //   field: 'produce_notice_order_no',
  //   title: '生产通知单',
  //   minWidth: 150,
  // },
  // {
  //   field: 'roll',
  //   title: '收货匹数',
  //   minWidth: 150,
  //   isPrice: true,
  // },
  {
    field: 'produce_notice_order_no',
    title: '生产通知单',
    minWidth: 150,
  },
  {
    field: 'roll',
    title: '收货匹数',
    minWidth: 150,
  },
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 150,
    soltName: 'raw_material_code',
  },
  // {
  //   field: 'raw_material_name',
  //   title: '原料名称',
  //   minWidth: 150,
  // },
  // {
  //   field: 'raw_material_brand',
  //   title: '原料品牌',
  //   minWidth: 150,
  // },
  // {
  //   field: 'raw_material_batch_number',
  //   title: '原料批号',
  //   minWidth: 150,
  // },
  {
    field: 'color_scheme',
    title: '原料颜色',
    minWidth: 150,
  },
  // {
  //   field: 'unit_name',
  //   title: '单位',
  //   minWidth: 150,
  // },
  {
    field: 'yarn_ratio',
    title: '用纱比例',
    minWidth: 150,
    isPrice: true,
  },
  {
    field: 'yarn_loss',
    title: '用纱损耗',
    minWidth: 150,
    isPrice: true,
  },
  {
    field: 'use_yarn_quantity',
    title: '用纱量',
    minWidth: 150,
    soltName: 'use_yarn_quantity',
  },
  {
    field: 'actually_use_yarn_quantity',
    title: '实际用纱量',
    minWidth: 150,
    soltName: 'actually_use_yarn_quantity',
  },
])
</script>

<template>
  <div class="list-page">
    <FildCard title="基础信息" :tool-bar="false">
      <template #right-top>
        <el-button v-btnAntiShake="handleSure" type="primary">
          提交
        </el-button>
      </template>
      <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem required label="营销体系:">
            <template #content>
              <el-form-item prop="marketingSystem">
                <SelectComponents
                  v-model="state.form.marketingSystem"
                  api="AdminsaleSystemgetSaleSystemDropdownList"
                  label-field="name"
                  value-field="id"
                  clearable
                  @select="val => getMarketingSystem(val)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="接收单位:">
            <template #content>
              <el-form-item prop="consignee">
                <SelectDialog
                  v-model="state.form.consignee"
                  :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory, name: componentRemoteSearch.unit_name }"
                  api="BusinessUnitSupplierEnumlist"
                  :column-list="[
                    {
                      field: 'name',
                      title: '名称',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'name',
                          isEdit: true,
                          title: '名称',
                          minWidth: 100,
                        },
                      ],
                    },
                    {
                      field: 'code',
                      title: '编号',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'code',
                          isEdit: true,
                          title: '编号',
                          minWidth: 100,
                        },
                      ],
                    },
                  ]"
                  @change-input="val => (componentRemoteSearch.unit_name = val)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="货源单位:">
            <template #content>
              <el-form-item prop="destprovidername">
                <SelectDialog
                  v-model="state.form.destprovidername"
                  :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory, name: componentRemoteSearch.name }"
                  api="BusinessUnitSupplierEnumlist"
                  :column-list="[
                    {
                      field: 'name',
                      title: '名称',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'name',
                          isEdit: true,
                          title: '名称',
                          minWidth: 100,
                        },
                      ],
                    },
                    {
                      field: 'code',
                      title: '供应商编号',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'code',
                          isEdit: true,
                          title: '供应商编号',
                          minWidth: 100,
                        },
                      ],
                    },
                  ]"
                  @change-input="val => (componentRemoteSearch.name = val)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="凭证号:">
            <template #content>
              <el-form-item prop="saleArea">
                <el-input v-model="state.form.voucherNumber" placeholder="凭证号" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="收货日期:">
            <template #content>
              <el-form-item prop="date">
                <el-date-picker v-model="state.form.date" type="date" placeholder="收货日期" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="单据备注:" copies="2">
            <template #content>
              <el-form-item prop="remark">
                <el-input v-model="state.form.remark" placeholder="单据备注" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="染厂用坯单号:" copies="1">
            <template #content>
              <el-form-item prop="dye_unit_use_order_no">
                <el-input v-model="state.form.dye_unit_use_order_no" placeholder="染厂用坯单号" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
        </div>
      </el-form>
    </FildCard>
    <FildCard title="坯布信息" class="table-card-full">
      <template #right-top>
        <el-button type="primary" :disabled="state.multipleSelection.length <= 0" @click="bulkHand">
          批量操作
        </el-button>
        <el-button :disabled="state.form.marketingSystem === '' || state.form.destprovidername === ''" type="primary" @click="handAdd">
          从生产通知单添加
        </el-button>
      </template>
      <Table ref="tableRef" :config="tableConfig" :table-list="state.tableList" :column-list="columnList">
        <template #grey_fabric_code="{ row }">
          {{ formatHashTag(row.grey_fabric_code, row.grey_fabric_name) }}
        </template>
        <template #produce_notice_order_no="{ row }">
          <el-link type="primary" :underline="false" @click="handleClickLink(row)">
            {{ row.produce_notice_order_no }}
          </el-link>
        </template>
        <template #total_weight="{ row }">
          {{ row.total_weight }} {{ row.unit_name }}
        </template>
        <template #average_weight="{ row }">
          {{ isNaN(row?.average_weight) ? 0 : row?.average_weight }} {{ row.unit_name }}
        </template>
        <template #SubordinateCustomer="{ row }">
          <!-- <SelectComponents
          :query="{ sale_system_id: state.form.marketingSystem }"
          placeholder="必选"
          api="GetCustomerEnumList"
          label-field="name"
          value-field="id"
          v-model="row.customer_id"
          clearable
        /> -->
          <SelectDialog
            v-model="row.customer_id"
            :label-name="row.customer_name"
            :query="{ sale_system_id: state.form.marketingSystem, name: componentRemoteSearch.customer_name }"
            api="GetCustomerEnumList"
            :column-list="[
              {
                title: '客户编号',
                minWidth: 100,
                required: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '客户编号',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '客户名称',
                minWidth: 100,
                colGroupHeader: true,
                required: true,
                childrenList: [
                  {
                    isEdit: true,
                    field: 'name',
                    title: '客户名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '电话',
                colGroupHeader: true,
                minWidth: 100,
                childrenList: [
                  {
                    field: 'phone',
                    isEdit: true,
                    title: '电话',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '销售员',
                minWidth: 100,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'seller_name',
                    title: '销售员',
                    soltName: 'seller_name',
                    isEdit: true,
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.customer_name = val)"
          />
        </template>
        <template #breadth="{ row }">
          <el-input v-model="row.grey_fabric_width" clearable class="input-with-select">
            <template #append>
              <SelectComponents
                v-model="row.grey_fabric_width_unit_id"
                placeholder="单位"
                style="width: 80px"
                :query="{ dictionary_id: DictionaryType.width_unit }"
                api="GetDictionaryDetailEnumListApi"
                label-field="name"
                value-field="id"
                clearable
              />
            </template>
          </el-input>
        </template>
        <template #weight="{ row }">
          <el-input v-model="row.grey_fabric_gram_weight" clearable class="input-with-select">
            <template #append>
              <SelectComponents
                v-model="row.grey_fabric_gram_weight_unit_id"
                placeholder="单位"
                style="width: 80px"
                :query="{ dictionary_id: DictionaryType.gram_weight_unit }"
                api="GetDictionaryDetailEnumListApi"
                label-field="name"
                value-field="id"
                clearable
              />
            </template>
          </el-input>
        </template>
        <template #needles="{ row }">
          <VxeInput v-model="row.needle_size" placeholder="请输入" />
        </template>
        <template #totalStitches="{ row }">
          <VxeInput v-model="row.total_needle_size" placeholder="请输入" />
        </template>
        <template #yarnBatch="{ row }">
          <VxeInput v-model="row.yarn_batch" placeholder="请输入" />
        </template>
        <template #blankColor="{ row }">
          <SelectComponents v-model="row.gray_fabric_color_id" api="getInfoProductGrayFabricColorList" label-field="name" value-field="id" clearable />
        </template>
        <template #weavingProcess="{ row }">
          <VxeInput v-model="row.weaving_process" placeholder="请输入" />
        </template>
        <template #greyFabricGrade="{ row }">
          <SelectComponents v-model="row.grey_fabric_level_id" api="getInfoBaseGreyFabricLevelList" label-field="name" value-field="id" clearable />
        </template>
        <template #machineNumber="{ row }">
          <VxeInput v-model="row.machine_number" placeholder="请输入" />
        </template>
        <template #horsepower="{ row }">
          <VxeInput v-model="row.roll" :min="0" type="float" placeholder="必填" @blur="handleBlur(row)" />
        </template>
        <template #xima="{ row, rowIndex }">
          <div class="flex items-center">
            <el-button type="primary" text link @click="handWrite(row, rowIndex)">
              录入
            </el-button>
            <div v-if="isXimaAlreadyEntered(row, { rollKey: 'roll', weightKey: 'total_weight' })" class="text-[#b5b39f] ml-[5px]">
              (已录完)
            </div>
            <div v-else class="text-[#efa6ae] ml-[5px]">
              ({{ (row?.roll - Number(sumNum(row?.item_fc_data, 'roll'))).toFixed(2) }}匹未录)
            </div>
          </div>
        </template>
        <template #unitPrice="{ row }">
          <VxeInput v-model="row.process_single_unit_price" type="float" placeholder="必填">
            <template #prefix>
              ￥
            </template>
          </VxeInput>
        </template>
        <template #otherPrice="{ row }">
          <VxeInput v-model="row.other_price" type="float" placeholder="请输入">
            <template #prefix>
              ￥
            </template>
          </VxeInput>
        </template>
        <!-- <template #totalPrice="{ row }">
            {{ Number(row.single_price) * Number(row.total_weight) + Number(row.other_price) || 0 }}
          </template> -->
        <template #fabircRemark="{ row }">
          <VxeInput v-model="row.grey_fabric_remark" placeholder="请输入" />
        </template>
        <template #remark="{ row }">
          <VxeInput v-model="row.remark" placeholder="请输入" />
        </template>
        <template #raw_material_yarn_name="{ row }">
          <VxeInput v-model="row.raw_material_yarn_name" placeholder="请输入" />
        </template>
        <template #raw_material_batch_num="{ row }">
          <VxeInput v-model="row.raw_material_batch_num" placeholder="请输入" />
        </template>
        <template #raw_material_batch_brand="{ row }">
          <VxeInput v-model="row.raw_material_batch_brand" placeholder="请输入" />
        </template>
        <template #operate="{ rowIndex }">
          <el-button text type="danger" @click="handDelete(rowIndex)">
            删除
          </el-button>
        </template>
      </Table>
    </FildCard>
    <FildCard title="用纱情况" :tool-bar="false" class="table-card-bottom">
      <!--      <div class="mb-[20px]"> -->
      <!--        提示：（双击行从库存中选择用纱） -->
      <!--      </div> -->
      <Table ref="tableRefs" :config="tableConfig_other" :table-list="state.yarnRatioList" :column-list="yarnRatio_columnList">
        <template #raw_material_code="{ row }">
          {{ formatHashTag(row.raw_material_code, row.raw_material_name) }}
        </template>
        <template #use_yarn_quantity="{ row }">
          {{ row.use_yarn_quantity }} {{ row.unit_name }}
        </template>
        <template #actually_use_yarn_quantity="{ row }">
          {{ sumNum(row?.use_yarn_item_data, 'use_yarn_quantity') }} {{ row.unit_name }}
        </template>
        <template #operate="{ row, rowIndex }">
          <el-space>
            <el-link type="primary" :underline="false" @click="openYarnTotalModal(row, rowIndex)">
              选择
            </el-link>
            <el-link type="primary" :underline="false" @click="handleAutoSelect(row, rowIndex)">
              自动选择
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
  </div>
  <UseYarnTotalModal
    ref="useYarnTotalModalRef"
    @handle-delete="handleYarnDelete"
    @handle-yarn-info-sure="handleYarnInfoSure"
  />
  <AddXimaDialog ref="AddXimaDialogRef" @handle-sure="handleSureXima" />
  <AddProductionNoDialog ref="AddFabricDialogRef" @handle-sure="handFabric" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
</template>

<style></style>
