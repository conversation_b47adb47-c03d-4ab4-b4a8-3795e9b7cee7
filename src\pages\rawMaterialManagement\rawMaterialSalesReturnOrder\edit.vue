<script lang="ts" setup name="RawMaterialSalesReturnOrderEdit">
import { Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import currency from 'currency.js'
import SelectInformation from './components/SelectInformation/index.vue'
import SelectPurchasing from './components/SelectPurchasing/index.vue'
import { SaleReturnOrderDetail, SaleReturnOrderEdit } from '@/api/rawMaterIalSaleReturnOrder'
import { BusinessUnitIdEnum, EmployeeType } from '@/common/enum'
import {
  formatDate,
  formatMeterDiv,
  formatMeterMul,
  formatPriceDiv,
  formatPriceMul,
  formatUnderweightRateDiv,
  formatUnitPriceDiv,
  formatUnitPriceMul,
  formatWeightDiv,
  formatWeightMul,
  sumNum,
} from '@/common/format'
import { getFilterData } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import SelectBusinessDialog from '@/components/SelectBusinessDialog/index.vue'

const routerList = useRouterList()
const router = useRoute()
const state = reactive({
  form: {
    sale_system_id: '',
    sale_system_name: '',
    remark: '',
    rtn_unit_name: '',
    rtn_unit_id: '',
    customer_name: '',
    customer_id: '',
    rtn_date: '',
    warehouse_manager_name: '',
    warehouse_manager_id: '',
    rtn_address: '',
    rtn_phone: '',
    seller_name: '',
    seller_id: '',
  },
  tableData: [] as any[],
  fromRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    rtn_unit_name: [{ required: true, message: '请选择退货单位', trigger: 'change' }],
    customer_id: [{ required: true, message: '请选客户名称', trigger: 'change' }],
    rtn_date: [{ required: true, message: '请选择退货日期', trigger: 'change' }],
  },
})

onMounted(() => {
  getDataDetail()
})

// 获取订单详情
const { fetchData: fetchDataDetail, data: dataDetail } = SaleReturnOrderDetail()
async function getDataDetail() {
  await fetchDataDetail({ id: router.params.id })
}
watch(
  () => dataDetail.value,
  (val) => {
    state.form.sale_system_id = val.sale_system_id
    state.form.sale_system_name = val.sale_system_name
    state.form.remark = val.remark
    state.form.rtn_unit_name = val.rtn_unit_name
    state.form.rtn_unit_id = val.rtn_unit_id
    state.form.customer_name = val.customer_name
    state.form.customer_id = val.customer_id
    state.form.rtn_date = val.rtn_date
    state.form.warehouse_manager_name = val.warehouse_manager_name
    state.form.warehouse_manager_id = val.warehouse_manager_id
    state.form.rtn_address = val.rtn_address
    state.form.rtn_phone = val.rtn_phone
    state.form.seller_name = val.seller_name
    state.form.seller_id = val.seller_id
    state.tableData = formatPriceData(val.items || [])
  },
)
// 整理金额和数量
function formatPriceData(data: any[]) {
  return data.map((item) => {
    return {
      ...item,
      whole_piece_count: formatPriceDiv(item.whole_piece_count),
      bulk_piece_count: formatPriceDiv(item.bulk_piece_count),
      whole_piece_weight: formatWeightDiv(item.whole_piece_weight),
      bulk_weight: formatWeightDiv(item.bulk_weight),
      total_weight: formatWeightDiv(item.total_weight),
      deduction_weight: formatWeightDiv(item.deduction_weight),
      weight_shortage_rate: formatUnderweightRateDiv(item.weight_shortage_rate || 0),
      other_price: formatPriceDiv(item.other_price),
      unit_price: formatUnitPriceDiv(item.unit_price),
      total_price: formatPriceDiv(item.total_price),
      weight_shortage_loss_rate: formatMeterDiv(item.weight_shortage_loss_rate),
    }
  })
}

const saleSaleSystemInfo = ref()
function getSaleSystem(row: any) {
  state.form.sale_system_name = row?.name
  saleSaleSystemInfo.value = row
  clearData()
}

const showAdd = ref(false)
const showPurchasing = ref(false)

function handDel(row: any, rowIndex: number) {
  ElMessageBox.confirm('是否删除该数据?', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      state.tableData.splice(rowIndex, 1)
    })
    .catch(() => {})
}

function handAdd() {
  if (!state.form.rtn_unit_id)
    return ElMessage.error('请选择退货单位')
  showAdd.value = true
}

function handAddSale() {
  showPurchasing.value = true
}

function getRtnUnit(row: any) {
  state.form.rtn_unit_name = row?.name
  state.form.rtn_address = row?.address
  state.form.rtn_phone = row?.phone
}

const columnList = ref([
  {
    title: '原料信息',
    childrenList: [
      {
        field: 'raw_material_code',
        title: '原料编号',
        fixed: 'left',
        width: 150,
      },
      {
        field: 'raw_material_name',
        title: '原料名称',
        fixed: 'left',
        width: 100,
      },
      {
        field: 'sale_order_no',
        title: '销售单号',
        width: 160,
      },
      {
        field: 'customer_id',
        title: '所属客户',
        width: 150,
        soltName: 'customer_id',
      },
      {
        field: 'supplier_id',
        title: '供应商',
        width: 130,
        soltName: 'supplier_id',
      },
      {
        field: 'brand',
        title: '原料品牌',
        width: 100,
        soltName: 'brand',
      },
      {
        field: 'craft',
        title: '原料工艺',
        width: 100,
      },
      {
        field: 'color_scheme',
        title: '原料颜色',
        width: 100,
        soltName: 'color_scheme',
      },
      {
        field: 'measurement_unit_id',
        title: '计量单位',
        width: 100,
        soltName: 'measurement_unit_id',
      },
      {
        field: 'dyelot_number',
        title: '缸号',
        width: 100,
        soltName: 'dyelot_number',
      },
      {
        field: 'batch_num',
        title: '原料批号',
        width: 100,
        soltName: 'batch_num',
      },
      {
        field: 'level',
        title: '原料等级',
        width: 130,
        soltName: 'level',
      },
      {
        field: 'production_date',
        title: '生产日期',
        width: 150,
        isDate: true,
        soltName: 'production_date',
      },
      {
        field: 'spinning_type',
        title: '纺纱类型',
        width: 100,
        soltName: 'spinning_type',
      },
      {
        field: 'cotton_origin',
        title: '棉花产地',
        width: 100,
        soltName: 'cotton_origin',
      },
      {
        field: 'yarn_origin',
        title: '棉纱产地',
        width: 100,
        soltName: 'yarn_origin',
      },
      {
        field: 'carton_num',
        title: '装箱单号',
        width: 100,
        soltName: 'carton_num',
      },
      {
        field: 'fapiao_num',
        title: '发票号',
        width: 100,
        soltName: 'fapiao_num',
      },
      // {
      //   field: 'weight_shortage_loss_rate',
      //   title: '欠重损耗率',
      //   width: 100,
      //   soltName: 'weight_shortage_loss_rate',
      // },
      {
        field: 'raw_material_remark',
        title: '原料备注',
        width: 100,
        soltName: 'raw_material_remark',
      },
    ],
  },
  {
    title: '整件',
    childrenList: [
      {
        field: 'whole_piece_count',
        title: '退货件数',
        width: 150,
        soltName: 'whole_piece_count',
        required: true,
      },
      {
        field: 'whole_piece_weight',
        title: '退货件重(kg)',
        width: 150,
        isWeight: true,
        soltName: 'whole_piece_weight',
        required: true,
      },
    ],
  },
  {
    title: '散件',
    childrenList: [
      {
        field: 'bulk_piece_count',
        title: '退货件数',
        width: 150,
        soltName: 'bulk_piece_count',
      },
      {
        field: 'bulk_weight',
        title: '退货数量',
        width: 150,
        isWeight: true,
        soltName: 'bulk_weight',
      },
    ],
  },
  {
    title: '',
    childrenList: [
      {
        field: 'total_weight',
        title: '总退货数量',
        width: 150,
        isWeight: true,
        soltName: 'total_weight',
      },
    ],
  },
  {
    title: '金额信息',
    childrenList: [
      {
        field: 'other_price',
        title: '其他金额',
        width: 150,
        soltName: 'other_price',
      },
      {
        field: 'unit_price',
        title: '单价',
        width: 150,
        soltName: 'unit_price',
        required: true,
      },
      {
        field: 'total_price',
        title: '总金额',
        width: 150,
        soltName: 'total_price',
      },
    ],
  },
  {
    title: '',
    childrenList: [
      {
        field: 'remark',
        title: '备注',
        width: 150,
        soltName: 'remark',
      },
    ],
  },
])

const changeRow = ref<any>()
function changeData(row: any) {
  changeRow.value = row
}

const tablesRef = ref()
watch(
  () => [
    changeRow.value?.whole_piece_count,
    changeRow.value?.whole_piece_weight,
    changeRow.value?.bulk_piece_count,
    changeRow.value?.deduction_weight,
    changeRow.value?.bulk_weight,
    changeRow.value?.avg_actl_gross_weight,
    changeRow.value?.avg_actl_tare,
    changeRow.value?.avg_actl_net_weight,
    changeRow.value?.avg_actl_weight_shortage,
    changeRow.value?.unit_price,
    changeRow.value?.total_weight,
    changeRow.value?.other_price,
    changeRow.value?.actl_total_net_weight,
    changeRow.value?.actl_total_weight_shortage,
    changeRow.value?.weight_shortage_rate,
  ],
  () => {
    computedData(changeRow.value)
  },
  {
    deep: true,
  },
)

function computedData(row: any) {
  // 总退货数量=整件退货件数*整件退货件重+散件退货数量
  row.total_weight = Number.parseFloat(
    currency(row.whole_piece_count || 0)
      .multiply(row?.whole_piece_weight || 0)
      .add(row?.bulk_weight || 0)
      .value.toFixed(4),
  )

  // 金额=总退货数量*单价+其他金额
  row.total_price = Number.parseFloat(
    currency(row?.total_weight || 0)
      .multiply(row?.unit_price || 0)
      .add(row?.other_price || 0)
      .value.toFixed(2),
  )

  tablesRef.value.tableRef.updateFooter()
}

const bulkShow = ref(false)
const bulkSetting = ref<any>({
  address: {},
})

const multipleSelection = ref<any[]>([])
async function bulkSubmit({ row, value }: any) {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  if (!value[row.field])
    return ElMessage.error('请输入参数')

  multipleSelection.value?.map((item: any) => {
    item[row.field] = value[row.field]
    computedData(item)
  })
  ElMessage.success('设置成功')
}

function bulkHand() {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')
  bulkShow.value = true
}

function onSubmit(row: any) {
  showAdd.value = false
  const data = row?.map((item: any) => {
    return {
      raw_material_code: item.code,
      raw_material_name: item.name,
      craft: item.craft,
      level: item.level,
      raw_material_id: item.id,
      customer_id: saleSaleSystemInfo.value?.default_customer_id || '',
      customer_name: saleSaleSystemInfo.value?.default_customer_name || '',
      whole_piece_count: 0,
      whole_piece_weight: 0,
      bulk_piece_count: 0,
      bulk_weight: 0,
      other_price: 0,
      unit_price: 0,
      color_id: '',
      measurement_unit_id: item.unit_id,
      dyelot_number: '',
    }
  })
  state.tableData = [...state.tableData, ...data]
}

function onSubmitPurchasing(row: any) {
  showPurchasing.value = false
  const data = row?.map((item: any) => {
    return {
      raw_material_code: item.raw_material_code,
      raw_material_name: item.raw_material_name,
      sale_order_no: item.order_no,
      brand: item.brand,
      color_scheme: item.color_scheme,
      batch_num: item.batch_num,
      // level: item.level,
      yarn_origin: item.yarn_origin,
      carton_num: item.carton_num,
      weight_shortage_loss_rate: item.weight_shortage_loss_rate,
      spinning_type: item.spinning_type,
      raw_material_remark: item.raw_material_remark,
      // whole_piece_count: item.whole_piece_count || 0,
      whole_piece_weight: item.whole_piece_weight || 0,
      // bulk_piece_count: item.bulk_piece_count || 0,
      bulk_weight: item.bulk_weight || 0,
      raw_material_id: item.raw_material_id,
      sale_order_item_id: item.id,
      customer_id: saleSaleSystemInfo.value?.default_customer_id || '',
      customer_name: saleSaleSystemInfo.value?.default_customer_name || '',
      other_price: 0,
      unit_price: 0,
      whole_piece_count: formatPriceDiv(item.whole_piece_unrtn || 0),
      bulk_piece_count: formatPriceDiv(item.bulk_piece_count_unrtn || 0),

      supplier_id: item.supplier_id || '',
      level_id: item.level_id || '',
      cotton_origin: item.cotton_origin || '',
      fapiao_num: item.fapiao_num || '',
      production_date: item.production_date || '',
      color_id: item.color_id,
      measurement_unit_id: item.measurement_unit_id,
      dyelot_number: '',
    }
  })
  state.tableData = [...state.tableData, ...data]
}

const { fetchData: fetchDataAdd, data: addData, success: successAdd, msg: msgAdd } = SaleReturnOrderEdit()
const ruleFormRef = ref()
async function handSubmit() {
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      if (validateList())
        return false
      const res = fomatData()
      await fetchDataAdd(getFilterData({ ...state.form, id: Number.parseInt(router.params.id as string), rtn_date: formatDate(state.form.rtn_date), items: res || [] }))
      if (successAdd.value) {
        getDataDetail()
        ElMessage.success('编辑成功')
        routerList.push({
          name: 'RawMaterialSalesReturnOrderDetail',
          params: { id: addData.value.id },
        })
      }
      else {
        ElMessage.error(msgAdd.value)
      }
    }
  })
}

// 验证坯布信息字段
function validateList() {
  let msg = ''
  if (!state.tableData || state.tableData.length === 0) {
    msg = '原料信息不能为空'
  }
  else {
    state.tableData?.some((item: any) => {
      if (!item.raw_material_code) {
        msg = `原料编号不能为空`
        return true
      }
      if (!item.raw_material_name) {
        msg = `编号为${item.raw_material_code}的数据,原料名称不能为空`
        return true
      }
      if (!item.whole_piece_count) {
        msg = `编号为${item.raw_material_code}的数据,,退货件数不能为空`
        return true
      }
      if (!item.whole_piece_weight) {
        msg = `编号为${item.raw_material_code}的数据,退货件重不能为空`
        return true
      }
      if (!item.unit_price) {
        msg = `编号为${item.raw_material_code}的数据,单价不能为空`
        return true
      }
    })
  }
  msg && ElMessage.error(msg)
  return msg
}

// // 整理提交信息
function fomatData() {
  return state.tableData?.map((item: any) => {
    const { bulk_weight, bulk_piece_count, other_price, weight_shortage_loss_rate, unit_price, whole_piece_count, whole_piece_weight, production_date } = item
    const res = getFilterData({
      ...item,
      production_date: formatDate(production_date),
      whole_piece_count: formatPriceMul(whole_piece_count),
      whole_piece_weight: formatWeightMul(whole_piece_weight),
      bulk_piece_count: formatPriceMul(bulk_piece_count),
      bulk_weight: formatWeightMul(bulk_weight),
      other_price: formatPriceMul(other_price),
      unit_price: formatUnitPriceMul(unit_price),
      weight_shortage_loss_rate: formatMeterMul(weight_shortage_loss_rate),
    })
    return res
  })
}

const bulkList = reactive<any>([
  {
    field: 'customer_id',
    field_name: 'customer_name',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
    query: { sale_system_id: state.form.sale_system_id },
  },
  {
    field: 'supplier_id',
    field_name: 'supplier_name',
    title: '供应商',
    component: 'select',
    api: 'BusinessUnitSupplierEnumlist',
    query: { unit_type_id: BusinessUnitIdEnum.rawMaterial },
  },
  {
    field: 'brand',
    title: '原料品牌',
    component: 'input',
    type: 'text',
  },
  {
    field: 'color_id',
    title: '原料颜色',
    component: 'select',
    api: 'GetRawMaterialColor',
  },
  {
    field: 'dyelot_number',
    title: '缸号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'batch_num',
    title: '原料批号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'level',
    title: '原料等级',
    component: 'select',
    api: 'GetInfoBaseRawMaterialLevelEnumList',
  },
  {
    field: 'production_date',
    title: '生产日期',
    component: 'selectDate',
    type: 'date',
  },
  {
    field: 'cotton_origin',
    title: '棉花产地',
    component: 'input',
    type: 'text',
  },
  {
    field: 'yarn_origin',
    title: '棉纱产地',
    component: 'input',
    type: 'text',
  },
  {
    field: 'carton_num',
    title: '装箱单号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'fapiao_num',
    title: '发票号',
    component: 'input',
    type: 'text',
  },
  // {
  //   field: 'weight_shortage_loss_rate',
  //   title: '欠重损耗率',
  //   component: 'input',
  //   type: 'float',
  // },
  {
    field: 'spinning_type',
    title: '纺纱类型',
    component: 'input',
    type: 'text',
  },
  {
    field: 'raw_material_remark',
    title: '原料备注',
    component: 'input',
    type: 'text',
  },
  {
    field: 'whole_piece_count',
    title: '整件退货件数',
    component: 'input',
    type: 'integer',
  },
  {
    field: 'whole_piece_weight',
    title: '整件退货件重',
    component: 'input',
    type: 'float',
  },
  {
    field: 'bulk_piece_count',
    title: '散件退货件数',
    component: 'input',
    type: 'integer',
  },
  {
    field: 'bulk_weight',
    title: '散件退货数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'other_price',
    title: '其他金额(kg)',
    component: 'input',
    type: 'float',
  },
  {
    field: 'unit_price',
    title: '单价',
    component: 'input',
    type: 'float',
    digits: 4,
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'textarea',
  },
])

function handAllSelect({ records }: any) {
  multipleSelection.value = records
}

function handleSelectionChange({ records }: any) {
  multipleSelection.value = records
}

function footerMethod({ columns, data }: { columns: any, data: any }) {
  const footerData = [
    columns.map((column: any, _columnIndex: number) => {
      if (['raw_material_code'].includes(column.field))
        return '合计'

      if (['bulk_piece_count', 'bulk_weight', 'whole_piece_count', 'total_weight', 'other_price', 'total_price'].includes(column.field))
        return sumNum(data, column.field)
    }),
  ]
  return footerData
}
function handBulkClose() {
  bulkShow.value = false
}

let oneStatus = true
function clearData() {
  if (!oneStatus) {
    state.tableData?.map((item) => {
      item.customer_id = saleSaleSystemInfo.value?.default_customer_id || ''
      item.customer_name = saleSaleSystemInfo.value?.default_customer_name || ''
    })
  }
  oneStatus = false
  bulkList[0].query = { sale_system_id: state.form.sale_system_id }
  bulkSetting.value.customer_id = ''
}
function getCoustome(row: any) {
  state.form.sale_system_id = row.select_sale_system_id
  state.form.customer_name = row.name
  state.form.seller_id = row.seller_id
  state.form.seller_name = row.seller_name
}
const tableConfig = ref({
  showOperate: true,
  operateWidth: 100,
  bulkSubmit,
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  footerMethod,
  showSpanHeader: true,
})
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <div class="line" />
    <template #right-top>
      <el-button v-btnAntiShake="handSubmit" type="primary">
        提交
      </el-button>
    </template>
    <slot>
      <el-form ref="ruleFormRef" :model="state.form" label-width="60px" label-position="top" :rules="state.fromRules">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem required label="客户名称:">
            <template #content>
              <el-form-item prop="customer_id">
                <SelectCustomerDialog
                  v-model="state.form.customer_id"
                  show-choice-system
                  is-merge
                  field="name"
                  :default-value="{
                    id: dataDetail.customer_id,
                    name: dataDetail.customer_name,
                  }"
                  @change-value="getCoustome"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="营销体系名称:">
            <template #content>
              <el-form-item prop="sale_system_id">
                <SelectComponents v-model="state.form.sale_system_id" api="GetSaleSystemDropdownListApi" label-field="name" value-field="id" @change-value="getSaleSystem" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="收货单位名称:">
            <template #content>
              <el-form-item prop="rtn_unit_id">
                <SelectBusinessDialog
                  v-model="state.form.rtn_unit_id"
                  :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory }"
                  api="GetBusinessUnitListApi"
                  :default-value="{
                    id: dataDetail.rtn_unit_id,
                    name: dataDetail.rtn_unit_name,
                  }"
                  @change-value="row => getRtnUnit(row)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="退货日期:">
            <template #content>
              <el-form-item prop="rtn_date">
                <SelectDate v-model="state.form.rtn_date" type="date" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="仓管员:">
            <template #content>
              <el-form-item prop="warehouse_manager_id">
                <SelectComponents
                  v-model="state.form.warehouse_manager_id"
                  api="Adminemployeelist"
                  :query="{ duty: EmployeeType.warehouseManager }"
                  label-field="name"
                  value-field="id"
                  @change-value="row => (state.form.warehouse_manager_name = row?.name)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="退货地址:">
            <template #content>
              <el-form-item prop="rtn_address">
                <el-input v-model="state.form.rtn_address" placeholder="退货地址" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="收货电话:">
            <template #content>
              <el-form-item prop="rtn_phone">
                <el-input v-model="state.form.rtn_phone" placeholder="退货电话" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="销售员:">
            <template #content>
              <el-form-item prop="seller_id">
                <SelectComponents
                  v-model="state.form.seller_id"
                  api="GetEmployeeListEnum"
                  :query="{ duty: EmployeeType.salesman }"
                  label-field="name"
                  value-field="id"
                  @change-value="row => (state.form.seller_name = row?.name)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="备注:" copies="2">
            <template #content>
              <el-form-item prop="userName">
                <el-input v-model="state.form.remark" type="textarea" placeholder="请输入备注" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
        </div>
      </el-form>
    </slot>
  </FildCard>
  <FildCard title="原料信息" :tool-bar="false" class="mt-[5px]">
    <template #right-top>
      <el-button style="margin-left: 10px" type="primary" @click="bulkHand">
        批量操作
      </el-button>
      <el-button style="margin-left: 10px" type="primary" :icon="Plus" @click="handAddSale">
        根据销售单添加
      </el-button>
      <el-button style="margin-left: 10px" type="primary" :icon="Plus" @click="handAdd">
        根据资料添加
      </el-button>
    </template>
    <Table ref="tablesRef" :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
      <template #customer_id="{ row }">
        <SelectCustomerDialog
          v-model="row.customer_id"
          :query="{ sale_system_id: state.form.sale_system_id }"
          :default-value="{
            id: row.customer_id,
            name: row.customer_name,
          }"
        />
      </template>
      <template #supplier_id="{ row }">
        <SelectBusinessDialog
          v-model="row.supplier_id"
          :query="{ unit_type_id: BusinessUnitIdEnum.rawMaterial }"
          api="BusinessUnitSupplierEnumlist"
          :default-value="{
            id: row.supplier_id,
            name: row.supplier_name,
          }"
        />
      </template>
      <template #brand="{ row }">
        <vxe-input v-model="row.brand" maxlength="200" :min="0" size="mini" />
      </template>
      <template #color_scheme="{ row }">
        <SelectDialog
          v-model="row.color_id"
          :query="{
            raw_matl_id: row.raw_material_id,
            name: row.color_name,
          }"
          api="GetRawMaterialColor"
          label-field="name"
          :column-list="[
            {
              field: 'name',
              title: '原料颜色',
              minWidth: 100,
            },
            {
              field: 'code',
              title: '原料色号',
              minWidth: 100,
            },
          ]"
          :table-column="[
            {
              field: 'name',
              title: '原料颜色',
              defaultData: {
                id: row?.color_id,
                name: row?.name,
                code: row?.code,
              },
            },
          ]"
          @on-input="(val) => row.color_name = val"
        />
      </template>
      <template #measurement_unit_id="{ row }">
        <SelectComponents v-model="row.measurement_unit_id" disabled api="getInfoBaseMeasurementUnitEnumList" label-field="name" value-field="id" clearable />
      </template>
      <template #dyelot_number="{ row }">
        <vxe-input v-model="row.dyelot_number" clearable />
      </template>
      <template #batch_num="{ row }">
        <vxe-input v-model="row.batch_num" maxlength="200" :min="0" size="mini" />
      </template>
      <template #level="{ row }">
        <SelectComponents v-model="row.level_id" size="small" api="GetInfoBaseRawMaterialLevelEnumList" label-field="name" value-field="id" />
      </template>
      <template #production_date="{ row }">
        <SelectDate v-model="row.production_date" style="width: 150px" type="date" />
      </template>
      <template #spinning_type="{ row }">
        <vxe-input v-model="row.spinning_type" maxlength="200" :min="0" size="mini" />
      </template>
      <template #cotton_origin="{ row }">
        <vxe-input v-model="row.cotton_origin" maxlength="200" :min="0" size="mini" />
      </template>
      <template #yarn_origin="{ row }">
        <vxe-input v-model="row.yarn_origin" maxlength="200" :min="0" size="mini" />
      </template>
      <template #carton_num="{ row }">
        <vxe-input v-model="row.carton_num" maxlength="200" :min="0" size="mini" />
      </template>
      <template #fapiao_num="{ row }">
        <vxe-input v-model="row.fapiao_num" maxlength="200" :min="0" size="mini" />
      </template>
      <!-- <template #weight_shortage_loss_rate="{ row }">
        <vxe-input maxlength="200" :min="0" type="float" size="mini" v-model="row.weight_shortage_loss_rate"></vxe-input>
      </template> -->
      <template #raw_material_remark="{ row }">
        <vxe-input v-model="row.raw_material_remark" maxlength="200" :min="0" size="mini" />
      </template>
      <template #whole_piece_count="{ row }">
        <vxe-input v-model="row.whole_piece_count" maxlength="200" type="integer" :min="0" size="mini" @input="changeData(row)" />
      </template>
      <template #whole_piece_weight="{ row }">
        <vxe-input v-model="row.whole_piece_weight" maxlength="200" type="float" :min="0" size="mini" @input="changeData(row)" />
      </template>
      <template #bulk_piece_count="{ row }">
        <vxe-input v-model="row.bulk_piece_count" maxlength="200" type="integer" :min="0" size="mini" @input="changeData(row)" />
      </template>
      <template #bulk_weight="{ row }">
        <vxe-input v-model="row.bulk_weight" maxlength="200" type="float" :min="0" size="mini" @input="changeData(row)" />
      </template>
      <template #total_weight="{ row }">
        {{ row.total_weight }}
      </template>
      <template #other_price="{ row }">
        <vxe-input v-model="row.other_price" maxlength="200" type="float" :min="0" size="mini" @input="changeData(row)" />
      </template>
      <template #unit_price="{ row }">
        <vxe-input v-model="row.unit_price" maxlength="200" type="float" :digits="4" :min="0" size="mini" @input="changeData(row)" />
      </template>
      <template #total_price="{ row }">
        {{ row.total_price }}
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" maxlength="200" size="mini" />
      </template>
      <template #operate="{ row, rowIndex }">
        <el-button type="text" @click="handDel(row, rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <SelectInformation v-model="showAdd" :unit_id="state.form.rtn_unit_id" @submit="onSubmit" />
  <SelectPurchasing v-model="showPurchasing" :sale_system_id="state.form.sale_system_id" :customer_id="state.form.customer_id" :customer_name="state.form.customer_name" @submit="onSubmitPurchasing" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}
</style>
