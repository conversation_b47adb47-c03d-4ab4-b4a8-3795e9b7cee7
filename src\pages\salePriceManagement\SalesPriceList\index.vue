<script setup lang="ts" name="SalesPriceList">
import { Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import dayjs from 'dayjs'
import { DeleteSalePriceColorKind, GetSalePriceColorKindList, GetSalePriceColorKindListExport, UpdateSalePriceColorKindStatus } from '@/api/salesPriceList'
import { formatTime } from '@/common/format'
import { debounce, getFilterData } from '@/common/util'
import Accordion from '@/components/Accordion/index.vue'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import StatusTag from '@/components/StatusTag/index.vue'
import Table from '@/components/Table.vue'
import router from '@/router'
import { useStorageForm } from '@/use/useStorageForm'
import SelectCascader from '@/components/SelectCascader/productType.vue'
import { GetKindAndProductList } from '@/api/finishedProductColorInformation'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'

enum SelectType {
  TOP_CONDITION_FILTER = 1, // 顶部条件筛选
  LEFT_FILTERING = 2, // 左侧筛选
}

const defaultSearchField = {
  status: '',
  product_kind_id: '',
  product_color_kind_id: '',
  product_color_id: '',
  product_id: '',
}
const searchData = ref(defaultSearchField)

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
  finish_product_code: '',
  finish_product_name: '',
})

// 选择成品
function changeProductSelect(val: any) {
  searchData.value.product_id = val?.id || ''
  componentRemoteSearch.finish_product_code = val?.finish_product_code || ''
  componentRemoteSearch.finish_product_name = val?.finish_product_name || ''
}

const state = reactive({
  tableData: [],
  filterDataSelect: {
    type_grey_fabric_id: null, // 布种类型
    finish_product_code: '', // 成品编号
  },
  multipleSelectionFilter: [] as any[],
  selectType: SelectType.TOP_CONDITION_FILTER, // 1表示顶部条件筛选，2表示左侧筛选
  selectProductIds: [] as any[],
})

const {
  fetchData: ApiCustomerListSelect,
  data: dataSelect,
  total: totalSelect,
  loading: loadingSelect,
  page: pageSelect,
  size: sizeSelect,
  handleSizeChange: handleSizeChangeSelect,
  handleCurrentChange: handleCurrentChangeSelect,
} = GetKindAndProductList()

// 获取筛选数据
const getDataSelect = debounce(() => {
  ApiCustomerListSelect(
    getFilterData({
      ...state.filterDataSelect,
      status: searchData.value.status,
      type_grey_fabric_id: state.filterDataSelect.type_grey_fabric_id?.join(','),
    }),
  )
}, 400)
watch(
  () => state.filterDataSelect,
  () => {
    getDataSelect()
    if (state.selectProductIds.length > 0) {
      state.selectProductIds = []
      getList()
    }
    clearSelectEvent()
  },
  {
    deep: true,
  },
)

const tableRefSelect = ref()

function clearSelectEvent() {
  state.selectProductIds = []
  const $table = tableRefSelect.value
  if ($table)
    $table.tableRef.clearCheckboxRow()
}

const columnListSelect = ref([
  {
    field: 'type_grey_fabric_name',
    title: '布种类型',
    fixed: 'left',
    minWidth: 100,
  },
  {
    field: 'finish_product_code',
    title: '成品编号',
    fixed: 'left',
    minWidth: 100,
  },
])

function filterHandleSelectionChange({ records, checked, row }: any) {
  if (checked) {
    state.multipleSelectionFilter = [...state.multipleSelectionFilter, ...records]
  }
  else {
    state.multipleSelectionFilter = state.multipleSelectionFilter.filter((item: any) => {
      return item.id !== row.id
    })
  }
  getSelectId([...new Set(state.multipleSelectionFilter)])
}

function getSelectId(records: any) {
  state.selectType = 2
  state.selectProductIds = []
  records?.map((item: any) => {
    state.selectProductIds = [...new Set([...state.selectProductIds, item.id])]
  })
  searchData.value = defaultSearchField
  getList()
}

const tableConfigSelect = ref({
  loading: loadingSelect,
  showPagition: true,
  page: pageSelect,
  filterStatus: false,
  size: sizeSelect,
  total: totalSelect,
  showCheckBox: true,
  showSort: false,
  show_footer: false,
  checkboxConfig: {
    highlight: true,
    reserve: true,
    trigger: 'row',
  },
  height: '100%',
  handleSizeChange: handleSizeChangeSelect,
  handleCurrentChange: handleCurrentChangeSelect,
  handleSelectionChange: filterHandleSelectionChange,
  pageLayout: 'prev, pager, next',
})

const columnListInit = [
  {
    field: 'name',
    title: '成品信息',
    childrenList: [
      {
        field: 'product_code',
        title: '编号',
        width: '80',
        soltName: 'product_code',
      },
      {
        field: 'product_name',
        title: '名称',
        width: '110',
      },
      {
        field: 'product_color_kind_name',
        title: '颜色类别',
        width: '6%',
      },
      {
        field: 'product_color_list',
        title: '色号颜色',
        width: '100',
        soltName: 'product_color_list',
      },
    ],
  },
  {
    field: 'measurement_unit_name',
    title: '主单位',
    width: '80',
  },
  {
    field: '',
    title: '标准报价',
    childrenList: [
      {
        field: 'target_length_cut_sale_price',
        title: '剪版价-非主单位',
        isUnitPrice: true,
        width: '120',
      },
      {
        field: 'target_weight_cut_sale_price',
        title: '剪版价-主单位',
        isUnitPrice: true,
        width: '110',
      },
      {
        field: 'weight_error',
        title: '空差减重',
        isWeight: true,
        width: '80',
      },
      {
        field: 'target_bulk_sale_price',
        title: '标准售价',
        isUnitPrice: true,
        width: '80',
      },
      {
        field: 'target_bulk_sale_limit_price',
        title: '最低售价',
        isUnitPrice: true,
        width: '80',
      },
    ],
  },
  {
    field: '',
    title: '其它信息',
    childrenList: [
      {
        field: 'product_kind_name',
        title: '布种类别',
        width: '80',
      },
      {
        field: 'product_paper_tube_weight',
        title: '纸筒',
        isWeight: true,
        width: '80',
      },
      {
        field: 'product_weight_error',
        title: '空差',
        isWeight: true,
        width: '60',
      },
      {
        field: 'product_ingredient',
        title: '成分',
        width: '100',
      },
      {
        field: 'order_no',
        title: '最新调价单',
        width: '8%',
      },
      {
        field: 'create_time',
        title: '更新时间',
        width: '100',
        isDate: true,
      },
    ],
  },
  {
    title: '其它信息',
    childrenList: [
      {
        field: 'status',
        title: '状态',
        width: '5%',
        soltName: 'status',
        showStatus: true,
        fixed: 'right',
      },
    ],
  },
]
const columnList = ref()

const searchDataTitle = ref<any>({
  product_title_id: [],
  product_left_id: [],
})

onMounted(() => {
  getList()
  getDataSelect()
})

// 获取列表
const { fetchData: fetchDataList, data: listData, loading, total, page, size, handleSizeChange, handleCurrentChange } = GetSalePriceColorKindList()
async function getList() {
  await fetchDataList(getQuery())
  state.selectType = SelectType.TOP_CONDITION_FILTER
  formatData()
}

watch(listData, () => {
  formatData()
})

// 列表请求参数
function getQuery() {
  return getFilterData({ ...searchData.value, product_ids: state.selectProductIds.join(',') })
}

// 整理数据
function formatData() {
  const config: any = []
  columnList.value = JSON.parse(JSON.stringify(columnListInit))
  state.tableData = listData.value?.list?.map((item: any, index: any) => {
    item?.level_item?.map((level_item: any) => {
      if (index === 0) {
        config.push({
          field: '',
          title: level_item.sale_level_name,
          childrenList: [
            {
              field: `weight_error_${level_item.sale_level_id}`,
              title: '空差减重',
              isWeight: true,
              width: '80',
            },
            {
              field: `target_bulk_sale_price_${level_item.sale_level_id}`,
              title: '售价',
              isUnitPrice: true,
              width: '60',
            },
          ],
        })
      }
      item[`weight_error_${level_item.sale_level_id}`] = level_item.weight_error
      item[`target_bulk_sale_price_${level_item.sale_level_id}`] = level_item.target_bulk_sale_price
    })
    return item
  })
  columnList.value.splice(3, 0, ...config)
}

watch(
  () => searchData.value,
  debounce(() => {
    if (state.selectType === SelectType.TOP_CONDITION_FILTER) {
      clearSelectEvent()
      getList()
    }
  }, 400),
)

// 新建
const { bindData } = useStorageForm({}, 'salePriceParams')
const showAdd = ref(false)
const selectRow = ref<any[]>([])
function handleAdd() {
  showAdd.value = true
  if (!selectRow.value.length)
    return ElMessage.error('请先选择调价数据')
  let ids_data = []
  ids_data = selectRow.value?.map((item) => {
    return item.id
  })
  bindData(ids_data.join(','))
  router.push({
    name: 'SalesPriceAdjustmentOrderAdd',
  })
}

// 调价
function handleCreate() {
  router.push({
    name: 'SalesPriceAdjustmentOrderCreate',
  })
}

// 删除
async function handDelete(row: any) {
  delConfirmBoxHand(row)
}

async function updateStatus(row: any) {
  disabledConfirmBoxHand(row, row.status === 1 ? 2 : 1)
}

watch(
  () => searchData.value,
  debounce(() => getList(), 300),
  {
    deep: true,
  },
)

function handAllSelect(val: any) {
  selectRow.value = val.records
}

function handleSelectionChange(val: any) {
  selectRow.value = val.records
}

async function onDelBulk() {
  if (selectRow.value.length <= 0)
    return ElMessage.error('请先勾选数据')
  delConfirmBoxHand(selectRow.value)
  selectRow.value = []
}

async function onStopBulk(status: 1 | 2) {
  if (selectRow.value.length <= 0)
    return ElMessage.error('请先勾选数据')
  disabledConfirmBoxHand(selectRow.value, status)
  selectRow.value = []
}

const { fetchData: updateFetchData, success: successUpdate, msg: msgUpdate } = UpdateSalePriceColorKindStatus()
async function disabledConfirmBoxHand(row: any, status: number) {
  ElMessageBox.confirm(`确认修改该数据状态？`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    inputPlaceholder: '请输入删除备注',
  })
    .then(async () => {
      const filterData: any = {
        item: [],
        status: 1,
      }
      if (!(Array.isArray(row))) {
        filterData.item = [
          {
            id: row.id,
            product_color_kind_id: row.product_color_kind_id,
            product_id: row.product_id,
            product_kind_id: row.product_kind_id,
          },
        ]
        filterData.status = status
      }
      else {
        row?.map((item) => {
          filterData.item.push({
            id: item.id,
            product_color_kind_id: item.product_color_kind_id,
            product_id: item.product_id,
          })
        })
        filterData.status = status
      }
      await updateFetchData(filterData)
      if (successUpdate.value) {
        ElMessage.success('修改成功')
        getList()
      }
      else {
        ElMessage.error(msgUpdate.value)
      }
    })
    .catch(() => {})
}

const { fetchData: fetchDataDel, success: successDel, msg: msgDel } = DeleteSalePriceColorKind()
async function delConfirmBoxHand(row: any) {
  ElMessageBox.prompt(`确认删除该数据？`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
  })
    .then(async ({ value }) => {
      const filterData: any = {
        item: [],
        delete_remark: '',
      }
      if (!(Array.isArray(row))) {
        filterData.item = [
          {
            id: row.id,
            product_color_kind_id: row.product_color_kind_id,
            product_id: row.product_id,
          },
        ]
        filterData.delete_remark = value
      }
      else {
        row?.map((item) => {
          filterData.item.push({
            id: item.id,
            product_color_kind_id: item.product_color_kind_id,
            product_id: item.product_id,
          })
        })
        filterData.delete_remark = value
      }
      await fetchDataDel(filterData)
      if (successDel.value) {
        ElMessage.success('删除成功')
        getList()
      }
      else {
        ElMessage.error(msgDel.value)
      }
    })
    .catch(() => {})
}

// function getSelectChildKey(val: number[] | string[] = []) {
//   searchDataTitle.value.product_left_id = val
//   searchData.value.product_ids = val.join(',')
//   searchDataTitle.value.product_title_id = ''
// }

// function selectChange(val: any = []) {
//   const ids = val && isArray(val) ? val?.map((item: any) => item.id) : []
//   searchData.value.product_ids = ids.join(',')
//   searchDataTitle.value.product_title_id = ids
//   searchDataTitle.value.product_left_id = []
// }
// 导出
const loadingExcel = ref(false)
async function handleExport() {
  if (!state?.tableData?.length || state?.tableData?.length <= 0)
    return ElMessage.warning('当前无数据可导出')
  const name_str = `销售报价表${dayjs().format('YYYY-MM-DD')}`
  const { fetchData: getFetch, success: getSuccess, msg: getMsg } = GetSalePriceColorKindListExport({ nameFile: name_str })
  loadingExcel.value = true
  await getFetch({
    ...getFilterData(getQuery()),
    download: 1,
  })
  if (getSuccess.value) {
    ElMessage({
      type: 'success',
      message: '成功',
    })
  }
  else {
    ElMessage({
      type: 'error',
      message: getMsg.value,
    })
  }
  loadingExcel.value = false
}

const tableConfig = computed(() => ({
  showSlotNums: true,
  showPagition: true,
  filterStatus: false,
  page: page.value,
  size: size.value,
  total: total.value,
  loading: loading.value,
  showCheckBox: true,
  height: '100%',
  showOperate: true,
  operateWidth: '8%',
  showSort: false,
  handleSizeChange,
  handleCurrentChange,
  handAllSelect,
  handleSelectionChange,
}))

function handleDetail(row: any) {
  router.push({
    name: 'FinishedProductInformationDetail',
    params: { id: row.product_id },
  })
}

function handleColorDetail(row: any) {
  router.push({
    name: 'FinishedProductColorInformationDetail',
    params: { id: row.product_color_id },
  })
}
</script>

<template>
  <div class="flex flex-col h-full overflow-hidden">
    <FildCard :tool-bar="false">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="成品编号:">
          <template #content>
            <SelectProductDialog
              v-model="searchDataTitle.product_id"
              field="finish_product_code"
              :label-name="componentRemoteSearch.finish_product_code"
              :query="{
                finish_product_code: componentRemoteSearch.finish_product_code,
              }"
              @on-input="
                (val) => (componentRemoteSearch.finish_product_code = val)
              "
              @change-value="changeProductSelect"
            />
            <!--            <SelectDialog -->
            <!--              v-model="searchDataTitle.product_id" -->
            <!--              label-field="finish_product_code" -->
            <!--              :label-name="componentRemoteSearch.finish_product_code" -->
            <!--              :query="{ -->
            <!--                finish_product_code: componentRemoteSearch.finish_product_code, -->
            <!--              }" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品名称', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      title: '成品名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品编号', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      title: '成品编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '成品编号', -->
            <!--                }, -->
            <!--              ]" -->
            <!--              @on-input=" -->
            <!--                (val) => (componentRemoteSearch.finish_product_code = val) -->
            <!--              " -->
            <!--              @change-value="changeProductSelect" -->
            <!--            /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称:">
          <template #content>
            <SelectProductDialog
              v-model="searchDataTitle.product_id"
              field="finish_product_name"
              :label-name="componentRemoteSearch.finish_product_name"
              :query="{
                finish_product_name: componentRemoteSearch.finish_product_name,
              }"
              @change-value="changeProductSelect"
              @on-input="
                (val) => (componentRemoteSearch.finish_product_name = val)
              "
            />
            <!--            <SelectDialog -->
            <!--              v-model="searchDataTitle.product_id" -->
            <!--              label-field="finish_product_name" -->
            <!--              :label-name="componentRemoteSearch.finish_product_name" -->
            <!--              :query="{ -->
            <!--                finish_product_name: componentRemoteSearch.finish_product_name, -->
            <!--              }" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品名称', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      title: '成品名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品编号', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      title: '成品编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  title: '成品名称', -->
            <!--                }, -->
            <!--              ]" -->
            <!--              @change-value="changeProductSelect" -->
            <!--              @on-input=" -->
            <!--                (val) => (componentRemoteSearch.finish_product_name = val) -->
            <!--              " -->
            <!--            /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="布种类型:">
          <template #content>
            <SelectCascader @change-value="({ ids }) => searchData.product_kind_id = ids?.[ids.length - 1] || ''" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="颜色类别:">
          <template #content>
            <SelectComponents v-model="searchData.product_color_kind_id" style="width: 200px" api="GetTypeFinishedProductColorEnumList" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="状态:">
          <template #content>
            <SelectComponents v-model="searchData.status" style="width: 200px" api="StatusListApi" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <div class="flex flex-row flex-1 overflow-hidden">
      <!--    <Accordion :open-status="true"> -->
      <!--      <div class="w-[500px]"> -->
      <!--        <ProductClassTree @select-child="getSelectChildKey" :default-ids="searchDataTitle.product_left_id"></ProductClassTree> -->
      <!--      </div> -->
      <!--    </Accordion> -->
      <Accordion class="mr-[5px] mt-[5px]" :open-status="false">
        <div class="flex mb-2">
          <SelectCascader v-model="state.filterDataSelect.type_grey_fabric_id" style="width: 100%" />
          <el-input v-model="state.filterDataSelect.finish_product_code" size="small" placeholder="请填写成品编号" class="ml-[5px]" clearable />
        </div>
        <Table ref="tableRefSelect" :config="tableConfigSelect" :table-list="dataSelect?.list" :column-list="columnListSelect" />
      </Accordion>
      <FildCard title="" :tool-bar="false" class="flex flex-col mt-[5px] h-full flex-1 overflow-hidden">
        <template #right-top>
          <el-button v-has="`SalesPriceList_add`" style="margin-left: 10px" type="primary" :icon="Plus" @click="handleCreate">
            新建
          </el-button>
          <el-button v-has="`SalesPriceList_add`" plain style="margin-left: 10px" type="primary" @click="handleAdd">
            调价
          </el-button>
          <el-dropdown>
            <el-button class="ml-2">
              批量操作
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <div v-has="`SalesPriceList_del`">
                  <el-dropdown-item @click="onDelBulk">
                    批量删除
                  </el-dropdown-item>
                </div>
                <div v-has="`SalesPriceList_statsu`">
                  <el-dropdown-item @click="onStopBulk(2)">
                    批量停用
                  </el-dropdown-item>
                </div>
                <div v-has="`SalesPriceList_statsu`">
                  <el-dropdown-item @click="onStopBulk(1)">
                    批量启用
                  </el-dropdown-item>
                </div>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <BottonExcel v-has="'SalesPriceList_export'" class="ml-2" :loading="loadingExcel" title="导出文件" @on-click-excel="handleExport" />
        </template>
        <Table :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
          <template #product_code="{ row }">
            <el-link type="primary" :underline="false" @click="handleDetail(row)">
              {{ row.product_code }}
            </el-link>
          </template>
          <template #product_color_list="{ row }">
            <template v-for="(item, index) in row.product_color_list" :key="index">
              <el-link type="primary" :underline="false" @click="handleColorDetail(item)">
                {{ item.product_color_code + item.product_color_name }}<span v-if="index !== row.product_color_list.length - 1">、</span>
              </el-link>
            </template>
          </template>
          <template #status_name="{ row }">
            <StatusTag :status="row.status" :name="row.status_name" />
          </template>
          <template #sale_system_name_list="{ row }">
            <span v-for="(item, index) in row.sale_system_name_list" :key="index">{{ item }}</span>
          </template>
          <template #duty_str="{ row }">
            <span v-for="(item, index) in row.duty_str" :key="index" class="mr-2">{{ item }}</span>
          </template>
          <template #update_time="{ row }">
            {{ formatTime(row.update_time) }}
          </template>
          <template #operate="{ row }">
            <el-button v-has="`SalesPriceList_del`" type="text" @click="handDelete(row)">
              删除
            </el-button>
            <el-button v-has="`SalesPriceList_statsu`" type="text" @click="updateStatus(row)">
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
          </template>
        </Table>
      </FildCard>
    </div>
  </div>
</template>

<style lang="scss" scoped>
::v-deep(.el-form-item) {
  margin-bottom: 0;
}

::v-deep(.el-descriptions) {
  margin-bottom: 10px;
}
</style>
