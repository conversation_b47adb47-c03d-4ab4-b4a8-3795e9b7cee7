<script setup lang="ts">
import { reactive } from 'vue'
import SelectRole from '@/components/SelectRole/index.vue'

export interface Props {
  selectTreeIds: number[]
}
const props = withDefaults(defineProps<Props>(), {
  selectTreeIds: () => [],
})
const emits = defineEmits(['submit', 'close'])

const state = reactive({
  showModal: false,
  modalName: '',
  selectData: null,
})

function handCancel() {
  state.showModal = false
}

function handChange(val: any) {
  state.selectData = val
}

function handClose() {
  emits('close')
}

async function handleSure() {
  emits('submit', state.selectData)
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="350" height="700" :mask="false" :lock-view="false" :esc-closable="true" resize @close="handClose">
    <SelectRole :select-tree-ids="props.selectTreeIds" :select-type="false" @change="handChange" />
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
