<script setup lang="ts" name="FinishedProductProcurementChangeOrder">
import { Plus } from '@element-plus/icons-vue'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import {
  FinishProductCancel,
  FinishProductDetail,
  FinishProductList,
  FinishProductPass,
} from '@/api/finishedProductProcurement'
import {
  formatDate,
  formatPriceDiv,
  formatUnitPriceDiv,
  formatWeightDiv,
  sumNum,
} from '@/common/format'
import {
  debounce,
  getFilterData,
  orderStatusConfirmBox,
  resetData,
} from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import StatusTag from '@/components/StatusTag/index.vue'
import Table from '@/components/Table.vue'

const router = useRouter()
const state = reactive({
  filterData: {
    order_num: '',
    supplier_id: '',
    receipt_unit_id: '',
    status: '',
    purchase_start_date: '',
    purchase_end_date: '',
  },
  address_list: [] as any,
  multipleSelection: [],
  tableData: [] as any[],
  tableUserData: [] as any[],
})

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  { deep: true },
)

const purchase_time = ref()
watch(
  () => purchase_time.value,
  (value: any) => {
    state.filterData.purchase_start_date = formatDate(value?.[0]) || ''
    state.filterData.purchase_end_date = formatDate(value?.[1]) || ''
  },
)
const showAddress = ref(false)
function openAddress(row: any) {
  showAddress.value = true

  state.address_list = row || []
}

const columnList = ref([
  {
    sortable: true,
    field: 'order_num',
    title: '订单编号',
    soltName: 'order_num',
    width: '8%',
    fixed: 'left',
  },
  {
    sortable: true,
    field: 'supplier_name',
    title: '供应商',
    width: 120,
  },
  {
    sortable: true,
    field: 'receipt_unit_name',
    title: '收货单位名称',
    width: 120,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系名称',
    width: 120,
  },
  {
    sortable: true,
    field: 'receipt_date',
    title: '单据日期',
    width: 120,
  },
  {
    sortable: true,
    field: 'purchase_date',
    title: '采购日期',
    width: 120,
  },
  {
    sortable: true,
    field: 'total_price',
    title: '单据金额',
    isPrice: true,
    width: 120,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    width: 120,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    isDate: true,
    width: 150,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    width: 120,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '修改时间',
    isDate: true,
    width: 150,
  },
  {
    sortable: true,
    field: 'auditor_name',
    title: '审核人',
    width: 120,
  },
  {
    sortable: true,
    field: 'audit_time',
    title: '审核时间',
    isDate: true,
    width: 120,
  },
  {
    sortable: true,
    field: 'status',
    title: '状态',
    showOrder_status: true,
    soltName: 'status',
    width: '5%',
    fixed: 'right',
  },
])

const userColumnList = ref([
  {
    title: '',
    childrenList: [
      {
        sortable: true,
        field: 'finish_product_code',
        title: '成品编号',
        width: 150,
      },
      {
        sortable: true,
        field: 'finish_product_name',
        title: '成品名称',
        width: 100,
      },
      {
        sortable: true,
        field: 'customer_name',
        title: '所属客户',
        width: 100,
      },
      {
        sortable: true,
        field: 'color_type_name',
        title: '颜色类别',
        width: '6%',
      },
      {
        sortable: true,
        field: 'color_code',
        title: '颜色编号',
        width: '6%',
      },
      {
        sortable: true,
        field: 'fabric_type_name',
        title: '布种类型',
        width: 100,
      },
      {
        sortable: true,
        field: 'color_Name',
        title: '颜色名称',
        width: 100,
      },
      {
        sortable: true,
        field: 'dye_factory_color',
        title: '染厂颜色',
        width: 100,
      },
      {
        sortable: true,
        field: 'dye_factory_color_num',
        title: '染厂色号',
        width: 100,
      },
      {
        sortable: true,
        field: 'dye_factory_vat_code',
        title: '染厂缸号',
        width: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'finish_product_level',
        title: '成品等级',
        width: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'dye_craft',
        title: '染整工艺',
        width: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'finish_product_width',
        title: '成品幅宽',
        width: 100,
      },
      {
        sortable: true,
        field: 'finish_product_gram_weight',
        title: '成品克重',
        width: 100,
      },
      {
        sortable: true,
        field: 'paper_tube_weight',
        title: '纸筒重量',
        width: 100,
      },
      {
        sortable: true,
        field: 'finish_product_ingredient',
        title: '成品成分',
        width: 100,
      },
      {
        sortable: true,
        field: '', // TODO 后端没返回
        title: '成品工艺',
        width: 100,
      },
      {
        sortable: true,
        field: 'remark',
        title: '备注',
        width: 100,
      },
    ],
  },
  {
    title: '采购数',
    childrenList: [
      {
        sortable: true,
        field: 'piece_count',
        title: '匹数',
        width: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'piece_weight',
        title: '均重',
        width: 100,
        isWeight: true,
      },
      {
        sortable: true,
        field: '', // TODO 后端没返回
        title: '数量总计',
        width: 100,
        isWeight: true,
      },
      {
        sortable: true,
        field: 'unit',
        title: '单位',
        width: 100,
      },
      {
        sortable: true,
        field: 'length',
        title: '辅助数量',
        width: 100,
        isPrice: true,
      },
    ],
  },
  {
    title: '进仓数',
    childrenList: [
      {
        sortable: true,
        field: 'piece_count',
        title: '匹数',
        width: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'piece_weight',
        title: '均重',
        width: 100,
        isWeight: true,
      },
      {
        sortable: true,
        field: '', // TODO 后端没返回
        title: '数量总计',
        width: 100,
        isWeight: true,
      },
      {
        sortable: true,
        field: 'length',
        title: '辅助数量',
        width: 100,
        isPrice: true,
      },
    ],
  },
  {
    title: '变更数',
    childrenList: [
      {
        sortable: true,
        field: 'piece_count',
        title: '匹数',
        width: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'piece_weight',
        title: '均重',
        width: 100,
        isWeight: true,
      },
      {
        sortable: true,
        field: '', // TODO 后端没返回
        title: '数量总计',
        width: 100,
        isWeight: true,
      },
      {
        sortable: true,
        field: 'length',
        title: '辅助数量',
        width: 100,
        isPrice: true,
      },
    ],
  },
])

onMounted(() => {
  getData()
})
const {
  fetchData: fetchDataList,
  data: dataList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
} = FinishProductList()
async function getData() {
  const status = ((state.filterData.status as unknown as []) || []).join(',')
  await fetchDataList(getFilterData({ ...state.filterData, status }))
}

function handReset() {
  state.filterData = resetData(state.filterData)
}

function handEdit(row: any) {
  router.push({
    name: 'FinishedProductProcurementChangeOrderEdit',
    params: { id: row?.id },
  })
}

const {
  fetchData: fetchDataDetail,
  data: dataDetail,
  loading: loadingDetail,
} = FinishProductDetail()
const userShow = ref(false)
async function showDetail(row: any) {
  await fetchDataDetail({ id: row.id })
  userShow.value = true
}

function handDetail(row: any) {
  router.push({
    name: 'FinishedProductProcurementChangeOrderDetail',
    params: { id: row?.id },
  })
}

async function handPass(id: number) {
  const msg = { title: '是否审核该订单', desc: '点击审核后订单将审核通过' }
  await orderStatusConfirmBox({ id, message: msg, api: FinishProductPass })
  getData()
}
async function handCancel(id: number) {
  const msg = {
    title: '是否消审该订单',
    desc: '点击消审后订单将变回待审核状态',
  }
  await orderStatusConfirmBox({ id, message: msg, api: FinishProductCancel })
  getData()
}

const footerCellClassName = ref<any>(() => {})

const userTableConfig = ref({
  showSpanHeader: true,
  loading: loadingDetail,
  showSlotNums: true,
  footerMethod: (val: any) => FooterMethod(val),
  footerCellClassName: footerCellClassName.value,
})
function FooterMethod({ columns, data }: any) {
  const footData: any = {}
  const res = [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['piece_count'].includes(column.property)) {
        footData[column.property] = formatPriceDiv(
          sumNum(data, 'piece_count') as any,
        )
        return footData[column.property]
      }
    }),
  ]
  userTableConfig.value.footerCellClassName = ({ column }: any) => {
    if (column.field === 'piece_count') {
      if (footData[column.field] > 0)
        return 'col-red'
      else if (footData[column.field] < 0)
        return 'col-green'
      else
        return ''
    }
  }
  return res
}

const tableConfig = ref({
  showSlotNums: true,
  loading: loadingList,
  showPagition: true,
  page: pageList,
  size: sizeList,
  total: totalList,
  showCheckBox: true,
  height: '430',
  showOperate: true,
  operateWidth: '180',
  showSort: false,
  handleSizeChange,
  handleCurrentChange,
})

onActivated(() => {
  getData()
})
</script>

<template>
  <FildCard title="" :tool-bar="false" class="mb-[5px]">
    <slot>
      <el-descriptions
        title=""
        :column="5"
        border
        size="small"
        class="mb-[20px]"
      >
        <el-descriptions-item label="订单编号">
          <el-input
            v-model="state.filterData.order_num"
            placeholder="订单编号"
          />
        </el-descriptions-item>
        <el-descriptions-item label="供应商">
          <SelectComponents
            v-model="state.filterData.supplier_id"
            style="width: 200px"
            api="BusinessUnitSupplierEnumAll"
            label-field="name"
            value-field="id"
            clearable
          />
        </el-descriptions-item>
        <el-descriptions-item label="收货单位">
          <SelectComponents
            v-model="state.filterData.receipt_unit_id"
            style="width: 200px"
            api="GetBusinessUnitListApi"
            label-field="name"
            value-field="id"
            clearable
          />
        </el-descriptions-item>
        <el-descriptions-item label="采购时间">
          <SelectDate v-model="purchase_time" />
        </el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <SelectComponents
            v-model="state.filterData.status"
            :multiple="true"
            style="width: 200px"
            api="GetAuditStatusEnum"
            label-field="name"
            value-field="id"
            clearable
          />
        </el-descriptions-item>
        <el-descriptions-item label="">
          <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
            清除条件
          </el-button>
        </el-descriptions-item>
      </el-descriptions>
    </slot>
  </FildCard>
  <FildCard>
    <template #right-top>
      <BottonExcel title="导出文件" />
      <el-button
        v-has="'FinishedProductProcurementAdd'"
        style="margin-left: 10px"
        type="primary"
        :icon="Plus"
        @click="handAdd"
      >
        新建
      </el-button>
    </template>
    <Table
      :config="tableConfig"
      :table-list="dataList.list"
      :column-list="columnList"
    >
      <template #order_num="{ row }">
        <el-link type="primary" @click="showDetail(row)">
          {{
            row.order_num
          }}
        </el-link>
      </template>
      <template #status="{ row }">
        <StatusTag :status="row.status" />
      </template>
      <template #operate="{ row }">
        <el-button
          v-has="'FinishedProductProcurementDetail'"
          type="text"
          @click="handDetail(row)"
        >
          查看
        </el-button>
        <el-button
          v-if="row.status === 1 || row.status === 3"
          v-has="'FinishedProductProcurementEdit'"
          type="text"
          @click="handEdit(row)"
        >
          编辑
        </el-button>
        <el-button
          v-if="row.status === 1"
          v-has="'FinishedProductProcurement_PASS'"
          type="text"
          @click="handPass(row.id)"
        >
          审核
        </el-button>
        <el-button
          v-if="row.status === 2"
          v-has="'FinishedProductProcurement_cancel'"
          type="text"
          @click="handCancel(row.id)"
        >
          消审
        </el-button>
      </template>
    </Table>
  </FildCard>
  <FildCard v-if="userShow" title="原料信息" class="mt-[5px]">
    <Table
      :config="userTableConfig"
      :table-list="dataDetail.items"
      :column-list="userColumnList"
    >
      <template #logistics="{ row }">
        <el-link type="primary" @click="openAddress(row)">
          查看
        </el-link>
      </template>
      <template #whole_piece_weight="{ row }">
        {{ formatWeightDiv(row.whole_piece_weight) }}
      </template>
      <template #whole_weight="{ row }">
        {{ formatWeightDiv(row.whole_weight) }}
      </template>
      <template #bulk_weight="{ row }">
        {{ formatWeightDiv(row.bulk_weight) }}
      </template>
      <template #total_weight="{ row }">
        {{ formatWeightDiv(row.total_weight) }}
      </template>
      <template #unit_price="{ row }">
        {{ formatUnitPriceDiv(row.unit_price) }}
      </template>
      <template #total_price="{ row }">
        {{ formatPriceDiv(row.total_price) }}
      </template>
    </Table>
  </FildCard>
</template>

<style lang="scss">
.yuan {
  width: 10px;
  height: 10px;
  background: #51c41b;
  border-radius: 50%;
  margin-right: 10px;
}

.yuan_red {
  width: 10px;
  height: 10px;
  background: #f5232d;
  border-radius: 50%;
  margin-right: 10px;
}
</style>
