<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import { debounce, getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { getFinishProductDropdownListPage } from '@/api/productSalePlan'
import SelectCascader from '@/components/SelectCascader/productType.vue'

export interface Props {
  id: number
  modelValue: boolean
}

const props = withDefaults(defineProps<Props>(), {
  id: 0,
  modelValue: false,
})
const emit = defineEmits(['update:modelValue', 'submit'])
const state = reactive({
  filterData: {
    finish_product_code: '',
    finish_product_name: '',
    measurement_unit_id: '',
    type_grey_fabric_id: '',
    finish_product_level_id: '',
    grey_fabric_code: '',
    grey_fabric_name: '',
  },
  tableList: [],
})

const { fetchData: fetchDataList, data: dataList, total, page, size, loading, handleSizeChange, handleCurrentChange } = getFinishProductDropdownListPage()
async function getData() {
  await fetchDataList(getFilterData(state.filterData))
}

const showModal = ref<boolean>(false)
watch(
  () => props.modelValue,
  (show) => {
    showModal.value = show
    if (show) {
      getData()
    }
    else {
      state.filterData = {
        finish_product_code: '',
        finish_product_name: '',
        measurement_unit_id: '',
        type_grey_fabric_id: '',
        finish_product_level_id: '',
        grey_fabric_code: '',
        grey_fabric_name: '',
      }
    }
  },
)

const columnList = ref<any>([
  {
    title: '成品编号',
    field: 'finish_product_code',
    fixed: 'left',
    minWidth: 100,
  },
  {
    title: '成品名称',
    field: 'finish_product_name',
    fixed: 'left',
    minWidth: 100,
  },
  {
    title: '成品全称',
    field: 'finish_product_full_name',
    fixed: 'left',
    minWidth: 100,
  },
  {
    title: '布种类型',
    field: 'type_grey_fabric_name',
    minWidth: 100,
  },
  {
    title: '成品幅宽',
    field: 'finish_product_width',
    soltName: 'finish_product_width',
    minWidth: 100,
  },
  {
    title: '成品克重',
    field: 'finish_product_gram_weight',
    soltName: 'finish_product_gram_weight',
    minWidth: 100,
  },
  {
    title: '坯布编号',
    field: 'grey_fabric_code',
    minWidth: 100,
  },
  {
    title: '坯布名称',
    field: 'grey_fabric_name',
    minWidth: 100,
  },
  {
    title: '成品成分',
    field: 'finish_product_ingredient',
    minWidth: 100,
  },
  {
    title: '成品工艺',
    field: 'finish_product_craft',
    minWidth: 100,
  },
  {
    title: '单位',
    field: 'measurement_unit_name',
    fixed: 'right',
    minWidth: 100,
  },
])

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  { deep: true },
)

const selectList = ref<any[]>()
function submit() {
  emit('submit', selectList.value)
}

function onClose() {
  emit('update:modelValue', false)
}

function handAllSelect({ records }: any) {
  selectList.value = records
}

function handleSelectionChange({ records }: any) {
  selectList.value = records
}

const tableConfig = ref({
  showCheckBox: true,
  show_footer: false,
  handAllSelect,
  handleSelectionChange,
  showPagition: true,
  loading,
  size,
  page,
  total,
  scrollY: { enabled: true, gt: 40 },
  height: '100%',
  filterStatus: false,
  checkRowKeys: [] as number[],
  handleSizeChange,
  handleCurrentChange,
})
</script>

<template>
  <vxe-modal v-model="showModal" show-footer title="添加成品商品" width="75vw" height="75vh" :mask="false" :lock-view="false" :esc-closable="true" resize @close="onClose">
    <div class="flex flex-col overflow-hidden h-full">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="成品编号:">
          <template #content>
            <el-input v-model="state.filterData.finish_product_code" placeholder="成品编号" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称:">
          <template #content>
            <el-input v-model="state.filterData.finish_product_name" placeholder="成品名称" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布编号:">
          <template #content>
            <el-input v-model="state.filterData.grey_fabric_code" placeholder="坯布编号" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布名称:">
          <template #content>
            <el-input v-model="state.filterData.grey_fabric_name" placeholder="坯布名称" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单位:">
          <template #content>
            <SelectComponents v-model="state.filterData.measurement_unit_id" api="getInfoBaseMeasurementUnitList" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="布种类型:">
          <template #content>
            <SelectCascader @change-value="({ ids }) => state.filterData.type_grey_fabric_id = ids?.[ids.length - 1] || ''" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品等级:">
          <template #content>
            <SelectComponents v-model="state.filterData.finish_product_level_id" api="GetInfoBaseFinishedProductLevelEnumList" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
      </div>
      <Table :config="tableConfig" :table-list="dataList.list" :column-list="columnList">
        <template #creatTime />
        <template #finish_product_width="{ row }">
          {{ row.finish_product_width }} {{ row.finish_product_width_unit_name }}
        </template>
        <template #finish_product_gram_weight="{ row }">
          {{ row.finish_product_gram_weight }}
          {{ row.finish_product_gram_weight_unit_name }}
        </template>
      </Table>
    </div>
    <template #footer>
      <el-button type="primary" size="small" @click="submit">
        提交
      </el-button>
    </template>
  </vxe-modal>
</template>
